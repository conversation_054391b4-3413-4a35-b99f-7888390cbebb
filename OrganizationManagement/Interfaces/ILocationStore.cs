using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
namespace OrganizationManagement.Interfaces;

public interface ILocationStore
{
    Task AddCountryAsync(Country country, bool saveChanges = false);
    Task AddCityAsync(City city, bool saveChanges = false);
    Task AddStateAsync(State state, bool saveChanges = false);
    Task AddCountriesAsync(IEnumerable<Country> countries, bool saveChanges = false);
    Task AddCitiesAsync(IEnumerable<City> cities, bool saveChanges = false);
    Task AddStatesAsync(IEnumerable<State> states, bool saveChanges = false);
    Task UpdateCountryAsync(Country county, bool saveChanges = false);
    Task UpdateCityAsync(City city, bool saveChanges = false);
    Task UpdateStateAsync(State state, bool saveChanges = false);
    Task UpdateCountriesAsync(IEnumerable<Country> countries, bool saveChanges = false);
    Task UpdateCitiesAsync(IEnumerable<City> cities, bool saveChanges = false);
    Task UpdateStatesAsync(IEnumerable<State> states, bool saveChanges = false);
    Task<IEnumerable<ActiveCountryDto>> GetActiveCountriesAsync();
    Task<IEnumerable<ActiveStateDto>> GetActiveStatesByCountryAsync(int countryId);
    Task<IEnumerable<ActiveCityDto>> GetActiveCitiesByStateAsync(int stateId);
    Task<int> GetAutoIncrementIdByIdForCountryAsync(string countryId);
    Task<int> GetAutoIncrementIdByIdForStateAsync(string stateId);
    Task<int> GetAutoIncrementIdByIdForCityAsync(string cityId);
    Task<IEnumerable<int>> GetAutoIncrementIdsByIdForCountryAsync(IEnumerable<string> countryIds);
    Task<IEnumerable<int>> GetAutoIncrementIdsByIdForStateAsync(IEnumerable<string> stateIds);
    Task<IEnumerable<int>> GetAutoIncrementIdsByIdForCityAsync(IEnumerable<string> cityIds);
    Task<IEnumerable<Country>> GetCountriesForUpdateAsync(IEnumerable<string> countryIds);
    Task<IEnumerable<State>> GetStatesForUpdateAsync(IEnumerable<string> stateIds);
    Task<IEnumerable<City>> GetCitiesForUpdateAsync(IEnumerable<string> cityIds);
    Task<Country?> GetCountryForUpdateAsync(string countryId);
    Task<State?> GetStateForUpdateAsync(string stateId);
    Task<City?> GetCityForUpdateAsync(string cityId);
    Task<bool> IsCountryExistsAsync(string name);
    Task<bool> IsStateExistsAsync(string name, int countryId);
    Task<bool> IsCityExistsAsync(string name, int stateId);
    Task<bool> IsCountryExistsAsync(string name, string updateId);
    Task<bool> IsStateExistsAsync(string name, int countryId, string updateId);
    Task<bool> IsCityExistsAsync(string name, int stateId, string updateId);
    Task<IDictionary<string, int>> IdConvertForCountryAsync(IEnumerable<string> countryIds);
    Task<IDictionary<string, int>> IdConvertForStateAsync(IEnumerable<string> stateIds);
    Task<IDictionary<string, int>> IdConvertForCityAsync(IEnumerable<string> cityIds);
    Task<string?> GetCountryIdByNameAsync(string countryName);
    Task<string?> GetStateIdByNameAsync(string stateName);
}