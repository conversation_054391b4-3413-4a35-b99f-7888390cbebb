using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Interfaces;

public interface IWorkcenterStore
{
    Task<Workcenter> AddWorkcenterAsync(Workcenter workcenter, bool saveChanges = false);
    Task<IEnumerable<Workcenter>> AddWorkcentersAsync(IEnumerable<Workcenter> workcenters, bool saveChanges = false);
    Task<Workcenter> UpdateWorkcenterAsync(Workcenter workcenter, bool saveChanges = false);
    Task<IEnumerable<Workcenter>> UpdateWorkcentersAsync(IEnumerable<Workcenter> workcenters, bool saveChanges = false);
    Task<Workcenter?> GetWorkcenterForUpdateAsync(string workcenterId);
    Task<IEnumerable<Workcenter>> GetWorkcentersForUpdateAsync(IEnumerable<string> workcenterIds);
    Task<WorkcenterAddress?> AddWorkcenterAddressAsync(WorkcenterAddress workcenterAddress, bool saveChanges = false);
    Task<IEnumerable<WorkcenterAddress>?> AddWorkcenterAddressesAsync(IEnumerable<WorkcenterAddress> workcenterAddresses, bool saveChanges = false);
    Task<WorkcenterAddress?> UpdateWorkcenterAddressAsync(WorkcenterAddress workcenterAddress, bool saveChanges = false);
    Task<IEnumerable<WorkcenterAddress>?> UpdateWorkcenterAddressesAsync(IEnumerable<WorkcenterAddress> workcenterAddresses, bool saveChanges = false);
    Task<WorkcenterAddress?> GetWorkcenterAddressForUpdateAsync(string workcenterAddressId);
    Task<IEnumerable<WorkcenterAddress>> GetWorkcenterAddressesForUpdateAsync(IEnumerable<string> workcenterAddressIds);
    Task<WorkcenterPhone?> AddWorkcenterPhoneAsync(WorkcenterPhone workcenterPhone, bool saveChanges = false);
    Task<IEnumerable<WorkcenterPhone>?> AddWorkcenterPhonesAsync(IEnumerable<WorkcenterPhone> workcenterPhones, bool saveChanges = false);
    Task<WorkcenterPhone?> UpdateWorkcenterPhoneAsync(WorkcenterPhone workcenterPhone, bool saveChanges = false);
    Task<IEnumerable<WorkcenterPhone>?> UpdateWorkcenterPhonesAsync(IEnumerable<WorkcenterPhone> workcenterPhones, bool saveChanges = false);
    Task<WorkcenterPhone?> GetWorkcenterPhoneForUpdateAsync(string workcenterPhoneId);
    Task<IEnumerable<WorkcenterPhone>> GetWorkcenterPhonesForUpdateAsync(IEnumerable<string> workcenterPhoneIds);
    Task<WorkcenterExtraFeature?> AddWorkcenterExtraFeatureAsync(WorkcenterExtraFeature workcenterExtraFeature, bool saveChanges = false);
    Task<IEnumerable<WorkcenterExtraFeature>?> AddWorkcenterExtraFeaturesAsync(IEnumerable<WorkcenterExtraFeature> workcenterExtraFeatures, bool saveChanges = false);
    Task<WorkcenterExtraFeature?> UpdateWorkcenterExtraFeatureAsync(WorkcenterExtraFeature workcenterExtraFeature, bool saveChanges = false);
    Task<IEnumerable<WorkcenterExtraFeature>?> UpdateWorkcenterExtraFeaturesAsync(IEnumerable<WorkcenterExtraFeature> workcenterExtraFeatures, bool saveChanges = false);
    Task<WorkcenterExtraFeature?> GetWorkcenterExtraFeatureForUpdateAsync(string workcenterExtraFeatureId);
    Task<IEnumerable<WorkcenterExtraFeature>> GetWorkcenterExtraFeaturesForUpdateAsync(IEnumerable<string> workcenterExtraFeatureIds);
    Task<IDictionary<string, int>> IdConvertForWorkcenterAsync(IEnumerable<string> workcenterIds);
    Task<IEnumerable<WorkcenterTreeDto>> GetWorkcentersForTreeAsync();
    Task<PagedListDto<WorkcenterViewDto>> GetWorkcentersAsync(PagedListCo<GetWorkcentersCo> co);
    Task<WorkcenterViewDto?> GetWorkcenterAsync(string workcenterId);
    Task<IEnumerable<WorkcenterAddressViewDto>> GetWorkcenterAddressesAsync(string workcenterId);
    Task<IEnumerable<WorkcenterPhoneViewDto>> GetWorkcenterPhonesAsync(string workcenterId);
    Task<IEnumerable<WorkcenterExtraFeatureViewDto>> GetWorkcenterExtraFeaturesAsync(string workcenterId);
    Task<WorkcenterViewCompleteDto?> GetWorkcenterCompleteAsync(string workcenterId);
    Task<PagedListDto<WorkcenterForAutoCompleteDto>> GetWorkcentersForAutoCompleteAsync(PagedListCo<GetWorkcentersCo> co);
}