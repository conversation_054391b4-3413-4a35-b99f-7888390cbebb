using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Interfaces;

public interface IWorkcenterManager
{
    Task<IEnumerable<Workcenter>> AddWorkcentersAsync(IEnumerable<WorkcenterAddDto> workcenters, bool saveChanges = false);
    Task<Workcenter> AddWorkcenterAsync(WorkcenterAddDto workcenter, bool saveChanges = false);
    Task<IEnumerable<Workcenter>> UpdateWorkcentersAsync(IEnumerable<WorkcenterUpdateDto> workcenters, bool saveChanges = false);
    Task<Workcenter> UpdateWorkcenterAsync(WorkcenterUpdateDto workcenter, bool saveChanges = false);
    Task<IDictionary<string, int>> IdConvertForWorkcenterAsync(IEnumerable<string> workcenterIds);
    Task<IEnumerable<WorkcenterTreeDto>> GetWorkcentersForTreeAsync(string? parentWorkcenterId = null);
    Task<WorkcenterViewDto?> GetWorkcenterAsync(string workcenterId);
    Task<IEnumerable<WorkcenterAddressViewDto>?> GetWorkcenterAddressesAsync(string workcenterId);
    Task<IEnumerable<WorkcenterPhoneViewDto>?> GetWorkcenterPhonesAsync(string workcenterId);
    Task<IEnumerable<WorkcenterExtraFeatureViewDto>?> GetWorkcenterExtraFeaturesAsync(string workcenterId);
    Task<WorkcenterViewCompleteDto?> GetWorkcenterCompleteAsync(string workcenterId);
    Task<PagedListDto<WorkcenterForAutoCompleteDto>?> GetWorkcentersForAutoCompleteAsync(PagedListCo<GetWorkcentersCo> co);
    Task<WorkcenterAddress?> AddWorkcenterAddressAsync(WorkcenterAddressAddDto workcenterAddress, bool saveChanges = false);
    Task<IEnumerable<WorkcenterAddress>?> AddWorkcenterAddressesAsync(IEnumerable<WorkcenterAddressAddDto> workcenterAddresses, bool saveChanges = false);
    Task<WorkcenterAddress?> UpdateWorkcenterAddressAsync(WorkcenterAddressUpdateDto workcenterAddress, bool saveChanges = false);
    Task<IEnumerable<WorkcenterAddress>?> UpdateWorkcenterAddressesAsync(IEnumerable<WorkcenterAddressUpdateDto> workcenterAddresses, bool saveChanges = false);
    Task<WorkcenterPhone?> AddWorkcenterPhoneAsync(WorkcenterPhoneAddDto workcenterPhone, bool saveChanges = false);
    Task<IEnumerable<WorkcenterPhone>?> AddWorkcenterPhonesAsync(IEnumerable<WorkcenterPhoneAddDto> workcenterPhones, bool saveChanges = false);
    Task<WorkcenterPhone?> UpdateWorkcenterPhoneAsync(WorkcenterPhoneUpdateDto workcenterPhone, bool saveChanges = false);
    Task<IEnumerable<WorkcenterPhone>?> UpdateWorkcenterPhonesAsync(IEnumerable<WorkcenterPhoneUpdateDto> workcenterPhones, bool saveChanges = false);
    Task<WorkcenterExtraFeature?> AddWorkcenterExtraFeatureAsync(WorkcenterExtraFeatureAddDto workcenterExtraFeature, bool saveChanges = false);
    Task<IEnumerable<WorkcenterExtraFeature>?> AddWorkcenterExtraFeaturesAsync(IEnumerable<WorkcenterExtraFeatureAddDto> workcenterExtraFeatures, bool saveChanges = false);
    Task<WorkcenterExtraFeature?> UpdateWorkcenterExtraFeatureAsync(WorkcenterExtraFeatureUpdateDto workcenterExtraFeature, bool saveChanges = false);
    Task<IEnumerable<WorkcenterExtraFeature>?> UpdateWorkcenterExtraFeaturesAsync(IEnumerable<WorkcenterExtraFeatureUpdateDto> workcenterExtraFeatures, bool saveChanges = false);
}