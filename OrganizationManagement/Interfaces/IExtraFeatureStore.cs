using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
namespace OrganizationManagement.Interfaces;

public interface IExtraFeatureStore
{
    Task<IEnumerable<Address>> AddAddressesAsync(IEnumerable<Address> address, bool saveChanges = false);
    Task<Address> AddAddressAsync(Address address, bool saveChanges = false);
    Task<IEnumerable<Phone>> AddPhonesAsync(IEnumerable<Phone> phone, bool saveChanges = false);
    Task<Phone> AddPhoneAsync(Phone phone, bool saveChanges = false);
    Task<IEnumerable<ExtraFeature>> AddExtraFeaturesAsync(IEnumerable<ExtraFeature> extraFeature, bool saveChanges = false);
    Task<ExtraFeature> AddExtraFeatureAsync(ExtraFeature extraFeature, bool saveChanges = false);
    Task<IEnumerable<Address>> UpdateAddressesAsync(IEnumerable<Address> address, bool saveChanges = false);
    Task<Address> UpdateAddressAsync(Address address, bool saveChanges = false);
    Task<IEnumerable<Phone>> UpdatePhonesAsync(IEnumerable<Phone> phone, bool saveChanges = false);
    Task<Phone> UpdatePhoneAsync(Phone phone, bool saveChanges = false);
    Task<IEnumerable<ExtraFeature>> UpdateExtraFeaturesAsync(IEnumerable<ExtraFeature> extraFeature, bool saveChanges = false);
    Task<ExtraFeature> UpdateExtraFeatureAsync(ExtraFeature extraFeature, bool saveChanges = false);
    Task<IEnumerable<PhoneViewDto>> GetPhonesAsync(IEnumerable<string> phoneIds);
    Task<IEnumerable<AddressViewDto>> GetAddressesAsync(IEnumerable<string> addressIds);
    Task<IEnumerable<ExtraFeatureViewDto>> GetExtraFeaturesAsync(IEnumerable<string> extraFeatureIds);
    Task<IEnumerable<Address>> GetAddressesForUpdateAsync(IEnumerable<string> addressIds);
    Task<IEnumerable<Phone>> GetPhonesForUpdateAsync(IEnumerable<string> phoneIds);
    Task<IEnumerable<ExtraFeature>> GetExtraFeaturesForUpdateAsync(IEnumerable<string> extraFeatureIds);
    Task<Address?> GetAddressForUpdateAsync(string addressId);
    Task<Phone?> GetPhoneForUpdateAsync(string phoneId);
    Task<ExtraFeature?> GetExtraFeatureForUpdateAsync(string extraFeatureId);
}