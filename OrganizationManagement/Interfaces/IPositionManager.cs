using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Interfaces;

public interface IPositionManager
{
    Task<IEnumerable<Position>> AddPositionsAsync(IEnumerable<PositionAddDto> positions, bool saveChanges = false);
    Task<Position> AddPositionAsync(PositionAddDto position, bool saveChanges = false);
    Task<IEnumerable<Position>> UpdatePositionsAsync(IEnumerable<PositionUpdateDto> positions, bool saveChanges = false);
    Task<Position> UpdatePositionAsync(PositionUpdateDto position, bool saveChanges = false);
    Task<IDictionary<string, int>> IdConvertForPositionAsync(IEnumerable<string> positionIds);
    Task<IEnumerable<PositionTreeDto>> GetPositionsForTreeAsync(string? parentPositionId = null);
    Task<PositionViewDto?> GetPositionAsync(string positionId);
    // Task<PositionViewCompleteDto?> GetPositionCompleteAsync(string positionId);
    Task<PagedListDto<PositionForAutoCompleteDto>?> GetPositionsForAutoCompleteAsync(PagedListCo<GetPositionsCo> co);
}