using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Interfaces;

public interface IUnitManager
{
    Task<IEnumerable<Unit>> AddUnitsAsync(IEnumerable<UnitAddDto> units, bool saveChanges = false);
    Task<Unit> AddUnitAsync(UnitAddDto unit, bool saveChanges = false);
    Task<IEnumerable<Unit>> UpdateUnitsAsync(IEnumerable<UnitUpdateDto> units, bool saveChanges = false);
    Task<Unit> UpdateUnitAsync(UnitUpdateDto unit, bool saveChanges = false);
    Task<IDictionary<string, int>> IdConvertForUnitAsync(IEnumerable<string> unitIds);
    Task<IEnumerable<UnitTreeDto>> GetUnitsForTreeAsync(string? parentUnitId = null);
    Task<UnitViewDto?> GetUnitAsync(string unitId);
    Task<IEnumerable<UnitAddressViewDto>?> GetUnitAddressesAsync(string unitId);
    Task<IEnumerable<UnitPhoneViewDto>?> GetUnitPhonesAsync(string unitId);
    Task<IEnumerable<UnitExtraFeatureViewDto>?> GetUnitExtraFeaturesAsync(string unitId);
    Task<UnitViewCompleteDto?> GetUnitCompleteAsync(string unitId);
    Task<PagedListDto<UnitForAutoCompleteDto>?> GetUnitsForAutoCompleteAsync(PagedListCo<GetUnitsCo> co);
    Task<UnitAddress?> AddUnitAddressAsync(UnitAddressAddDto unitAddress, bool saveChanges = false);
    Task<IEnumerable<UnitAddress>?> AddUnitAddressesAsync(IEnumerable<UnitAddressAddDto> unitAddresses, bool saveChanges = false);
    Task<UnitAddress?> UpdateUnitAddressAsync(UnitAddressUpdateDto unitAddress, bool saveChanges = false);
    Task<IEnumerable<UnitAddress>?> UpdateUnitAddressesAsync(IEnumerable<UnitAddressUpdateDto> unitAddresses, bool saveChanges = false);
    Task<UnitPhone?> AddUnitPhoneAsync(UnitPhoneAddDto unitPhone, bool saveChanges = false);
    Task<IEnumerable<UnitPhone>?> AddUnitPhonesAsync(IEnumerable<UnitPhoneAddDto> unitPhones, bool saveChanges = false);
    Task<UnitPhone?> UpdateUnitPhoneAsync(UnitPhoneUpdateDto unitPhone, bool saveChanges = false);
    Task<IEnumerable<UnitPhone>?> UpdateUnitPhonesAsync(IEnumerable<UnitPhoneUpdateDto> unitPhones, bool saveChanges = false);
    Task<UnitExtraFeature?> AddUnitExtraFeatureAsync(UnitExtraFeatureAddDto unitExtraFeature, bool saveChanges = false);
    Task<IEnumerable<UnitExtraFeature>?> AddUnitExtraFeaturesAsync(IEnumerable<UnitExtraFeatureAddDto> unitExtraFeatures, bool saveChanges = false);
    Task<UnitExtraFeature?> UpdateUnitExtraFeatureAsync(UnitExtraFeatureUpdateDto unitExtraFeature, bool saveChanges = false);
    Task<IEnumerable<UnitExtraFeature>?> UpdateUnitExtraFeaturesAsync(IEnumerable<UnitExtraFeatureUpdateDto> unitExtraFeatures, bool saveChanges = false);
}