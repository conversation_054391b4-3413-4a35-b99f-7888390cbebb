using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
namespace OrganizationManagement.Interfaces;

public interface IExtraFeatureManager
{
    Task<IEnumerable<Address>> AddAddressesAsync(IEnumerable<AddressAddDto> addresses, bool saveChanges = false);
    Task<Address> AddAddressAsync(AddressAddDto address, bool saveChanges = false);
    Task<IEnumerable<Phone>> AddPhonesAsync(IEnumerable<PhoneAddDto> phones, bool saveChanges = false);
    Task<Phone> AddPhoneAsync(PhoneAddDto phone, bool saveChanges = false);
    Task<IEnumerable<ExtraFeature>> AddExtraFeaturesAsync(IEnumerable<ExtraFeatureAddDto> extraFeatures, bool saveChanges = false);
    Task<ExtraFeature> AddExtraFeatureAsync(ExtraFeatureAddDto extraFeature, bool saveChanges = false);
    Task<IEnumerable<Address>> UpdateAddressesAsync(IEnumerable<AddressUpdateDto> addresses, bool saveChanges = false);
    Task<Address> UpdateAddressAsync(AddressUpdateDto address, bool saveChanges = false);
    Task<IEnumerable<Phone>> UpdatePhonesAsync(IEnumerable<PhoneUpdateDto> phones, bool saveChanges = false);
    Task<Phone> UpdatePhoneAsync(PhoneUpdateDto phone, bool saveChanges = false);
    Task<IEnumerable<ExtraFeature>> UpdateExtraFeaturesAsync(IEnumerable<ExtraFeatureUpdateDto> extraFeatures, bool saveChanges = false);
    Task<ExtraFeature> UpdateExtraFeatureAsync(ExtraFeatureUpdateDto extraFeature, bool saveChanges = false);
    // Task<IEnumerable<AddressDto>> GetAddressesAsync(IEnumerable<string> addressIds);
    // Task<IEnumerable<PhoneDto>> GetPhonesAsync(IEnumerable<string> phoneIds);
    // Task<IEnumerable<ExtraFeatureDto>> GetExtraFeaturesAsync(IEnumerable<string> extraFeatureIds);
}