using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Interfaces;

public interface IPersonStore
{
    Task<Person> AddPersonAsync(Person person, bool saveChanges = false);
    Task<IEnumerable<Person>> AddPersonsAsync(IEnumerable<Person> persons, bool saveChanges = false);
    Task<Person> UpdatePersonAsync(Person person, bool saveChanges = false);
    Task<IEnumerable<Person>> UpdatePersonsAsync(IEnumerable<Person> persons, bool saveChanges = false);
    Task<Person?> GetPersonForUpdateAsync(string personId);
    Task<IEnumerable<Person>> GetPersonsForUpdateAsync(IEnumerable<string> personIds);
    Task<PersonAddress?> AddPersonAddressAsync(PersonAddress personAddress, bool saveChanges = false);
    Task<IEnumerable<PersonAddress>?> AddPersonAddressesAsync(IEnumerable<PersonAddress> personAddresses, bool saveChanges = false);
    Task<PersonAddress?> UpdatePersonAddressAsync(PersonAddress personAddress, bool saveChanges = false);
    Task<IEnumerable<PersonAddress>?> UpdatePersonAddressesAsync(IEnumerable<PersonAddress> personAddresses, bool saveChanges = false);
    Task<PersonAddress?> GetPersonAddressForUpdateAsync(string personAddressId);
    Task<IEnumerable<PersonAddress>> GetPersonAddressesForUpdateAsync(IEnumerable<string> personAddressIds);
    Task<PersonPhone?> AddPersonPhoneAsync(PersonPhone personPhone, bool saveChanges = false);
    Task<IEnumerable<PersonPhone>?> AddPersonPhonesAsync(IEnumerable<PersonPhone> personPhones, bool saveChanges = false);
    Task<PersonPhone?> UpdatePersonPhoneAsync(PersonPhone personPhone, bool saveChanges = false);
    Task<IEnumerable<PersonPhone>?> UpdatePersonPhonesAsync(IEnumerable<PersonPhone> personPhones, bool saveChanges = false);
    Task<PersonPhone?> GetPersonPhoneForUpdateAsync(string personPhoneId);
    Task<IEnumerable<PersonPhone>> GetPersonPhonesForUpdateAsync(IEnumerable<string> personPhoneIds);
    Task<PersonExtraFeature?> AddPersonExtraFeatureAsync(PersonExtraFeature personExtraFeature, bool saveChanges = false);
    Task<IEnumerable<PersonExtraFeature>?> AddPersonExtraFeaturesAsync(IEnumerable<PersonExtraFeature> personExtraFeatures, bool saveChanges = false);
    Task<PersonExtraFeature?> UpdatePersonExtraFeatureAsync(PersonExtraFeature personExtraFeature, bool saveChanges = false);
    Task<IEnumerable<PersonExtraFeature>?> UpdatePersonExtraFeaturesAsync(IEnumerable<PersonExtraFeature> personExtraFeatures, bool saveChanges = false);
    Task<PersonExtraFeature?> GetPersonExtraFeatureForUpdateAsync(string personExtraFeatureId);
    Task<IEnumerable<PersonExtraFeature>> GetPersonExtraFeaturesForUpdateAsync(IEnumerable<string> personExtraFeatureIds);
    Task<IDictionary<string, int>> IdConvertForPersonAsync(IEnumerable<string> personIds);
    Task<PagedListDto<PersonViewDto>> GetPersonsAsync(PagedListCo<GetPersonsCo> co);
    Task<PersonViewDto?> GetPersonAsync(string personId);
    Task<IEnumerable<PersonAddressViewDto>> GetPersonAddressesAsync(string personId);
    Task<IEnumerable<PersonPhoneViewDto>> GetPersonPhonesAsync(string personId);
    Task<IEnumerable<PersonExtraFeatureViewDto>> GetPersonExtraFeaturesAsync(string personId);
    Task<PersonDetailViewDto?> GetPersonDetailAsync(string personId);
    Task<PersonViewCompleteDto?> GetPersonCompleteAsync(string personId);
    Task<PagedListDto<PersonForAutoCompleteDto>> GetPersonsForAutoCompleteAsync(PagedListCo<GetPersonsCo> co);
    Task<PersonDetail> AddPersonDetailAsync(PersonDetail personDetail, bool saveChanges = false);
    Task<IEnumerable<PersonDetail>> AddPersonDetailsAsync(IEnumerable<PersonDetail> personDetails, bool saveChanges = false);
    Task<PersonDetail> UpdatePersonDetailAsync(PersonDetail personDetail, bool saveChanges = false);
    Task<IEnumerable<PersonDetail>> UpdatePersonDetailsAsync(IEnumerable<PersonDetail> personDetails, bool saveChanges = false);
    Task<IEnumerable<PersonDetail>> GetPersonDetailsForUpdateAsync(IEnumerable<string> personDetailIds);
    Task<PersonDetail?> GetPersonDetailForUpdateAsync(string personDetailId);
    Task<IDictionary<string, bool>> HasPersonDetailsAsync(IEnumerable<string> personIds);
    Task<bool> HasPersonDetailAsync(string personId);
}