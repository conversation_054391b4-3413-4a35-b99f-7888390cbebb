using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
namespace OrganizationManagement.Interfaces;

public interface ILocationManager
{
    Task<IEnumerable<ActiveCountryDto>> GetActiveCountriesAsync(GetActiveCountriesCo co);
    Task<IEnumerable<ActiveStateDto>> GetActiveStatesByCountryAsync(GetActiveStatesCo co);
    Task<IEnumerable<ActiveCityDto>> GetActiveCitiesByStateAsync(GetActiveCitiesCo co);
    Task AddCountriesAsync(IEnumerable<CountryAddDto> countries);
    Task AddStatesAsync(IEnumerable<StateAddDto> states);
    Task AddCitiesAsync(IEnumerable<CityAddDto> cities);
    Task UpdateCountriesAsync(IEnumerable<CountryUpdateDto> countries);
    Task UpdateStatesAsync(IEnumerable<StateUpdateDto> states);
    Task UpdateCitiesAsync(IEnumerable<CityUpdateDto> cities);
    Task<IDictionary<string, int>> IdConvertForCountryAsync(IEnumerable<string> countryIds);
    Task<IDictionary<string, int>> IdConvertForStateAsync(IEnumerable<string> stateIds);
    Task<IDictionary<string, int>> IdConvertForCityAsync(IEnumerable<string> cityIds);
}