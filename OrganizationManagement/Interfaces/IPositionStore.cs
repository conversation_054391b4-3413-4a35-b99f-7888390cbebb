using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Interfaces;

public interface IPositionStore
{
    Task<Position> AddPositionAsync(Position position, bool saveChanges = false);
    Task<IEnumerable<Position>> AddPositionsAsync(IEnumerable<Position> positions, bool saveChanges = false);
    Task<Position> UpdatePositionAsync(Position position, bool saveChanges = false);
    Task<IEnumerable<Position>> UpdatePositionsAsync(IEnumerable<Position> positions, bool saveChanges = false);
    Task<Position?> GetPositionForUpdateAsync(string positionId);
    Task<IEnumerable<Position>> GetPositionsForUpdateAsync(IEnumerable<string> positionIds);
    Task<IDictionary<string, int>> IdConvertForPositionAsync(IEnumerable<string> positionIds);
    Task<IEnumerable<PositionTreeDto>> GetPositionsForTreeAsync();
    Task<PagedListDto<PositionViewDto>> GetPositionsAsync(PagedListCo<GetPositionsCo> co);
    Task<PositionViewDto?> GetPositionAsync(string positionId);
    Task<PagedListDto<PositionForAutoCompleteDto>> GetPositionsForAutoCompleteAsync(PagedListCo<GetPositionsCo> co);
}