using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Interfaces;

public interface IPersonManager
{
    Task<IEnumerable<Person>> AddPersonsAsync(IEnumerable<PersonAddDto> persons, bool saveChanges = false);
    Task<Person> AddPersonAsync(PersonAddDto person, bool saveChanges = false);
    Task<IEnumerable<Person>> UpdatePersonsAsync(IEnumerable<PersonUpdateDto> persons, bool saveChanges = false);
    Task<Person> UpdatePersonAsync(PersonUpdateDto person, bool saveChanges = false);
    Task<IDictionary<string, int>> IdConvertForPersonAsync(IEnumerable<string> personIds);
    Task<PersonViewDto?> GetPersonAsync(string personId);
    Task<IEnumerable<PersonAddressViewDto>?> GetPersonAddressesAsync(string personId);
    Task<IEnumerable<PersonPhoneViewDto>?> GetPersonPhonesAsync(string personId);
    Task<IEnumerable<PersonExtraFeatureViewDto>?> GetPersonExtraFeaturesAsync(string personId);
    Task<PersonDetailViewDto?> GetPersonDetailAsync(string personId);
    Task<PersonViewCompleteDto?> GetPersonCompleteAsync(string personId);
    Task<PagedListDto<PersonForAutoCompleteDto>?> GetPersonsForAutoCompleteAsync(PagedListCo<GetPersonsCo> co);
    Task<PersonAddress?> AddPersonAddressAsync(PersonAddressAddDto personAddress, bool saveChanges = false);
    Task<IEnumerable<PersonAddress>?> AddPersonAddressesAsync(IEnumerable<PersonAddressAddDto> personAddresses, bool saveChanges = false);
    Task<PersonAddress?> UpdatePersonAddressAsync(PersonAddressUpdateDto personAddress, bool saveChanges = false);
    Task<IEnumerable<PersonAddress>?> UpdatePersonAddressesAsync(IEnumerable<PersonAddressUpdateDto> personAddresses, bool saveChanges = false);
    Task<PersonPhone?> AddPersonPhoneAsync(PersonPhoneAddDto personPhone, bool saveChanges = false);
    Task<IEnumerable<PersonPhone>?> AddPersonPhonesAsync(IEnumerable<PersonPhoneAddDto> personPhones, bool saveChanges = false);
    Task<PersonPhone?> UpdatePersonPhoneAsync(PersonPhoneUpdateDto personPhone, bool saveChanges = false);
    Task<IEnumerable<PersonPhone>?> UpdatePersonPhonesAsync(IEnumerable<PersonPhoneUpdateDto> personPhones, bool saveChanges = false);
    Task<PersonExtraFeature?> AddPersonExtraFeatureAsync(PersonExtraFeatureAddDto personExtraFeature, bool saveChanges = false);
    Task<IEnumerable<PersonExtraFeature>?> AddPersonExtraFeaturesAsync(IEnumerable<PersonExtraFeatureAddDto> personExtraFeatures, bool saveChanges = false);
    Task<PersonExtraFeature?> UpdatePersonExtraFeatureAsync(PersonExtraFeatureUpdateDto personExtraFeature, bool saveChanges = false);
    Task<IEnumerable<PersonExtraFeature>?> UpdatePersonExtraFeaturesAsync(IEnumerable<PersonExtraFeatureUpdateDto> personExtraFeatures, bool saveChanges = false);
    Task<IEnumerable<PersonDetail>> AddPersonDetailsAsync(IEnumerable<PersonDetailAddDto> persons, bool saveChanges = false);
    Task<PersonDetail> AddPersonDetailAsync(PersonDetailAddDto person, bool saveChanges = false);
    Task<IEnumerable<PersonDetail>> UpdatePersonDetailsAsync(IEnumerable<PersonDetailUpdateDto> persons, bool saveChanges = false);
    Task<PersonDetail> UpdatePersonDetailAsync(PersonDetailUpdateDto person, bool saveChanges = false);
}