using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Interfaces;

public interface IUnitStore
{
    Task<Unit> AddUnitAsync(Unit unit, bool saveChanges = false);
    Task<IEnumerable<Unit>> AddUnitsAsync(IEnumerable<Unit> units, bool saveChanges = false);
    Task<Unit> UpdateUnitAsync(Unit unit, bool saveChanges = false);
    Task<IEnumerable<Unit>> UpdateUnitsAsync(IEnumerable<Unit> units, bool saveChanges = false);
    Task<Unit?> GetUnitForUpdateAsync(string unitId);
    Task<IEnumerable<Unit>> GetUnitsForUpdateAsync(IEnumerable<string> unitIds);
    Task<UnitAddress?> AddUnitAddressAsync(UnitAddress unitAddress, bool saveChanges = false);
    Task<IEnumerable<UnitAddress>?> AddUnitAddressesAsync(IEnumerable<UnitAddress> unitAddresses, bool saveChanges = false);
    Task<UnitAddress?> UpdateUnitAddressAsync(UnitAddress unitAddress, bool saveChanges = false);
    Task<IEnumerable<UnitAddress>?> UpdateUnitAddressesAsync(IEnumerable<UnitAddress> unitAddresses, bool saveChanges = false);
    Task<UnitAddress?> GetUnitAddressForUpdateAsync(string unitAddressId);
    Task<IEnumerable<UnitAddress>> GetUnitAddressesForUpdateAsync(IEnumerable<string> unitAddressIds);
    Task<UnitPhone?> AddUnitPhoneAsync(UnitPhone unitPhone, bool saveChanges = false);
    Task<IEnumerable<UnitPhone>?> AddUnitPhonesAsync(IEnumerable<UnitPhone> unitPhones, bool saveChanges = false);
    Task<UnitPhone?> UpdateUnitPhoneAsync(UnitPhone unitPhone, bool saveChanges = false);
    Task<IEnumerable<UnitPhone>?> UpdateUnitPhonesAsync(IEnumerable<UnitPhone> unitPhones, bool saveChanges = false);
    Task<UnitPhone?> GetUnitPhoneForUpdateAsync(string unitPhoneId);
    Task<IEnumerable<UnitPhone>> GetUnitPhonesForUpdateAsync(IEnumerable<string> unitPhoneIds);
    Task<UnitExtraFeature?> AddUnitExtraFeatureAsync(UnitExtraFeature unitExtraFeature, bool saveChanges = false);
    Task<IEnumerable<UnitExtraFeature>?> AddUnitExtraFeaturesAsync(IEnumerable<UnitExtraFeature> unitExtraFeatures, bool saveChanges = false);
    Task<UnitExtraFeature?> UpdateUnitExtraFeatureAsync(UnitExtraFeature unitExtraFeature, bool saveChanges = false);
    Task<IEnumerable<UnitExtraFeature>?> UpdateUnitExtraFeaturesAsync(IEnumerable<UnitExtraFeature> unitExtraFeatures, bool saveChanges = false);
    Task<UnitExtraFeature?> GetUnitExtraFeatureForUpdateAsync(string unitExtraFeatureId);
    Task<IEnumerable<UnitExtraFeature>> GetUnitExtraFeaturesForUpdateAsync(IEnumerable<string> unitExtraFeatureIds);
    Task<IDictionary<string, int>> IdConvertForUnitAsync(IEnumerable<string> unitIds);
    Task<IEnumerable<UnitTreeDto>> GetUnitsForTreeAsync();
    Task<PagedListDto<UnitViewDto>> GetUnitsAsync(PagedListCo<GetUnitsCo> co);
    Task<UnitViewDto?> GetUnitAsync(string unitId);
    Task<IEnumerable<UnitAddressViewDto>> GetUnitAddressesAsync(string unitId);
    Task<IEnumerable<UnitPhoneViewDto>> GetUnitPhonesAsync(string unitId);
    Task<IEnumerable<UnitExtraFeatureViewDto>> GetUnitExtraFeaturesAsync(string unitId);
    Task<UnitViewCompleteDto?> GetUnitCompleteAsync(string unitId);
    Task<PagedListDto<UnitForAutoCompleteDto>> GetUnitsForAutoCompleteAsync(PagedListCo<GetUnitsCo> co);
}