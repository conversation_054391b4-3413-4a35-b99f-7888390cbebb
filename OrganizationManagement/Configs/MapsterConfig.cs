using Mapster;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
namespace OrganizationManagement.Configs;

public static class MapsterConfig
{
    public static void RegisterMappings()
    {
        TypeAdapterConfig<Country, CountryAddDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Country, CountryUpdateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<State, StateAddDto>.NewConfig().TwoWays();
        TypeAdapterConfig<State, StateUpdateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<City, CityAddDto>.NewConfig().TwoWays();
        TypeAdapterConfig<City, CityUpdateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Address, AddressAddDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Address, AddressUpdateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Phone, PhoneAddDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Phone, PhoneUpdateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<ExtraFeature, ExtraFeatureAddDto>.NewConfig().TwoWays();
        TypeAdapterConfig<ExtraFeature, ExtraFeatureUpdateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Unit, UnitAddDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Unit, UnitUpdateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Position, PositionAddDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Position, PositionUpdateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Workcenter, WorkcenterAddDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Workcenter, WorkcenterUpdateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Person, PersonAddDto>.NewConfig().TwoWays();
        TypeAdapterConfig<Person, PersonUpdateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<PersonDetail, PersonDetailAddDto>.NewConfig().TwoWays();
        TypeAdapterConfig<PersonDetail, PersonDetailUpdateDto>.NewConfig().TwoWays();
    }
}