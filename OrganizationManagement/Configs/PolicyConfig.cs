using Microsoft.AspNetCore.Authorization;
using OrganizationManagement.Consts;
using Rlx.Shared.Helpers;
namespace OrganizationManagement.Configs;

public static class PolicyConfig
{
    public static void ConfigurePolicies(this AuthorizationOptions options)
    {
        options.AddPage("test");
        options.AddAction("test");
        options.AddAction("getunittree");
        options.AddAction("getunit");
        options.AddAction("saveunit");
        options.AddAction("getworkcentertree");
        options.AddAction("getworkcenter");
        options.AddAction("saveworkcenter");
        options.AddAction("getpositiontree");
        options.AddAction("getposition");
        options.AddAction("saveposition");
        options.AddAction("getperson");
        options.AddAction("saveperson");
    }
    private static void AddPage(this AuthorizationOptions options, string val)
    {
        options.AddRlxPolicy([new Rlx.Shared.Models.RlxPolicy { ClaimType = OrganizationManagementConst.PermissionPageOrgman, ClaimValue = val },
        new Rlx.Shared.Models.RlxPolicy { ClaimType = OrganizationManagementConst.PermissionPageAll, ClaimValue = "all" },
        new Rlx.Shared.Models.RlxPolicy { ClaimType = OrganizationManagementConst.PermissionPageOrgman, ClaimValue = "all" }]);
    }
    private static void AddAction(this AuthorizationOptions options, string val)
    {
        options.AddRlxPolicy([new Rlx.Shared.Models.RlxPolicy { ClaimType = OrganizationManagementConst.PermissionActionOrgman, ClaimValue = val },
        new Rlx.Shared.Models.RlxPolicy { ClaimType = OrganizationManagementConst.PermissionActionAll, ClaimValue = "all" },
        new Rlx.Shared.Models.RlxPolicy { ClaimType = OrganizationManagementConst.PermissionActionOrgman, ClaimValue = "all" }]);
    }
}