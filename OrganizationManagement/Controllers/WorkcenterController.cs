using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using OrganizationManagement.Consts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Dtos;
using Rlx.Shared.Bases;
using Rlx.Shared.Helpers;
using Rlx.Shared.Resources;
namespace OrganizationManagement.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class WorkcenterController : BaseApiController
{
    private readonly IWorkcenterManager _workcenterManager;
    public WorkcenterController(IStringLocalizer<SharedResource> localizer, IWorkcenterManager workcenterManager) : base(localizer)
    {
        _workcenterManager = workcenterManager;
    }
    [HttpGet]
    public IActionResult Test()
    {
        return Ok("ok");
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getworkcentertree")]
    [HttpGet]
    public async Task<IActionResult> GetWorkcenterTree()
    {
        var data = await _workcenterManager.GetWorkcentersForTreeAsync();
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getworkcentertree")]
    [HttpGet("{workcenterId}")]
    public async Task<IActionResult> GetWorkcenterTreeByWorkcenterId([Required] string workcenterId)
    {
        var data = await _workcenterManager.GetWorkcentersForTreeAsync(workcenterId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getworkcenter")]
    [HttpGet("{workcenterId}")]
    public async Task<IActionResult> GetWorkcenter([Required] string workcenterId)
    {
        var data = await _workcenterManager.GetWorkcenterAsync(workcenterId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getworkcenter")]
    [HttpGet("{workcenterId}")]
    public async Task<IActionResult> GetWorkcenterPhones([Required] string workcenterId)
    {
        var data = await _workcenterManager.GetWorkcenterPhonesAsync(workcenterId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getworkcenter")]
    [HttpGet("{workcenterId}")]
    public async Task<IActionResult> GetWorkcenterExtraFeatures([Required] string workcenterId)
    {
        var data = await _workcenterManager.GetWorkcenterExtraFeaturesAsync(workcenterId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getworkcenter")]
    [HttpGet("{workcenterId}")]
    public async Task<IActionResult> GetWorkcenterComplete([Required] string workcenterId)
    {
        var data = await _workcenterManager.GetWorkcenterCompleteAsync(workcenterId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveworkcenter")]
    [HttpPost]
    public async Task<IActionResult> AddWorkcenter(WorkcenterAddDto dto)
    {
        await _workcenterManager.AddWorkcenterAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveworkcenter")]
    [HttpPut]
    public async Task<IActionResult> UpdateWorkcenter(WorkcenterUpdateDto dto)
    {
        await _workcenterManager.UpdateWorkcenterAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveworkcenter")]
    [HttpPost]
    public async Task<IActionResult> AddWorkcenterAddress(WorkcenterAddressAddDto dto)
    {
        await _workcenterManager.AddWorkcenterAddressAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveworkcenter")]
    [HttpPut]
    public async Task<IActionResult> UpdateWorkcenterAddress(WorkcenterAddressUpdateDto dto)
    {
        await _workcenterManager.UpdateWorkcenterAddressAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveworkcenter")]
    [HttpPost]
    public async Task<IActionResult> AddWorkcenterPhones(WorkcenterPhoneAddDto dto)
    {
        await _workcenterManager.AddWorkcenterPhoneAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveworkcenter")]
    [HttpPut]
    public async Task<IActionResult> UpdateWorkcenterPhone(WorkcenterPhoneUpdateDto dto)
    {
        await _workcenterManager.UpdateWorkcenterPhoneAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveworkcenter")]
    [HttpPost]
    public async Task<IActionResult> AddWorkcenterExtraFeature(WorkcenterExtraFeatureAddDto dto)
    {
        await _workcenterManager.AddWorkcenterExtraFeatureAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveworkcenter")]
    [HttpPut]
    public async Task<IActionResult> UpdateWorkcenterExtraFeature(WorkcenterExtraFeatureUpdateDto dto)
    {
        await _workcenterManager.UpdateWorkcenterExtraFeatureAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
}
