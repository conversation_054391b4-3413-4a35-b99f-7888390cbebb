using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using OrganizationManagement.Consts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Dtos;
using Rlx.Shared.Bases;
using Rlx.Shared.Helpers;
using Rlx.Shared.Resources;
namespace OrganizationManagement.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class PersonController : BaseApiController
{
    private readonly IPersonManager _personManager;
    public PersonController(IStringLocalizer<SharedResource> localizer, IPersonManager personManager) : base(localizer)
    {
        _personManager = personManager;
    }
    [HttpGet]
    public IActionResult Test()
    {
        return Ok("ok");
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getperson")]
    [HttpGet("{personId}")]
    public async Task<IActionResult> GetPerson([Required] string personId)
    {
        var data = await _personManager.GetPersonAsync(personId);
        var response = RlxApiResponser<PersonViewDto?>.Success(data);
        return SuccessResponse(response);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getperson")]
    [HttpGet("{personId}")]
    public async Task<IActionResult> GetPersonDetail([Required] string personId)
    {
        var data = await _personManager.GetPersonDetailAsync(personId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getperson")]
    [HttpGet("{personId}")]
    public async Task<IActionResult> GetPersonPhones([Required] string personId)
    {
        var data = await _personManager.GetPersonPhonesAsync(personId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getperson")]
    [HttpGet("{personId}")]
    public async Task<IActionResult> GetPersonExtraFeatures([Required] string personId)
    {
        var data = await _personManager.GetPersonExtraFeaturesAsync(personId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getperson")]
    [HttpGet("{personId}")]
    public async Task<IActionResult> GetPersonComplete([Required] string personId)
    {
        var data = await _personManager.GetPersonCompleteAsync(personId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveperson")]
    [HttpPost]
    public async Task<IActionResult> AddPerson(PersonAddDto dto)
    {
        await _personManager.AddPersonAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveperson")]
    [HttpPut]
    public async Task<IActionResult> UpdatePerson(PersonUpdateDto dto)
    {
        await _personManager.UpdatePersonAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveperson")]
    [HttpPost]
    public async Task<IActionResult> AddPersonAddress(PersonAddressAddDto dto)
    {
        await _personManager.AddPersonAddressAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveperson")]
    [HttpPut]
    public async Task<IActionResult> UpdatePersonAddress(PersonAddressUpdateDto dto)
    {
        await _personManager.UpdatePersonAddressAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveperson")]
    [HttpPost]
    public async Task<IActionResult> AddPersonPhones(PersonPhoneAddDto dto)
    {
        await _personManager.AddPersonPhoneAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveperson")]
    [HttpPut]
    public async Task<IActionResult> UpdatePersonPhone(PersonPhoneUpdateDto dto)
    {
        await _personManager.UpdatePersonPhoneAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveperson")]
    [HttpPost]
    public async Task<IActionResult> AddPersonExtraFeature(PersonExtraFeatureAddDto dto)
    {
        await _personManager.AddPersonExtraFeatureAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveperson")]
    [HttpPut]
    public async Task<IActionResult> UpdatePersonExtraFeature(PersonExtraFeatureUpdateDto dto)
    {
        await _personManager.UpdatePersonExtraFeatureAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
}
