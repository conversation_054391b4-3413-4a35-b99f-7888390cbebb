using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using OrganizationManagement.Consts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Dtos;
using Rlx.Shared.Bases;
using Rlx.Shared.Helpers;
using Rlx.Shared.Resources;
namespace OrganizationManagement.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class UnitController : BaseApiController
{
    private readonly IUnitManager _unitManager;
    public UnitController(IStringLocalizer<SharedResource> localizer, IUnitManager unitManager) : base(localizer)
    {
        _unitManager = unitManager;
    }
    [HttpGet]
    public IActionResult Test()
    {
        return Ok("ok");
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getunittree")]
    [HttpGet]
    public async Task<IActionResult> GetUnitTree()
    {
        var data = await _unitManager.GetUnitsForTreeAsync();
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getunittree")]
    [HttpGet("{unitId}")]
    public async Task<IActionResult> GetUnitTreeByUnitId([Required] string unitId)
    {
        var data = await _unitManager.GetUnitsForTreeAsync(unitId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getunit")]
    [HttpGet("{unitId}")]
    public async Task<IActionResult> GetUnit([Required] string unitId)
    {
        var data = await _unitManager.GetUnitAsync(unitId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getunit")]
    [HttpGet("{unitId}")]
    public async Task<IActionResult> GetUnitPhones([Required] string unitId)
    {
        var data = await _unitManager.GetUnitPhonesAsync(unitId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getunit")]
    [HttpGet("{unitId}")]
    public async Task<IActionResult> GetUnitExtraFeatures([Required] string unitId)
    {
        var data = await _unitManager.GetUnitExtraFeaturesAsync(unitId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getunit")]
    [HttpGet("{unitId}")]
    public async Task<IActionResult> GetUnitComplete([Required] string unitId)
    {
        var data = await _unitManager.GetUnitCompleteAsync(unitId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveunit")]
    [HttpPost]
    public async Task<IActionResult> AddUnit(UnitAddDto dto)
    {
        await _unitManager.AddUnitAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveunit")]
    [HttpPut]
    public async Task<IActionResult> UpdateUnit(UnitUpdateDto dto)
    {
        await _unitManager.UpdateUnitAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveunit")]
    [HttpPost]
    public async Task<IActionResult> AddUnitAddress(UnitAddressAddDto dto)
    {
        await _unitManager.AddUnitAddressAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveunit")]
    [HttpPut]
    public async Task<IActionResult> UpdateUnitAddress(UnitAddressUpdateDto dto)
    {
        await _unitManager.UpdateUnitAddressAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveunit")]
    [HttpPost]
    public async Task<IActionResult> AddUnitPhones(UnitPhoneAddDto dto)
    {
        await _unitManager.AddUnitPhoneAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveunit")]
    [HttpPut]
    public async Task<IActionResult> UpdateUnitPhone(UnitPhoneUpdateDto dto)
    {
        await _unitManager.UpdateUnitPhoneAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveunit")]
    [HttpPost]
    public async Task<IActionResult> AddUnitExtraFeature(UnitExtraFeatureAddDto dto)
    {
        await _unitManager.AddUnitExtraFeatureAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveunit")]
    [HttpPut]
    public async Task<IActionResult> UpdateUnitExtraFeature(UnitExtraFeatureUpdateDto dto)
    {
        await _unitManager.UpdateUnitExtraFeatureAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
}
