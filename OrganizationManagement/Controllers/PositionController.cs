using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using OrganizationManagement.Consts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Dtos;
using Rlx.Shared.Bases;
using Rlx.Shared.Helpers;
using Rlx.Shared.Resources;
namespace OrganizationManagement.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class PositionController : BaseApiController
{
    private readonly IPositionManager _positionManager;
    public PositionController(IStringLocalizer<SharedResource> localizer, IPositionManager positionManager) : base(localizer)
    {
        _positionManager = positionManager;
    }
    [HttpGet]
    public IActionResult Test()
    {
        return Ok("ok");
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getpositiontree")]
    [HttpGet]
    public async Task<IActionResult> GetPositionTree()
    {
        var data = await _positionManager.GetPositionsForTreeAsync();
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getpositiontree")]
    [HttpGet("{positionId}")]
    public async Task<IActionResult> GetPositionTreeByPositionId([Required] string positionId)
    {
        var data = await _positionManager.GetPositionsForTreeAsync(positionId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.getposition")]
    [HttpGet("{positionId}")]
    public async Task<IActionResult> GetPosition([Required] string positionId)
    {
        var data = await _positionManager.GetPositionAsync(positionId);
        return SuccessResponse(data);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveposition")]
    [HttpPost]
    public async Task<IActionResult> AddPosition(PositionAddDto dto)
    {
        await _positionManager.AddPositionAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
    [Authorize($"{OrganizationManagementConst.PermissionActionOrgman}.saveposition")]
    [HttpPut]
    public async Task<IActionResult> UpdatePosition(PositionUpdateDto dto)
    {
        await _positionManager.UpdatePositionAsync(dto, true);
        return SuccessResponse(_localizer["Success"].Value);
    }
}
