using Microsoft.EntityFrameworkCore;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Stores;

public class PositionStore : IPositionStore
{
    private readonly OrganizationManagementDbContext _context;
    public PositionStore(OrganizationManagementDbContext context)
    {
        _context = context;
    }
    public async Task<Position> AddPositionAsync(Position position, bool saveChanges = false)
    {
        _context.Add(position);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return position;
    }
    public async Task<IEnumerable<Position>> AddPositionsAsync(IEnumerable<Position> positions, bool saveChanges = false)
    {
        _context.AddRange(positions);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return positions;
    }
    public async Task<Position?> GetPositionForUpdateAsync(string positionId)
    {
        return await _context.Positions.FirstOrDefaultAsync(u => u.Id == positionId);
    }
    public async Task<IEnumerable<PositionTreeDto>> GetPositionsForTreeAsync()
    {
        return await _context.Positions
        .Where(u => !u.Deleted)
            .Select(u => new PositionTreeDto
            {
                Id = u.Id,
                Name = u.Name,
                ParentId = u.ParentId,
                Code = u.Code,
                AutoIncrementId = u.AutoIncrementId,
            })
            .ToListAsync();
    }
    public async Task<IEnumerable<Position>> GetPositionsForUpdateAsync(IEnumerable<string> positionIds)
    {
        return await _context.Positions.Where(u => positionIds.Contains(u.Id)).ToListAsync();
    }
    public async Task<IDictionary<string, int>> IdConvertForPositionAsync(IEnumerable<string> positionIds)
    {
        return await _context.Positions
              .Where(u => positionIds.Contains(u.Id))
              .ToDictionaryAsync(u => u.Id, u => u.AutoIncrementId);
    }
    public async Task<Position> UpdatePositionAsync(Position position, bool saveChanges = false)
    {
        _context.Update(position);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return position;
    }
    public async Task<IEnumerable<Position>> UpdatePositionsAsync(IEnumerable<Position> positions, bool saveChanges = false)
    {
        _context.UpdateRange(positions);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return positions;
    }
    public async Task<PagedListDto<PositionViewDto>> GetPositionsAsync(PagedListCo<GetPositionsCo> co)
    {
        return await GetPositionsAsync(co, q => q.Select(x => new PositionViewDto
        {
            Id = x.Id,
            Disabled = x.Disabled,
            Deleted = x.Deleted,
            PublicUnitId = x.Unit!.Id,
            PublicParentId = x.Parent != null ? x.Parent.Id : null,
            PublicPositionTypeId = x.PositionType!.Id,
            Name = x.Name,
            Code = x.Code,
        }));
    }
    public async Task<PagedListDto<PositionForAutoCompleteDto>> GetPositionsForAutoCompleteAsync(PagedListCo<GetPositionsCo> co)
    {
        return await GetPositionsAsync(co, q => q.Select(x => new PositionForAutoCompleteDto
        {
            Id = x.Id,
            Name = x.Name,
            Code = x.Code,
        }));
    }
    private async Task<PagedListDto<T>> GetPositionsAsync<T>(PagedListCo<GetPositionsCo> co, Func<IQueryable<Position>, IQueryable<T>> selector)
    {
        var pl = new PagedListDto<T>
        {
            Page = co.Pager.Page,
            Size = co.Pager.Size
        };
        var query = _context.Positions.AsQueryable();
        if (co.Criteria != null)
        {
            if (!string.IsNullOrEmpty(co.Criteria.Id))
            {
                query = query.Where(x => x.Id == co.Criteria.Id);
            }
            if (!string.IsNullOrEmpty(co.Criteria.NameContains))
            {
                query = query.Where(x => x.Name.Contains(co.Criteria.NameContains));
            }
            if (!string.IsNullOrEmpty(co.Criteria.Name))
            {
                query = query.Where(x => x.Name == co.Criteria.Name);
            }
            if (!string.IsNullOrEmpty(co.Criteria.CodeContains))
            {
                query = query.Where(x => x.Code != null && x.Code.Contains(co.Criteria.CodeContains));
            }
            if (co.Criteria.Ids != null && co.Criteria.Ids.Any())
            {
                query = query.Where(x => co.Criteria.Ids.Contains(x.Id));
            }
            if (co.Criteria.Disabled.HasValue)
            {
                query = query.Where(x => x.Disabled == co.Criteria.Disabled);
            }
            if (co.Criteria.Deleted.HasValue)
            {
                query = query.Where(x => x.Deleted == co.Criteria.Deleted);
            }
        }
        pl.Count = await query.CountAsync();
        if (pl.Count != 0)
        {
            //todo: sorting
            pl.Data = await selector(query).Skip(co.Pager.Skip).Take(co.Pager.Size).ToListAsync();
        }
        return pl;
    }
    public async Task<PositionViewDto?> GetPositionAsync(string positionId)
    {
        return await _context.Positions
            .Where(u => u.Id == positionId)
            .Select(u => new PositionViewDto
            {
                Id = u.Id,
                Name = u.Name,
                Code = u.Code,
                PublicParentId = u.Parent != null ? u.Parent.Id : null,
                PublicPositionTypeId = u.PositionType!.Id,
                PublicUnitId = u.Unit!.Id,
                Disabled = u.Disabled,
                Deleted = u.Deleted
            })
            .FirstOrDefaultAsync();
    }
    public async Task<PositionViewCompleteDto?> GetPositionCompleteAsync(string positionId)
    {
        return await _context.Positions
            .Where(u => u.Id == positionId)
            .Select(u => new PositionViewCompleteDto
            {
                Id = u.Id,
                Name = u.Name,
                Code = u.Code,
                PublicParentId = u.Parent != null ? u.Parent.Id : null,
                PublicPositionTypeId = u.PositionType!.Id,
                Disabled = u.Disabled,
                Deleted = u.Deleted,
                PublicUnitId = u.Unit!.Id,
            })
            .FirstOrDefaultAsync();
    }
}