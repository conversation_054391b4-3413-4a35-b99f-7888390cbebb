using Microsoft.EntityFrameworkCore;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Stores;

public class UnitStore : IUnitStore
{
    private readonly OrganizationManagementDbContext _context;
    public UnitStore(OrganizationManagementDbContext context)
    {
        _context = context;
    }
    public async Task<Unit> AddUnitAsync(Unit unit, bool saveChanges = false)
    {
        _context.Add(unit);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unit;
    }
    public async Task<IEnumerable<Unit>> AddUnitsAsync(IEnumerable<Unit> units, bool saveChanges = false)
    {
        _context.AddRange(units);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return units;
    }
    public async Task<Unit?> GetUnitForUpdateAsync(string unitId)
    {
        return await _context.Units.FirstOrDefaultAsync(u => u.Id == unitId);
    }
    public async Task<IEnumerable<UnitTreeDto>> GetUnitsForTreeAsync()
    {
        return await _context.Units
        .Where(u => !u.Deleted)
            .Select(u => new UnitTreeDto
            {
                Id = u.Id,
                Name = u.Name,
                ParentId = u.ParentId,
                Code = u.Code,
                AutoIncrementId = u.AutoIncrementId,
            })
            .ToListAsync();
    }
    public async Task<IEnumerable<Unit>> GetUnitsForUpdateAsync(IEnumerable<string> unitIds)
    {
        return await _context.Units.Where(u => unitIds.Contains(u.Id)).ToListAsync();
    }
    public async Task<IDictionary<string, int>> IdConvertForUnitAsync(IEnumerable<string> unitIds)
    {
        return await _context.Units
              .Where(u => unitIds.Contains(u.Id))
              .ToDictionaryAsync(u => u.Id, u => u.AutoIncrementId);
    }
    public async Task<Unit> UpdateUnitAsync(Unit unit, bool saveChanges = false)
    {
        _context.Update(unit);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unit;
    }
    public async Task<IEnumerable<Unit>> UpdateUnitsAsync(IEnumerable<Unit> units, bool saveChanges = false)
    {
        _context.UpdateRange(units);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return units;
    }
    public async Task<PagedListDto<UnitViewDto>> GetUnitsAsync(PagedListCo<GetUnitsCo> co)
    {
        return await GetUnitsAsync(co, q => q.Select(x => new UnitViewDto
        {
            Id = x.Id,
            Disabled = x.Disabled,
            Deleted = x.Deleted,
            PublicParentId = x.Parent != null ? x.Parent.Id : null,
            PublicUnitTypeId = x.UnitType!.Id,
            Name = x.Name,
            Code = x.Code,
        }));
    }
    public async Task<PagedListDto<UnitForAutoCompleteDto>> GetUnitsForAutoCompleteAsync(PagedListCo<GetUnitsCo> co)
    {
        return await GetUnitsAsync(co, q => q.Select(x => new UnitForAutoCompleteDto
        {
            Id = x.Id,
            Name = x.Name,
            Code = x.Code,
        }));
    }
    private async Task<PagedListDto<T>> GetUnitsAsync<T>(PagedListCo<GetUnitsCo> co, Func<IQueryable<Unit>, IQueryable<T>> selector)
    {
        var pl = new PagedListDto<T>
        {
            Page = co.Pager.Page,
            Size = co.Pager.Size
        };
        var query = _context.Units.AsQueryable();
        if (co.Criteria != null)
        {
            if (!string.IsNullOrEmpty(co.Criteria.Id))
            {
                query = query.Where(x => x.Id == co.Criteria.Id);
            }
            if (!string.IsNullOrEmpty(co.Criteria.NameContains))
            {
                query = query.Where(x => x.Name.Contains(co.Criteria.NameContains));
            }
            if (!string.IsNullOrEmpty(co.Criteria.Name))
            {
                query = query.Where(x => x.Name == co.Criteria.Name);
            }
            if (!string.IsNullOrEmpty(co.Criteria.CodeContains))
            {
                query = query.Where(x => x.Code != null && x.Code.Contains(co.Criteria.CodeContains));
            }
            if (co.Criteria.Ids != null && co.Criteria.Ids.Any())
            {
                query = query.Where(x => co.Criteria.Ids.Contains(x.Id));
            }
            if (co.Criteria.Disabled.HasValue)
            {
                query = query.Where(x => x.Disabled == co.Criteria.Disabled);
            }
            if (co.Criteria.Deleted.HasValue)
            {
                query = query.Where(x => x.Deleted == co.Criteria.Deleted);
            }
        }
        pl.Count = await query.CountAsync();
        if (pl.Count != 0)
        {
            //todo: sorting
            pl.Data = await selector(query).Skip(co.Pager.Skip).Take(co.Pager.Size).ToListAsync();
        }
        return pl;
    }
    public async Task<IEnumerable<UnitAddressViewDto>> GetUnitAddressesAsync(string unitId)
    {
        return await _context.UnitAddresses
                 .Where(u => !u.Deleted && u.Unit != null && u.Unit.Id == unitId && u.Address != null)
              .Select(u => new UnitAddressViewDto
              {
                  Id = u.Id,
                  PublicUnitId = u.Unit!.Id,
                  PublicAddressId = u.Address!.Id,
                  Address = new AddressViewDto
                  {
                      Id = u.Address!.Id,
                      AddressText = u.Address.AddressText,
                      PostalCode = u.Address.PostalCode,
                      PublicStateId = u.Address.State != null ? u.Address.State.Id : null,
                      PublicCountryId = u.Address.Country != null ? u.Address.Country.Id : null,
                      PublicCityId = u.Address.City != null ? u.Address.City.Id : null,
                  },
                  IsDefault = u.IsDefault
              }).ToListAsync();
    }
    public async Task<IEnumerable<UnitPhoneViewDto>> GetUnitPhonesAsync(string unitId)
    {
        return await _context.UnitPhones
                 .Where(p => !p.Deleted && p.Unit != null && p.Unit.Id == unitId && p.Phone != null)
              .Select(u => new UnitPhoneViewDto
              {
                  Id = u.Id,
                  PublicUnitId = u.Unit!.Id,
                  PublicPhoneId = u.Phone!.Id,
                  Phone = new PhoneViewDto
                  {
                      Id = u.Phone.Id,
                      Number = u.Phone.Number,
                      CountryCode = u.Phone.CountryCode,
                  }
              }).ToListAsync();
    }
    public async Task<IEnumerable<UnitExtraFeatureViewDto>> GetUnitExtraFeaturesAsync(string unitId)
    {
        return await _context.UnitExtraFeatures
                 .Where(ef => !ef.Deleted && ef.Unit != null && ef.Unit.Id == unitId && ef.ExtraFeature != null)
              .Select(u => new UnitExtraFeatureViewDto
              {
                  Id = u.Id,
                  PublicUnitId = u.Unit!.Id,
                  PublicExtraFeatureId = u.ExtraFeature!.Id,
                  ExtraFeature = new ExtraFeatureViewDto
                  {
                      Id = u.ExtraFeature.Id,
                      Title = u.ExtraFeature.Title,
                      Value = u.ExtraFeature.Value,
                  }
              }).ToListAsync();
    }
    public async Task<UnitAddress?> AddUnitAddressAsync(UnitAddress unitAddress, bool saveChanges = false)
    {
        _context.Add(unitAddress);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unitAddress;
    }
    public async Task<IEnumerable<UnitAddress>?> AddUnitAddressesAsync(IEnumerable<UnitAddress> unitAddresses, bool saveChanges = false)
    {
        _context.AddRange(unitAddresses);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unitAddresses;
    }
    public async Task<UnitAddress?> UpdateUnitAddressAsync(UnitAddress unitAddress, bool saveChanges = false)
    {
        _context.Update(unitAddress);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unitAddress;
    }
    public async Task<IEnumerable<UnitAddress>?> UpdateUnitAddressesAsync(IEnumerable<UnitAddress> unitAddresses, bool saveChanges = false)
    {
        _context.UpdateRange(unitAddresses);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unitAddresses;
    }
    public async Task<UnitAddress?> GetUnitAddressForUpdateAsync(string unitAddressId)
    {
        return await _context.UnitAddresses
            .Where(u => u.Id == unitAddressId)
            .FirstOrDefaultAsync();
    }
    public async Task<IEnumerable<UnitAddress>> GetUnitAddressesForUpdateAsync(IEnumerable<string> unitAddressIds)
    {
        return await _context.UnitAddresses
            .Where(u => unitAddressIds.Contains(u.Id))
            .ToListAsync();
    }
    public async Task<UnitPhone?> AddUnitPhoneAsync(UnitPhone unitPhone, bool saveChanges = false)
    {
        _context.Add(unitPhone);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unitPhone;
    }
    public async Task<IEnumerable<UnitPhone>?> AddUnitPhonesAsync(IEnumerable<UnitPhone> unitPhones, bool saveChanges = false)
    {
        _context.AddRange(unitPhones);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unitPhones;
    }
    public async Task<UnitPhone?> UpdateUnitPhoneAsync(UnitPhone unitPhone, bool saveChanges = false)
    {
        _context.Update(unitPhone);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unitPhone;
    }
    public async Task<IEnumerable<UnitPhone>?> UpdateUnitPhonesAsync(IEnumerable<UnitPhone> unitPhones, bool saveChanges = false)
    {
        _context.UpdateRange(unitPhones);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unitPhones;
    }
    public async Task<UnitPhone?> GetUnitPhoneForUpdateAsync(string unitPhoneId)
    {
        return await _context.UnitPhones
            .Where(u => u.Id == unitPhoneId)
            .FirstOrDefaultAsync();
    }
    public async Task<IEnumerable<UnitPhone>> GetUnitPhonesForUpdateAsync(IEnumerable<string> unitPhoneIds)
    {
        return await _context.UnitPhones
            .Where(u => unitPhoneIds.Contains(u.Id))
            .ToListAsync();
    }
    public async Task<UnitExtraFeature?> AddUnitExtraFeatureAsync(UnitExtraFeature unitExtraFeature, bool saveChanges = false)
    {
        _context.Add(unitExtraFeature);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unitExtraFeature;
    }
    public async Task<IEnumerable<UnitExtraFeature>?> AddUnitExtraFeaturesAsync(IEnumerable<UnitExtraFeature> unitExtraFeatures, bool saveChanges = false)
    {
        _context.AddRange(unitExtraFeatures);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unitExtraFeatures;
    }
    public async Task<UnitExtraFeature?> UpdateUnitExtraFeatureAsync(UnitExtraFeature unitExtraFeature, bool saveChanges = false)
    {
        _context.Update(unitExtraFeature);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unitExtraFeature;
    }
    public async Task<IEnumerable<UnitExtraFeature>?> UpdateUnitExtraFeaturesAsync(IEnumerable<UnitExtraFeature> unitExtraFeatures, bool saveChanges = false)
    {
        _context.UpdateRange(unitExtraFeatures);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return unitExtraFeatures;
    }
    public async Task<UnitExtraFeature?> GetUnitExtraFeatureForUpdateAsync(string unitExtraFeatureId)
    {
        return await _context.UnitExtraFeatures
            .Where(u => u.Id == unitExtraFeatureId)
            .FirstOrDefaultAsync();
    }
    public async Task<IEnumerable<UnitExtraFeature>> GetUnitExtraFeaturesForUpdateAsync(IEnumerable<string> unitExtraFeatureIds)
    {
        return await _context.UnitExtraFeatures
            .Where(u => unitExtraFeatureIds.Contains(u.Id))
            .ToListAsync();
    }
    public async Task<UnitViewDto?> GetUnitAsync(string unitId)
    {
        return await _context.Units
            .Where(u => u.Id == unitId)
            .Select(u => new UnitViewDto
            {
                Id = u.Id,
                Name = u.Name,
                Code = u.Code,
                PublicParentId = u.Parent != null ? u.Parent.Id : null,
                PublicUnitTypeId = u.UnitType!.Id,
                Disabled = u.Disabled,
                Deleted = u.Deleted
            })
            .FirstOrDefaultAsync();
    }
    public async Task<UnitViewCompleteDto?> GetUnitCompleteAsync(string unitId)
    {
        return await _context.Units
            .Where(u => u.Id == unitId)
            .Select(u => new UnitViewCompleteDto
            {
                Id = u.Id,
                Name = u.Name,
                Code = u.Code,
                PublicParentId = u.Parent != null ? u.Parent.Id : null,
                PublicUnitTypeId = u.UnitType!.Id,
                Disabled = u.Disabled,
                Deleted = u.Deleted,
                Addresses = u.UnitAddresses != null ? u.UnitAddresses.Where(a => !a.Deleted).Select(a => new UnitAddressViewDto
                {
                    Id = a.Id,
                    PublicUnitId = a.Unit!.Id,
                    PublicAddressId = a.Address!.Id,
                    Address = new AddressViewDto
                    {
                        Id = a.Address!.Id,
                        AddressText = a.Address.AddressText,
                        PostalCode = a.Address.PostalCode,
                        PublicStateId = a.Address.State != null ? a.Address.State.Id : null,
                        PublicCountryId = a.Address.Country != null ? a.Address.Country.Id : null,
                        PublicCityId = a.Address.City != null ? a.Address.City.Id : null,
                    },
                    IsDefault = a.IsDefault
                }).ToList() : null,
                Phones = u.UnitPhones != null ? u.UnitPhones.Where(p => !p.Deleted).Select(p => new UnitPhoneViewDto
                {
                    Id = p.Id,
                    PublicUnitId = p.Unit!.Id,
                    PublicPhoneId = p.Phone!.Id,
                    Phone = new PhoneViewDto
                    {
                        Id = p.Phone.Id,
                        Number = p.Phone.Number,
                        CountryCode = p.Phone.CountryCode,
                    }
                }).ToList() : null,
                ExtraFeatures = u.UnitExtraFeatures != null ? u.UnitExtraFeatures.Where(ef => !ef.Deleted).Select(ef => new UnitExtraFeatureViewDto
                {
                    Id = ef.Id,
                    PublicUnitId = ef.Unit!.Id,
                    PublicExtraFeatureId = ef.ExtraFeature!.Id,
                    ExtraFeature = new ExtraFeatureViewDto
                    {
                        Id = ef.ExtraFeature.Id,
                        Title = ef.ExtraFeature.Title,
                        Value = ef.ExtraFeature.Value,
                    }
                }).ToList() : null
            })
            .FirstOrDefaultAsync();
    }
}