using Microsoft.EntityFrameworkCore;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Stores;

public class WorkcenterStore : IWorkcenterStore
{
    private readonly OrganizationManagementDbContext _context;
    public WorkcenterStore(OrganizationManagementDbContext context)
    {
        _context = context;
    }
    public async Task<Workcenter> AddWorkcenterAsync(Workcenter workcenter, bool saveChanges = false)
    {
        _context.Add(workcenter);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenter;
    }
    public async Task<IEnumerable<Workcenter>> AddWorkcentersAsync(IEnumerable<Workcenter> workcenters, bool saveChanges = false)
    {
        _context.AddRange(workcenters);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenters;
    }
    public async Task<Workcenter?> GetWorkcenterForUpdateAsync(string workcenterId)
    {
        return await _context.Workcenters.FirstOrDefaultAsync(u => u.Id == workcenterId);
    }
    public async Task<IEnumerable<WorkcenterTreeDto>> GetWorkcentersForTreeAsync()
    {
        return await _context.Workcenters
        .Where(u => !u.Deleted)
            .Select(u => new WorkcenterTreeDto
            {
                Id = u.Id,
                Name = u.Name,
                ParentId = u.ParentId,
                Code = u.Code,
                AutoIncrementId = u.AutoIncrementId,
            })
            .ToListAsync();
    }
    public async Task<IEnumerable<Workcenter>> GetWorkcentersForUpdateAsync(IEnumerable<string> workcenterIds)
    {
        return await _context.Workcenters.Where(u => workcenterIds.Contains(u.Id)).ToListAsync();
    }
    public async Task<IDictionary<string, int>> IdConvertForWorkcenterAsync(IEnumerable<string> workcenterIds)
    {
        return await _context.Workcenters
              .Where(u => workcenterIds.Contains(u.Id))
              .ToDictionaryAsync(u => u.Id, u => u.AutoIncrementId);
    }
    public async Task<Workcenter> UpdateWorkcenterAsync(Workcenter workcenter, bool saveChanges = false)
    {
        _context.Update(workcenter);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenter;
    }
    public async Task<IEnumerable<Workcenter>> UpdateWorkcentersAsync(IEnumerable<Workcenter> workcenters, bool saveChanges = false)
    {
        _context.UpdateRange(workcenters);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenters;
    }
    public async Task<PagedListDto<WorkcenterViewDto>> GetWorkcentersAsync(PagedListCo<GetWorkcentersCo> co)
    {
        return await GetWorkcentersAsync(co, q => q.Select(x => new WorkcenterViewDto
        {
            Id = x.Id,
            Disabled = x.Disabled,
            Deleted = x.Deleted,
            PublicParentId = x.Parent != null ? x.Parent.Id : null,
            PublicWorkcenterTypeId = x.WorkcenterType!.Id,
            Name = x.Name,
            Code = x.Code,
        }));
    }
    public async Task<PagedListDto<WorkcenterForAutoCompleteDto>> GetWorkcentersForAutoCompleteAsync(PagedListCo<GetWorkcentersCo> co)
    {
        return await GetWorkcentersAsync(co, q => q.Select(x => new WorkcenterForAutoCompleteDto
        {
            Id = x.Id,
            Name = x.Name,
            Code = x.Code,
        }));
    }
    private async Task<PagedListDto<T>> GetWorkcentersAsync<T>(PagedListCo<GetWorkcentersCo> co, Func<IQueryable<Workcenter>, IQueryable<T>> selector)
    {
        var pl = new PagedListDto<T>
        {
            Page = co.Pager.Page,
            Size = co.Pager.Size
        };
        var query = _context.Workcenters.AsQueryable();
        if (co.Criteria != null)
        {
            if (!string.IsNullOrEmpty(co.Criteria.Id))
            {
                query = query.Where(x => x.Id == co.Criteria.Id);
            }
            if (!string.IsNullOrEmpty(co.Criteria.NameContains))
            {
                query = query.Where(x => x.Name.Contains(co.Criteria.NameContains));
            }
            if (!string.IsNullOrEmpty(co.Criteria.Name))
            {
                query = query.Where(x => x.Name == co.Criteria.Name);
            }
            if (!string.IsNullOrEmpty(co.Criteria.CodeContains))
            {
                query = query.Where(x => x.Code != null && x.Code.Contains(co.Criteria.CodeContains));
            }
            if (co.Criteria.Ids != null && co.Criteria.Ids.Any())
            {
                query = query.Where(x => co.Criteria.Ids.Contains(x.Id));
            }
            if (co.Criteria.Disabled.HasValue)
            {
                query = query.Where(x => x.Disabled == co.Criteria.Disabled);
            }
            if (co.Criteria.Deleted.HasValue)
            {
                query = query.Where(x => x.Deleted == co.Criteria.Deleted);
            }
        }
        pl.Count = await query.CountAsync();
        if (pl.Count != 0)
        {
            //todo: sorting
            pl.Data = await selector(query).Skip(co.Pager.Skip).Take(co.Pager.Size).ToListAsync();
        }
        return pl;
    }
    public async Task<IEnumerable<WorkcenterAddressViewDto>> GetWorkcenterAddressesAsync(string workcenterId)
    {
        return await _context.WorkcenterAddresses
                 .Where(u => !u.Deleted && u.Workcenter != null && u.Workcenter.Id == workcenterId && u.Address != null)
              .Select(u => new WorkcenterAddressViewDto
              {
                  Id = u.Id,
                  PublicWorkcenterId = u.Workcenter!.Id,
                  PublicAddressId = u.Address!.Id,
                  Address = new AddressViewDto
                  {
                      Id = u.Address!.Id,
                      AddressText = u.Address.AddressText,
                      PostalCode = u.Address.PostalCode,
                      PublicStateId = u.Address.State != null ? u.Address.State.Id : null,
                      PublicCountryId = u.Address.Country != null ? u.Address.Country.Id : null,
                      PublicCityId = u.Address.City != null ? u.Address.City.Id : null,
                  },
                  IsDefault = u.IsDefault
              }).ToListAsync();
    }
    public async Task<IEnumerable<WorkcenterPhoneViewDto>> GetWorkcenterPhonesAsync(string workcenterId)
    {
        return await _context.WorkcenterPhones
                 .Where(p => !p.Deleted && p.Workcenter != null && p.Workcenter.Id == workcenterId && p.Phone != null)
              .Select(u => new WorkcenterPhoneViewDto
              {
                  Id = u.Id,
                  PublicWorkcenterId = u.Workcenter!.Id,
                  PublicPhoneId = u.Phone!.Id,
                  Phone = new PhoneViewDto
                  {
                      Id = u.Phone.Id,
                      Number = u.Phone.Number,
                      CountryCode = u.Phone.CountryCode,
                  }
              }).ToListAsync();
    }
    public async Task<IEnumerable<WorkcenterExtraFeatureViewDto>> GetWorkcenterExtraFeaturesAsync(string workcenterId)
    {
        return await _context.WorkcenterExtraFeatures
                 .Where(ef => !ef.Deleted && ef.Workcenter != null && ef.Workcenter.Id == workcenterId && ef.ExtraFeature != null)
              .Select(u => new WorkcenterExtraFeatureViewDto
              {
                  Id = u.Id,
                  PublicWorkcenterId = u.Workcenter!.Id,
                  PublicExtraFeatureId = u.ExtraFeature!.Id,
                  ExtraFeature = new ExtraFeatureViewDto
                  {
                      Id = u.ExtraFeature.Id,
                      Title = u.ExtraFeature.Title,
                      Value = u.ExtraFeature.Value,
                  }
              }).ToListAsync();
    }
    public async Task<WorkcenterAddress?> AddWorkcenterAddressAsync(WorkcenterAddress workcenterAddress, bool saveChanges = false)
    {
        _context.Add(workcenterAddress);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenterAddress;
    }
    public async Task<IEnumerable<WorkcenterAddress>?> AddWorkcenterAddressesAsync(IEnumerable<WorkcenterAddress> workcenterAddresses, bool saveChanges = false)
    {
        _context.AddRange(workcenterAddresses);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenterAddresses;
    }
    public async Task<WorkcenterAddress?> UpdateWorkcenterAddressAsync(WorkcenterAddress workcenterAddress, bool saveChanges = false)
    {
        _context.Update(workcenterAddress);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenterAddress;
    }
    public async Task<IEnumerable<WorkcenterAddress>?> UpdateWorkcenterAddressesAsync(IEnumerable<WorkcenterAddress> workcenterAddresses, bool saveChanges = false)
    {
        _context.UpdateRange(workcenterAddresses);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenterAddresses;
    }
    public async Task<WorkcenterAddress?> GetWorkcenterAddressForUpdateAsync(string workcenterAddressId)
    {
        return await _context.WorkcenterAddresses
            .Where(u => u.Id == workcenterAddressId)
            .FirstOrDefaultAsync();
    }
    public async Task<IEnumerable<WorkcenterAddress>> GetWorkcenterAddressesForUpdateAsync(IEnumerable<string> workcenterAddressIds)
    {
        return await _context.WorkcenterAddresses
            .Where(u => workcenterAddressIds.Contains(u.Id))
            .ToListAsync();
    }
    public async Task<WorkcenterPhone?> AddWorkcenterPhoneAsync(WorkcenterPhone workcenterPhone, bool saveChanges = false)
    {
        _context.Add(workcenterPhone);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenterPhone;
    }
    public async Task<IEnumerable<WorkcenterPhone>?> AddWorkcenterPhonesAsync(IEnumerable<WorkcenterPhone> workcenterPhones, bool saveChanges = false)
    {
        _context.AddRange(workcenterPhones);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenterPhones;
    }
    public async Task<WorkcenterPhone?> UpdateWorkcenterPhoneAsync(WorkcenterPhone workcenterPhone, bool saveChanges = false)
    {
        _context.Update(workcenterPhone);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenterPhone;
    }
    public async Task<IEnumerable<WorkcenterPhone>?> UpdateWorkcenterPhonesAsync(IEnumerable<WorkcenterPhone> workcenterPhones, bool saveChanges = false)
    {
        _context.UpdateRange(workcenterPhones);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenterPhones;
    }
    public async Task<WorkcenterPhone?> GetWorkcenterPhoneForUpdateAsync(string workcenterPhoneId)
    {
        return await _context.WorkcenterPhones
            .Where(u => u.Id == workcenterPhoneId)
            .FirstOrDefaultAsync();
    }
    public async Task<IEnumerable<WorkcenterPhone>> GetWorkcenterPhonesForUpdateAsync(IEnumerable<string> workcenterPhoneIds)
    {
        return await _context.WorkcenterPhones
            .Where(u => workcenterPhoneIds.Contains(u.Id))
            .ToListAsync();
    }
    public async Task<WorkcenterExtraFeature?> AddWorkcenterExtraFeatureAsync(WorkcenterExtraFeature workcenterExtraFeature, bool saveChanges = false)
    {
        _context.Add(workcenterExtraFeature);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenterExtraFeature;
    }
    public async Task<IEnumerable<WorkcenterExtraFeature>?> AddWorkcenterExtraFeaturesAsync(IEnumerable<WorkcenterExtraFeature> workcenterExtraFeatures, bool saveChanges = false)
    {
        _context.AddRange(workcenterExtraFeatures);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenterExtraFeatures;
    }
    public async Task<WorkcenterExtraFeature?> UpdateWorkcenterExtraFeatureAsync(WorkcenterExtraFeature workcenterExtraFeature, bool saveChanges = false)
    {
        _context.Update(workcenterExtraFeature);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenterExtraFeature;
    }
    public async Task<IEnumerable<WorkcenterExtraFeature>?> UpdateWorkcenterExtraFeaturesAsync(IEnumerable<WorkcenterExtraFeature> workcenterExtraFeatures, bool saveChanges = false)
    {
        _context.UpdateRange(workcenterExtraFeatures);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return workcenterExtraFeatures;
    }
    public async Task<WorkcenterExtraFeature?> GetWorkcenterExtraFeatureForUpdateAsync(string workcenterExtraFeatureId)
    {
        return await _context.WorkcenterExtraFeatures
            .Where(u => u.Id == workcenterExtraFeatureId)
            .FirstOrDefaultAsync();
    }
    public async Task<IEnumerable<WorkcenterExtraFeature>> GetWorkcenterExtraFeaturesForUpdateAsync(IEnumerable<string> workcenterExtraFeatureIds)
    {
        return await _context.WorkcenterExtraFeatures
            .Where(u => workcenterExtraFeatureIds.Contains(u.Id))
            .ToListAsync();
    }
    public async Task<WorkcenterViewDto?> GetWorkcenterAsync(string workcenterId)
    {
        return await _context.Workcenters
            .Where(u => u.Id == workcenterId)
            .Select(u => new WorkcenterViewDto
            {
                Id = u.Id,
                Name = u.Name,
                Code = u.Code,
                PublicParentId = u.Parent != null ? u.Parent.Id : null,
                PublicWorkcenterTypeId = u.WorkcenterType!.Id,
                Disabled = u.Disabled,
                Deleted = u.Deleted
            })
            .FirstOrDefaultAsync();
    }
    public async Task<WorkcenterViewCompleteDto?> GetWorkcenterCompleteAsync(string workcenterId)
    {
        return await _context.Workcenters
            .Where(u => u.Id == workcenterId)
            .Select(u => new WorkcenterViewCompleteDto
            {
                Id = u.Id,
                Name = u.Name,
                Code = u.Code,
                PublicParentId = u.Parent != null ? u.Parent.Id : null,
                PublicWorkcenterTypeId = u.WorkcenterType!.Id,
                Disabled = u.Disabled,
                Deleted = u.Deleted,
                Addresses = u.WorkcenterAddresses != null ? u.WorkcenterAddresses.Where(a => !a.Deleted).Select(a => new WorkcenterAddressViewDto
                {
                    Id = a.Id,
                    PublicWorkcenterId = a.Workcenter!.Id,
                    PublicAddressId = a.Address!.Id,
                    Address = new AddressViewDto
                    {
                        Id = a.Address!.Id,
                        AddressText = a.Address.AddressText,
                        PostalCode = a.Address.PostalCode,
                        PublicStateId = a.Address.State != null ? a.Address.State.Id : null,
                        PublicCountryId = a.Address.Country != null ? a.Address.Country.Id : null,
                        PublicCityId = a.Address.City != null ? a.Address.City.Id : null,
                    },
                    IsDefault = a.IsDefault
                }).ToList() : null,
                Phones = u.WorkcenterPhones != null ? u.WorkcenterPhones.Where(p => !p.Deleted).Select(p => new WorkcenterPhoneViewDto
                {
                    Id = p.Id,
                    PublicWorkcenterId = p.Workcenter!.Id,
                    PublicPhoneId = p.Phone!.Id,
                    Phone = new PhoneViewDto
                    {
                        Id = p.Phone.Id,
                        Number = p.Phone.Number,
                        CountryCode = p.Phone.CountryCode,
                    }
                }).ToList() : null,
                ExtraFeatures = u.WorkcenterExtraFeatures != null ? u.WorkcenterExtraFeatures.Where(ef => !ef.Deleted).Select(ef => new WorkcenterExtraFeatureViewDto
                {
                    Id = ef.Id,
                    PublicWorkcenterId = ef.Workcenter!.Id,
                    PublicExtraFeatureId = ef.ExtraFeature!.Id,
                    ExtraFeature = new ExtraFeatureViewDto
                    {
                        Id = ef.ExtraFeature.Id,
                        Title = ef.ExtraFeature.Title,
                        Value = ef.ExtraFeature.Value,
                    }
                }).ToList() : null
            })
            .FirstOrDefaultAsync();
    }
}