using Microsoft.EntityFrameworkCore;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
namespace OrganizationManagement.Stores;

public class ExtraFeatureStore : IExtraFeatureStore
{
    private readonly OrganizationManagementDbContext _context;
    public ExtraFeatureStore(OrganizationManagementDbContext context)
    {
        _context = context;
    }
    public async Task<Address> AddAddressAsync(Address address, bool saveChanges = false)
    {
        _context.Addresses.Add(address);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return address;
    }
    public async Task<IEnumerable<Address>> AddAddressesAsync(IEnumerable<Address> address, bool saveChanges = false)
    {
        await _context.Addresses.AddRangeAsync(address);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return address;
    }
    public async Task<ExtraFeature> AddExtraFeatureAsync(ExtraFeature extraFeature, bool saveChanges = false)
    {
        _context.ExtraFeatures.Add(extraFeature);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return extraFeature;
    }
    public async Task<IEnumerable<ExtraFeature>> AddExtraFeaturesAsync(IEnumerable<ExtraFeature> extraFeature, bool saveChanges = false)
    {
        await _context.ExtraFeatures.AddRangeAsync(extraFeature);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return extraFeature;
    }
    public async Task<Phone> AddPhoneAsync(Phone phone, bool saveChanges = false)
    {
        _context.Phones.Add(phone);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return phone;
    }
    public async Task<IEnumerable<Phone>> AddPhonesAsync(IEnumerable<Phone> phone, bool saveChanges = false)
    {
        await _context.Phones.AddRangeAsync(phone);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return phone;
    }
    public async Task<IEnumerable<AddressViewDto>> GetAddressesAsync(IEnumerable<string> addressIds)
    {
        return await _context.Addresses
            .Where(a => addressIds.Contains(a.Id))
            .Select(a => new AddressViewDto
            {
                Id = a.Id,
                PostalCode = a.PostalCode,
                Title = a.Title,
                AddressText = a.AddressText,
                PublicCountryId = a.Country == null ? null : a.Country.Id,
                PublicStateId = a.State == null ? null : a.State.Id,
                PublicCityId = a.City == null ? null : a.City.Id,
            })
            .ToListAsync();
    }
    public async Task<IEnumerable<Address>> GetAddressesForUpdateAsync(IEnumerable<string> addressIds)
    {
        return await _context.Addresses
             .Where(a => addressIds.Contains(a.Id))
             .ToListAsync();
    }
    public async Task<Address?> GetAddressForUpdateAsync(string addressId)
    {
        return await _context.Addresses.Where(a => a.Id == addressId).FirstOrDefaultAsync();
    }
    public async Task<ExtraFeature?> GetExtraFeatureForUpdateAsync(string extraFeatureId)
    {
        return await _context.ExtraFeatures
             .Where(e => e.Id == extraFeatureId)
             .FirstOrDefaultAsync();
    }
    public async Task<IEnumerable<ExtraFeatureViewDto>> GetExtraFeaturesAsync(IEnumerable<string> extraFeatureIds)
    {
        return await _context.ExtraFeatures
            .Where(e => extraFeatureIds.Contains(e.Id))
            .Select(e => new ExtraFeatureViewDto
            {
                Id = e.Id,
                Title = e.Title,
                Value = e.Value,
            })
            .ToListAsync();
    }
    public async Task<IEnumerable<ExtraFeature>> GetExtraFeaturesForUpdateAsync(IEnumerable<string> extraFeatureIds)
    {
        return await _context.ExtraFeatures
             .Where(e => extraFeatureIds.Contains(e.Id))
             .ToListAsync();
    }
    public async Task<Phone?> GetPhoneForUpdateAsync(string phoneId)
    {
        return await _context.Phones
             .Where(p => p.Id == phoneId)
             .FirstOrDefaultAsync();
    }
    public async Task<IEnumerable<PhoneViewDto>> GetPhonesAsync(IEnumerable<string> phoneIds)
    {
        var phones = _context.Phones
              .Where(p => phoneIds.Contains(p.Id))
              .Select(p => new PhoneViewDto
              {
                  Id = p.Id,
                  Number = p.Number,
                  CountryCode = p.CountryCode,
              });
        return await phones.ToListAsync();
    }
    public async Task<IEnumerable<Phone>> GetPhonesForUpdateAsync(IEnumerable<string> phoneIds)
    {
        return await _context.Phones
              .Where(p => phoneIds.Contains(p.Id))
              .ToListAsync();
    }
    public async Task<Address> UpdateAddressAsync(Address address, bool saveChanges = false)
    {
        _context.Addresses.Update(address);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return address;
    }
    public async Task<IEnumerable<Address>> UpdateAddressesAsync(IEnumerable<Address> address, bool saveChanges = false)
    {
        _context.Addresses.UpdateRange(address);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return address;
    }
    public async Task<ExtraFeature> UpdateExtraFeatureAsync(ExtraFeature extraFeature, bool saveChanges = false)
    {
        _context.ExtraFeatures.Update(extraFeature);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return extraFeature;
    }
    public async Task<IEnumerable<ExtraFeature>> UpdateExtraFeaturesAsync(IEnumerable<ExtraFeature> extraFeature, bool saveChanges = false)
    {
        _context.ExtraFeatures.UpdateRange(extraFeature);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return extraFeature;
    }
    public async Task<Phone> UpdatePhoneAsync(Phone phone, bool saveChanges = false)
    {
        _context.Phones.Update(phone);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return phone;
    }
    public async Task<IEnumerable<Phone>> UpdatePhonesAsync(IEnumerable<Phone> phone, bool saveChanges = false)
    {
        _context.Phones.UpdateRange(phone);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return phone;
    }
}