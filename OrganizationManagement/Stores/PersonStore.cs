using Microsoft.EntityFrameworkCore;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Stores;

public class PersonStore : IPersonStore
{
    private readonly OrganizationManagementDbContext _context;
    public PersonStore(OrganizationManagementDbContext context)
    {
        _context = context;
    }
    public async Task<Person> AddPersonAsync(Person person, bool saveChanges = false)
    {
        _context.Add(person);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return person;
    }
    public async Task<IEnumerable<Person>> AddPersonsAsync(IEnumerable<Person> persons, bool saveChanges = false)
    {
        _context.AddRange(persons);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return persons;
    }
    public async Task<Person?> GetPersonForUpdateAsync(string personId)
    {
        return await _context.Persons.FirstOrDefaultAsync(u => u.Id == personId);
    }
    public async Task<IEnumerable<Person>> GetPersonsForUpdateAsync(IEnumerable<string> personIds)
    {
        return await _context.Persons.Where(u => personIds.Contains(u.Id)).ToListAsync();
    }
    public async Task<IDictionary<string, int>> IdConvertForPersonAsync(IEnumerable<string> personIds)
    {
        return await _context.Persons
              .Where(u => personIds.Contains(u.Id))
              .ToDictionaryAsync(u => u.Id, u => u.AutoIncrementId);
    }
    public async Task<Person> UpdatePersonAsync(Person person, bool saveChanges = false)
    {
        _context.Update(person);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return person;
    }
    public async Task<IEnumerable<Person>> UpdatePersonsAsync(IEnumerable<Person> persons, bool saveChanges = false)
    {
        _context.UpdateRange(persons);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return persons;
    }
    public async Task<PagedListDto<PersonViewDto>> GetPersonsAsync(PagedListCo<GetPersonsCo> co)
    {
        return await GetPersonsAsync(co, q => q.Select(x => new PersonViewDto
        {
            Id = x.Id,
            Name = x.Name,
            Surname = x.Surname,
            IdentityNumber = x.IdentityNumber,
            Disabled = x.Disabled,
            DateOfBirth = x.DateOfBirth,
            MiddleName = x.MiddleName,
            PublicCountryId = x.Country!.Id,
            PublicTitleId = x.Title != null ? x.Title.Id : null
        }));
    }
    public async Task<PagedListDto<PersonForAutoCompleteDto>> GetPersonsForAutoCompleteAsync(PagedListCo<GetPersonsCo> co)
    {
        return await GetPersonsAsync(co, q => q.Select(x => new PersonForAutoCompleteDto
        {
            Id = x.Id,
            Name = x.Name,
            Surname = x.Surname,
            IdentityNumber = x.IdentityNumber,
        }));
    }
    private async Task<PagedListDto<T>> GetPersonsAsync<T>(PagedListCo<GetPersonsCo> co, Func<IQueryable<Person>, IQueryable<T>> selector)
    {
        var pl = new PagedListDto<T>
        {
            Page = co.Pager.Page,
            Size = co.Pager.Size
        };
        var query = _context.Persons.AsQueryable();
        if (co.Criteria != null)
        {
            if (!string.IsNullOrEmpty(co.Criteria.Id))
            {
                query = query.Where(x => x.Id == co.Criteria.Id);
            }
            if (!string.IsNullOrEmpty(co.Criteria.NameContains))
            {
                query = query.Where(x => x.Name.Contains(co.Criteria.NameContains));
            }
            if (!string.IsNullOrEmpty(co.Criteria.Name))
            {
                query = query.Where(x => x.Name == co.Criteria.Name);
            }
            if (!string.IsNullOrEmpty(co.Criteria.Surname))
            {
                query = query.Where(x => x.Surname != null && x.Surname.Contains(co.Criteria.Surname));
            }
            if (co.Criteria.Ids != null && co.Criteria.Ids.Any())
            {
                query = query.Where(x => co.Criteria.Ids.Contains(x.Id));
            }
            if (co.Criteria.Disabled.HasValue)
            {
                query = query.Where(x => x.Disabled == co.Criteria.Disabled);
            }
            if (co.Criteria.Deleted.HasValue)
            {
                query = query.Where(x => x.Deleted == co.Criteria.Deleted);
            }
        }
        pl.Count = await query.CountAsync();
        if (pl.Count != 0)
        {
            //todo: sorting
            pl.Data = await selector(query).Skip(co.Pager.Skip).Take(co.Pager.Size).ToListAsync();
        }
        return pl;
    }
    public async Task<IEnumerable<PersonAddressViewDto>> GetPersonAddressesAsync(string personId)
    {
        return await _context.PersonAddresses
                 .Where(u => !u.Deleted && u.Person != null && u.Person.Id == personId && u.Address != null)
              .Select(u => new PersonAddressViewDto
              {
                  Id = u.Id,
                  PublicPersonId = u.Person!.Id,
                  PublicAddressId = u.Address!.Id,
                  Address = new AddressViewDto
                  {
                      Id = u.Address!.Id,
                      AddressText = u.Address.AddressText,
                      PostalCode = u.Address.PostalCode,
                      PublicStateId = u.Address.State != null ? u.Address.State.Id : null,
                      PublicCountryId = u.Address.Country != null ? u.Address.Country.Id : null,
                      PublicCityId = u.Address.City != null ? u.Address.City.Id : null,
                  },
                  IsDefault = u.IsDefault
              }).ToListAsync();
    }
    public async Task<IEnumerable<PersonPhoneViewDto>> GetPersonPhonesAsync(string personId)
    {
        return await _context.PersonPhones
                 .Where(p => !p.Deleted && p.Person != null && p.Person.Id == personId && p.Phone != null)
              .Select(u => new PersonPhoneViewDto
              {
                  Id = u.Id,
                  PublicPersonId = u.Person!.Id,
                  PublicPhoneId = u.Phone!.Id,
                  Phone = new PhoneViewDto
                  {
                      Id = u.Phone.Id,
                      Number = u.Phone.Number,
                      CountryCode = u.Phone.CountryCode,
                  }
              }).ToListAsync();
    }
    public async Task<IEnumerable<PersonExtraFeatureViewDto>> GetPersonExtraFeaturesAsync(string personId)
    {
        return await _context.PersonExtraFeatures
                 .Where(ef => !ef.Deleted && ef.Person != null && ef.Person.Id == personId && ef.ExtraFeature != null)
              .Select(u => new PersonExtraFeatureViewDto
              {
                  Id = u.Id,
                  PublicPersonId = u.Person!.Id,
                  PublicExtraFeatureId = u.ExtraFeature!.Id,
                  ExtraFeature = new ExtraFeatureViewDto
                  {
                      Id = u.ExtraFeature.Id,
                      Title = u.ExtraFeature.Title,
                      Value = u.ExtraFeature.Value,
                  }
              }).ToListAsync();
    }
    public async Task<PersonAddress?> AddPersonAddressAsync(PersonAddress personAddress, bool saveChanges = false)
    {
        _context.Add(personAddress);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personAddress;
    }
    public async Task<IEnumerable<PersonAddress>?> AddPersonAddressesAsync(IEnumerable<PersonAddress> personAddresses, bool saveChanges = false)
    {
        _context.AddRange(personAddresses);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personAddresses;
    }
    public async Task<PersonAddress?> UpdatePersonAddressAsync(PersonAddress personAddress, bool saveChanges = false)
    {
        _context.Update(personAddress);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personAddress;
    }
    public async Task<IEnumerable<PersonAddress>?> UpdatePersonAddressesAsync(IEnumerable<PersonAddress> personAddresses, bool saveChanges = false)
    {
        _context.UpdateRange(personAddresses);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personAddresses;
    }
    public async Task<PersonAddress?> GetPersonAddressForUpdateAsync(string personAddressId)
    {
        return await _context.PersonAddresses
            .Where(u => u.Id == personAddressId)
            .FirstOrDefaultAsync();
    }
    public async Task<IEnumerable<PersonAddress>> GetPersonAddressesForUpdateAsync(IEnumerable<string> personAddressIds)
    {
        return await _context.PersonAddresses
            .Where(u => personAddressIds.Contains(u.Id))
            .ToListAsync();
    }
    public async Task<PersonPhone?> AddPersonPhoneAsync(PersonPhone personPhone, bool saveChanges = false)
    {
        _context.Add(personPhone);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personPhone;
    }
    public async Task<IEnumerable<PersonPhone>?> AddPersonPhonesAsync(IEnumerable<PersonPhone> personPhones, bool saveChanges = false)
    {
        _context.AddRange(personPhones);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personPhones;
    }
    public async Task<PersonPhone?> UpdatePersonPhoneAsync(PersonPhone personPhone, bool saveChanges = false)
    {
        _context.Update(personPhone);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personPhone;
    }
    public async Task<IEnumerable<PersonPhone>?> UpdatePersonPhonesAsync(IEnumerable<PersonPhone> personPhones, bool saveChanges = false)
    {
        _context.UpdateRange(personPhones);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personPhones;
    }
    public async Task<PersonPhone?> GetPersonPhoneForUpdateAsync(string personPhoneId)
    {
        return await _context.PersonPhones
            .Where(u => u.Id == personPhoneId)
            .FirstOrDefaultAsync();
    }
    public async Task<IEnumerable<PersonPhone>> GetPersonPhonesForUpdateAsync(IEnumerable<string> personPhoneIds)
    {
        return await _context.PersonPhones
            .Where(u => personPhoneIds.Contains(u.Id))
            .ToListAsync();
    }
    public async Task<PersonExtraFeature?> AddPersonExtraFeatureAsync(PersonExtraFeature personExtraFeature, bool saveChanges = false)
    {
        _context.Add(personExtraFeature);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personExtraFeature;
    }
    public async Task<IEnumerable<PersonExtraFeature>?> AddPersonExtraFeaturesAsync(IEnumerable<PersonExtraFeature> personExtraFeatures, bool saveChanges = false)
    {
        _context.AddRange(personExtraFeatures);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personExtraFeatures;
    }
    public async Task<PersonExtraFeature?> UpdatePersonExtraFeatureAsync(PersonExtraFeature personExtraFeature, bool saveChanges = false)
    {
        _context.Update(personExtraFeature);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personExtraFeature;
    }
    public async Task<IEnumerable<PersonExtraFeature>?> UpdatePersonExtraFeaturesAsync(IEnumerable<PersonExtraFeature> personExtraFeatures, bool saveChanges = false)
    {
        _context.UpdateRange(personExtraFeatures);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personExtraFeatures;
    }
    public async Task<PersonExtraFeature?> GetPersonExtraFeatureForUpdateAsync(string personExtraFeatureId)
    {
        return await _context.PersonExtraFeatures
            .Where(u => u.Id == personExtraFeatureId)
            .FirstOrDefaultAsync();
    }
    public async Task<IEnumerable<PersonExtraFeature>> GetPersonExtraFeaturesForUpdateAsync(IEnumerable<string> personExtraFeatureIds)
    {
        return await _context.PersonExtraFeatures
            .Where(u => personExtraFeatureIds.Contains(u.Id))
            .ToListAsync();
    }
    public async Task<PersonDetailViewDto?> GetPersonDetailAsync(string personId)
    {
        return await _context.PersonDetails
            .Where(u => u.Person != null && u.Person.Id == personId)
            .Select(u => new PersonDetailViewDto
            {
                Id = u.Id,
                PublicPersonId = personId,
                MotherName = u.MotherName,
                FatherName = u.FatherName,
                PlaceOfBirth = u.PlaceOfBirth,
                PublicCityId = u.City != null ? u.City.Id : null,
                PublicStateId = u.State != null ? u.State.Id : null,
                ManualCityName = u.ManualCityName,
                ManualStateName = u.ManualStateName,
                PublicGenderId = u.Gender != null ? u.Gender.Id : null,
                PublicMaritalStatusId = u.MaritalStatus != null ? u.MaritalStatus.Id : null,
                PublicBloodGroupId = u.BloodGroup != null ? u.BloodGroup.Id : null,
                IdCardSerialNumber = u.IdCardSerialNumber,
                IdCardAcceptanceDate = u.IdCardAcceptanceDate,
            })
            .FirstOrDefaultAsync();
    }
    public async Task<PersonViewDto?> GetPersonAsync(string personId)
    {
        return await _context.Persons
            .Where(u => u.Id == personId && !u.Deleted)
            .Select(u => new PersonViewDto
            {
                Id = u.Id,
                Name = u.Name,
                Surname = u.Surname,
                IdentityNumber = u.IdentityNumber,
                MiddleName = u.MiddleName,
                DateOfBirth = u.DateOfBirth,
                Disabled = u.Disabled,
                PublicCountryId = u.Country!.Id,
                PublicTitleId = u.Title != null ? u.Title.Id : null
            })
            .FirstOrDefaultAsync();
    }
    public async Task<PersonViewCompleteDto?> GetPersonCompleteAsync(string personId)
    {
        return await _context.Persons
            .Where(u => u.Id == personId && !u.Deleted) // Ensure the person exists
            .Select(u => new PersonViewCompleteDto
            {
                Id = u.Id,
                Name = u.Name,
                Surname = u.Surname,
                IdentityNumber = u.IdentityNumber,
                MiddleName = u.MiddleName,
                DateOfBirth = u.DateOfBirth,
                PublicCountryId = u.Country!.Id,
                PublicTitleId = u.Title != null ? u.Title.Id : null,
                Disabled = u.Disabled,
                Detail = u.PersonDetail != null ? new PersonDetailViewDto
                {
                    Id = u.PersonDetail.Id,
                    PublicPersonId = u.Id,
                    MotherName = u.PersonDetail.MotherName,
                    FatherName = u.PersonDetail.FatherName,
                    PlaceOfBirth = u.PersonDetail.PlaceOfBirth,
                    PublicCityId = u.PersonDetail.City != null ? u.PersonDetail.City.Id : null,
                    PublicStateId = u.PersonDetail.State != null ? u.PersonDetail.State.Id : null,
                    ManualCityName = u.PersonDetail.ManualCityName,
                    ManualStateName = u.PersonDetail.ManualStateName,
                    PublicGenderId = u.PersonDetail.Gender != null ? u.PersonDetail.Gender.Id : null,
                    PublicMaritalStatusId = u.PersonDetail.MaritalStatus != null ? u.PersonDetail.MaritalStatus.Id : null,
                    PublicBloodGroupId = u.PersonDetail.BloodGroup != null ? u.PersonDetail.BloodGroup.Id : null,
                    IdCardSerialNumber = u.PersonDetail.IdCardSerialNumber,
                    IdCardAcceptanceDate = u.PersonDetail.IdCardAcceptanceDate,
                } : null,
                Addresses = u.PersonAddresses != null ? u.PersonAddresses.Where(a => !a.Deleted).Select(a => new PersonAddressViewDto
                {
                    Id = a.Id,
                    PublicPersonId = a.Person!.Id,
                    PublicAddressId = a.Address!.Id,
                    Address = new AddressViewDto
                    {
                        Id = a.Address!.Id,
                        AddressText = a.Address.AddressText,
                        PostalCode = a.Address.PostalCode,
                        PublicStateId = a.Address.State != null ? a.Address.State.Id : null,
                        PublicCountryId = a.Address.Country != null ? a.Address.Country.Id : null,
                        PublicCityId = a.Address.City != null ? a.Address.City.Id : null,
                    },
                    IsDefault = a.IsDefault
                }).ToList() : null,
                Phones = u.PersonPhones != null ? u.PersonPhones.Where(p => !p.Deleted).Select(p => new PersonPhoneViewDto
                {
                    Id = p.Id,
                    PublicPersonId = p.Person!.Id,
                    PublicPhoneId = p.Phone!.Id,
                    Phone = new PhoneViewDto
                    {
                        Id = p.Phone.Id,
                        Number = p.Phone.Number,
                        CountryCode = p.Phone.CountryCode,
                    }
                }).ToList() : null,
                ExtraFeatures = u.PersonExtraFeatures != null ? u.PersonExtraFeatures.Where(ef => !ef.Deleted).Select(ef => new PersonExtraFeatureViewDto
                {
                    Id = ef.Id,
                    PublicPersonId = ef.Person!.Id,
                    PublicExtraFeatureId = ef.ExtraFeature!.Id,
                    ExtraFeature = new ExtraFeatureViewDto
                    {
                        Id = ef.ExtraFeature.Id,
                        Title = ef.ExtraFeature.Title,
                        Value = ef.ExtraFeature.Value,
                    }
                }).ToList() : null,
                Passports = u.PersonPassports != null ? u.PersonPassports.Where(p => !p.Deleted).Select(p => new PersonPassportViewDto
                {
                    Id = p.Id,
                    PublicPersonId = p.Person!.Id,
                    Number = p.Number,
                    AcceptanceDate = p.AcceptanceDate,
                    Disabled = p.Disabled,
                }).ToList() : null
            })
            .FirstOrDefaultAsync();
    }
    public async Task<PersonDetail> AddPersonDetailAsync(PersonDetail personDetail, bool saveChanges = false)
    {
        _context.Add(personDetail);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personDetail;
    }
    public async Task<IEnumerable<PersonDetail>> AddPersonDetailsAsync(IEnumerable<PersonDetail> personDetails, bool saveChanges = false)
    {
        _context.AddRange(personDetails);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personDetails.ToList();
    }
    public async Task<PersonDetail> UpdatePersonDetailAsync(PersonDetail personDetail, bool saveChanges = false)
    {
        _context.Update(personDetail);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personDetail;
    }
    public async Task<IEnumerable<PersonDetail>> UpdatePersonDetailsAsync(IEnumerable<PersonDetail> personDetails, bool saveChanges = false)
    {
        _context.UpdateRange(personDetails);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return personDetails.ToList();
    }
    public async Task<IEnumerable<PersonDetail>> GetPersonDetailsForUpdateAsync(IEnumerable<string> personDetailIds)
    {
        return await _context.PersonDetails.Where(a => personDetailIds.Contains(a.Id)).ToListAsync();
    }
    public async Task<PersonDetail?> GetPersonDetailForUpdateAsync(string personDetailId)
    {
        return await _context.PersonDetails.FirstOrDefaultAsync(a => a.Id == personDetailId);
    }
    public async Task<IDictionary<string, bool>> HasPersonDetailsAsync(IEnumerable<string> personIds)
    {
        var savedPersonIds = await _context.PersonDetails.Where(a => a.Person != null && personIds.Contains(a.Person.Id)).Select(a => a.Person!.Id).ToListAsync();
        return personIds.ToDictionary(a => a, a => savedPersonIds.Contains(a));
    }
    public async Task<bool> HasPersonDetailAsync(string personId)
    {
        return await _context.PersonDetails.Where(a => a.Person != null && a.Person.Id == personId).AnyAsync();
    }
}