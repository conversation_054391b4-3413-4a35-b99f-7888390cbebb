using Microsoft.EntityFrameworkCore;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
namespace OrganizationManagement.Stores;

public class LocationStore : ILocationStore
{
    private readonly OrganizationManagementDbContext _context;
    public LocationStore(OrganizationManagementDbContext context)
    {
        _context = context;
    }
    public async Task AddCitiesAsync(IEnumerable<City> city, bool saveChanges = false)
    {
        _context.Cities.AddRange(city);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
    }
    public async Task AddCityAsync(City city, bool saveChanges = false)
    {
        _context.Cities.Add(city);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
    }
    public async Task AddCountriesAsync(IEnumerable<Country> country, bool saveChanges = false)
    {
        _context.Countries.AddRange(country);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
    }
    public async Task AddCountryAsync(Country country, bool saveChanges = false)
    {
        _context.Countries.Add(country);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
    }
    public async Task AddStateAsync(State state, bool saveChanges = false)
    {
        _context.States.Add(state);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
    }
    public async Task AddStatesAsync(IEnumerable<State> state, bool saveChanges = false)
    {
        _context.States.AddRange(state);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
    }
    public async Task<IEnumerable<ActiveCityDto>> GetActiveCitiesByStateAsync(int stateId)
    {
        var cities = await _context.Cities
        .Where(c => c.StateId == stateId && !c.Disabled && !c.Deleted)
            .Select(c => new ActiveCityDto
            {
                Id = c.Id,
                Name = c.Name,
            }).ToListAsync();
        return cities;
    }
    public async Task<IEnumerable<ActiveCountryDto>> GetActiveCountriesAsync()
    {
        var countries = await _context.Countries
        .Where(c => !c.Disabled && !c.Deleted)
            .Select(c => new ActiveCountryDto
            {
                Id = c.Id,
                Name = c.Name,
            }).ToListAsync();
        return countries;
    }
    public async Task<IEnumerable<ActiveStateDto>> GetActiveStatesByCountryAsync(int countryId)
    {
        var states = await _context.States
        .Where(s => s.CountryId == countryId && !s.Disabled && !s.Deleted)
            .Select(s => new ActiveStateDto
            {
                Id = s.Id,
                Name = s.Name,
            }).ToListAsync();
        return states;
    }
    public async Task<int> GetAutoIncrementIdByIdForCityAsync(string cityId)
    {
        var autoIncrementIds = await GetAutoIncrementIdsByIdForCityAsync([cityId]);
        return autoIncrementIds.FirstOrDefault();
    }
    public async Task<int> GetAutoIncrementIdByIdForCountryAsync(string countryId)
    {
        var autoIncrementIds = await GetAutoIncrementIdsByIdForCountryAsync([countryId]);
        return autoIncrementIds.FirstOrDefault();
    }
    public async Task<int> GetAutoIncrementIdByIdForStateAsync(string stateId)
    {
        var autoIncrementIds = await GetAutoIncrementIdsByIdForStateAsync([stateId]);
        return autoIncrementIds.FirstOrDefault();
    }
    public async Task<IEnumerable<int>> GetAutoIncrementIdsByIdForCityAsync(IEnumerable<string> cityIds)
    {
        return await _context.Cities
            .Where(c => cityIds.Contains(c.Id))
            .Select(c => c.AutoIncrementId)
            .ToListAsync();
    }
    public async Task<IEnumerable<int>> GetAutoIncrementIdsByIdForCountryAsync(IEnumerable<string> countryIds)
    {
        return await _context.Countries
            .Where(c => countryIds.Contains(c.Id))
            .Select(c => c.AutoIncrementId)
            .ToListAsync();
    }
    public async Task<IEnumerable<int>> GetAutoIncrementIdsByIdForStateAsync(IEnumerable<string> stateIds)
    {
        return await _context.States
            .Where(s => stateIds.Contains(s.Id))
            .Select(s => s.AutoIncrementId)
            .ToListAsync();
    }
    public async Task<IEnumerable<City>> GetCitiesForUpdateAsync(IEnumerable<string> cityIds)
    {
        return await _context.Cities.Where(c => cityIds.Contains(c.Id)).ToListAsync();
    }
    public async Task<City?> GetCityForUpdateAsync(string cityId)
    {
        return await _context.Cities.FirstOrDefaultAsync(c => c.Id == cityId);
    }
    public async Task<IEnumerable<Country>> GetCountriesForUpdateAsync(IEnumerable<string> countryIds)
    {
        return await _context.Countries.Where(c => countryIds.Contains(c.Id)).ToListAsync();
    }
    public async Task<Country?> GetCountryForUpdateAsync(string countryId)
    {
        return await _context.Countries.FirstOrDefaultAsync(c => c.Id == countryId);
    }
    public async Task<string?> GetCountryIdByNameAsync(string countryName)
    {
        return await _context.Countries
             .Where(c => c.Name == countryName)
             .Select(c => c.Id)
             .FirstOrDefaultAsync();
    }
    public async Task<State?> GetStateForUpdateAsync(string stateId)
    {
        return await _context.States.FirstOrDefaultAsync(s => s.Id == stateId);
    }
    public async Task<string?> GetStateIdByNameAsync(string stateName)
    {
        return await _context.States
            .Where(s => s.Name == stateName)
            .Select(s => s.Id)
            .FirstOrDefaultAsync();
    }
    public async Task<IEnumerable<State>> GetStatesForUpdateAsync(IEnumerable<string> stateIds)
    {
        return await _context.States.Where(s => stateIds.Contains(s.Id)).ToListAsync();
    }
    public async Task<IDictionary<string, int>> IdConvertForCityAsync(IEnumerable<string> cityIds)
    {
        return await _context.Cities
            .Where(c => cityIds.Contains(c.Id))
            .ToDictionaryAsync(c => c.Id, c => c.AutoIncrementId);
    }
    public async Task<IDictionary<string, int>> IdConvertForCountryAsync(IEnumerable<string> countryIds)
    {
        return await _context.Countries
            .Where(c => countryIds.Contains(c.Id))
            .ToDictionaryAsync(c => c.Id, c => c.AutoIncrementId);
    }
    public async Task<IDictionary<string, int>> IdConvertForStateAsync(IEnumerable<string> stateIds)
    {
        return await _context.States
            .Where(s => stateIds.Contains(s.Id))
            .ToDictionaryAsync(s => s.Id, s => s.AutoIncrementId);
    }
    public async Task<bool> IsCityExistsAsync(string name, int stateId)
    {
        return await _context.Cities
             .AnyAsync(c => c.Name == name && c.StateId == stateId && !c.Deleted);
    }
    public Task<bool> IsCityExistsAsync(string name, int stateId, string updateId)
    {
        return _context.Cities
            .AnyAsync(c => c.Name == name && c.StateId == stateId && !c.Deleted && c.Id != updateId);
    }
    public async Task<bool> IsCountryExistsAsync(string name)
    {
        return await _context.Countries
            .AnyAsync(c => c.Name == name && !c.Deleted);
    }
    public Task<bool> IsCountryExistsAsync(string name, string updateId)
    {
        return _context.Countries
             .AnyAsync(c => c.Name == name && !c.Deleted && c.Id != updateId);
    }
    public Task<bool> IsStateExistsAsync(string name, int countryId)
    {
        return _context.States
            .AnyAsync(s => s.Name == name && s.CountryId == countryId && !s.Deleted);
    }
    public Task<bool> IsStateExistsAsync(string name, int countryId, string updateId)
    {
        return _context.States
           .AnyAsync(s => s.Name == name && s.CountryId == countryId && !s.Deleted && s.Id != updateId);
    }
    public async Task UpdateCitiesAsync(IEnumerable<City> city, bool saveChanges = false)
    {
        _context.Cities.UpdateRange(city);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
    }
    public async Task UpdateCityAsync(City city, bool saveChanges = false)
    {
        _context.Cities.Update(city);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
    }
    public async Task UpdateCountriesAsync(IEnumerable<Country> country, bool saveChanges = false)
    {
        _context.Countries.UpdateRange(country);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
    }
    public async Task UpdateCountryAsync(Country county, bool saveChanges = false)
    {
        _context.Countries.Update(county);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
    }
    public async Task UpdateStateAsync(State state, bool saveChanges = false)
    {
        _context.States.Update(state);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
    }
    public async Task UpdateStatesAsync(IEnumerable<State> state, bool saveChanges = false)
    {
        _context.States.UpdateRange(state);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
    }
}