using Mapster;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Managers;

public class PositionManager : IPositionManager
{
    private readonly IPositionStore _positionStore;
    private readonly IRlxLocalizationManager<OrganizationManagementLocalizationDbContext> _localizationManager;
    private readonly IRlxEnumManager<OrganizationManagementEnumDbContext> _enumManager;
    private readonly ILocationManager _locationManager;
    private readonly ILogger<PositionManager> _logger;
    private readonly IExtraFeatureManager _extraFeatureManager;
    public PositionManager(IPositionStore positionStore,
        IRlxLocalizationManager<OrganizationManagementLocalizationDbContext> localizationManager, IRlxEnumManager<OrganizationManagementEnumDbContext> enumManager, ILocationManager locationManager, ILogger<PositionManager> logger, IExtraFeatureManager extraFeatureManager)
    {
        _localizationManager = localizationManager;
        _positionStore = positionStore;
        _enumManager = enumManager;
        _locationManager = locationManager;
        _logger = logger;
        _extraFeatureManager = extraFeatureManager;
    }
    public async Task<IEnumerable<Position>> AddPositionsAsync(IEnumerable<PositionAddDto> positions, bool saveChanges = false)
    {
        var positionList = new List<Position>();
        var parentIds = positions.Where(a => !string.IsNullOrEmpty(a.PublicParentId)).Select(a => a.PublicParentId!).Distinct().ToList();
        var positionTypeIds = positions.Where(a => !string.IsNullOrEmpty(a.PublicPositionTypeId)).Select(a => a.PublicPositionTypeId!).Distinct().ToList();
        var positionTypeIdMap = await _enumManager.IdConvertForEnumValue(positionTypeIds);
        var parentIdMap = await IdConvertForPositionAsync(parentIds);
        foreach (var position in positions)
        {
            var addPosition = position.Adapt<Position>();
            if (positionTypeIdMap.TryGetValue(position.PublicPositionTypeId!, out var positionTypeId))
            {
                addPosition.PositionTypeId = positionTypeId;
            }
            if (parentIdMap.TryGetValue(position.PublicParentId!, out var parentId))
            {
                addPosition.ParentId = parentId;
            }
            positionList.Add(addPosition);
        }
        await _positionStore.AddPositionsAsync(positionList);
        return positionList;
    }
    public async Task<Position> AddPositionAsync(PositionAddDto position, bool saveChanges = false)
    {
        return (await AddPositionsAsync([position], saveChanges)).First();
    }
    public async Task<IEnumerable<Position>> UpdatePositionsAsync(IEnumerable<PositionUpdateDto> positions, bool saveChanges = false)
    {
        var positionList = new List<Position>();
        var parentIds = positions.Where(a => !string.IsNullOrEmpty(a.PublicParentId)).Select(a => a.PublicParentId!).Distinct().ToList();
        var positionTypeIds = positions.Where(a => !string.IsNullOrEmpty(a.PublicPositionTypeId)).Select(a => a.PublicPositionTypeId!).Distinct().ToList();
        var positionTypeIdMap = await _enumManager.IdConvertForEnumValue(positionTypeIds);
        var parentIdMap = await IdConvertForPositionAsync(parentIds);
        var positionIds = positions.Select(a => a.Id).Distinct().ToList();
        var existingPositions = (await _positionStore.GetPositionsForUpdateAsync(positionIds))?.ToDictionary(a => a.Id);
        if (existingPositions == null || !existingPositions.Any())
        {
            throw new Exception("No positions found for update.");
        }
        foreach (var position in positions)
        {
            existingPositions.TryGetValue(position.Id, out var updatePosition);
            if (updatePosition == null)
            {
                throw new Exception($"Position not found: {position.Id}");
            }
            position.Adapt(updatePosition);
            if (positionTypeIdMap.TryGetValue(position.PublicPositionTypeId!, out var positionTypeId))
            {
                updatePosition.PositionTypeId = positionTypeId;
            }
            if (parentIdMap.TryGetValue(position.PublicParentId!, out var parentId))
            {
                updatePosition.ParentId = parentId;
            }
            positionList.Add(updatePosition);
        }
        await _positionStore.UpdatePositionsAsync(positionList, saveChanges);
        return positionList;
    }
    public async Task<Position> UpdatePositionAsync(PositionUpdateDto position, bool saveChanges = false)
    {
        return (await UpdatePositionsAsync([position], saveChanges)).First();
    }
    public async Task<IDictionary<string, int>> IdConvertForPositionAsync(IEnumerable<string> positionIds)
    {
        if (positionIds == null || !positionIds.Any())
            return new Dictionary<string, int>();
        return await _positionStore.IdConvertForPositionAsync(positionIds);
    }
    public async Task<IEnumerable<PositionTreeDto>> GetPositionsForTreeAsync(string? startPositionId = null)
    {
        var positions = await _positionStore.GetPositionsForTreeAsync();
        if (positions == null || !positions.Any())
        {
            return [];
        }
        var currentPosition = startPositionId != null ? positions.FirstOrDefault(u => u.Id == startPositionId) : null;
        var positionIds = positions.Select(u => u.Id).ToList();
        var groupedPositions = positions.GroupBy(u => u.ParentId).ToDictionary(g => g.Key ?? 0, g => g.ToList());
        var treeList = new List<PositionTreeDto>();
        CreateTree(groupedPositions, treeList, currentPosition, true);
        return treeList;
    }
    private void CreateTree(IDictionary<int, List<PositionTreeDto>> groupedPositions, List<PositionTreeDto> treeList, PositionTreeDto? currentPosition = null, bool isRoot = false)
    {
        var currentPositionPrivateId = currentPosition?.AutoIncrementId;
        if (isRoot)
        {
            if (currentPosition != null)
            {
                treeList.Add(currentPosition);
            }
        }
        groupedPositions.TryGetValue(currentPositionPrivateId ?? 0, out var children);
        if (children == null || !children.Any())
            return;
        if (isRoot && currentPosition == null)
            treeList.AddRange(children);
        foreach (var child in children)
        {
            if (currentPosition != null)
            {
                currentPosition.Children.Add(child);
            }
            CreateTree(groupedPositions, treeList, child);
        }
    }
    public async Task<PositionViewDto?> GetPositionAsync(string positionId)
    {
        var position = await _positionStore.GetPositionAsync(positionId);
        return position;
    }
    public async Task<PagedListDto<PositionForAutoCompleteDto>?> GetPositionsForAutoCompleteAsync(PagedListCo<GetPositionsCo> co)
    {
        // Validate olabilir
        var positions = await _positionStore.GetPositionsForAutoCompleteAsync(co);
        return positions;
    }
    // public async Task<PositionViewCompleteDto?> GetPositionCompleteAsync(string positionId)
    // {
    //     var position = await _positionStore.GetPositionCompleteAsync(positionId);
    //     return position;
    // }
}