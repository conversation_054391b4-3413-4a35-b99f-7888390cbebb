using Mapster;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Managers;

public class PersonManager : IPersonManager
{
    private readonly IPersonStore _personStore;
    private readonly IRlxLocalizationManager<OrganizationManagementLocalizationDbContext> _localizationManager;
    private readonly IRlxEnumManager<OrganizationManagementEnumDbContext> _enumManager;
    private readonly ILocationManager _locationManager;
    private readonly ILogger<PersonManager> _logger;
    private readonly IExtraFeatureManager _extraFeatureManager;
    public PersonManager(IPersonStore personStore,
        IRlxLocalizationManager<OrganizationManagementLocalizationDbContext> localizationManager, IRlxEnumManager<OrganizationManagementEnumDbContext> enumManager, ILocationManager locationManager, ILogger<PersonManager> logger, IExtraFeatureManager extraFeatureManager)
    {
        _localizationManager = localizationManager;
        _personStore = personStore;
        _enumManager = enumManager;
        _locationManager = locationManager;
        _logger = logger;
        _extraFeatureManager = extraFeatureManager;
    }
    public async Task<IEnumerable<Person>> AddPersonsAsync(IEnumerable<PersonAddDto> persons, bool saveChanges = false)
    {
        var personList = new List<Person>();
        var countryIds = persons.Where(a => !string.IsNullOrEmpty(a.PublicCountryId)).Select(a => a.PublicCountryId!).Distinct().ToList();
        var titleIds = persons.Where(a => !string.IsNullOrEmpty(a.PublicTitleId)).Select(a => a.PublicTitleId!).Distinct().ToList();
        var countryIdMap = await _locationManager.IdConvertForCountryAsync(countryIds);
        var titleIdMap = await _enumManager.IdConvertForEnumValue(titleIds);
        foreach (var person in persons)
        {
            var addPerson = person.Adapt<Person>();
            if (countryIdMap.TryGetValue(person.PublicCountryId!, out var countryId))
            {
                addPerson.CountryId = countryId;
            }
            if (titleIdMap.TryGetValue(person.PublicTitleId!, out var titleId))
            {
                addPerson.TitleId = titleId;
            }
            personList.Add(addPerson);
        }
        await _personStore.AddPersonsAsync(personList);
        return personList;
    }
    public async Task<Person> AddPersonAsync(PersonAddDto person, bool saveChanges = false)
    {
        return (await AddPersonsAsync([person], saveChanges)).First();
    }
    public async Task<IEnumerable<Person>> UpdatePersonsAsync(IEnumerable<PersonUpdateDto> persons, bool saveChanges = false)
    {
        var personList = new List<Person>();
        var countryIds = persons.Where(a => !string.IsNullOrEmpty(a.PublicCountryId)).Select(a => a.PublicCountryId!).Distinct().ToList();
        var titleIds = persons.Where(a => !string.IsNullOrEmpty(a.PublicTitleId)).Select(a => a.PublicTitleId!).Distinct().ToList();
        var countryIdMap = await _locationManager.IdConvertForCountryAsync(countryIds);
        var titleIdMap = await _enumManager.IdConvertForEnumValue(titleIds);
        var personIds = persons.Select(a => a.Id).Distinct().ToList();
        var existingPersons = (await _personStore.GetPersonsForUpdateAsync(personIds))?.ToDictionary(a => a.Id);
        if (existingPersons == null || !existingPersons.Any())
        {
            throw new Exception("No persons found for update.");
        }
        foreach (var person in persons)
        {
            existingPersons.TryGetValue(person.Id, out var updatePerson);
            if (updatePerson == null)
            {
                throw new Exception($"Person not found: {person.Id}");
            }
            person.Adapt(updatePerson);
            if (titleIdMap.TryGetValue(person.PublicTitleId!, out var titleId))
            {
                updatePerson.TitleId = titleId;
            }
            if (countryIdMap.TryGetValue(person.PublicCountryId!, out var countryId))
            {
                updatePerson.CountryId = countryId;
            }
            personList.Add(updatePerson);
        }
        await _personStore.UpdatePersonsAsync(personList, saveChanges);
        return personList;
    }
    public async Task<Person> UpdatePersonAsync(PersonUpdateDto person, bool saveChanges = false)
    {
        return (await UpdatePersonsAsync([person], saveChanges)).First();
    }
    public async Task<IDictionary<string, int>> IdConvertForPersonAsync(IEnumerable<string> personIds)
    {
        if (personIds == null || !personIds.Any())
            return new Dictionary<string, int>();
        return await _personStore.IdConvertForPersonAsync(personIds);
    }
    public async Task<PersonViewDto?> GetPersonAsync(string personId)
    {
        var person = await _personStore.GetPersonAsync(personId);
        return person;
    }
    public async Task<IEnumerable<PersonAddressViewDto>?> GetPersonAddressesAsync(string personId)
    {
        var personAddresses = await _personStore.GetPersonAddressesAsync(personId);
        return personAddresses;
    }
    public async Task<IEnumerable<PersonPhoneViewDto>?> GetPersonPhonesAsync(string personId)
    {
        var personPhones = await _personStore.GetPersonPhonesAsync(personId);
        return personPhones;
    }
    public async Task<IEnumerable<PersonExtraFeatureViewDto>?> GetPersonExtraFeaturesAsync(string personId)
    {
        var personExtraFeatures = await _personStore.GetPersonExtraFeaturesAsync(personId);
        return personExtraFeatures;
    }
    public async Task<PagedListDto<PersonForAutoCompleteDto>?> GetPersonsForAutoCompleteAsync(PagedListCo<GetPersonsCo> co)
    {
        // Validate olabilir
        var persons = await _personStore.GetPersonsForAutoCompleteAsync(co);
        return persons;
    }
    public async Task<PersonViewCompleteDto?> GetPersonCompleteAsync(string personId)
    {
        var person = await _personStore.GetPersonCompleteAsync(personId);
        return person;
    }
    public async Task<PersonAddress?> AddPersonAddressAsync(PersonAddressAddDto personAddress, bool saveChanges = false)
    {
        return (await AddPersonAddressesAsync([personAddress], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to add person address.");
    }
    public async Task<IEnumerable<PersonAddress>?> AddPersonAddressesAsync(IEnumerable<PersonAddressAddDto> personAddresses, bool saveChanges = false)
    {
        var personAddressList = new List<PersonAddress>();
        var personIds = personAddresses.Select(a => a.PublicPersonId).Distinct().ToList();
        var personIdMap = await _personStore.IdConvertForPersonAsync(personIds);
        foreach (var personAddress in personAddresses)
        {
            if (!personIdMap.TryGetValue(personAddress.PublicPersonId, out var personId))
            {
                throw new Exception($"Person not found: {personAddress.PublicPersonId}");
            }
            var addPersonAddress = new PersonAddress
            {
                Id = Guid.NewGuid().ToString(),
                PersonId = personId,
                IsDefault = personAddress.IsDefault
            };
            personAddressList.Add(addPersonAddress);
            var address = await _extraFeatureManager.AddAddressAsync(personAddress.Address, false);
            addPersonAddress.Address = address;
        }
        return await _personStore.AddPersonAddressesAsync(personAddressList, saveChanges);
    }
    public async Task<PersonAddress?> UpdatePersonAddressAsync(PersonAddressUpdateDto personAddress, bool saveChanges = false)
    {
        return (await UpdatePersonAddressesAsync([personAddress], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to update person address.");
    }
    public async Task<IEnumerable<PersonAddress>?> UpdatePersonAddressesAsync(IEnumerable<PersonAddressUpdateDto> personAddresses, bool saveChanges = false)
    {
        var personAddressList = new List<PersonAddress>();
        var personIds = personAddresses.Select(a => a.PublicPersonId).Distinct().ToList();
        var personIdMap = await _personStore.IdConvertForPersonAsync(personIds);
        var personAddressIds = personAddresses.Select(a => a.Id).Distinct().ToList();
        var existingPersonAddresses = (await _personStore.GetPersonAddressesForUpdateAsync(personAddressIds))?.ToDictionary(a => a.Id);
        if (existingPersonAddresses == null || !existingPersonAddresses.Any())
        {
            throw new Exception("No person addresses found for update.");
        }
        foreach (var personAddress in personAddresses)
        {
            if (!personIdMap.TryGetValue(personAddress.PublicPersonId, out var personId))
            {
                throw new Exception($"Person not found: {personAddress.PublicPersonId}");
            }
            existingPersonAddresses.TryGetValue(personAddress.Id, out var updatePersonAddress);
            if (updatePersonAddress == null)
            {
                throw new Exception($"Person address not found: {personAddress.Id}");
            }
            updatePersonAddress.PersonId = personId;
            updatePersonAddress.IsDefault = personAddress.IsDefault;
            updatePersonAddress.Disabled = personAddress.Disabled;
            updatePersonAddress.Deleted = personAddress.Deleted;
            var address = await _extraFeatureManager.UpdateAddressAsync(personAddress.Address, false);
            updatePersonAddress.Address = address;
            personAddressList.Add(updatePersonAddress);
        }
        return await _personStore.UpdatePersonAddressesAsync(personAddressList, saveChanges);
    }
    public async Task<PersonPhone?> AddPersonPhoneAsync(PersonPhoneAddDto personPhone, bool saveChanges = false)
    {
        return (await AddPersonPhonesAsync([personPhone], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to add person phone.");
    }
    public async Task<IEnumerable<PersonPhone>?> AddPersonPhonesAsync(IEnumerable<PersonPhoneAddDto> personPhones, bool saveChanges = false)
    {
        var personPhoneList = new List<PersonPhone>();
        var personIds = personPhones.Select(a => a.PublicPersonId).Distinct().ToList();
        var personIdMap = await _personStore.IdConvertForPersonAsync(personIds);
        foreach (var personPhone in personPhones)
        {
            if (!personIdMap.TryGetValue(personPhone.PublicPersonId, out var personId))
            {
                throw new Exception($"Person not found: {personPhone.PublicPersonId}");
            }
            var addPersonPhone = new PersonPhone
            {
                Id = Guid.NewGuid().ToString(),
                PersonId = personId,
            };
            personPhoneList.Add(addPersonPhone);
            var phone = await _extraFeatureManager.AddPhoneAsync(personPhone.Phone, false);
            addPersonPhone.Phone = phone;
        }
        return await _personStore.AddPersonPhonesAsync(personPhoneList, saveChanges);
    }
    public async Task<PersonPhone?> UpdatePersonPhoneAsync(PersonPhoneUpdateDto personPhone, bool saveChanges = false)
    {
        return (await UpdatePersonPhonesAsync([personPhone], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to update person phone.");
    }
    public async Task<IEnumerable<PersonPhone>?> UpdatePersonPhonesAsync(IEnumerable<PersonPhoneUpdateDto> personPhones, bool saveChanges = false)
    {
        var personPhoneList = new List<PersonPhone>();
        var personIds = personPhones.Select(a => a.PublicPersonId).Distinct().ToList();
        var personIdMap = await _personStore.IdConvertForPersonAsync(personIds);
        var personPhoneIds = personPhones.Select(a => a.Id).Distinct().ToList();
        var existingPersonPhones = (await _personStore.GetPersonPhonesForUpdateAsync(personPhoneIds))?.ToDictionary(a => a.Id);
        if (existingPersonPhones == null || !existingPersonPhones.Any())
        {
            throw new Exception("No person phones found for update.");
        }
        foreach (var personPhone in personPhones)
        {
            if (!personIdMap.TryGetValue(personPhone.PublicPersonId, out var personId))
            {
                throw new Exception($"Person not found: {personPhone.PublicPersonId}");
            }
            existingPersonPhones.TryGetValue(personPhone.Id, out var updatePersonPhone);
            if (updatePersonPhone == null)
            {
                throw new Exception($"Person phone not found: {personPhone.Id}");
            }
            updatePersonPhone.PersonId = personId;
            updatePersonPhone.Disabled = personPhone.Disabled;
            updatePersonPhone.Deleted = personPhone.Deleted;
            var phone = await _extraFeatureManager.UpdatePhoneAsync(personPhone.Phone, false);
            updatePersonPhone.Phone = phone;
            personPhoneList.Add(updatePersonPhone);
        }
        return await _personStore.UpdatePersonPhonesAsync(personPhoneList, saveChanges);
    }
    public async Task<PersonExtraFeature?> AddPersonExtraFeatureAsync(PersonExtraFeatureAddDto personExtraFeature, bool saveChanges = false)
    {
        return (await AddPersonExtraFeaturesAsync([personExtraFeature], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to add person extra feature.");
    }
    public async Task<IEnumerable<PersonExtraFeature>?> AddPersonExtraFeaturesAsync(IEnumerable<PersonExtraFeatureAddDto> personExtraFeatures, bool saveChanges = false)
    {
        var personExtraFeatureList = new List<PersonExtraFeature>();
        var personIds = personExtraFeatures.Select(a => a.PublicPersonId).Distinct().ToList();
        var personIdMap = await _personStore.IdConvertForPersonAsync(personIds);
        foreach (var personExtraFeature in personExtraFeatures)
        {
            if (!personIdMap.TryGetValue(personExtraFeature.PublicPersonId, out var personId))
            {
                throw new Exception($"Person not found: {personExtraFeature.PublicPersonId}");
            }
            var addPersonExtraFeature = new PersonExtraFeature
            {
                Id = Guid.NewGuid().ToString(),
                PersonId = personId,
            };
            personExtraFeatureList.Add(addPersonExtraFeature);
            var extraFeature = await _extraFeatureManager.AddExtraFeatureAsync(personExtraFeature.ExtraFeature, false);
            addPersonExtraFeature.ExtraFeature = extraFeature;
        }
        return await _personStore.AddPersonExtraFeaturesAsync(personExtraFeatureList, saveChanges);
    }
    public async Task<PersonExtraFeature?> UpdatePersonExtraFeatureAsync(PersonExtraFeatureUpdateDto personExtraFeature, bool saveChanges = false)
    {
        return (await UpdatePersonExtraFeaturesAsync([personExtraFeature], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to update person extra feature.");
    }
    public async Task<IEnumerable<PersonExtraFeature>?> UpdatePersonExtraFeaturesAsync(IEnumerable<PersonExtraFeatureUpdateDto> personExtraFeatures, bool saveChanges = false)
    {
        var personExtraFeatureList = new List<PersonExtraFeature>();
        var personIds = personExtraFeatures.Select(a => a.PublicPersonId).Distinct().ToList();
        var personIdMap = await _personStore.IdConvertForPersonAsync(personIds);
        var personExtraFeatureIds = personExtraFeatures.Select(a => a.Id).Distinct().ToList();
        var existingPersonExtraFeatures = (await _personStore.GetPersonExtraFeaturesForUpdateAsync(personExtraFeatureIds))?.ToDictionary(a => a.Id);
        if (existingPersonExtraFeatures == null || !existingPersonExtraFeatures.Any())
        {
            throw new Exception("No person extra features found for update.");
        }
        foreach (var personExtraFeature in personExtraFeatures)
        {
            if (!personIdMap.TryGetValue(personExtraFeature.PublicPersonId, out var personId))
            {
                throw new Exception($"Person not found: {personExtraFeature.PublicPersonId}");
            }
            existingPersonExtraFeatures.TryGetValue(personExtraFeature.Id, out var updatePersonExtraFeature);
            if (updatePersonExtraFeature == null)
            {
                throw new Exception($"Person extra feature not found: {personExtraFeature.Id}");
            }
            updatePersonExtraFeature.PersonId = personId;
            updatePersonExtraFeature.Disabled = personExtraFeature.Disabled;
            updatePersonExtraFeature.Deleted = personExtraFeature.Deleted;
            updatePersonExtraFeature.ExtraFeature = await _extraFeatureManager.UpdateExtraFeatureAsync(personExtraFeature.ExtraFeature, false);
            personExtraFeatureList.Add(updatePersonExtraFeature);
        }
        return await _personStore.UpdatePersonExtraFeaturesAsync(personExtraFeatureList, saveChanges);
    }
    public async Task<IEnumerable<PersonDetail>> AddPersonDetailsAsync(IEnumerable<PersonDetailAddDto> personDetails, bool saveChanges = false)
    {
        var personDetailList = new List<PersonDetail>();
        var personIds = personDetails.Select(a => a.PublicPersonId).Distinct().ToList();
        var personIdMap = await _personStore.IdConvertForPersonAsync(personIds);
        var personDetailSavedBefore = await _personStore.HasPersonDetailsAsync(personIds);
        var genderIds = personDetails.Where(a => !string.IsNullOrEmpty(a.PublicGenderId)).Select(a => a.PublicGenderId!).Distinct().ToList();
        var genderIdMap = await _enumManager.IdConvertForEnumValue(genderIds);
        var maritalIds = personDetails.Where(a => !string.IsNullOrEmpty(a.PublicMaritalStatusId)).Select(a => a.PublicMaritalStatusId!).Distinct().ToList();
        var maritalIdMap = await _enumManager.IdConvertForEnumValue(maritalIds);
        var bloodGroupIds = personDetails.Where(a => !string.IsNullOrEmpty(a.PublicBloodGroupId)).Select(a => a.PublicBloodGroupId!).Distinct().ToList();
        var bloodGroupIdMap = await _enumManager.IdConvertForEnumValue(bloodGroupIds);
        var stateIds = personDetails.Where(a => !string.IsNullOrEmpty(a.PublicStateId)).Select(a => a.PublicStateId!).Distinct().ToList();
        var stateIdMap = await _locationManager.IdConvertForStateAsync(stateIds);
        var cityIds = personDetails.Where(a => !string.IsNullOrEmpty(a.PublicCityId)).Select(a => a.PublicCityId!).Distinct().ToList();
        var cityIdMap = await _locationManager.IdConvertForCityAsync(cityIds);
        foreach (var pd in personDetails)
        {
            if (!personIdMap.TryGetValue(pd.PublicPersonId, out var personId))
            {
                throw new Exception($"Person not found: {pd.PublicPersonId}");
            }
            if (personDetailSavedBefore.ContainsKey(pd.PublicPersonId))
            {
                throw new Exception($"Person detail already saved: {personId}");
            }
            var addPersonDetail = pd.Adapt<PersonDetail>();
            addPersonDetail.PersonId = personId;
            if (pd.PublicGenderId != null && genderIdMap.TryGetValue(pd.PublicGenderId, out int genderId))
            {
                addPersonDetail.GenderId = genderId;
            }
            if (pd.PublicMaritalStatusId != null && maritalIdMap.TryGetValue(pd.PublicMaritalStatusId, out int maritalId))
            {
                addPersonDetail.MaritalStatusId = maritalId;
            }
            if (pd.PublicBloodGroupId != null && bloodGroupIdMap.TryGetValue(pd.PublicBloodGroupId, out int bloodGroupId))
            {
                addPersonDetail.BloodGroupId = bloodGroupId;
            }
            if (pd.PublicStateId != null && stateIdMap.TryGetValue(pd.PublicStateId, out int stateId))
            {
                addPersonDetail.StateId = stateId;
            }
            if (pd.PublicCityId != null && cityIdMap.TryGetValue(pd.PublicCityId, out int cityId))
            {
                addPersonDetail.CityId = cityId;
            }
            personDetailList.Add(addPersonDetail);
        }
        return await _personStore.AddPersonDetailsAsync(personDetailList, saveChanges);
    }
    public async Task<PersonDetail> AddPersonDetailAsync(PersonDetailAddDto personDetail, bool saveChanges = false)
    {
        return (await AddPersonDetailsAsync([personDetail], saveChanges))?.FirstOrDefault()
              ?? throw new Exception("Failed to add person detail.");
    }
    public async Task<IEnumerable<PersonDetail>> UpdatePersonDetailsAsync(IEnumerable<PersonDetailUpdateDto> personDetails, bool saveChanges = false)
    {
        var personDetailList = new List<PersonDetail>();
        var personDetailIds = personDetails.Select(a => a.Id).Distinct().ToList();
        var savedPersonDetails = (await _personStore.GetPersonDetailsForUpdateAsync(personDetailIds)).ToDictionary(a => a.Id);
        var personIds = personDetails.Select(a => a.PublicPersonId).Distinct().ToList();
        var personIdMap = await _personStore.IdConvertForPersonAsync(personIds);
        var genderIds = personDetails.Where(a => !string.IsNullOrEmpty(a.PublicGenderId)).Select(a => a.PublicGenderId!).Distinct().ToList();
        var genderIdMap = await _enumManager.IdConvertForEnumValue(genderIds);
        var maritalIds = personDetails.Where(a => !string.IsNullOrEmpty(a.PublicMaritalStatusId)).Select(a => a.PublicMaritalStatusId!).Distinct().ToList();
        var maritalIdMap = await _enumManager.IdConvertForEnumValue(maritalIds);
        var bloodGroupIds = personDetails.Where(a => !string.IsNullOrEmpty(a.PublicBloodGroupId)).Select(a => a.PublicBloodGroupId!).Distinct().ToList();
        var bloodGroupIdMap = await _enumManager.IdConvertForEnumValue(bloodGroupIds);
        var stateIds = personDetails.Where(a => !string.IsNullOrEmpty(a.PublicStateId)).Select(a => a.PublicStateId!).Distinct().ToList();
        var stateIdMap = await _locationManager.IdConvertForStateAsync(stateIds);
        var cityIds = personDetails.Where(a => !string.IsNullOrEmpty(a.PublicCityId)).Select(a => a.PublicCityId!).Distinct().ToList();
        var cityIdMap = await _locationManager.IdConvertForCityAsync(cityIds);
        foreach (var pd in personDetails)
        {
            if (!savedPersonDetails.TryGetValue(pd.Id, out var personDetail))
            {
                throw new Exception($"Person detail not found: {pd.Id}");
            }
            if (!personIdMap.TryGetValue(pd.PublicPersonId, out var personId))
            {
                throw new Exception($"Person not found: {pd.PublicPersonId}");
            }
            var addPersonDetail = pd.Adapt<PersonDetail>();
            addPersonDetail.PersonId = personId;
            if (pd.PublicGenderId != null && genderIdMap.TryGetValue(pd.PublicGenderId, out int genderId))
            {
                addPersonDetail.GenderId = genderId;
            }
            if (pd.PublicMaritalStatusId != null && maritalIdMap.TryGetValue(pd.PublicMaritalStatusId, out int maritalId))
            {
                addPersonDetail.MaritalStatusId = maritalId;
            }
            if (pd.PublicBloodGroupId != null && bloodGroupIdMap.TryGetValue(pd.PublicBloodGroupId, out int bloodGroupId))
            {
                addPersonDetail.BloodGroupId = bloodGroupId;
            }
            if (pd.PublicStateId != null && stateIdMap.TryGetValue(pd.PublicStateId, out int stateId))
            {
                addPersonDetail.StateId = stateId;
            }
            if (pd.PublicCityId != null && cityIdMap.TryGetValue(pd.PublicCityId, out int cityId))
            {
                addPersonDetail.CityId = cityId;
            }
            personDetailList.Add(addPersonDetail);
        }
        return await _personStore.AddPersonDetailsAsync(personDetailList, saveChanges);
    }
    public async Task<PersonDetail> UpdatePersonDetailAsync(PersonDetailUpdateDto person, bool saveChanges = false)
    {
        return (await UpdatePersonDetailsAsync([person], saveChanges))?.FirstOrDefault()
              ?? throw new Exception("Failed to update person detail.");
    }
    public async Task<PersonDetailViewDto?> GetPersonDetailAsync(string personId)
    {
        var personDetail = await _personStore.GetPersonDetailAsync(personId);
        return personDetail;
    }
}