using Mapster;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Helpers;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
namespace OrganizationManagement.Managers;

public class LocationManager : ILocationManager
{
    private readonly ILocationStore _locationStore;
    private readonly IRlxLocalizationManager<OrganizationManagementLocalizationDbContext> _localizationManager;
    public LocationManager(ILocationStore locationStore,
        IRlxLocalizationManager<OrganizationManagementLocalizationDbContext> localizationManager)
    {
        _localizationManager = localizationManager;
        _locationStore = locationStore;
    }
    public async Task<IEnumerable<ActiveCityDto>> GetActiveCitiesByStateAsync(GetActiveCitiesCo co)
    {
        var cities = await _locationStore.GetActiveCitiesByStateAsync(co.StateId);
        await cities.AttachLocalizationsAsync(
         c => c.Id,
         ids => _localizationManager.GetLocalizationsAsync(new GetRlxLocalizationsCo
         {
             Culture = co.Culture,
             ReferenceIds = ids,
         }),
         l => l.ReferenceId,
         (c, l) => c.Localizations = l
         );
        return cities;
    }
    public async Task<IEnumerable<ActiveCountryDto>> GetActiveCountriesAsync(GetActiveCountriesCo co)
    {
        var countries = await _locationStore.GetActiveCountriesAsync();
        await countries.AttachLocalizationsAsync(
            c => c.Id,
            ids => _localizationManager.GetLocalizationsAsync(new GetRlxLocalizationsCo
            {
                Culture = co.Culture,
                ReferenceIds = ids,
            }),
            l => l.ReferenceId,
            (c, l) => c.Localizations = l
            );
        return countries;
    }
    public async Task<IEnumerable<ActiveStateDto>> GetActiveStatesByCountryAsync(GetActiveStatesCo co)
    {
        var states = await _locationStore.GetActiveStatesByCountryAsync(co.CountryId);
        await states.AttachLocalizationsAsync(
            s => s.Id,
            ids => _localizationManager.GetLocalizationsAsync(new GetRlxLocalizationsCo
            {
                Culture = co.Culture,
                ReferenceIds = ids,
            }),
            l => l.ReferenceId,
            (s, l) => s.Localizations = l
            );
        return states;
    }
    public async Task AddCountriesAsync(IEnumerable<CountryAddDto> countries)
    {
        var addList = new List<Country>();
        foreach (var country in countries)
        {
            if (await _locationStore.IsCountryExistsAsync(country.Name))
                throw new Exception($"Country already exists: {country.Name}");
            var addCountry = country.Adapt<Country>();
            addList.Add(addCountry);
        }
        await _locationStore.AddCountriesAsync(addList, true);
    }
    public async Task AddStatesAsync(IEnumerable<StateAddDto> states)
    {
        var countryIds = states.Select(c => c.CountryId).Distinct();
        var countryIdMap = await _locationStore.IdConvertForCountryAsync(countryIds);
        var addList = new List<State>();
        foreach (var state in states)
        {
            countryIdMap.TryGetValue(state.CountryId, out var countryId);
            if (countryId == 0)
                throw new Exception($"Country id not found: {state.CountryId}");
            if (await _locationStore.IsStateExistsAsync(state.Name, countryId))
                throw new Exception($"State already exists: {state.Name}");
            var addState = state.Adapt<State>();
            addState.CountryId = countryId;
            addList.Add(addState);
        }
        await _locationStore.AddStatesAsync(addList, true);
    }
    public async Task AddCitiesAsync(IEnumerable<CityAddDto> cities)
    {
        var stateIds = cities.Select(c => c.PublicStateId).Distinct();
        var stateIdMap = await _locationStore.IdConvertForStateAsync(stateIds);
        var addList = new List<City>();
        foreach (var city in cities)
        {
            stateIdMap.TryGetValue(city.PublicStateId, out var stateId);
            if (stateId == 0)
                throw new Exception($"State id not found: {city.PublicStateId}");
            if (await _locationStore.IsCityExistsAsync(city.Name, stateId))
                throw new Exception($"City already exists: {city.Name}");
            var addCity = city.Adapt<City>();
            addCity.StateId = stateId;
            addList.Add(addCity);
        }
        await _locationStore.AddCitiesAsync(addList, true);
    }
    public async Task UpdateCountriesAsync(IEnumerable<CountryUpdateDto> countries)
    {
        var updateList = new List<Country>();
        foreach (var country in countries)
        {
            if (await _locationStore.IsCountryExistsAsync(country.Name, country.Id))
                throw new Exception($"Country already exists: {country.Name}");
            var updateCountry = await _locationStore.GetCountryForUpdateAsync(country.Id);
            if (updateCountry == null)
                throw new Exception($"Country not found: {country.Id}");
            country.Adapt(updateCountry);
            updateList.Add(updateCountry);
        }
        await _locationStore.UpdateCountriesAsync(updateList, true);
    }
    public async Task UpdateStatesAsync(IEnumerable<StateUpdateDto> states)
    {
        var countryIds = states.Select(c => c.CountryId).Distinct();
        var countryIdMap = await _locationStore.IdConvertForCountryAsync(countryIds);
        var updateList = new List<State>();
        foreach (var state in states)
        {
            countryIdMap.TryGetValue(state.CountryId, out var countryId);
            if (countryId == 0)
                throw new Exception($"Country id not found: {state.CountryId}");
            if (await _locationStore.IsStateExistsAsync(state.Name, countryId, state.Id))
                throw new Exception($"State already exists: {state.Name}");
            var updateState = await _locationStore.GetStateForUpdateAsync(state.Id);
            if (updateState == null)
                throw new Exception($"State not found: {state.Id}");
            state.Adapt(updateState);
            updateList.Add(updateState);
        }
        await _locationStore.UpdateStatesAsync(updateList, true);
    }
    public async Task UpdateCitiesAsync(IEnumerable<CityUpdateDto> cities)
    {
        var stateIds = cities.Select(c => c.PublicStateId).Distinct();
        var stateIdMap = await _locationStore.IdConvertForStateAsync(stateIds);
        var updateList = new List<City>();
        foreach (var city in cities)
        {
            stateIdMap.TryGetValue(city.PublicStateId, out var stateId);
            if (stateId == 0)
                throw new Exception($"State id not found: {city.PublicStateId}");
            if (await _locationStore.IsCityExistsAsync(city.Name, stateId, city.Id))
                throw new Exception($"City already exists: {city.Name}");
            var updateCity = await _locationStore.GetCityForUpdateAsync(city.Id);
            if (updateCity == null)
                throw new Exception($"City not found: {city.Id}");
            city.Adapt(updateCity);
            updateList.Add(updateCity);
        }
        await _locationStore.UpdateCitiesAsync(updateList, true);
    }
    public async Task<IDictionary<string, int>> IdConvertForCountryAsync(IEnumerable<string> countryIds)
    {
        if (countryIds == null || !countryIds.Any())
            return new Dictionary<string, int>();
        return await _locationStore.IdConvertForCountryAsync(countryIds);
    }
    public async Task<IDictionary<string, int>> IdConvertForStateAsync(IEnumerable<string> stateIds)
    {
        if (stateIds == null || !stateIds.Any())
            return new Dictionary<string, int>();
        return await _locationStore.IdConvertForStateAsync(stateIds);
    }
    public async Task<IDictionary<string, int>> IdConvertForCityAsync(IEnumerable<string> cityIds)
    {
        if (cityIds == null || !cityIds.Any())
            return new Dictionary<string, int>();
        return await _locationStore.IdConvertForCityAsync(cityIds);
    }
}
