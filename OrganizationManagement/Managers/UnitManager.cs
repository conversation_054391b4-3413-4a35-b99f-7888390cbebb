using Mapster;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Managers;

public class UnitManager : IUnitManager
{
    private readonly IUnitStore _unitStore;
    private readonly IRlxLocalizationManager<OrganizationManagementLocalizationDbContext> _localizationManager;
    private readonly IRlxEnumManager<OrganizationManagementEnumDbContext> _enumManager;
    private readonly ILocationManager _locationManager;
    private readonly ILogger<UnitManager> _logger;
    private readonly IExtraFeatureManager _extraFeatureManager;
    public UnitManager(IUnitStore unitStore,
        IRlxLocalizationManager<OrganizationManagementLocalizationDbContext> localizationManager, IRlxEnumManager<OrganizationManagementEnumDbContext> enumManager, ILocationManager locationManager, ILogger<UnitManager> logger, IExtraFeatureManager extraFeatureManager)
    {
        _localizationManager = localizationManager;
        _unitStore = unitStore;
        _enumManager = enumManager;
        _locationManager = locationManager;
        _logger = logger;
        _extraFeatureManager = extraFeatureManager;
    }
    public async Task<IEnumerable<Unit>> AddUnitsAsync(IEnumerable<UnitAddDto> units, bool saveChanges = false)
    {
        var unitList = new List<Unit>();
        var parentIds = units.Where(a => !string.IsNullOrEmpty(a.PublicParentId)).Select(a => a.PublicParentId!).Distinct().ToList();
        var unitTypeIds = units.Where(a => !string.IsNullOrEmpty(a.PublicUnitTypeId)).Select(a => a.PublicUnitTypeId!).Distinct().ToList();
        var unitTypeIdMap = await _enumManager.IdConvertForEnumValue(unitTypeIds);
        var parentIdMap = await IdConvertForUnitAsync(parentIds);
        foreach (var unit in units)
        {
            var addUnit = unit.Adapt<Unit>();
            if (unitTypeIdMap.TryGetValue(unit.PublicUnitTypeId!, out var unitTypeId))
            {
                addUnit.UnitTypeId = unitTypeId;
            }
            if (parentIdMap.TryGetValue(unit.PublicParentId!, out var parentId))
            {
                addUnit.ParentId = parentId;
            }
            unitList.Add(addUnit);
        }
        await _unitStore.AddUnitsAsync(unitList);
        return unitList;
    }
    public async Task<Unit> AddUnitAsync(UnitAddDto unit, bool saveChanges = false)
    {
        return (await AddUnitsAsync([unit], saveChanges)).First();
    }
    public async Task<IEnumerable<Unit>> UpdateUnitsAsync(IEnumerable<UnitUpdateDto> units, bool saveChanges = false)
    {
        var unitList = new List<Unit>();
        var parentIds = units.Where(a => !string.IsNullOrEmpty(a.PublicParentId)).Select(a => a.PublicParentId!).Distinct().ToList();
        var unitTypeIds = units.Where(a => !string.IsNullOrEmpty(a.PublicUnitTypeId)).Select(a => a.PublicUnitTypeId!).Distinct().ToList();
        var unitTypeIdMap = await _enumManager.IdConvertForEnumValue(unitTypeIds);
        var parentIdMap = await IdConvertForUnitAsync(parentIds);
        var unitIds = units.Select(a => a.Id).Distinct().ToList();
        var existingUnits = (await _unitStore.GetUnitsForUpdateAsync(unitIds))?.ToDictionary(a => a.Id);
        if (existingUnits == null || !existingUnits.Any())
        {
            throw new Exception("No units found for update.");
        }
        foreach (var unit in units)
        {
            existingUnits.TryGetValue(unit.Id, out var updateUnit);
            if (updateUnit == null)
            {
                throw new Exception($"Unit not found: {unit.Id}");
            }
            unit.Adapt(updateUnit);
            if (unitTypeIdMap.TryGetValue(unit.PublicUnitTypeId!, out var unitTypeId))
            {
                updateUnit.UnitTypeId = unitTypeId;
            }
            if (parentIdMap.TryGetValue(unit.PublicParentId!, out var parentId))
            {
                updateUnit.ParentId = parentId;
            }
            unitList.Add(updateUnit);
        }
        await _unitStore.UpdateUnitsAsync(unitList, saveChanges);
        return unitList;
    }
    public async Task<Unit> UpdateUnitAsync(UnitUpdateDto unit, bool saveChanges = false)
    {
        return (await UpdateUnitsAsync([unit], saveChanges)).First();
    }
    public async Task<IDictionary<string, int>> IdConvertForUnitAsync(IEnumerable<string> unitIds)
    {
        if (unitIds == null || !unitIds.Any())
            return new Dictionary<string, int>();
        return await _unitStore.IdConvertForUnitAsync(unitIds);
    }
    public async Task<IEnumerable<UnitTreeDto>> GetUnitsForTreeAsync(string? startUnitId = null)
    {
        var units = await _unitStore.GetUnitsForTreeAsync();
        if (units == null || !units.Any())
        {
            return [];
        }
        var currentUnit = startUnitId != null ? units.FirstOrDefault(u => u.Id == startUnitId) : null;
        var unitIds = units.Select(u => u.Id).ToList();
        var groupedUnits = units.GroupBy(u => u.ParentId).ToDictionary(g => g.Key ?? 0, g => g.ToList());
        var treeList = new List<UnitTreeDto>();
        CreateTree(groupedUnits, treeList, currentUnit, true);
        return treeList;
    }
    private void CreateTree(IDictionary<int, List<UnitTreeDto>> groupedUnits, List<UnitTreeDto> treeList, UnitTreeDto? currentUnit = null, bool isRoot = false)
    {
        var currentUnitPrivateId = currentUnit?.AutoIncrementId;
        if (isRoot)
        {
            if (currentUnit != null)
            {
                treeList.Add(currentUnit);
            }
        }
        groupedUnits.TryGetValue(currentUnitPrivateId ?? 0, out var children);
        if (children == null || !children.Any())
            return;
        if (isRoot && currentUnit == null)
            treeList.AddRange(children);
        foreach (var child in children)
        {
            if (currentUnit != null)
            {
                currentUnit.Children.Add(child);
            }
            CreateTree(groupedUnits, treeList, child);
        }
    }
    public async Task<UnitViewDto?> GetUnitAsync(string unitId)
    {
        var unit = await _unitStore.GetUnitAsync(unitId);
        return unit;
    }
    public async Task<IEnumerable<UnitAddressViewDto>?> GetUnitAddressesAsync(string unitId)
    {
        var unitAddresses = await _unitStore.GetUnitAddressesAsync(unitId);
        return unitAddresses;
    }
    public async Task<IEnumerable<UnitPhoneViewDto>?> GetUnitPhonesAsync(string unitId)
    {
        var unitPhones = await _unitStore.GetUnitPhonesAsync(unitId);
        return unitPhones;
    }
    public async Task<IEnumerable<UnitExtraFeatureViewDto>?> GetUnitExtraFeaturesAsync(string unitId)
    {
        var unitExtraFeatures = await _unitStore.GetUnitExtraFeaturesAsync(unitId);
        return unitExtraFeatures;
    }
    public async Task<PagedListDto<UnitForAutoCompleteDto>?> GetUnitsForAutoCompleteAsync(PagedListCo<GetUnitsCo> co)
    {
        // Validate olabilir
        var units = await _unitStore.GetUnitsForAutoCompleteAsync(co);
        return units;
    }
    public async Task<UnitViewCompleteDto?> GetUnitCompleteAsync(string unitId)
    {
        var unit = await _unitStore.GetUnitCompleteAsync(unitId);
        return unit;
    }
    public async Task<UnitAddress?> AddUnitAddressAsync(UnitAddressAddDto unitAddress, bool saveChanges = false)
    {
        return (await AddUnitAddressesAsync([unitAddress], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to add unit address.");
    }
    public async Task<IEnumerable<UnitAddress>?> AddUnitAddressesAsync(IEnumerable<UnitAddressAddDto> unitAddresses, bool saveChanges = false)
    {
        var unitAddressList = new List<UnitAddress>();
        var unitIds = unitAddresses.Select(a => a.PublicUnitId).Distinct().ToList();
        var unitIdMap = await _unitStore.IdConvertForUnitAsync(unitIds);
        foreach (var unitAddress in unitAddresses)
        {
            if (!unitIdMap.TryGetValue(unitAddress.PublicUnitId, out var unitId))
            {
                throw new Exception($"Unit not found: {unitAddress.PublicUnitId}");
            }
            var addUnitAddress = new UnitAddress
            {
                Id = Guid.NewGuid().ToString(),
                UnitId = unitId,
                IsDefault = unitAddress.IsDefault
            };
            unitAddressList.Add(addUnitAddress);
            var address = await _extraFeatureManager.AddAddressAsync(unitAddress.Address, false);
            addUnitAddress.Address = address;
        }
        return await _unitStore.AddUnitAddressesAsync(unitAddressList, saveChanges);
    }
    public async Task<UnitAddress?> UpdateUnitAddressAsync(UnitAddressUpdateDto unitAddress, bool saveChanges = false)
    {
        return (await UpdateUnitAddressesAsync([unitAddress], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to update unit address.");
    }
    public async Task<IEnumerable<UnitAddress>?> UpdateUnitAddressesAsync(IEnumerable<UnitAddressUpdateDto> unitAddresses, bool saveChanges = false)
    {
        var unitAddressList = new List<UnitAddress>();
        var unitIds = unitAddresses.Select(a => a.PublicUnitId).Distinct().ToList();
        var unitIdMap = await _unitStore.IdConvertForUnitAsync(unitIds);
        var unitAddressIds = unitAddresses.Select(a => a.Id).Distinct().ToList();
        var existingUnitAddresses = (await _unitStore.GetUnitAddressesForUpdateAsync(unitAddressIds))?.ToDictionary(a => a.Id);
        if (existingUnitAddresses == null || !existingUnitAddresses.Any())
        {
            throw new Exception("No unit addresses found for update.");
        }
        foreach (var unitAddress in unitAddresses)
        {
            if (!unitIdMap.TryGetValue(unitAddress.PublicUnitId, out var unitId))
            {
                throw new Exception($"Unit not found: {unitAddress.PublicUnitId}");
            }
            existingUnitAddresses.TryGetValue(unitAddress.Id, out var updateUnitAddress);
            if (updateUnitAddress == null)
            {
                throw new Exception($"Unit address not found: {unitAddress.Id}");
            }
            updateUnitAddress.UnitId = unitId;
            updateUnitAddress.IsDefault = unitAddress.IsDefault;
            updateUnitAddress.Disabled = unitAddress.Disabled;
            updateUnitAddress.Deleted = unitAddress.Deleted;
            var address = await _extraFeatureManager.UpdateAddressAsync(unitAddress.Address, false);
            updateUnitAddress.Address = address;
            unitAddressList.Add(updateUnitAddress);
        }
        return await _unitStore.UpdateUnitAddressesAsync(unitAddressList, saveChanges);
    }
    public async Task<UnitPhone?> AddUnitPhoneAsync(UnitPhoneAddDto unitPhone, bool saveChanges = false)
    {
        return (await AddUnitPhonesAsync([unitPhone], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to add unit phone.");
    }
    public async Task<IEnumerable<UnitPhone>?> AddUnitPhonesAsync(IEnumerable<UnitPhoneAddDto> unitPhones, bool saveChanges = false)
    {
        var unitPhoneList = new List<UnitPhone>();
        var unitIds = unitPhones.Select(a => a.PublicUnitId).Distinct().ToList();
        var unitIdMap = await _unitStore.IdConvertForUnitAsync(unitIds);
        foreach (var unitPhone in unitPhones)
        {
            if (!unitIdMap.TryGetValue(unitPhone.PublicUnitId, out var unitId))
            {
                throw new Exception($"Unit not found: {unitPhone.PublicUnitId}");
            }
            var addUnitPhone = new UnitPhone
            {
                Id = Guid.NewGuid().ToString(),
                UnitId = unitId,
            };
            unitPhoneList.Add(addUnitPhone);
            var phone = await _extraFeatureManager.AddPhoneAsync(unitPhone.Phone, false);
            addUnitPhone.Phone = phone;
        }
        return await _unitStore.AddUnitPhonesAsync(unitPhoneList, saveChanges);
    }
    public async Task<UnitPhone?> UpdateUnitPhoneAsync(UnitPhoneUpdateDto unitPhone, bool saveChanges = false)
    {
        return (await UpdateUnitPhonesAsync([unitPhone], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to update unit phone.");
    }
    public async Task<IEnumerable<UnitPhone>?> UpdateUnitPhonesAsync(IEnumerable<UnitPhoneUpdateDto> unitPhones, bool saveChanges = false)
    {
        var unitPhoneList = new List<UnitPhone>();
        var unitIds = unitPhones.Select(a => a.PublicUnitId).Distinct().ToList();
        var unitIdMap = await _unitStore.IdConvertForUnitAsync(unitIds);
        var unitPhoneIds = unitPhones.Select(a => a.Id).Distinct().ToList();
        var existingUnitPhones = (await _unitStore.GetUnitPhonesForUpdateAsync(unitPhoneIds))?.ToDictionary(a => a.Id);
        if (existingUnitPhones == null || !existingUnitPhones.Any())
        {
            throw new Exception("No unit phones found for update.");
        }
        foreach (var unitPhone in unitPhones)
        {
            if (!unitIdMap.TryGetValue(unitPhone.PublicUnitId, out var unitId))
            {
                throw new Exception($"Unit not found: {unitPhone.PublicUnitId}");
            }
            existingUnitPhones.TryGetValue(unitPhone.Id, out var updateUnitPhone);
            if (updateUnitPhone == null)
            {
                throw new Exception($"Unit phone not found: {unitPhone.Id}");
            }
            updateUnitPhone.UnitId = unitId;
            updateUnitPhone.Disabled = unitPhone.Disabled;
            updateUnitPhone.Deleted = unitPhone.Deleted;
            var phone = await _extraFeatureManager.UpdatePhoneAsync(unitPhone.Phone, false);
            updateUnitPhone.Phone = phone;
            unitPhoneList.Add(updateUnitPhone);
        }
        return await _unitStore.UpdateUnitPhonesAsync(unitPhoneList, saveChanges);
    }
    public async Task<UnitExtraFeature?> AddUnitExtraFeatureAsync(UnitExtraFeatureAddDto unitExtraFeature, bool saveChanges = false)
    {
        return (await AddUnitExtraFeaturesAsync([unitExtraFeature], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to add unit extra feature.");
    }
    public async Task<IEnumerable<UnitExtraFeature>?> AddUnitExtraFeaturesAsync(IEnumerable<UnitExtraFeatureAddDto> unitExtraFeatures, bool saveChanges = false)
    {
        var unitExtraFeatureList = new List<UnitExtraFeature>();
        var unitIds = unitExtraFeatures.Select(a => a.PublicUnitId).Distinct().ToList();
        var unitIdMap = await _unitStore.IdConvertForUnitAsync(unitIds);
        foreach (var unitExtraFeature in unitExtraFeatures)
        {
            if (!unitIdMap.TryGetValue(unitExtraFeature.PublicUnitId, out var unitId))
            {
                throw new Exception($"Unit not found: {unitExtraFeature.PublicUnitId}");
            }
            var addUnitExtraFeature = new UnitExtraFeature
            {
                Id = Guid.NewGuid().ToString(),
                UnitId = unitId,
            };
            unitExtraFeatureList.Add(addUnitExtraFeature);
            var extraFeature = await _extraFeatureManager.AddExtraFeatureAsync(unitExtraFeature.ExtraFeature, false);
            addUnitExtraFeature.ExtraFeature = extraFeature;
        }
        return await _unitStore.AddUnitExtraFeaturesAsync(unitExtraFeatureList, saveChanges);
    }
    public async Task<UnitExtraFeature?> UpdateUnitExtraFeatureAsync(UnitExtraFeatureUpdateDto unitExtraFeature, bool saveChanges = false)
    {
        return (await UpdateUnitExtraFeaturesAsync([unitExtraFeature], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to update unit extra feature.");
    }
    public async Task<IEnumerable<UnitExtraFeature>?> UpdateUnitExtraFeaturesAsync(IEnumerable<UnitExtraFeatureUpdateDto> unitExtraFeatures, bool saveChanges = false)
    {
        var unitExtraFeatureList = new List<UnitExtraFeature>();
        var unitIds = unitExtraFeatures.Select(a => a.PublicUnitId).Distinct().ToList();
        var unitIdMap = await _unitStore.IdConvertForUnitAsync(unitIds);
        var unitExtraFeatureIds = unitExtraFeatures.Select(a => a.Id).Distinct().ToList();
        var existingUnitExtraFeatures = (await _unitStore.GetUnitExtraFeaturesForUpdateAsync(unitExtraFeatureIds))?.ToDictionary(a => a.Id);
        if (existingUnitExtraFeatures == null || !existingUnitExtraFeatures.Any())
        {
            throw new Exception("No unit extra features found for update.");
        }
        foreach (var unitExtraFeature in unitExtraFeatures)
        {
            if (!unitIdMap.TryGetValue(unitExtraFeature.PublicUnitId, out var unitId))
            {
                throw new Exception($"Unit not found: {unitExtraFeature.PublicUnitId}");
            }
            existingUnitExtraFeatures.TryGetValue(unitExtraFeature.Id, out var updateUnitExtraFeature);
            if (updateUnitExtraFeature == null)
            {
                throw new Exception($"Unit extra feature not found: {unitExtraFeature.Id}");
            }
            updateUnitExtraFeature.UnitId = unitId;
            updateUnitExtraFeature.Disabled = unitExtraFeature.Disabled;
            updateUnitExtraFeature.Deleted = unitExtraFeature.Deleted;
            updateUnitExtraFeature.ExtraFeature = await _extraFeatureManager.UpdateExtraFeatureAsync(unitExtraFeature.ExtraFeature, false);
            unitExtraFeatureList.Add(updateUnitExtraFeature);
        }
        return await _unitStore.UpdateUnitExtraFeaturesAsync(unitExtraFeatureList, saveChanges);
    }
}