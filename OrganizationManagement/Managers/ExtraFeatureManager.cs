using Mapster;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Interfaces;
namespace OrganizationManagement.Managers;

public class ExtraFeatureManager : IExtraFeatureManager
{
    private readonly IExtraFeatureStore _extraFeatureStore;
    private readonly IRlxLocalizationManager<OrganizationManagementLocalizationDbContext> _localizationManager;
    private readonly ILocationManager _locationManager;
    public ExtraFeatureManager(IExtraFeatureStore extraFeatureStore,
        IRlxLocalizationManager<OrganizationManagementLocalizationDbContext> localizationManager, ILocationManager locationManager)
    {
        _localizationManager = localizationManager;
        _extraFeatureStore = extraFeatureStore;
        _locationManager = locationManager;
    }
    public async Task<Address> AddAddressAsync(AddressAddDto address, bool saveChanges = false)
    {
        return (await AddAddressesAsync([address], saveChanges)).First();
    }
    public async Task<IEnumerable<Address>> AddAddressesAsync(IEnumerable<AddressAddDto> addresses, bool saveChanges = false)
    {
        var addressList = new List<Address>();
        var countryIds = addresses.Where(a => !string.IsNullOrEmpty(a.PublicCountryId)).Select(a => a.PublicCountryId!).Distinct().ToList();
        var stateIds = addresses.Where(a => !string.IsNullOrEmpty(a.PublicStateId)).Select(a => a.PublicStateId!).Distinct().ToList();
        var cityIds = addresses.Where(a => !string.IsNullOrEmpty(a.PublicCityId)).Select(a => a.PublicCityId!).Distinct().ToList();
        var countryIdMap = await _locationManager.IdConvertForCountryAsync(countryIds);
        var stateIdMap = await _locationManager.IdConvertForStateAsync(stateIds);
        var cityIdMap = await _locationManager.IdConvertForCityAsync(cityIds);
        foreach (var address in addresses)
        {
            var addAddress = address.Adapt<Address>();
            if (countryIdMap.TryGetValue(address.PublicCountryId!, out var countryId))
            {
                addAddress.CountryId = countryId;
            }
            if (stateIdMap.TryGetValue(address.PublicStateId!, out var stateId))
            {
                addAddress.StateId = stateId;
            }
            if (cityIdMap.TryGetValue(address.PublicCityId!, out var cityId))
            {
                addAddress.CityId = cityId;
            }
            addressList.Add(addAddress);
        }
        await _extraFeatureStore.AddAddressesAsync(addressList);
        return addressList;
    }
    public async Task<ExtraFeature> AddExtraFeatureAsync(ExtraFeatureAddDto extraFeature, bool saveChanges = false)
    {
        return (await AddExtraFeaturesAsync([extraFeature], saveChanges)).First();
    }
    public async Task<IEnumerable<ExtraFeature>> AddExtraFeaturesAsync(IEnumerable<ExtraFeatureAddDto> extraFeatures, bool saveChanges = false)
    {
        var extraFeatureList = new List<ExtraFeature>();
        foreach (var feature in extraFeatures)
        {
            var addFeature = feature.Adapt<ExtraFeature>();
            extraFeatureList.Add(addFeature);
        }
        await _extraFeatureStore.AddExtraFeaturesAsync(extraFeatureList);
        return extraFeatureList;
    }
    public async Task<IEnumerable<Phone>> AddPhonesAsync(IEnumerable<PhoneAddDto> phones, bool saveChanges = false)
    {
        var phoneList = new List<Phone>();
        foreach (var phone in phones)
        {
            var addPhone = phone.Adapt<Phone>();
            phoneList.Add(addPhone);
        }
        await _extraFeatureStore.AddPhonesAsync(phoneList);
        return phoneList;
    }
    public async Task<Phone> AddPhoneAsync(PhoneAddDto phone, bool saveChanges = false)
    {
        return (await AddPhonesAsync([phone], saveChanges)).First();
    }
    public async Task<IEnumerable<Address>> UpdateAddressesAsync(IEnumerable<AddressUpdateDto> addresses, bool saveChanges = false)
    {
        var addressList = new List<Address>();
        var countryIds = addresses.Where(a => !string.IsNullOrEmpty(a.PublicCountryId)).Select(a => a.PublicCountryId!).Distinct().ToList();
        var stateIds = addresses.Where(a => !string.IsNullOrEmpty(a.PublicStateId)).Select(a => a.PublicStateId!).Distinct().ToList();
        var cityIds = addresses.Where(a => !string.IsNullOrEmpty(a.PublicCityId)).Select(a => a.PublicCityId!).Distinct().ToList();
        var countryIdMap = await _locationManager.IdConvertForCountryAsync(countryIds);
        var stateIdMap = await _locationManager.IdConvertForStateAsync(stateIds);
        var cityIdMap = await _locationManager.IdConvertForCityAsync(cityIds);
        var addressIds = addresses.Select(a => a.Id).Distinct().ToList();
        var existingAddresses = (await _extraFeatureStore.GetAddressesForUpdateAsync(addressIds))?.ToDictionary(a => a.Id);
        if (existingAddresses == null || !existingAddresses.Any())
        {
            throw new Exception("No addresses found for update.");
        }
        foreach (var address in addresses)
        {
            existingAddresses.TryGetValue(address.Id, out var updateAddress);
            if (updateAddress == null)
            {
                throw new Exception($"Address not found: {address.Id}");
            }
            address.Adapt(updateAddress);
            if (countryIdMap.TryGetValue(address.PublicCountryId!, out var countryId))
            {
                updateAddress.CountryId = countryId;
            }
            if (stateIdMap.TryGetValue(address.PublicStateId!, out var stateId))
            {
                updateAddress.StateId = stateId;
            }
            if (cityIdMap.TryGetValue(address.PublicCityId!, out var cityId))
            {
                updateAddress.CityId = cityId;
            }
            addressList.Add(updateAddress);
        }
        await _extraFeatureStore.UpdateAddressesAsync(addressList, saveChanges);
        return addressList;
    }
    public async Task<Address> UpdateAddressAsync(AddressUpdateDto address, bool saveChanges = false)
    {
        return (await UpdateAddressesAsync([address], saveChanges)).First();
    }
    public async Task<IEnumerable<Phone>> UpdatePhonesAsync(IEnumerable<PhoneUpdateDto> phones, bool saveChanges = false)
    {
        var phoneList = new List<Phone>();
        var phoneIds = phones.Select(p => p.Id).Distinct().ToList();
        var existingPhones = (await _extraFeatureStore.GetPhonesForUpdateAsync(phoneIds))?.ToDictionary(p => p.Id);
        if (existingPhones == null || !existingPhones.Any())
        {
            throw new Exception("No phones found for update.");
        }
        foreach (var phone in phones)
        {
            existingPhones.TryGetValue(phone.Id, out var updatePhone);
            if (updatePhone == null)
            {
                throw new Exception($"Phone not found: {phone.Id}");
            }
            phone.Adapt(updatePhone);
            phoneList.Add(updatePhone);
        }
        await _extraFeatureStore.UpdatePhonesAsync(phoneList, saveChanges);
        return phoneList;
    }
    public async Task<Phone> UpdatePhoneAsync(PhoneUpdateDto phone, bool saveChanges = false)
    {
        return (await UpdatePhonesAsync([phone], saveChanges)).First();
    }
    public async Task<IEnumerable<ExtraFeature>> UpdateExtraFeaturesAsync(IEnumerable<ExtraFeatureUpdateDto> extraFeatures, bool saveChanges = false)
    {
        var extraFeatureList = new List<ExtraFeature>();
        var extraFeatureIds = extraFeatures.Select(e => e.Id).Distinct().ToList();
        var existingExtraFeatures = (await _extraFeatureStore.GetExtraFeaturesForUpdateAsync(extraFeatureIds))?.ToDictionary(e => e.Id);
        if (existingExtraFeatures == null || !existingExtraFeatures.Any())
        {
            throw new Exception("No extra features found for update.");
        }
        foreach (var feature in extraFeatures)
        {
            existingExtraFeatures.TryGetValue(feature.Id, out var updateFeature);
            if (updateFeature == null)
            {
                throw new Exception($"Extra feature not found: {feature.Id}");
            }
            feature.Adapt(updateFeature);
            extraFeatureList.Add(updateFeature);
        }
        await _extraFeatureStore.UpdateExtraFeaturesAsync(extraFeatureList, saveChanges);
        return extraFeatureList;
    }
    public async Task<ExtraFeature> UpdateExtraFeatureAsync(ExtraFeatureUpdateDto extraFeature, bool saveChanges = false)
    {
        return (await UpdateExtraFeaturesAsync([extraFeature], saveChanges)).First();
    }
    // public Task<IEnumerable<AddressDto>> GetAddressesAsync(IEnumerable<string> addressIds)
    // {
    //     throw new NotImplementedException();
    // }
    // public Task<IEnumerable<PhoneDto>> GetPhonesAsync(IEnumerable<string> phoneIds)
    // {
    //     throw new NotImplementedException();
    // }
    // public Task<IEnumerable<ExtraFeatureDto>> GetExtraFeaturesAsync(IEnumerable<string> extraFeatureIds)
    // {
    //     throw new NotImplementedException();
    // }
}
