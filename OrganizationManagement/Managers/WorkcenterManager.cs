using Mapster;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Models.Cos;
using OrganizationManagement.Models.Dtos;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Managers;

public class WorkcenterManager : IWorkcenterManager
{
    private readonly IWorkcenterStore _workcenterStore;
    private readonly IRlxLocalizationManager<OrganizationManagementLocalizationDbContext> _localizationManager;
    private readonly IRlxEnumManager<OrganizationManagementEnumDbContext> _enumManager;
    private readonly ILocationManager _locationManager;
    private readonly ILogger<WorkcenterManager> _logger;
    private readonly IExtraFeatureManager _extraFeatureManager;
    public WorkcenterManager(IWorkcenterStore workcenterStore,
        IRlxLocalizationManager<OrganizationManagementLocalizationDbContext> localizationManager, IRlxEnumManager<OrganizationManagementEnumDbContext> enumManager, ILocationManager locationManager, ILogger<WorkcenterManager> logger, IExtraFeatureManager extraFeatureManager)
    {
        _localizationManager = localizationManager;
        _workcenterStore = workcenterStore;
        _enumManager = enumManager;
        _locationManager = locationManager;
        _logger = logger;
        _extraFeatureManager = extraFeatureManager;
    }
    public async Task<IEnumerable<Workcenter>> AddWorkcentersAsync(IEnumerable<WorkcenterAddDto> workcenters, bool saveChanges = false)
    {
        var workcenterList = new List<Workcenter>();
        var parentIds = workcenters.Where(a => !string.IsNullOrEmpty(a.PublicParentId)).Select(a => a.PublicParentId!).Distinct().ToList();
        var workcenterTypeIds = workcenters.Where(a => !string.IsNullOrEmpty(a.PublicWorkcenterTypeId)).Select(a => a.PublicWorkcenterTypeId!).Distinct().ToList();
        var workcenterTypeIdMap = await _enumManager.IdConvertForEnumValue(workcenterTypeIds);
        var parentIdMap = await IdConvertForWorkcenterAsync(parentIds);
        foreach (var workcenter in workcenters)
        {
            var addWorkcenter = workcenter.Adapt<Workcenter>();
            if (workcenterTypeIdMap.TryGetValue(workcenter.PublicWorkcenterTypeId!, out var workcenterTypeId))
            {
                addWorkcenter.WorkcenterTypeId = workcenterTypeId;
            }
            if (parentIdMap.TryGetValue(workcenter.PublicParentId!, out var parentId))
            {
                addWorkcenter.ParentId = parentId;
            }
            workcenterList.Add(addWorkcenter);
        }
        await _workcenterStore.AddWorkcentersAsync(workcenterList);
        return workcenterList;
    }
    public async Task<Workcenter> AddWorkcenterAsync(WorkcenterAddDto workcenter, bool saveChanges = false)
    {
        return (await AddWorkcentersAsync([workcenter], saveChanges)).First();
    }
    public async Task<IEnumerable<Workcenter>> UpdateWorkcentersAsync(IEnumerable<WorkcenterUpdateDto> workcenters, bool saveChanges = false)
    {
        var workcenterList = new List<Workcenter>();
        var parentIds = workcenters.Where(a => !string.IsNullOrEmpty(a.PublicParentId)).Select(a => a.PublicParentId!).Distinct().ToList();
        var workcenterTypeIds = workcenters.Where(a => !string.IsNullOrEmpty(a.PublicWorkcenterTypeId)).Select(a => a.PublicWorkcenterTypeId!).Distinct().ToList();
        var workcenterTypeIdMap = await _enumManager.IdConvertForEnumValue(workcenterTypeIds);
        var parentIdMap = await IdConvertForWorkcenterAsync(parentIds);
        var workcenterIds = workcenters.Select(a => a.Id).Distinct().ToList();
        var existingWorkcenters = (await _workcenterStore.GetWorkcentersForUpdateAsync(workcenterIds))?.ToDictionary(a => a.Id);
        if (existingWorkcenters == null || !existingWorkcenters.Any())
        {
            throw new Exception("No workcenters found for update.");
        }
        foreach (var workcenter in workcenters)
        {
            existingWorkcenters.TryGetValue(workcenter.Id, out var updateWorkcenter);
            if (updateWorkcenter == null)
            {
                throw new Exception($"Workcenter not found: {workcenter.Id}");
            }
            workcenter.Adapt(updateWorkcenter);
            if (workcenterTypeIdMap.TryGetValue(workcenter.PublicWorkcenterTypeId!, out var workcenterTypeId))
            {
                updateWorkcenter.WorkcenterTypeId = workcenterTypeId;
            }
            if (parentIdMap.TryGetValue(workcenter.PublicParentId!, out var parentId))
            {
                updateWorkcenter.ParentId = parentId;
            }
            workcenterList.Add(updateWorkcenter);
        }
        await _workcenterStore.UpdateWorkcentersAsync(workcenterList, saveChanges);
        return workcenterList;
    }
    public async Task<Workcenter> UpdateWorkcenterAsync(WorkcenterUpdateDto workcenter, bool saveChanges = false)
    {
        return (await UpdateWorkcentersAsync([workcenter], saveChanges)).First();
    }
    public async Task<IDictionary<string, int>> IdConvertForWorkcenterAsync(IEnumerable<string> workcenterIds)
    {
        if (workcenterIds == null || !workcenterIds.Any())
            return new Dictionary<string, int>();
        return await _workcenterStore.IdConvertForWorkcenterAsync(workcenterIds);
    }
    public async Task<IEnumerable<WorkcenterTreeDto>> GetWorkcentersForTreeAsync(string? startWorkcenterId = null)
    {
        var workcenters = await _workcenterStore.GetWorkcentersForTreeAsync();
        if (workcenters == null || !workcenters.Any())
        {
            return [];
        }
        var currentWorkcenter = startWorkcenterId != null ? workcenters.FirstOrDefault(u => u.Id == startWorkcenterId) : null;
        var workcenterIds = workcenters.Select(u => u.Id).ToList();
        var groupedWorkcenters = workcenters.GroupBy(u => u.ParentId).ToDictionary(g => g.Key ?? 0, g => g.ToList());
        var treeList = new List<WorkcenterTreeDto>();
        CreateTree(groupedWorkcenters, treeList, currentWorkcenter, true);
        return treeList;
    }
    private void CreateTree(IDictionary<int, List<WorkcenterTreeDto>> groupedWorkcenters, List<WorkcenterTreeDto> treeList, WorkcenterTreeDto? currentWorkcenter = null, bool isRoot = false)
    {
        var currentWorkcenterPrivateId = currentWorkcenter?.AutoIncrementId;
        if (isRoot)
        {
            if (currentWorkcenter != null)
            {
                treeList.Add(currentWorkcenter);
            }
        }
        groupedWorkcenters.TryGetValue(currentWorkcenterPrivateId ?? 0, out var children);
        if (children == null || !children.Any())
            return;
        if (isRoot && currentWorkcenter == null)
            treeList.AddRange(children);
        foreach (var child in children)
        {
            if (currentWorkcenter != null)
            {
                currentWorkcenter.Children.Add(child);
            }
            CreateTree(groupedWorkcenters, treeList, child);
        }
    }
    public async Task<WorkcenterViewDto?> GetWorkcenterAsync(string workcenterId)
    {
        var workcenter = await _workcenterStore.GetWorkcenterAsync(workcenterId);
        return workcenter;
    }
    public async Task<IEnumerable<WorkcenterAddressViewDto>?> GetWorkcenterAddressesAsync(string workcenterId)
    {
        var workcenterAddresses = await _workcenterStore.GetWorkcenterAddressesAsync(workcenterId);
        return workcenterAddresses;
    }
    public async Task<IEnumerable<WorkcenterPhoneViewDto>?> GetWorkcenterPhonesAsync(string workcenterId)
    {
        var workcenterPhones = await _workcenterStore.GetWorkcenterPhonesAsync(workcenterId);
        return workcenterPhones;
    }
    public async Task<IEnumerable<WorkcenterExtraFeatureViewDto>?> GetWorkcenterExtraFeaturesAsync(string workcenterId)
    {
        var workcenterExtraFeatures = await _workcenterStore.GetWorkcenterExtraFeaturesAsync(workcenterId);
        return workcenterExtraFeatures;
    }
    public async Task<PagedListDto<WorkcenterForAutoCompleteDto>?> GetWorkcentersForAutoCompleteAsync(PagedListCo<GetWorkcentersCo> co)
    {
        // Validate olabilir
        var workcenters = await _workcenterStore.GetWorkcentersForAutoCompleteAsync(co);
        return workcenters;
    }
    public async Task<WorkcenterViewCompleteDto?> GetWorkcenterCompleteAsync(string workcenterId)
    {
        var workcenter = await _workcenterStore.GetWorkcenterCompleteAsync(workcenterId);
        return workcenter;
    }
    public async Task<WorkcenterAddress?> AddWorkcenterAddressAsync(WorkcenterAddressAddDto workcenterAddress, bool saveChanges = false)
    {
        return (await AddWorkcenterAddressesAsync([workcenterAddress], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to add workcenter address.");
    }
    public async Task<IEnumerable<WorkcenterAddress>?> AddWorkcenterAddressesAsync(IEnumerable<WorkcenterAddressAddDto> workcenterAddresses, bool saveChanges = false)
    {
        var workcenterAddressList = new List<WorkcenterAddress>();
        var workcenterIds = workcenterAddresses.Select(a => a.PublicWorkcenterId).Distinct().ToList();
        var workcenterIdMap = await _workcenterStore.IdConvertForWorkcenterAsync(workcenterIds);
        foreach (var workcenterAddress in workcenterAddresses)
        {
            if (!workcenterIdMap.TryGetValue(workcenterAddress.PublicWorkcenterId, out var workcenterId))
            {
                throw new Exception($"Workcenter not found: {workcenterAddress.PublicWorkcenterId}");
            }
            var addWorkcenterAddress = new WorkcenterAddress
            {
                Id = Guid.NewGuid().ToString(),
                WorkcenterId = workcenterId,
                IsDefault = workcenterAddress.IsDefault
            };
            workcenterAddressList.Add(addWorkcenterAddress);
            var address = await _extraFeatureManager.AddAddressAsync(workcenterAddress.Address, false);
            addWorkcenterAddress.Address = address;
        }
        return await _workcenterStore.AddWorkcenterAddressesAsync(workcenterAddressList, saveChanges);
    }
    public async Task<WorkcenterAddress?> UpdateWorkcenterAddressAsync(WorkcenterAddressUpdateDto workcenterAddress, bool saveChanges = false)
    {
        return (await UpdateWorkcenterAddressesAsync([workcenterAddress], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to update workcenter address.");
    }
    public async Task<IEnumerable<WorkcenterAddress>?> UpdateWorkcenterAddressesAsync(IEnumerable<WorkcenterAddressUpdateDto> workcenterAddresses, bool saveChanges = false)
    {
        var workcenterAddressList = new List<WorkcenterAddress>();
        var workcenterIds = workcenterAddresses.Select(a => a.PublicWorkcenterId).Distinct().ToList();
        var workcenterIdMap = await _workcenterStore.IdConvertForWorkcenterAsync(workcenterIds);
        var workcenterAddressIds = workcenterAddresses.Select(a => a.Id).Distinct().ToList();
        var existingWorkcenterAddresses = (await _workcenterStore.GetWorkcenterAddressesForUpdateAsync(workcenterAddressIds))?.ToDictionary(a => a.Id);
        if (existingWorkcenterAddresses == null || !existingWorkcenterAddresses.Any())
        {
            throw new Exception("No workcenter addresses found for update.");
        }
        foreach (var workcenterAddress in workcenterAddresses)
        {
            if (!workcenterIdMap.TryGetValue(workcenterAddress.PublicWorkcenterId, out var workcenterId))
            {
                throw new Exception($"Workcenter not found: {workcenterAddress.PublicWorkcenterId}");
            }
            existingWorkcenterAddresses.TryGetValue(workcenterAddress.Id, out var updateWorkcenterAddress);
            if (updateWorkcenterAddress == null)
            {
                throw new Exception($"Workcenter address not found: {workcenterAddress.Id}");
            }
            updateWorkcenterAddress.WorkcenterId = workcenterId;
            updateWorkcenterAddress.IsDefault = workcenterAddress.IsDefault;
            updateWorkcenterAddress.Disabled = workcenterAddress.Disabled;
            updateWorkcenterAddress.Deleted = workcenterAddress.Deleted;
            var address = await _extraFeatureManager.UpdateAddressAsync(workcenterAddress.Address, false);
            updateWorkcenterAddress.Address = address;
            workcenterAddressList.Add(updateWorkcenterAddress);
        }
        return await _workcenterStore.UpdateWorkcenterAddressesAsync(workcenterAddressList, saveChanges);
    }
    public async Task<WorkcenterPhone?> AddWorkcenterPhoneAsync(WorkcenterPhoneAddDto workcenterPhone, bool saveChanges = false)
    {
        return (await AddWorkcenterPhonesAsync([workcenterPhone], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to add workcenter phone.");
    }
    public async Task<IEnumerable<WorkcenterPhone>?> AddWorkcenterPhonesAsync(IEnumerable<WorkcenterPhoneAddDto> workcenterPhones, bool saveChanges = false)
    {
        var workcenterPhoneList = new List<WorkcenterPhone>();
        var workcenterIds = workcenterPhones.Select(a => a.PublicWorkcenterId).Distinct().ToList();
        var workcenterIdMap = await _workcenterStore.IdConvertForWorkcenterAsync(workcenterIds);
        foreach (var workcenterPhone in workcenterPhones)
        {
            if (!workcenterIdMap.TryGetValue(workcenterPhone.PublicWorkcenterId, out var workcenterId))
            {
                throw new Exception($"Workcenter not found: {workcenterPhone.PublicWorkcenterId}");
            }
            var addWorkcenterPhone = new WorkcenterPhone
            {
                Id = Guid.NewGuid().ToString(),
                WorkcenterId = workcenterId,
            };
            workcenterPhoneList.Add(addWorkcenterPhone);
            var phone = await _extraFeatureManager.AddPhoneAsync(workcenterPhone.Phone, false);
            addWorkcenterPhone.Phone = phone;
        }
        return await _workcenterStore.AddWorkcenterPhonesAsync(workcenterPhoneList, saveChanges);
    }
    public async Task<WorkcenterPhone?> UpdateWorkcenterPhoneAsync(WorkcenterPhoneUpdateDto workcenterPhone, bool saveChanges = false)
    {
        return (await UpdateWorkcenterPhonesAsync([workcenterPhone], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to update workcenter phone.");
    }
    public async Task<IEnumerable<WorkcenterPhone>?> UpdateWorkcenterPhonesAsync(IEnumerable<WorkcenterPhoneUpdateDto> workcenterPhones, bool saveChanges = false)
    {
        var workcenterPhoneList = new List<WorkcenterPhone>();
        var workcenterIds = workcenterPhones.Select(a => a.PublicWorkcenterId).Distinct().ToList();
        var workcenterIdMap = await _workcenterStore.IdConvertForWorkcenterAsync(workcenterIds);
        var workcenterPhoneIds = workcenterPhones.Select(a => a.Id).Distinct().ToList();
        var existingWorkcenterPhones = (await _workcenterStore.GetWorkcenterPhonesForUpdateAsync(workcenterPhoneIds))?.ToDictionary(a => a.Id);
        if (existingWorkcenterPhones == null || !existingWorkcenterPhones.Any())
        {
            throw new Exception("No workcenter phones found for update.");
        }
        foreach (var workcenterPhone in workcenterPhones)
        {
            if (!workcenterIdMap.TryGetValue(workcenterPhone.PublicWorkcenterId, out var workcenterId))
            {
                throw new Exception($"Workcenter not found: {workcenterPhone.PublicWorkcenterId}");
            }
            existingWorkcenterPhones.TryGetValue(workcenterPhone.Id, out var updateWorkcenterPhone);
            if (updateWorkcenterPhone == null)
            {
                throw new Exception($"Workcenter phone not found: {workcenterPhone.Id}");
            }
            updateWorkcenterPhone.WorkcenterId = workcenterId;
            updateWorkcenterPhone.Disabled = workcenterPhone.Disabled;
            updateWorkcenterPhone.Deleted = workcenterPhone.Deleted;
            var phone = await _extraFeatureManager.UpdatePhoneAsync(workcenterPhone.Phone, false);
            updateWorkcenterPhone.Phone = phone;
            workcenterPhoneList.Add(updateWorkcenterPhone);
        }
        return await _workcenterStore.UpdateWorkcenterPhonesAsync(workcenterPhoneList, saveChanges);
    }
    public async Task<WorkcenterExtraFeature?> AddWorkcenterExtraFeatureAsync(WorkcenterExtraFeatureAddDto workcenterExtraFeature, bool saveChanges = false)
    {
        return (await AddWorkcenterExtraFeaturesAsync([workcenterExtraFeature], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to add workcenter extra feature.");
    }
    public async Task<IEnumerable<WorkcenterExtraFeature>?> AddWorkcenterExtraFeaturesAsync(IEnumerable<WorkcenterExtraFeatureAddDto> workcenterExtraFeatures, bool saveChanges = false)
    {
        var workcenterExtraFeatureList = new List<WorkcenterExtraFeature>();
        var workcenterIds = workcenterExtraFeatures.Select(a => a.PublicWorkcenterId).Distinct().ToList();
        var workcenterIdMap = await _workcenterStore.IdConvertForWorkcenterAsync(workcenterIds);
        foreach (var workcenterExtraFeature in workcenterExtraFeatures)
        {
            if (!workcenterIdMap.TryGetValue(workcenterExtraFeature.PublicWorkcenterId, out var workcenterId))
            {
                throw new Exception($"Workcenter not found: {workcenterExtraFeature.PublicWorkcenterId}");
            }
            var addWorkcenterExtraFeature = new WorkcenterExtraFeature
            {
                Id = Guid.NewGuid().ToString(),
                WorkcenterId = workcenterId,
            };
            workcenterExtraFeatureList.Add(addWorkcenterExtraFeature);
            var extraFeature = await _extraFeatureManager.AddExtraFeatureAsync(workcenterExtraFeature.ExtraFeature, false);
            addWorkcenterExtraFeature.ExtraFeature = extraFeature;
        }
        return await _workcenterStore.AddWorkcenterExtraFeaturesAsync(workcenterExtraFeatureList, saveChanges);
    }
    public async Task<WorkcenterExtraFeature?> UpdateWorkcenterExtraFeatureAsync(WorkcenterExtraFeatureUpdateDto workcenterExtraFeature, bool saveChanges = false)
    {
        return (await UpdateWorkcenterExtraFeaturesAsync([workcenterExtraFeature], saveChanges))?.FirstOrDefault()
               ?? throw new Exception("Failed to update workcenter extra feature.");
    }
    public async Task<IEnumerable<WorkcenterExtraFeature>?> UpdateWorkcenterExtraFeaturesAsync(IEnumerable<WorkcenterExtraFeatureUpdateDto> workcenterExtraFeatures, bool saveChanges = false)
    {
        var workcenterExtraFeatureList = new List<WorkcenterExtraFeature>();
        var workcenterIds = workcenterExtraFeatures.Select(a => a.PublicWorkcenterId).Distinct().ToList();
        var workcenterIdMap = await _workcenterStore.IdConvertForWorkcenterAsync(workcenterIds);
        var workcenterExtraFeatureIds = workcenterExtraFeatures.Select(a => a.Id).Distinct().ToList();
        var existingWorkcenterExtraFeatures = (await _workcenterStore.GetWorkcenterExtraFeaturesForUpdateAsync(workcenterExtraFeatureIds))?.ToDictionary(a => a.Id);
        if (existingWorkcenterExtraFeatures == null || !existingWorkcenterExtraFeatures.Any())
        {
            throw new Exception("No workcenter extra features found for update.");
        }
        foreach (var workcenterExtraFeature in workcenterExtraFeatures)
        {
            if (!workcenterIdMap.TryGetValue(workcenterExtraFeature.PublicWorkcenterId, out var workcenterId))
            {
                throw new Exception($"Workcenter not found: {workcenterExtraFeature.PublicWorkcenterId}");
            }
            existingWorkcenterExtraFeatures.TryGetValue(workcenterExtraFeature.Id, out var updateWorkcenterExtraFeature);
            if (updateWorkcenterExtraFeature == null)
            {
                throw new Exception($"Workcenter extra feature not found: {workcenterExtraFeature.Id}");
            }
            updateWorkcenterExtraFeature.WorkcenterId = workcenterId;
            updateWorkcenterExtraFeature.Disabled = workcenterExtraFeature.Disabled;
            updateWorkcenterExtraFeature.Deleted = workcenterExtraFeature.Deleted;
            updateWorkcenterExtraFeature.ExtraFeature = await _extraFeatureManager.UpdateExtraFeatureAsync(workcenterExtraFeature.ExtraFeature, false);
            workcenterExtraFeatureList.Add(updateWorkcenterExtraFeature);
        }
        return await _workcenterStore.UpdateWorkcenterExtraFeaturesAsync(workcenterExtraFeatureList, saveChanges);
    }
}