using Rlx.Shared.Models;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace OrganizationManagement.Models.OrganizationManagementDbContextModels;

public class Position : EntityBaseModel
{
    public required string Name { get; set; }
    public string? Code { get; set; }
    public int? ParentId { get; set; }
    public int UnitId { get; set; }
    public int PositionTypeId { get; set; }
    public virtual Unit? Unit { get; set; }
    public virtual ICollection<Position>? Children { get; set; }
    public virtual ICollection<PersonPosition>? PersonPositions { get; set; }
    public virtual Position? Parent { get; set; }
    public virtual RlxEnumValue? PositionType { get; set; }
}