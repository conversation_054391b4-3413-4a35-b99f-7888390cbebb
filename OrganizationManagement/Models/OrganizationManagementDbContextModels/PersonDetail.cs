using Rlx.Shared.Models;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace OrganizationManagement.Models.OrganizationManagementDbContextModels;

public class PersonDetail : EntityBaseModel
{
    public int PersonId { get; set; }
    public string? MotherName { get; set; }
    public string? FatherName { get; set; }
    public string? PlaceOfBirth { get; set; }
    public int? GenderId { get; set; }
    public int? MaritalStatusId { get; set; }
    public int? BloodGroupId { get; set; }
    public string? IdCardSerialNumber { get; set; }
    public DateTime? IdCardAcceptanceDate { get; set; }
    public int? StateId { get; set; }
    public int? CityId { get; set; }
    public string? ManualCityName { get; set; }
    public string? ManualStateName { get; set; }
    public virtual State? State { get; set; }
    public virtual City? City { get; set; }
    public virtual RlxEnumValue? BloodGroup { get; set; }
    public virtual RlxEnumValue? MaritalStatus { get; set; }
    public virtual RlxEnumValue? Gender { get; set; }
    public virtual Person? Person { get; set; }
}
