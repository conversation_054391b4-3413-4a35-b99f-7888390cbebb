using Rlx.Shared.Models;
namespace OrganizationManagement.Models.OrganizationManagementDbContextModels;

public class Address : EntityBaseIdModel
{
    public string? Title { get; set; }
    public required string AddressText { get; set; }
    public string? PostalCode { get; set; }
    public int? StateId { get; set; }
    public int? CountryId { get; set; }
    public int? CityId { get; set; }
    public string? ManualStateName { get; set; }
    public string? ManualCityName { get; set; }
    public virtual State? State { get; set; }
    public virtual Country? Country { get; set; }
    public virtual City? City { get; set; }
}
