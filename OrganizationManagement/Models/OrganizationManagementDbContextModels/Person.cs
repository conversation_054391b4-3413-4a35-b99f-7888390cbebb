using Rlx.Shared.Models;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace OrganizationManagement.Models.OrganizationManagementDbContextModels;

public class Person : EntityBaseModel
{
    public required string IdentityNumber { get; set; }
    public required string Name { get; set; }
    public required string Surname { get; set; }
    public string? MiddleName { get; set; }
    public DateTime DateOfBirth { get; set; }
    public int CountryId { get; set; }
    public int? TitleId { get; set; }
    public virtual ICollection<PersonUser>? PersonUsers { get; set; }
    public virtual Country? Country { get; set; }
    public virtual RlxEnumValue? Title { get; set; }
    public virtual ICollection<PersonAddress>? PersonAddresses { get; set; }
    public virtual ICollection<PersonPhone>? PersonPhones { get; set; }
    public virtual ICollection<PersonExtraFeature>? PersonExtraFeatures { get; set; }
    public virtual ICollection<PersonPassport>? PersonPassports { get; set; }
    public virtual ICollection<PersonPosition>? PersonPositions { get; set; }
    public virtual PersonDetail? PersonDetail { get; set; }
}
