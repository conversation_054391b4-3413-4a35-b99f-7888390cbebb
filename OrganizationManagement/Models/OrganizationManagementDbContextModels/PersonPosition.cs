using Rlx.Shared.Models;
namespace OrganizationManagement.Models.OrganizationManagementDbContextModels;
public class PersonPosition : EntityBaseModel
{
    public int PersonId { get; set; }
    public int PositionId { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public virtual Person? Person { get; set; }
    public virtual Position? Position { get; set; }
}
