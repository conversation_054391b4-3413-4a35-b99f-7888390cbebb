using Rlx.Shared.Models;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace OrganizationManagement.Models.OrganizationManagementDbContextModels;

public class Unit : EntityBaseModel
{
    public required string Name { get; set; }
    public string? Code { get; set; }
    public int? ParentId { get; set; }
    public int UnitTypeId { get; set; }
    public virtual Unit? Parent { get; set; }
    public virtual ICollection<Unit>? Children { get; set; }
    public virtual ICollection<Position>? Positions { get; set; }
    public virtual RlxEnumValue? UnitType { get; set; }
    public virtual ICollection<UnitAddress>? UnitAddresses { get; set; }
    public virtual ICollection<UnitPhone>? UnitPhones { get; set; }
    public virtual ICollection<UnitExtraFeature>? UnitExtraFeatures { get; set; }
}