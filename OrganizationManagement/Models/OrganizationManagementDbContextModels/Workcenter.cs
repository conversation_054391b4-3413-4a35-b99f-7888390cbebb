using Rlx.Shared.Models;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace OrganizationManagement.Models.OrganizationManagementDbContextModels;
public class Workcenter : EntityBaseModel
{
    public required string Name { get; set; }
    public string? Code { get; set; }
    public int? ParentId { get; set; }
    public int WorkcenterTypeId { get; set; }
    public int? UnitId { get; set; }
    public virtual Workcenter? Parent { get; set; }
    public virtual ICollection<Workcenter>? Children { get; set; }
    public virtual Unit? Unit { get; set; }
    public virtual RlxEnumValue? WorkcenterType { get; set; }
    public virtual ICollection<WorkcenterAddress>? WorkcenterAddresses { get; set; }
    public virtual ICollection<WorkcenterPhone>? WorkcenterPhones { get; set; }
    public virtual ICollection<WorkcenterExtraFeature>? WorkcenterExtraFeatures { get; set; }
}