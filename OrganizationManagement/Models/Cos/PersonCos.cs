namespace OrganizationManagement.Models.Cos;

public class GetPersonsCo
{
    public string? Culture { get; set; }
    public string? Id { get; set; }
    public string[]? Ids { get; set; }
    public string? Name { get; set; }
    public string? Surname { get; set; }
    public string? Fullname { get; set; }
    public string? NameContains { get; set; }
    public string? SurnameContains { get; set; }
    public string? FullnameContains { get; set; }
    public string? Contains { get; set; }
    public bool? Disabled { get; set; }
    public bool? Deleted { get; set; }
}