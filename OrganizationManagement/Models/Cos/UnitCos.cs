namespace OrganizationManagement.Models.Cos;

public class GetUnitsCo
{
    public string? Culture { get; set; }
    public int? ParentId { get; set; }
    public string? Id { get; set; }
    public string[]? Ids { get; set; }
    public string? Name { get; set; }
    public string? Code { get; set; }
    public string? NameContains { get; set; }
    public string? CodeContains { get; set; }
    public string? Contains { get; set; }
    public bool? Disabled { get; set; }
    public bool? Deleted { get; set; }
}