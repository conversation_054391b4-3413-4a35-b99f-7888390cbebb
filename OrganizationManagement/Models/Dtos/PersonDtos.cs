using System.ComponentModel.DataAnnotations;
namespace OrganizationManagement.Models.Dtos;

public class PersonAddDto
{
    [Required]
    [MaxLength(250)]
    public required string IdentityNumber { get; set; }
    [Required]
    [MaxLength(250)]
    public required string Name { get; set; }
    [Required]
    [MaxLength(250)]
    public required string Surname { get; set; }
    [MaxLength(250)]
    public string? MiddleName { get; set; }
    public DateTime DateOfBirth { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicCountryId { get; set; }
    [MaxLength(50)]
    public string? PublicTitleId { get; set; }
    public bool Disabled { get; set; }
}
public class PersonUpdateDto : PersonAddDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    public bool Deleted { get; set; }
}
public class PersonForAutoCompleteDto
{
    public required string Id { get; set; }
    public required string Name { get; set; }
    public required string Surname { get; set; }
    public required string IdentityNumber { get; set; }
}
public class PersonViewDto
{
    public required string Id { get; set; }
    public required string IdentityNumber { get; set; }
    public required string Name { get; set; }
    public required string Surname { get; set; }
    public string? MiddleName { get; set; }
    public DateTime DateOfBirth { get; set; }
    public required string PublicCountryId { get; set; }
    public string? PublicTitleId { get; set; }
    public bool Disabled { get; set; }
}
public class PersonViewCompleteDto : PersonViewDto
{
    public PersonDetailViewDto? Detail { get; set; }
    public List<PersonAddressViewDto>? Addresses { get; set; }
    public List<PersonPhoneViewDto>? Phones { get; set; }
    public List<PersonExtraFeatureViewDto>? ExtraFeatures { get; set; }
    public List<PersonPassportViewDto>? Passports { get; set; }
}