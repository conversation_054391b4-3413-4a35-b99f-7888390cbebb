using System.ComponentModel.DataAnnotations;
namespace OrganizationManagement.Models.Dtos;

public class PersonExtraFeatureAddDto
{
    [Required]
    [MaxLength(50)]
    public required string PublicPersonId { get; set; }
    public required ExtraFeatureAddDto ExtraFeature { get; set; }
    public bool Disabled { get; set; }
}
public class PersonExtraFeatureUpdateDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicPersonId { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicExtraFeatureId { get; set; }
    public bool Disabled { get; set; }
    public bool Deleted { get; set; }
    public required ExtraFeatureUpdateDto ExtraFeature { get; set; }
}
public class PersonExtraFeatureViewDto
{
    public required string Id { get; set; }
    public required string PublicPersonId { get; set; }
    public required string PublicExtraFeatureId { get; set; }
    public ExtraFeatureViewDto? ExtraFeature { get; set; }
}