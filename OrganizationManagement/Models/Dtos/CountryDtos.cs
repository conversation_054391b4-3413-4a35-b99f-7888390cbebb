using System.ComponentModel.DataAnnotations;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Models.Dtos;

public class CountryAddDto
{
    [Required]
    [MaxLength(250)]
    public required string Name { get; set; }
    [MaxLength(50)]
    public string? IsoCode2 { get; set; }
    [MaxLength(50)]
    public string? IsoCode3 { get; set; }
    public bool Disabled { get; set; }
}
public class CountryUpdateDto : CountryAddDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    public bool Deleted { get; set; }
}
public class ActiveCountryDto
{
    public required string Id { get; set; }
    public required string Name { get; set; }
    public List<RlxLocalizationDto>? Localizations { get; set; }
}