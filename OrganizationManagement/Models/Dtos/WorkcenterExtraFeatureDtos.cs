using System.ComponentModel.DataAnnotations;
namespace OrganizationManagement.Models.Dtos;

public class WorkcenterExtraFeatureAddDto
{
    [Required]
    [MaxLength(50)]
    public required string PublicWorkcenterId { get; set; }
    public required ExtraFeatureAddDto ExtraFeature { get; set; }
    public bool Disabled { get; set; }
}
public class WorkcenterExtraFeatureUpdateDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicWorkcenterId { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicExtraFeatureId { get; set; }
    public bool Disabled { get; set; }
    public bool Deleted { get; set; }
    public required ExtraFeatureUpdateDto ExtraFeature { get; set; }
}
public class WorkcenterExtraFeatureViewDto
{
    public required string Id { get; set; }
    public required string PublicWorkcenterId { get; set; }
    public required string PublicExtraFeatureId { get; set; }
    public ExtraFeatureViewDto? ExtraFeature { get; set; }
}