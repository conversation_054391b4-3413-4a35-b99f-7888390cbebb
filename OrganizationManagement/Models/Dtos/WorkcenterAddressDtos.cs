using System.ComponentModel.DataAnnotations;
namespace OrganizationManagement.Models.Dtos;

public class WorkcenterAddressAddDto
{
    [Required]
    [MaxLength(50)]
    public required string PublicWorkcenterId { get; set; }
    public required AddressAddDto Address { get; set; }
    public bool IsDefault { get; set; }
    public bool Disabled { get; set; }
}
public class WorkcenterAddressUpdateDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicWorkcenterId { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicAddressId { get; set; }
    public bool IsDefault { get; set; }
    public bool Disabled { get; set; }
    public bool Deleted { get; set; }
    public required AddressUpdateDto Address { get; set; }
}
public class WorkcenterAddressViewDto
{
    public required string Id { get; set; }
    public required string PublicWorkcenterId { get; set; }
    public required string PublicAddressId { get; set; }
    public AddressViewDto? Address { get; set; }
    public bool IsDefault { get; set; }
    public bool Disabled { get; set; }
}