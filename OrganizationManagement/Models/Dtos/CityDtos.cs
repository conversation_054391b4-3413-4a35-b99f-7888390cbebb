using System.ComponentModel.DataAnnotations;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Models.Dtos;

public class CityAddDto
{
    [Required]
    [MaxLength(250)]
    public required string Name { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicStateId { get; set; }
    public bool? Disabled { get; set; }
}
public class CityUpdateDto : CityAddDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    public bool? Deleted { get; set; }
}
public class ActiveCityDto
{
    public required string Id { get; set; }
    public required string Name { get; set; }
    public IEnumerable<RlxLocalizationDto>? Localizations { get; set; }
}