using System.ComponentModel.DataAnnotations;
namespace OrganizationManagement.Models.Dtos;

public class PhoneAddDto
{
    [MaxLength(50)]
    public string? CountryCode { get; set; }
    [Required]
    [MaxLength(50)]
    public required string Number { get; set; }
}
public class PhoneUpdateDto : PhoneAddDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
}
public class PhoneViewDto
{
    public required string Id { get; set; }
    public string? CountryCode { get; set; }
    public required string Number { get; set; }
}
