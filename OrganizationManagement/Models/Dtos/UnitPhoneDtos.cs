using System.ComponentModel.DataAnnotations;
namespace OrganizationManagement.Models.Dtos;

public class UnitPhoneAddDto
{
    [Required]
    [MaxLength(50)]
    public required string PublicUnitId { get; set; }
    public required PhoneAddDto Phone { get; set; }
    public bool Disabled { get; set; }
}
public class UnitPhoneUpdateDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicUnitId { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicPhoneId { get; set; }
    public bool Disabled { get; set; }
    public bool Deleted { get; set; }
    public required PhoneUpdateDto Phone { get; set; }
}
public class UnitPhoneViewDto
{
    public required string Id { get; set; }
    public required string PublicUnitId { get; set; }
    public required string PublicPhoneId { get; set; }
    public PhoneViewDto? Phone { get; set; }
}