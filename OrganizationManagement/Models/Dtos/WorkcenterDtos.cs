using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
namespace OrganizationManagement.Models.Dtos;

public class WorkcenterAddDto
{
    [Required]
    [MaxLength(250)]
    public required string Name { get; set; }
    [MaxLength(50)]
    public string? Code { get; set; }
    [MaxLength(50)]
    public string? PublicParentId { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicWorkcenterTypeId { get; set; }
    public bool Disabled { get; set; }
}
public class WorkcenterUpdateDto : WorkcenterAddDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    public bool Deleted { get; set; }
}
public class WorkcenterTreeDto
{
    public required string Id { get; set; }
    [JsonIgnore]
    public int? AutoIncrementId { get; set; }
    public required string Name { get; set; }
    public string? Code { get; set; }
    [JsonIgnore]
    public int? ParentId { get; set; }
    public string? PublicParentId { get; set; }
    public IList<WorkcenterTreeDto> Children { get; set; } = new List<WorkcenterTreeDto>();
}
public class WorkcenterForAutoCompleteDto
{
    public required string Id { get; set; }
    public required string Name { get; set; }
    public string? Code { get; set; }
}
public class WorkcenterViewDto
{
    public required string Id { get; set; }
    public required string Name { get; set; }
    public string? Code { get; set; }
    public string? PublicParentId { get; set; }
    public required string PublicWorkcenterTypeId { get; set; }
    public bool Disabled { get; set; }
    public bool Deleted { get; set; }
}
public class WorkcenterViewCompleteDto : WorkcenterViewDto
{
    public List<WorkcenterAddressViewDto>? Addresses { get; set; }
    public List<WorkcenterPhoneViewDto>? Phones { get; set; }
    public List<WorkcenterExtraFeatureViewDto>? ExtraFeatures { get; set; }
}