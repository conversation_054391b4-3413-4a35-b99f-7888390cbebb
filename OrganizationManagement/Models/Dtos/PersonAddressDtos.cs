using System.ComponentModel.DataAnnotations;
namespace OrganizationManagement.Models.Dtos;

public class PersonAddressAddDto
{
    [Required]
    [MaxLength(50)]
    public required string PublicPersonId { get; set; }
    public required AddressAddDto Address { get; set; }
    public bool IsDefault { get; set; }
    public bool Disabled { get; set; }
}
public class PersonAddressUpdateDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicPersonId { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicAddressId { get; set; }
    public bool IsDefault { get; set; }
    public bool Disabled { get; set; }
    public bool Deleted { get; set; }
    public required AddressUpdateDto Address { get; set; }
}
public class PersonAddressViewDto
{
    public required string Id { get; set; }
    public required string PublicPersonId { get; set; }
    public required string PublicAddressId { get; set; }
    public AddressViewDto? Address { get; set; }
    public bool IsDefault { get; set; }
    public bool Disabled { get; set; }
}