using System.ComponentModel.DataAnnotations;
namespace OrganizationManagement.Models.Dtos;

public class ExtraFeatureAddDto
{
    [Required]
    [MaxLength(250)]
    public required string Title { get; set; }
    [Required]
    [MaxLength(250)]
    public required string Value { get; set; }
}
public class ExtraFeatureUpdateDto : ExtraFeatureAddDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
}
public class ExtraFeatureViewDto
{
    public required string Id { get; set; }
    public required string Title { get; set; }
    public required string Value { get; set; }
}