using System.ComponentModel.DataAnnotations;
namespace OrganizationManagement.Models.Dtos;

public class AddressAddDto
{
    [MaxLength(250)]
    public string? Title { get; set; }
    [Required]
    [MaxLength(500)]
    public required string AddressText { get; set; }
    [MaxLength(50)]
    public string? PostalCode { get; set; }
    public string? PublicStateId { get; set; }
    public string? PublicCountryId { get; set; }
    public string? PublicCityId { get; set; }
    [MaxLength(250)]
    public string? ManualStateName { get; set; }
    [MaxLength(250)]
    public string? ManualCityName { get; set; }
}
public class AddressUpdateDto : AddressAddDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
}
public class AddressViewDto
{
    public required string Id { get; set; }
    public string? Title { get; set; }
    public required string AddressText { get; set; }
    public string? PostalCode { get; set; }
    public string? PublicStateId { get; set; }
    public string? PublicCountryId { get; set; }
    public string? PublicCityId { get; set; }
    public string? ManualStateName { get; set; }
    public string? ManualCityName { get; set; }
}