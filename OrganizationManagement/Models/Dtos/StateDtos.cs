using System.ComponentModel.DataAnnotations;
using Rlx.Shared.Models.Dtos;
namespace OrganizationManagement.Models.Dtos;

public class StateAddDto
{
    [Required]
    [MaxLength(250)]
    public required string Name { get; set; }
    [MaxLength(50)]
    public string? Code { get; set; }
    [Required]
    [MaxLength(50)]
    public required string CountryId { get; set; }
    public bool Disabled { get; set; }
}
public class StateUpdateDto : StateAddDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    public bool Deleted { get; set; }
}
public class ActiveStateDto
{
    public required string Id { get; set; }
    public required string Name { get; set; }
    public List<RlxLocalizationDto>? Localizations { get; set; }
}