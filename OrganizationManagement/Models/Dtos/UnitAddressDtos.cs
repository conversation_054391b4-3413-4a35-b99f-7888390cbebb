using System.ComponentModel.DataAnnotations;
namespace OrganizationManagement.Models.Dtos;

public class UnitAddressAddDto
{
    [Required]
    [MaxLength(50)]
    public required string PublicUnitId { get; set; }
    public required AddressAddDto Address { get; set; }
    public bool IsDefault { get; set; }
    public bool Disabled { get; set; }
}
public class UnitAddressUpdateDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicUnitId { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicAddressId { get; set; }
    public bool IsDefault { get; set; }
    public bool Disabled { get; set; }
    public bool Deleted { get; set; }
    public required AddressUpdateDto Address { get; set; }
}
public class UnitAddressViewDto
{
    public required string Id { get; set; }
    public required string PublicUnitId { get; set; }
    public required string PublicAddressId { get; set; }
    public AddressViewDto? Address { get; set; }
    public bool IsDefault { get; set; }
    public bool Disabled { get; set; }
}