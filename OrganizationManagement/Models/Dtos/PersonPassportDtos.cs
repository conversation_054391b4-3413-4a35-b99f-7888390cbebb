using System.ComponentModel.DataAnnotations;
namespace OrganizationManagement.Models.Dtos;

public class PersonPassportAddDto
{
    [Required]
    [MaxLength(50)]
    public required string PublicPersonId { get; set; }
    [Required]
    [MaxLength(250)]
    public required string Number { get; set; }
    public DateTime AcceptanceDate { get; set; }
    public bool Disabled { get; set; }
}
public class PersonPassportUpdateDto : PersonPassportAddDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    public bool Deleted { get; set; }
}
public class PersonPassportViewDto
{
    public required string Id { get; set; }
    public required string PublicPersonId { get; set; }
    public required string Number { get; set; }
    public DateTime AcceptanceDate { get; set; }
    public bool Disabled { get; set; }
}