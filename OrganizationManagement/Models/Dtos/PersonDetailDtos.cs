using System.ComponentModel.DataAnnotations;
namespace OrganizationManagement.Models.Dtos;

public class PersonDetailAddDto
{
    [Required]
    [MaxLength(50)]
    public required string PublicPersonId { get; set; }
    [MaxLength(250)]
    public string? MotherName { get; set; }
    [MaxLength(250)]
    public string? FatherName { get; set; }
    [MaxLength(250)]
    public string? PlaceOfBirth { get; set; }
    [MaxLength(50)]
    public string? PublicGenderId { get; set; }
    [MaxLength(50)]
    public string? PublicMaritalStatusId { get; set; }
    [MaxLength(50)]
    public string? PublicBloodGroupId { get; set; }
    [MaxLength(250)]
    public string? IdCardSerialNumber { get; set; }
    public DateTime? IdCardAcceptanceDate { get; set; }
    [MaxLength(50)]
    public string? PublicStateId { get; set; }
    [MaxLength(50)]
    public string? PublicCityId { get; set; }
    [MaxLength(250)]
    public string? ManualCityName { get; set; }
    [MaxLength(250)]
    public string? ManualStateName { get; set; }
}
public class PersonDetailUpdateDto : PersonDetailAddDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
}
public class PersonDetailViewDto
{
    public required string Id { get; set; }
    public required string PublicPersonId { get; set; }
    public string? MotherName { get; set; }
    public string? FatherName { get; set; }
    public string? PlaceOfBirth { get; set; }
    public string? PublicGenderId { get; set; }
    public string? PublicMaritalStatusId { get; set; }
    public string? PublicBloodGroupId { get; set; }
    public string? IdCardSerialNumber { get; set; }
    public DateTime? IdCardAcceptanceDate { get; set; }
    public string? PublicStateId { get; set; }
    public string? PublicCityId { get; set; }
    public string? ManualCityName { get; set; }
    public string? ManualStateName { get; set; }
}