using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
namespace OrganizationManagement.Models.Dtos;

public class UnitAddDto
{
    [Required]
    [MaxLength(250)]
    public required string Name { get; set; }
    [MaxLength(250)]
    public string? Code { get; set; }
    [MaxLength(50)]
    public string? PublicParentId { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicUnitTypeId { get; set; }
    public bool Disabled { get; set; }
}
public class UnitUpdateDto : UnitAddDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    public bool Deleted { get; set; }
}
public class UnitTreeDto
{
    public required string Id { get; set; }
    [JsonIgnore]
    public int? AutoIncrementId { get; set; }
    public required string Name { get; set; }
    public string? Code { get; set; }
    [JsonIgnore]
    public int? ParentId { get; set; }
    public string? PublicParentId { get; set; }
    public IList<UnitTreeDto> Children { get; set; } = new List<UnitTreeDto>();
}
public class UnitForAutoCompleteDto
{
    public required string Id { get; set; }
    public required string Name { get; set; }
    public string? Code { get; set; }
}
public class UnitViewDto
{
    public required string Id { get; set; }
    public required string Name { get; set; }
    public string? Code { get; set; }
    public string? PublicParentId { get; set; }
    public required string PublicUnitTypeId { get; set; }
    public bool Disabled { get; set; }
    public bool Deleted { get; set; }
}
public class UnitViewCompleteDto : UnitViewDto
{
    public List<UnitAddressViewDto>? Addresses { get; set; }
    public List<UnitPhoneViewDto>? Phones { get; set; }
    public List<UnitExtraFeatureViewDto>? ExtraFeatures { get; set; }
}