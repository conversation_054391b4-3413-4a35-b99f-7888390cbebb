using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
namespace OrganizationManagement.Models.Dtos;

public class PositionAddDto
{
    [Required]
    [MaxLength(250)]
    public required string Name { get; set; }
    [MaxLength(250)]
    public string? Code { get; set; }
    [MaxLength(50)]
    public string? PublicParentId { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicUnitId { get; set; }
    [Required]
    [MaxLength(50)]
    public required string PublicPositionTypeId { get; set; }
    public bool Disabled { get; set; }
}
public class PositionUpdateDto : PositionAddDto
{
    [Required]
    [MaxLength(50)]
    public required string Id { get; set; }
    public bool Deleted { get; set; }
}
public class PositionTreeDto
{
    public required string Id { get; set; }
    [JsonIgnore]
    public int? AutoIncrementId { get; set; }
    public required string Name { get; set; }
    public string? Code { get; set; }
    [JsonIgnore]
    public int? ParentId { get; set; }
    public string? PublicParentId { get; set; }
    public IList<PositionTreeDto> Children { get; set; } = new List<PositionTreeDto>();
}
public class PositionForAutoCompleteDto
{
    public required string Id { get; set; }
    public required string Name { get; set; }
    public string? Code { get; set; }
}
public class PositionViewDto
{
    public required string Id { get; set; }
    public required string Name { get; set; }
    public string? Code { get; set; }
    public string? PublicParentId { get; set; }
    public required string PublicPositionTypeId { get; set; }
    public required string PublicUnitId { get; set; }
    public bool Disabled { get; set; }
    public bool Deleted { get; set; }
}
public class PositionViewCompleteDto : PositionViewDto
{
}