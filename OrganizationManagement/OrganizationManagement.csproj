<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.15" />
    <PackageReference Include="OpenIddict.Validation" Version="6.2.1" />
    <PackageReference Include="OpenIddict.Validation.AspNetCore" Version="6.2.1" />
    <PackageReference Include="OpenIddict.Validation.SystemNetHttp" Version="6.2.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.8" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.2" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.8" />
    <PackageReference Include="Rlx.Shared" Version="1.0.33" />
    <PackageReference Include="RabbitMQ.Client" Version="7.0.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.24" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <None Update="Certs\encryption.pfx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <!-- <None Update="Certs\signing.pfx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None> -->
  </ItemGroup>
</Project>