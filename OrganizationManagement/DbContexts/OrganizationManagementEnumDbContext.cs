using Microsoft.EntityFrameworkCore;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Interfaces;
namespace OrganizationManagement.DbContexts;
public class OrganizationManagementEnumDbContext : RlxEnumDbContext
{
    private readonly IConfiguration _configuration;
    private readonly IEntityChangeLogHelper _entityChangeLogHelper;
    public OrganizationManagementEnumDbContext(DbContextOptions<OrganizationManagementEnumDbContext> options, IEntityChangeLogHelper entityChangeLogHelper, IConfiguration configuration) : base(options)
    {
        _entityChangeLogHelper = entityChangeLogHelper;
        _configuration = configuration;
    }
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        if (_configuration["EntityLog:Enabled"] == "1")
        {
            await _entityChangeLogHelper.AddEntityChangeLogAsync(dbContext: this);
        }
        return await base.SaveChangesAsync(cancellationToken);
    }
}