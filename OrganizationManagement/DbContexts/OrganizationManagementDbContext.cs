using Microsoft.EntityFrameworkCore;
using OrganizationManagement.Models.OrganizationManagementDbContextModels;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.RlxEnumDbContextModels;
using Rlx.Shared.Models.RlxLocalizationDbContextModels;
namespace OrganizationManagement.DbContexts
{
    public class OrganizationManagementDbContext : DbContext
    {
        private readonly IEntityChangeLogHelper _entityChangeLogHelper;
        private readonly IConfiguration _configuration;
        public OrganizationManagementDbContext(DbContextOptions<OrganizationManagementDbContext> options, IEntityChangeLogHelper entityChangeLogHelper, IConfiguration configuration) : base(options)
        {
            _entityChangeLogHelper = entityChangeLogHelper;
            _configuration = configuration;
            ChangeTracker.LazyLoadingEnabled = false;
        }
        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
            builder.Entity<Address>(e =>
            {
                e.<PERSON><PERSON>ey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
                e.Property(e2 => e2.Title).HasMaxLength(250);
                e.Property(e2 => e2.AddressText).HasMaxLength(500);
                e.Property(e2 => e2.PostalCode).HasMaxLength(50);
                e.Property(e2 => e2.ManualStateName).HasMaxLength(250);
                e.Property(e2 => e2.ManualCityName).HasMaxLength(250);
                e.HasOne(e2 => e2.State)
                    .WithMany()
                    .HasForeignKey(e2 => e2.StateId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.Country)
                    .WithMany()
                    .HasForeignKey(e2 => e2.CountryId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.City)
                    .WithMany()
                    .HasForeignKey(e2 => e2.CityId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<City>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
                e.Property(e2 => e2.Name).HasMaxLength(250);
                e.HasOne(e2 => e2.State)
                    .WithMany(e2 => e2.Cities)
                    .HasForeignKey(e2 => e2.StateId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<Country>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
                e.Property(e2 => e2.Name).HasMaxLength(250);
                e.Property(e2 => e2.IsoCode2).HasMaxLength(50);
                e.Property(e2 => e2.IsoCode3).HasMaxLength(50);
            });
            builder.Entity<ExtraFeature>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
                e.Property(e2 => e2.Title).HasMaxLength(250);
                e.Property(e2 => e2.Value).HasMaxLength(250);
            });
            builder.Entity<Person>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
                e.Property(e2 => e2.IdentityNumber).HasMaxLength(250);
                e.Property(e2 => e2.Name).HasMaxLength(250);
                e.Property(e2 => e2.Surname).HasMaxLength(250);
                e.Property(e2 => e2.MiddleName).HasMaxLength(250);
                e.HasOne(e2 => e2.Country)
                    .WithMany()
                    .HasForeignKey(e2 => e2.CountryId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.Title)
                    .WithMany()
                    .HasForeignKey(e2 => e2.TitleId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<PersonAddress>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.HasOne(e2 => e2.Person)
                    .WithMany(e2 => e2.PersonAddresses)
                    .HasForeignKey(e2 => e2.PersonId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.Address)
                    .WithMany()
                    .HasForeignKey(e2 => e2.AddressId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<PersonDetail>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
                e.Property(e2 => e2.MotherName).HasMaxLength(250);
                e.Property(e2 => e2.FatherName).HasMaxLength(250);
                e.Property(e2 => e2.PlaceOfBirth).HasMaxLength(250);
                e.Property(e2 => e2.IdCardSerialNumber).HasMaxLength(250);
                e.Property(e2 => e2.ManualCityName).HasMaxLength(250);
                e.Property(e2 => e2.ManualStateName).HasMaxLength(250);
                e.HasOne(e2 => e2.Gender)
                    .WithMany()
                    .HasForeignKey(e2 => e2.GenderId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.MaritalStatus)
                    .WithMany()
                    .HasForeignKey(e2 => e2.MaritalStatusId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.BloodGroup)
                    .WithMany()
                    .HasForeignKey(e2 => e2.BloodGroupId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.State)
                    .WithMany()
                    .HasForeignKey(e2 => e2.StateId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.City)
                    .WithMany()
                    .HasForeignKey(e2 => e2.CityId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.Person)
                    .WithOne()
                    .HasForeignKey<PersonDetail>(e2 => e2.PersonId)
                    .HasPrincipalKey<Person>(e2 => e2.AutoIncrementId);
            });
            builder.Entity<PersonExtraFeature>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.HasOne(e2 => e2.Person)
                    .WithMany(e2 => e2.PersonExtraFeatures)
                    .HasForeignKey(e2 => e2.PersonId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.ExtraFeature)
                    .WithMany()
                    .HasForeignKey(e2 => e2.ExtraFeatureId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<PersonPassport>(e =>
                      {
                          e.HasKey(e2 => e2.Id);
                          e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                          e.Property(e2 => e2.Number).HasMaxLength(250);
                          e.HasOne(e2 => e2.Person)
                              .WithMany(e2 => e2.PersonPassports)
                              .HasForeignKey(e2 => e2.PersonId)
                              .HasPrincipalKey(e2 => e2.AutoIncrementId);
                      });
            builder.Entity<PersonPhone>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.HasOne(e2 => e2.Person)
                    .WithMany(e2 => e2.PersonPhones)
                    .HasForeignKey(e2 => e2.PersonId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.Phone)
                    .WithMany()
                    .HasForeignKey(e2 => e2.PhoneId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<PersonPosition>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.HasOne(e2 => e2.Person)
                    .WithMany(e2 => e2.PersonPositions)
                    .HasForeignKey(e2 => e2.PersonId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.Position)
                    .WithMany()
                    .HasForeignKey(e2 => e2.PositionId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<PersonUser>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.HasOne(e2 => e2.Person)
                    .WithMany(e2 => e2.PersonUsers)
                    .HasForeignKey(e2 => e2.PersonId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<Phone>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
                e.Property(e2 => e2.CountryCode).HasMaxLength(50);
                e.Property(e2 => e2.Number).HasMaxLength(50);
            });
            builder.Entity<Position>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
                e.Property(e2 => e2.Name).HasMaxLength(250);
                e.Property(e2 => e2.Code).HasMaxLength(250);
                e.HasMany(e2 => e2.Children)
                    .WithOne(e2 => e2.Parent)
                    .HasForeignKey(e2 => e2.ParentId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasMany(e2 => e2.PersonPositions)
                    .WithOne(e2 => e2.Position)
                    .HasForeignKey(e2 => e2.PositionId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne<Unit>()
                .WithMany(e2 => e2.Positions)
                .HasForeignKey(e2 => e2.UnitId)
                .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.PositionType)
                .WithMany()
              .HasForeignKey(e2 => e2.PositionTypeId)
              .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<State>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
                e.Property(e2 => e2.Name).HasMaxLength(250);
                e.Property(e2 => e2.Code).HasMaxLength(50);
                e.HasOne(e2 => e2.Country)
                    .WithMany(e2 => e2.States)
                    .HasForeignKey(e2 => e2.CountryId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<Unit>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
                e.Property(e2 => e2.Name).HasMaxLength(250);
                e.Property(e2 => e2.Code).HasMaxLength(250);
                e.HasMany(e2 => e2.Children)
                    .WithOne(e2 => e2.Parent)
                    .HasForeignKey(e2 => e2.ParentId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasMany(e2 => e2.Positions)
                    .WithOne(e2 => e2.Unit)
                    .HasForeignKey(e2 => e2.UnitId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.UnitType)
                .WithMany()
                .HasForeignKey(e2 => e2.UnitTypeId)
                .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<UnitAddress>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.HasOne(e2 => e2.Unit)
                    .WithMany(e2 => e2.UnitAddresses)
                    .HasForeignKey(e2 => e2.UnitId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.Address)
                    .WithMany()
                    .HasForeignKey(e2 => e2.AddressId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<UnitExtraFeature>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.HasOne(e2 => e2.Unit)
                    .WithMany(e2 => e2.UnitExtraFeatures)
                    .HasForeignKey(e2 => e2.UnitId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.ExtraFeature)
                    .WithMany()
                    .HasForeignKey(e2 => e2.ExtraFeatureId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<UnitPhone>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.HasOne(e2 => e2.Unit)
                    .WithMany(e2 => e2.UnitPhones)
                    .HasForeignKey(e2 => e2.UnitId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.Phone)
                    .WithMany()
                    .HasForeignKey(e2 => e2.PhoneId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<Workcenter>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
                e.Property(e2 => e2.Name).HasMaxLength(250);
                e.Property(e2 => e2.Code).HasMaxLength(50);
                e.HasOne(e2 => e2.Unit)
                    .WithMany()
                    .HasForeignKey(e2 => e2.UnitId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasMany(e2 => e2.Children)
                    .WithOne(e2 => e2.Parent)
                    .HasForeignKey(e2 => e2.ParentId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.WorkcenterType)
                    .WithMany()
                    .HasForeignKey(e2 => e2.WorkcenterTypeId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<WorkcenterAddress>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.HasOne(e2 => e2.Workcenter)
                    .WithMany(e2 => e2.WorkcenterAddresses)
                    .HasForeignKey(e2 => e2.WorkcenterId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.Address)
                    .WithMany()
                    .HasForeignKey(e2 => e2.AddressId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<WorkcenterExtraFeature>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.HasOne(e2 => e2.Workcenter)
                    .WithMany(e2 => e2.WorkcenterExtraFeatures)
                    .HasForeignKey(e2 => e2.WorkcenterId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.ExtraFeature)
                    .WithMany()
                    .HasForeignKey(e2 => e2.ExtraFeatureId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<WorkcenterPhone>(e =>
            {
                e.HasKey(e2 => e2.Id);
                e.Property(e2 => e2.Id).HasMaxLength(50).IsRequired();
                e.HasOne(e2 => e2.Workcenter)
                    .WithMany(e2 => e2.WorkcenterPhones)
                    .HasForeignKey(e2 => e2.WorkcenterId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
                e.HasOne(e2 => e2.Phone)
                    .WithMany()
                    .HasForeignKey(e2 => e2.PhoneId)
                    .HasPrincipalKey(e2 => e2.AutoIncrementId);
            });
            builder.Entity<RlxEnum>().ToView("RlxEnums");
            builder.Entity<RlxEnumValue>().ToView("RlxEnumValues");
            builder.Entity<RlxLocalization>().ToView("RlxLocalizations");
        }
        public DbSet<Unit> Units { get; set; } = null!;
        public DbSet<Position> Positions { get; set; } = null!;
        public DbSet<RlxLocalization> RlxLocalizations { get; set; } = null!;
        public DbSet<RlxEnum> RlxEnums { get; set; } = null!;
        public DbSet<RlxEnumValue> RlxEnumValues { get; set; } = null!;
        public DbSet<Country> Countries { get; set; } = null!;
        public DbSet<State> States { get; set; } = null!;
        public DbSet<City> Cities { get; set; } = null!;
        public DbSet<Address> Addresses { get; set; } = null!;
        public DbSet<Phone> Phones { get; set; } = null!;
        public DbSet<ExtraFeature> ExtraFeatures { get; set; } = null!;
        public DbSet<Person> Persons { get; set; } = null!;
        public DbSet<PersonDetail> PersonDetails { get; set; } = null!;
        public DbSet<PersonAddress> PersonAddresses { get; set; } = null!;
        public DbSet<PersonPhone> PersonPhones { get; set; } = null!;
        public DbSet<PersonExtraFeature> PersonExtraFeatures { get; set; } = null!;
        public DbSet<PersonPassport> PersonPassports { get; set; } = null!;
        public DbSet<PersonPosition> PersonPositions { get; set; } = null!;
        public DbSet<PersonUser> PersonUsers { get; set; } = null!;
        public DbSet<UnitAddress> UnitAddresses { get; set; } = null!;
        public DbSet<UnitPhone> UnitPhones { get; set; } = null!;
        public DbSet<UnitExtraFeature> UnitExtraFeatures { get; set; } = null!;
        public DbSet<Workcenter> Workcenters { get; set; } = null!;
        public DbSet<WorkcenterAddress> WorkcenterAddresses { get; set; } = null!;
        public DbSet<WorkcenterPhone> WorkcenterPhones { get; set; } = null!;
        public DbSet<WorkcenterExtraFeature> WorkcenterExtraFeatures { get; set; } = null!;
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            if (_configuration["EntityLog:Enabled"] == "1")
            {
                await _entityChangeLogHelper.AddEntityChangeLogAsync(dbContext: this);
            }
            return await base.SaveChangesAsync(cancellationToken);
        }
    }
}