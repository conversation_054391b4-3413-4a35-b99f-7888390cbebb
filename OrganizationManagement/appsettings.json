{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"RlxIdentityShared": "Host=localhost;Database=RlxIdentity;Username=readonly_user;Password=**************;Port=6007", "OrganizationManagement": "Host=localhost;Database=OrganizationManagement;Username=postgres;Password=**********************;Port=6007"}, "EntityLog": {"Host": "localhost", "Port": "6004", "Username": "UKjbldeMP7swx72qgIfqtU", "Password": "j8WkyK91Y6yjKS8vHG52lZ", "Enabled": "1", "ExcludeEntities": [""]}, "RequestLog": {"Host": "localhost", "Port": "6004", "Username": "UKjbldeMP7swx72qgIfqtU", "Password": "j8WkyK91Y6yjKS8vHG52lZ", "Enabled": "1", "Module": "OrganizationManagement"}, "SystemLog": {"Host": "localhost", "Port": "6004", "Username": "UKjbldeMP7swx72qgIfqtU", "Password": "j8WkyK91Y6yjKS8vHG52lZ", "Enabled": "1", "Module": "OrganizationManagement"}, "RedisCache": {"ConnectionString": "localhost:6006,password=qkpWm2qLJqzVwN"}, "CorsOrigins": ["https://localhost:3001", "https://localhost:4001", "https://localhost:5001", "http://localhost:6001"], "CookieDomain": ".arel.edu.tr", "OpenIddict": {"Issuer": "https://local-rlxidentity-api.arel.edu.tr:6010/"}}