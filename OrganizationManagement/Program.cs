using System.Security.Cryptography.X509Certificates;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.EntityFrameworkCore;
using OrganizationManagement.Configs;
using OrganizationManagement.DbContexts;
using OrganizationManagement.Interfaces;
using OrganizationManagement.Managers;
using OrganizationManagement.Stores;
using Rlx.Shared.Configs;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Factories;
using Rlx.Shared.Handlers;
using Rlx.Shared.Helpers;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Managers;
using Rlx.Shared.Middlewares;
using Rlx.Shared.Services;
using Rlx.Shared.Stores;
using StackExchange.Redis;
var builder = WebApplication.CreateBuilder(args);
MapsterConfig.RegisterMappings();
RlxLocalizationMapsterConfig.RegisterMappings();
RlxEnumMapsterConfig.RegisterMappings();
builder.Services.AddLocalization();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddHttpContextAccessor();
builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
});
builder.Services.AddDbContext<OrganizationManagementDbContext>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("OrganizationManagement"));
});
builder.Services.AddDbContext<OrganizationManagementLocalizationDbContext>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("OrganizationManagement"));
});
builder.Services.AddDbContext<OrganizationManagementEnumDbContext>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("OrganizationManagement"));
});
builder.Services.AddDbContext<RlxIdentitySharedDbContext>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("RlxIdentityShared"));
});
var encryptionCert = new X509Certificate2("Certs/encryption.pfx", "YxDnlZdvPimyg5");
builder.Services.AddAuthentication(OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme);
builder.Services.AddOpenIddict()
    .AddValidation(options =>
    {
        options.SetIssuer(builder.Configuration["OpenIddict:Issuer"]!);
        options.UseSystemNetHttp();
        options.UseAspNetCore();
        options.AddEncryptionCertificate(encryptionCert);
    });
builder.Services.AddAuthorization(options =>
{
    options.ConfigurePolicies();
});
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigin", b =>
    {
        b.WithOrigins(builder.Configuration.GetSection("CorsOrigins").Get<string[]>()!)
               .AllowCredentials()
               .AllowAnyHeader()
               .AllowAnyMethod();
    });
});
builder.Services.AddTransient<IClaimsTransformation, RoleClaimsTransformation>();
builder.Services.AddScoped<IRlxIdentitySharedManager, RlxIdentitySharedManager>();
builder.Services.AddScoped<IRlxIdentitySharedStore, RlxIdentitySharedStore>();
builder.Services.AddScoped<IEntityChangeLogHelper, EntityChangeLogHelper>();
builder.Services.AddScoped<IRlxLocalizationManager<OrganizationManagementLocalizationDbContext>, RlxLocalizationManager<OrganizationManagementLocalizationDbContext>>();
builder.Services.AddScoped<IRlxLocalizationStore<OrganizationManagementLocalizationDbContext>, RlxLocalizationStore<OrganizationManagementLocalizationDbContext>>();
builder.Services.AddScoped<IRlxEnumManager<OrganizationManagementEnumDbContext>, RlxEnumManager<OrganizationManagementEnumDbContext>>();
builder.Services.AddScoped<IRlxEnumStore<OrganizationManagementEnumDbContext>, RlxEnumStore<OrganizationManagementEnumDbContext>>();
builder.Services.AddScoped<IUserContextHelper, UserContextHelper>();
builder.Services.AddScoped<IConnectionMultiplexer>(sp => ConnectionMultiplexer.Connect(builder.Configuration["RedisCache:ConnectionString"]!));
builder.Services.AddScoped<IRlxCacheService, RedisCacheService>();
builder.Services.AddScoped(typeof(IRlxSystemLogHelper<>), typeof(RlxSystemLogHelper<>));
builder.Services.AddSingleton(typeof(RlxQueueServiceFactory));
builder.Services.AddSingleton<ExceptionHandler>();
builder.Services.AddScoped<IUnitManager, UnitManager>();
builder.Services.AddScoped<IPersonManager, PersonManager>();
builder.Services.AddScoped<IPositionManager, PositionManager>();
builder.Services.AddScoped<IWorkcenterManager, WorkcenterManager>();
builder.Services.AddScoped<ILocationManager, LocationManager>();
builder.Services.AddScoped<IExtraFeatureManager, ExtraFeatureManager>();
builder.Services.AddScoped<IUnitStore, UnitStore>();
builder.Services.AddScoped<IPersonStore, PersonStore>();
builder.Services.AddScoped<IPositionStore, PositionStore>();
builder.Services.AddScoped<IWorkcenterStore, WorkcenterStore>();
builder.Services.AddScoped<ILocationStore, LocationStore>();
builder.Services.AddScoped<IExtraFeatureStore, ExtraFeatureStore>();
var app = builder.Build();
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    // ForwardedHeaders = ForwardedHeaders.XForwardedProto | ForwardedHeaders.XForwardedFor,
    // KnownNetworks = 
    // {
    //     new Microsoft.AspNetCore.HttpOverrides.IPNetwork(IPAddress.Parse("***********"), 16) // Docker ağınızı buraya yazın
    // }
    ForwardedHeaders = ForwardedHeaders.All, // Tüm header'ları kabul et
    ForwardLimit = null, // Limit kaldır (birden fazla proxy varsa)
    KnownProxies = { }, // Tüm proxy'lere güven (TEHLİKELİ, production'da kullanmayın!)
    KnownNetworks = { } // Tüm ağlara güven
});
using (var scope = app.Services.CreateScope())
{
    var dbContext = scope.ServiceProvider.GetRequiredService<OrganizationManagementDbContext>();
    dbContext.Database.Migrate();
    var localizationDbContext = scope.ServiceProvider.GetRequiredService<OrganizationManagementLocalizationDbContext>();
    localizationDbContext.Database.Migrate();
    var enumDbContext = scope.ServiceProvider.GetRequiredService<OrganizationManagementEnumDbContext>();
    enumDbContext.Database.Migrate();
}
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    await DataSeed.Initialize(services);
}
app.UseMiddleware<RequestLoggingMiddleware>();
app.UseLocalization();
// app.UseMiddleware<LocalizationMiddleware>();
app.UseExceptionHandler(
    errorApp =>
{
    errorApp.Run(async context =>
    {
        var exceptionHandler = app.Services.GetRequiredService<ExceptionHandler>();
        var exceptionFeature = context.Features.Get<IExceptionHandlerPathFeature>();
        if (exceptionFeature?.Error != null)
        {
            await exceptionHandler.TryHandleAsync(context, exceptionFeature.Error, context.RequestAborted);
        }
    });
});
// app.Use(async (context, next) =>
// {
//     var logger = app.Services.GetRequiredService<ILogger<Program>>();
//     logger.LogInformation($"SCHEME: {context.Request.Scheme}");
//     logger.LogInformation($"IS HTTPS: {context.Request.IsHttps}");
//     await next();
// });
// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    // app.UseDeveloperExceptionPage();
}
else
{
    // app.UseExceptionHandler("/Error"); // Production ortamında özel hata sayfası
}
app.UseCors("AllowSpecificOrigin");
// // app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.Run();
