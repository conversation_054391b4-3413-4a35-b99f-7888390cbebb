﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OrganizationManagement.Migrations
{
    /// <inheritdoc />
    public partial class om4 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Address_City_CityId",
                table: "Address");

            migrationBuilder.DropForeignKey(
                name: "FK_Address_Country_CountryId",
                table: "Address");

            migrationBuilder.DropForeignKey(
                name: "FK_Address_State_StateId",
                table: "Address");

            migrationBuilder.DropForeignKey(
                name: "FK_City_State_StateId",
                table: "City");

            migrationBuilder.DropForeignKey(
                name: "FK_Person_Country_CountryId",
                table: "Person");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonAddress_Address_AddressId",
                table: "PersonAddress");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonAddress_Person_PersonId",
                table: "PersonAddress");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonDetail_City_CityId",
                table: "PersonDetail");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonDetail_Person_Id",
                table: "PersonDetail");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonDetail_State_StateId",
                table: "PersonDetail");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonExtraFeature_ExtraFeature_ExtraFeatureId",
                table: "PersonExtraFeature");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonExtraFeature_Person_PersonId",
                table: "PersonExtraFeature");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonPassport_Person_PersonId",
                table: "PersonPassport");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonPhone_Person_PersonId",
                table: "PersonPhone");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonPhone_Phone_PhoneId",
                table: "PersonPhone");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonPosition_Person_PersonId",
                table: "PersonPosition");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonPosition_Positions_PositionId",
                table: "PersonPosition");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonUser_Person_PersonId",
                table: "PersonUser");

            migrationBuilder.DropForeignKey(
                name: "FK_State_Country_CountryId",
                table: "State");

            migrationBuilder.DropForeignKey(
                name: "FK_UnitAddress_Address_AddressId",
                table: "UnitAddress");

            migrationBuilder.DropForeignKey(
                name: "FK_UnitAddress_Units_UnitId",
                table: "UnitAddress");

            migrationBuilder.DropForeignKey(
                name: "FK_UnitExtraFeature_ExtraFeature_ExtraFeatureId",
                table: "UnitExtraFeature");

            migrationBuilder.DropForeignKey(
                name: "FK_UnitExtraFeature_Units_UnitId",
                table: "UnitExtraFeature");

            migrationBuilder.DropForeignKey(
                name: "FK_UnitPhone_Phone_PhoneId",
                table: "UnitPhone");

            migrationBuilder.DropForeignKey(
                name: "FK_UnitPhone_Units_UnitId",
                table: "UnitPhone");

            migrationBuilder.DropForeignKey(
                name: "FK_Workcenter_Units_UnitId",
                table: "Workcenter");

            migrationBuilder.DropForeignKey(
                name: "FK_Workcenter_Workcenter_ParentId",
                table: "Workcenter");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkcenterAddress_Address_AddressId",
                table: "WorkcenterAddress");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkcenterAddress_Workcenter_WorkcenterId",
                table: "WorkcenterAddress");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkcenterExtraFeature_ExtraFeature_ExtraFeatureId",
                table: "WorkcenterExtraFeature");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkcenterExtraFeature_Workcenter_WorkcenterId",
                table: "WorkcenterExtraFeature");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkcenterPhone_Phone_PhoneId",
                table: "WorkcenterPhone");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkcenterPhone_Workcenter_WorkcenterId",
                table: "WorkcenterPhone");

            migrationBuilder.DropPrimaryKey(
                name: "PK_WorkcenterPhone",
                table: "WorkcenterPhone");

            migrationBuilder.DropPrimaryKey(
                name: "PK_WorkcenterExtraFeature",
                table: "WorkcenterExtraFeature");

            migrationBuilder.DropPrimaryKey(
                name: "PK_WorkcenterAddress",
                table: "WorkcenterAddress");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Workcenter_AutoIncrementId",
                table: "Workcenter");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Workcenter",
                table: "Workcenter");

            migrationBuilder.DropPrimaryKey(
                name: "PK_UnitPhone",
                table: "UnitPhone");

            migrationBuilder.DropPrimaryKey(
                name: "PK_UnitExtraFeature",
                table: "UnitExtraFeature");

            migrationBuilder.DropPrimaryKey(
                name: "PK_UnitAddress",
                table: "UnitAddress");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_State_AutoIncrementId",
                table: "State");

            migrationBuilder.DropPrimaryKey(
                name: "PK_State",
                table: "State");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Phone_AutoIncrementId",
                table: "Phone");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Phone",
                table: "Phone");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonUser",
                table: "PersonUser");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonPosition",
                table: "PersonPosition");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonPhone",
                table: "PersonPhone");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonPassport",
                table: "PersonPassport");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonExtraFeature",
                table: "PersonExtraFeature");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonDetail",
                table: "PersonDetail");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonAddress",
                table: "PersonAddress");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Person_AutoIncrementId",
                table: "Person");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Person",
                table: "Person");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_ExtraFeature_AutoIncrementId",
                table: "ExtraFeature");

            migrationBuilder.DropPrimaryKey(
                name: "PK_ExtraFeature",
                table: "ExtraFeature");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Country_AutoIncrementId",
                table: "Country");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Country",
                table: "Country");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_City_AutoIncrementId",
                table: "City");

            migrationBuilder.DropPrimaryKey(
                name: "PK_City",
                table: "City");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Address_AutoIncrementId",
                table: "Address");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Address",
                table: "Address");

            migrationBuilder.RenameTable(
                name: "WorkcenterPhone",
                newName: "WorkcenterPhones");

            migrationBuilder.RenameTable(
                name: "WorkcenterExtraFeature",
                newName: "WorkcenterExtraFeatures");

            migrationBuilder.RenameTable(
                name: "WorkcenterAddress",
                newName: "WorkcenterAddresses");

            migrationBuilder.RenameTable(
                name: "Workcenter",
                newName: "Workcenters");

            migrationBuilder.RenameTable(
                name: "UnitPhone",
                newName: "UnitPhones");

            migrationBuilder.RenameTable(
                name: "UnitExtraFeature",
                newName: "UnitExtraFeatures");

            migrationBuilder.RenameTable(
                name: "UnitAddress",
                newName: "UnitAddresses");

            migrationBuilder.RenameTable(
                name: "State",
                newName: "States");

            migrationBuilder.RenameTable(
                name: "Phone",
                newName: "Phones");

            migrationBuilder.RenameTable(
                name: "PersonUser",
                newName: "PersonUsers");

            migrationBuilder.RenameTable(
                name: "PersonPosition",
                newName: "PersonPositions");

            migrationBuilder.RenameTable(
                name: "PersonPhone",
                newName: "PersonPhones");

            migrationBuilder.RenameTable(
                name: "PersonPassport",
                newName: "PersonPassports");

            migrationBuilder.RenameTable(
                name: "PersonExtraFeature",
                newName: "PersonExtraFeatures");

            migrationBuilder.RenameTable(
                name: "PersonDetail",
                newName: "PersonDetails");

            migrationBuilder.RenameTable(
                name: "PersonAddress",
                newName: "PersonAddresses");

            migrationBuilder.RenameTable(
                name: "Person",
                newName: "Persons");

            migrationBuilder.RenameTable(
                name: "ExtraFeature",
                newName: "ExtraFeatures");

            migrationBuilder.RenameTable(
                name: "Country",
                newName: "Countries");

            migrationBuilder.RenameTable(
                name: "City",
                newName: "Cities");

            migrationBuilder.RenameTable(
                name: "Address",
                newName: "Addresses");

            migrationBuilder.RenameIndex(
                name: "IX_WorkcenterPhone_WorkcenterId",
                table: "WorkcenterPhones",
                newName: "IX_WorkcenterPhones_WorkcenterId");

            migrationBuilder.RenameIndex(
                name: "IX_WorkcenterPhone_PhoneId",
                table: "WorkcenterPhones",
                newName: "IX_WorkcenterPhones_PhoneId");

            migrationBuilder.RenameIndex(
                name: "IX_WorkcenterExtraFeature_WorkcenterId",
                table: "WorkcenterExtraFeatures",
                newName: "IX_WorkcenterExtraFeatures_WorkcenterId");

            migrationBuilder.RenameIndex(
                name: "IX_WorkcenterExtraFeature_ExtraFeatureId",
                table: "WorkcenterExtraFeatures",
                newName: "IX_WorkcenterExtraFeatures_ExtraFeatureId");

            migrationBuilder.RenameIndex(
                name: "IX_WorkcenterAddress_WorkcenterId",
                table: "WorkcenterAddresses",
                newName: "IX_WorkcenterAddresses_WorkcenterId");

            migrationBuilder.RenameIndex(
                name: "IX_WorkcenterAddress_AddressId",
                table: "WorkcenterAddresses",
                newName: "IX_WorkcenterAddresses_AddressId");

            migrationBuilder.RenameIndex(
                name: "IX_Workcenter_WorkcenterTypeId",
                table: "Workcenters",
                newName: "IX_Workcenters_WorkcenterTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_Workcenter_UnitId",
                table: "Workcenters",
                newName: "IX_Workcenters_UnitId");

            migrationBuilder.RenameIndex(
                name: "IX_Workcenter_ParentId",
                table: "Workcenters",
                newName: "IX_Workcenters_ParentId");

            migrationBuilder.RenameIndex(
                name: "IX_UnitPhone_UnitId",
                table: "UnitPhones",
                newName: "IX_UnitPhones_UnitId");

            migrationBuilder.RenameIndex(
                name: "IX_UnitPhone_PhoneId",
                table: "UnitPhones",
                newName: "IX_UnitPhones_PhoneId");

            migrationBuilder.RenameIndex(
                name: "IX_UnitExtraFeature_UnitId",
                table: "UnitExtraFeatures",
                newName: "IX_UnitExtraFeatures_UnitId");

            migrationBuilder.RenameIndex(
                name: "IX_UnitExtraFeature_ExtraFeatureId",
                table: "UnitExtraFeatures",
                newName: "IX_UnitExtraFeatures_ExtraFeatureId");

            migrationBuilder.RenameIndex(
                name: "IX_UnitAddress_UnitId",
                table: "UnitAddresses",
                newName: "IX_UnitAddresses_UnitId");

            migrationBuilder.RenameIndex(
                name: "IX_UnitAddress_AddressId",
                table: "UnitAddresses",
                newName: "IX_UnitAddresses_AddressId");

            migrationBuilder.RenameIndex(
                name: "IX_State_CountryId",
                table: "States",
                newName: "IX_States_CountryId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonUser_PersonId",
                table: "PersonUsers",
                newName: "IX_PersonUsers_PersonId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonPosition_PositionId",
                table: "PersonPositions",
                newName: "IX_PersonPositions_PositionId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonPosition_PersonId",
                table: "PersonPositions",
                newName: "IX_PersonPositions_PersonId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonPhone_PhoneId",
                table: "PersonPhones",
                newName: "IX_PersonPhones_PhoneId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonPhone_PersonId",
                table: "PersonPhones",
                newName: "IX_PersonPhones_PersonId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonPassport_PersonId",
                table: "PersonPassports",
                newName: "IX_PersonPassports_PersonId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonExtraFeature_PersonId",
                table: "PersonExtraFeatures",
                newName: "IX_PersonExtraFeatures_PersonId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonExtraFeature_ExtraFeatureId",
                table: "PersonExtraFeatures",
                newName: "IX_PersonExtraFeatures_ExtraFeatureId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonDetail_StateId",
                table: "PersonDetails",
                newName: "IX_PersonDetails_StateId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonDetail_MaritalStatusId",
                table: "PersonDetails",
                newName: "IX_PersonDetails_MaritalStatusId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonDetail_GenderId",
                table: "PersonDetails",
                newName: "IX_PersonDetails_GenderId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonDetail_CityId",
                table: "PersonDetails",
                newName: "IX_PersonDetails_CityId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonDetail_BloodGroupId",
                table: "PersonDetails",
                newName: "IX_PersonDetails_BloodGroupId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonAddress_PersonId",
                table: "PersonAddresses",
                newName: "IX_PersonAddresses_PersonId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonAddress_AddressId",
                table: "PersonAddresses",
                newName: "IX_PersonAddresses_AddressId");

            migrationBuilder.RenameIndex(
                name: "IX_Person_TitleId",
                table: "Persons",
                newName: "IX_Persons_TitleId");

            migrationBuilder.RenameIndex(
                name: "IX_Person_CountryId",
                table: "Persons",
                newName: "IX_Persons_CountryId");

            migrationBuilder.RenameIndex(
                name: "IX_City_StateId",
                table: "Cities",
                newName: "IX_Cities_StateId");

            migrationBuilder.RenameIndex(
                name: "IX_Address_StateId",
                table: "Addresses",
                newName: "IX_Addresses_StateId");

            migrationBuilder.RenameIndex(
                name: "IX_Address_CountryId",
                table: "Addresses",
                newName: "IX_Addresses_CountryId");

            migrationBuilder.RenameIndex(
                name: "IX_Address_CityId",
                table: "Addresses",
                newName: "IX_Addresses_CityId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_WorkcenterPhones",
                table: "WorkcenterPhones",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_WorkcenterExtraFeatures",
                table: "WorkcenterExtraFeatures",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_WorkcenterAddresses",
                table: "WorkcenterAddresses",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Workcenters_AutoIncrementId",
                table: "Workcenters",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Workcenters",
                table: "Workcenters",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UnitPhones",
                table: "UnitPhones",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UnitExtraFeatures",
                table: "UnitExtraFeatures",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UnitAddresses",
                table: "UnitAddresses",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_States_AutoIncrementId",
                table: "States",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_States",
                table: "States",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Phones_AutoIncrementId",
                table: "Phones",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Phones",
                table: "Phones",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonUsers",
                table: "PersonUsers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonPositions",
                table: "PersonPositions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonPhones",
                table: "PersonPhones",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonPassports",
                table: "PersonPassports",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonExtraFeatures",
                table: "PersonExtraFeatures",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonDetails",
                table: "PersonDetails",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonAddresses",
                table: "PersonAddresses",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Persons_AutoIncrementId",
                table: "Persons",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Persons",
                table: "Persons",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_ExtraFeatures_AutoIncrementId",
                table: "ExtraFeatures",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_ExtraFeatures",
                table: "ExtraFeatures",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Countries_AutoIncrementId",
                table: "Countries",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Countries",
                table: "Countries",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Cities_AutoIncrementId",
                table: "Cities",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Cities",
                table: "Cities",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Addresses_AutoIncrementId",
                table: "Addresses",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Addresses",
                table: "Addresses",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Addresses_Cities_CityId",
                table: "Addresses",
                column: "CityId",
                principalTable: "Cities",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_Addresses_Countries_CountryId",
                table: "Addresses",
                column: "CountryId",
                principalTable: "Countries",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_Addresses_States_StateId",
                table: "Addresses",
                column: "StateId",
                principalTable: "States",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_Cities_States_StateId",
                table: "Cities",
                column: "StateId",
                principalTable: "States",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonAddresses_Addresses_AddressId",
                table: "PersonAddresses",
                column: "AddressId",
                principalTable: "Addresses",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonAddresses_Persons_PersonId",
                table: "PersonAddresses",
                column: "PersonId",
                principalTable: "Persons",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonDetails_Cities_CityId",
                table: "PersonDetails",
                column: "CityId",
                principalTable: "Cities",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_PersonDetails_Persons_Id",
                table: "PersonDetails",
                column: "Id",
                principalTable: "Persons",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonDetails_States_StateId",
                table: "PersonDetails",
                column: "StateId",
                principalTable: "States",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_PersonExtraFeatures_ExtraFeatures_ExtraFeatureId",
                table: "PersonExtraFeatures",
                column: "ExtraFeatureId",
                principalTable: "ExtraFeatures",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonExtraFeatures_Persons_PersonId",
                table: "PersonExtraFeatures",
                column: "PersonId",
                principalTable: "Persons",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonPassports_Persons_PersonId",
                table: "PersonPassports",
                column: "PersonId",
                principalTable: "Persons",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonPhones_Persons_PersonId",
                table: "PersonPhones",
                column: "PersonId",
                principalTable: "Persons",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonPhones_Phones_PhoneId",
                table: "PersonPhones",
                column: "PhoneId",
                principalTable: "Phones",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonPositions_Persons_PersonId",
                table: "PersonPositions",
                column: "PersonId",
                principalTable: "Persons",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonPositions_Positions_PositionId",
                table: "PersonPositions",
                column: "PositionId",
                principalTable: "Positions",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Persons_Countries_CountryId",
                table: "Persons",
                column: "CountryId",
                principalTable: "Countries",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonUsers_Persons_PersonId",
                table: "PersonUsers",
                column: "PersonId",
                principalTable: "Persons",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_States_Countries_CountryId",
                table: "States",
                column: "CountryId",
                principalTable: "Countries",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UnitAddresses_Addresses_AddressId",
                table: "UnitAddresses",
                column: "AddressId",
                principalTable: "Addresses",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UnitAddresses_Units_UnitId",
                table: "UnitAddresses",
                column: "UnitId",
                principalTable: "Units",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UnitExtraFeatures_ExtraFeatures_ExtraFeatureId",
                table: "UnitExtraFeatures",
                column: "ExtraFeatureId",
                principalTable: "ExtraFeatures",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UnitExtraFeatures_Units_UnitId",
                table: "UnitExtraFeatures",
                column: "UnitId",
                principalTable: "Units",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UnitPhones_Phones_PhoneId",
                table: "UnitPhones",
                column: "PhoneId",
                principalTable: "Phones",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UnitPhones_Units_UnitId",
                table: "UnitPhones",
                column: "UnitId",
                principalTable: "Units",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkcenterAddresses_Addresses_AddressId",
                table: "WorkcenterAddresses",
                column: "AddressId",
                principalTable: "Addresses",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkcenterAddresses_Workcenters_WorkcenterId",
                table: "WorkcenterAddresses",
                column: "WorkcenterId",
                principalTable: "Workcenters",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkcenterExtraFeatures_ExtraFeatures_ExtraFeatureId",
                table: "WorkcenterExtraFeatures",
                column: "ExtraFeatureId",
                principalTable: "ExtraFeatures",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkcenterExtraFeatures_Workcenters_WorkcenterId",
                table: "WorkcenterExtraFeatures",
                column: "WorkcenterId",
                principalTable: "Workcenters",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkcenterPhones_Phones_PhoneId",
                table: "WorkcenterPhones",
                column: "PhoneId",
                principalTable: "Phones",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkcenterPhones_Workcenters_WorkcenterId",
                table: "WorkcenterPhones",
                column: "WorkcenterId",
                principalTable: "Workcenters",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Workcenters_Units_UnitId",
                table: "Workcenters",
                column: "UnitId",
                principalTable: "Units",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_Workcenters_Workcenters_ParentId",
                table: "Workcenters",
                column: "ParentId",
                principalTable: "Workcenters",
                principalColumn: "AutoIncrementId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Addresses_Cities_CityId",
                table: "Addresses");

            migrationBuilder.DropForeignKey(
                name: "FK_Addresses_Countries_CountryId",
                table: "Addresses");

            migrationBuilder.DropForeignKey(
                name: "FK_Addresses_States_StateId",
                table: "Addresses");

            migrationBuilder.DropForeignKey(
                name: "FK_Cities_States_StateId",
                table: "Cities");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonAddresses_Addresses_AddressId",
                table: "PersonAddresses");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonAddresses_Persons_PersonId",
                table: "PersonAddresses");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonDetails_Cities_CityId",
                table: "PersonDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonDetails_Persons_Id",
                table: "PersonDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonDetails_States_StateId",
                table: "PersonDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonExtraFeatures_ExtraFeatures_ExtraFeatureId",
                table: "PersonExtraFeatures");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonExtraFeatures_Persons_PersonId",
                table: "PersonExtraFeatures");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonPassports_Persons_PersonId",
                table: "PersonPassports");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonPhones_Persons_PersonId",
                table: "PersonPhones");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonPhones_Phones_PhoneId",
                table: "PersonPhones");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonPositions_Persons_PersonId",
                table: "PersonPositions");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonPositions_Positions_PositionId",
                table: "PersonPositions");

            migrationBuilder.DropForeignKey(
                name: "FK_Persons_Countries_CountryId",
                table: "Persons");

            migrationBuilder.DropForeignKey(
                name: "FK_PersonUsers_Persons_PersonId",
                table: "PersonUsers");

            migrationBuilder.DropForeignKey(
                name: "FK_States_Countries_CountryId",
                table: "States");

            migrationBuilder.DropForeignKey(
                name: "FK_UnitAddresses_Addresses_AddressId",
                table: "UnitAddresses");

            migrationBuilder.DropForeignKey(
                name: "FK_UnitAddresses_Units_UnitId",
                table: "UnitAddresses");

            migrationBuilder.DropForeignKey(
                name: "FK_UnitExtraFeatures_ExtraFeatures_ExtraFeatureId",
                table: "UnitExtraFeatures");

            migrationBuilder.DropForeignKey(
                name: "FK_UnitExtraFeatures_Units_UnitId",
                table: "UnitExtraFeatures");

            migrationBuilder.DropForeignKey(
                name: "FK_UnitPhones_Phones_PhoneId",
                table: "UnitPhones");

            migrationBuilder.DropForeignKey(
                name: "FK_UnitPhones_Units_UnitId",
                table: "UnitPhones");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkcenterAddresses_Addresses_AddressId",
                table: "WorkcenterAddresses");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkcenterAddresses_Workcenters_WorkcenterId",
                table: "WorkcenterAddresses");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkcenterExtraFeatures_ExtraFeatures_ExtraFeatureId",
                table: "WorkcenterExtraFeatures");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkcenterExtraFeatures_Workcenters_WorkcenterId",
                table: "WorkcenterExtraFeatures");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkcenterPhones_Phones_PhoneId",
                table: "WorkcenterPhones");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkcenterPhones_Workcenters_WorkcenterId",
                table: "WorkcenterPhones");

            migrationBuilder.DropForeignKey(
                name: "FK_Workcenters_Units_UnitId",
                table: "Workcenters");

            migrationBuilder.DropForeignKey(
                name: "FK_Workcenters_Workcenters_ParentId",
                table: "Workcenters");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Workcenters_AutoIncrementId",
                table: "Workcenters");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Workcenters",
                table: "Workcenters");

            migrationBuilder.DropPrimaryKey(
                name: "PK_WorkcenterPhones",
                table: "WorkcenterPhones");

            migrationBuilder.DropPrimaryKey(
                name: "PK_WorkcenterExtraFeatures",
                table: "WorkcenterExtraFeatures");

            migrationBuilder.DropPrimaryKey(
                name: "PK_WorkcenterAddresses",
                table: "WorkcenterAddresses");

            migrationBuilder.DropPrimaryKey(
                name: "PK_UnitPhones",
                table: "UnitPhones");

            migrationBuilder.DropPrimaryKey(
                name: "PK_UnitExtraFeatures",
                table: "UnitExtraFeatures");

            migrationBuilder.DropPrimaryKey(
                name: "PK_UnitAddresses",
                table: "UnitAddresses");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_States_AutoIncrementId",
                table: "States");

            migrationBuilder.DropPrimaryKey(
                name: "PK_States",
                table: "States");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Phones_AutoIncrementId",
                table: "Phones");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Phones",
                table: "Phones");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonUsers",
                table: "PersonUsers");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Persons_AutoIncrementId",
                table: "Persons");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Persons",
                table: "Persons");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonPositions",
                table: "PersonPositions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonPhones",
                table: "PersonPhones");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonPassports",
                table: "PersonPassports");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonExtraFeatures",
                table: "PersonExtraFeatures");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonDetails",
                table: "PersonDetails");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PersonAddresses",
                table: "PersonAddresses");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_ExtraFeatures_AutoIncrementId",
                table: "ExtraFeatures");

            migrationBuilder.DropPrimaryKey(
                name: "PK_ExtraFeatures",
                table: "ExtraFeatures");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Countries_AutoIncrementId",
                table: "Countries");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Countries",
                table: "Countries");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Cities_AutoIncrementId",
                table: "Cities");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Cities",
                table: "Cities");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Addresses_AutoIncrementId",
                table: "Addresses");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Addresses",
                table: "Addresses");

            migrationBuilder.RenameTable(
                name: "Workcenters",
                newName: "Workcenter");

            migrationBuilder.RenameTable(
                name: "WorkcenterPhones",
                newName: "WorkcenterPhone");

            migrationBuilder.RenameTable(
                name: "WorkcenterExtraFeatures",
                newName: "WorkcenterExtraFeature");

            migrationBuilder.RenameTable(
                name: "WorkcenterAddresses",
                newName: "WorkcenterAddress");

            migrationBuilder.RenameTable(
                name: "UnitPhones",
                newName: "UnitPhone");

            migrationBuilder.RenameTable(
                name: "UnitExtraFeatures",
                newName: "UnitExtraFeature");

            migrationBuilder.RenameTable(
                name: "UnitAddresses",
                newName: "UnitAddress");

            migrationBuilder.RenameTable(
                name: "States",
                newName: "State");

            migrationBuilder.RenameTable(
                name: "Phones",
                newName: "Phone");

            migrationBuilder.RenameTable(
                name: "PersonUsers",
                newName: "PersonUser");

            migrationBuilder.RenameTable(
                name: "Persons",
                newName: "Person");

            migrationBuilder.RenameTable(
                name: "PersonPositions",
                newName: "PersonPosition");

            migrationBuilder.RenameTable(
                name: "PersonPhones",
                newName: "PersonPhone");

            migrationBuilder.RenameTable(
                name: "PersonPassports",
                newName: "PersonPassport");

            migrationBuilder.RenameTable(
                name: "PersonExtraFeatures",
                newName: "PersonExtraFeature");

            migrationBuilder.RenameTable(
                name: "PersonDetails",
                newName: "PersonDetail");

            migrationBuilder.RenameTable(
                name: "PersonAddresses",
                newName: "PersonAddress");

            migrationBuilder.RenameTable(
                name: "ExtraFeatures",
                newName: "ExtraFeature");

            migrationBuilder.RenameTable(
                name: "Countries",
                newName: "Country");

            migrationBuilder.RenameTable(
                name: "Cities",
                newName: "City");

            migrationBuilder.RenameTable(
                name: "Addresses",
                newName: "Address");

            migrationBuilder.RenameIndex(
                name: "IX_Workcenters_WorkcenterTypeId",
                table: "Workcenter",
                newName: "IX_Workcenter_WorkcenterTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_Workcenters_UnitId",
                table: "Workcenter",
                newName: "IX_Workcenter_UnitId");

            migrationBuilder.RenameIndex(
                name: "IX_Workcenters_ParentId",
                table: "Workcenter",
                newName: "IX_Workcenter_ParentId");

            migrationBuilder.RenameIndex(
                name: "IX_WorkcenterPhones_WorkcenterId",
                table: "WorkcenterPhone",
                newName: "IX_WorkcenterPhone_WorkcenterId");

            migrationBuilder.RenameIndex(
                name: "IX_WorkcenterPhones_PhoneId",
                table: "WorkcenterPhone",
                newName: "IX_WorkcenterPhone_PhoneId");

            migrationBuilder.RenameIndex(
                name: "IX_WorkcenterExtraFeatures_WorkcenterId",
                table: "WorkcenterExtraFeature",
                newName: "IX_WorkcenterExtraFeature_WorkcenterId");

            migrationBuilder.RenameIndex(
                name: "IX_WorkcenterExtraFeatures_ExtraFeatureId",
                table: "WorkcenterExtraFeature",
                newName: "IX_WorkcenterExtraFeature_ExtraFeatureId");

            migrationBuilder.RenameIndex(
                name: "IX_WorkcenterAddresses_WorkcenterId",
                table: "WorkcenterAddress",
                newName: "IX_WorkcenterAddress_WorkcenterId");

            migrationBuilder.RenameIndex(
                name: "IX_WorkcenterAddresses_AddressId",
                table: "WorkcenterAddress",
                newName: "IX_WorkcenterAddress_AddressId");

            migrationBuilder.RenameIndex(
                name: "IX_UnitPhones_UnitId",
                table: "UnitPhone",
                newName: "IX_UnitPhone_UnitId");

            migrationBuilder.RenameIndex(
                name: "IX_UnitPhones_PhoneId",
                table: "UnitPhone",
                newName: "IX_UnitPhone_PhoneId");

            migrationBuilder.RenameIndex(
                name: "IX_UnitExtraFeatures_UnitId",
                table: "UnitExtraFeature",
                newName: "IX_UnitExtraFeature_UnitId");

            migrationBuilder.RenameIndex(
                name: "IX_UnitExtraFeatures_ExtraFeatureId",
                table: "UnitExtraFeature",
                newName: "IX_UnitExtraFeature_ExtraFeatureId");

            migrationBuilder.RenameIndex(
                name: "IX_UnitAddresses_UnitId",
                table: "UnitAddress",
                newName: "IX_UnitAddress_UnitId");

            migrationBuilder.RenameIndex(
                name: "IX_UnitAddresses_AddressId",
                table: "UnitAddress",
                newName: "IX_UnitAddress_AddressId");

            migrationBuilder.RenameIndex(
                name: "IX_States_CountryId",
                table: "State",
                newName: "IX_State_CountryId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonUsers_PersonId",
                table: "PersonUser",
                newName: "IX_PersonUser_PersonId");

            migrationBuilder.RenameIndex(
                name: "IX_Persons_TitleId",
                table: "Person",
                newName: "IX_Person_TitleId");

            migrationBuilder.RenameIndex(
                name: "IX_Persons_CountryId",
                table: "Person",
                newName: "IX_Person_CountryId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonPositions_PositionId",
                table: "PersonPosition",
                newName: "IX_PersonPosition_PositionId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonPositions_PersonId",
                table: "PersonPosition",
                newName: "IX_PersonPosition_PersonId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonPhones_PhoneId",
                table: "PersonPhone",
                newName: "IX_PersonPhone_PhoneId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonPhones_PersonId",
                table: "PersonPhone",
                newName: "IX_PersonPhone_PersonId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonPassports_PersonId",
                table: "PersonPassport",
                newName: "IX_PersonPassport_PersonId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonExtraFeatures_PersonId",
                table: "PersonExtraFeature",
                newName: "IX_PersonExtraFeature_PersonId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonExtraFeatures_ExtraFeatureId",
                table: "PersonExtraFeature",
                newName: "IX_PersonExtraFeature_ExtraFeatureId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonDetails_StateId",
                table: "PersonDetail",
                newName: "IX_PersonDetail_StateId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonDetails_MaritalStatusId",
                table: "PersonDetail",
                newName: "IX_PersonDetail_MaritalStatusId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonDetails_GenderId",
                table: "PersonDetail",
                newName: "IX_PersonDetail_GenderId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonDetails_CityId",
                table: "PersonDetail",
                newName: "IX_PersonDetail_CityId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonDetails_BloodGroupId",
                table: "PersonDetail",
                newName: "IX_PersonDetail_BloodGroupId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonAddresses_PersonId",
                table: "PersonAddress",
                newName: "IX_PersonAddress_PersonId");

            migrationBuilder.RenameIndex(
                name: "IX_PersonAddresses_AddressId",
                table: "PersonAddress",
                newName: "IX_PersonAddress_AddressId");

            migrationBuilder.RenameIndex(
                name: "IX_Cities_StateId",
                table: "City",
                newName: "IX_City_StateId");

            migrationBuilder.RenameIndex(
                name: "IX_Addresses_StateId",
                table: "Address",
                newName: "IX_Address_StateId");

            migrationBuilder.RenameIndex(
                name: "IX_Addresses_CountryId",
                table: "Address",
                newName: "IX_Address_CountryId");

            migrationBuilder.RenameIndex(
                name: "IX_Addresses_CityId",
                table: "Address",
                newName: "IX_Address_CityId");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Workcenter_AutoIncrementId",
                table: "Workcenter",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Workcenter",
                table: "Workcenter",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_WorkcenterPhone",
                table: "WorkcenterPhone",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_WorkcenterExtraFeature",
                table: "WorkcenterExtraFeature",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_WorkcenterAddress",
                table: "WorkcenterAddress",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UnitPhone",
                table: "UnitPhone",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UnitExtraFeature",
                table: "UnitExtraFeature",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UnitAddress",
                table: "UnitAddress",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_State_AutoIncrementId",
                table: "State",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_State",
                table: "State",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Phone_AutoIncrementId",
                table: "Phone",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Phone",
                table: "Phone",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonUser",
                table: "PersonUser",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Person_AutoIncrementId",
                table: "Person",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Person",
                table: "Person",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonPosition",
                table: "PersonPosition",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonPhone",
                table: "PersonPhone",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonPassport",
                table: "PersonPassport",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonExtraFeature",
                table: "PersonExtraFeature",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonDetail",
                table: "PersonDetail",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PersonAddress",
                table: "PersonAddress",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_ExtraFeature_AutoIncrementId",
                table: "ExtraFeature",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_ExtraFeature",
                table: "ExtraFeature",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Country_AutoIncrementId",
                table: "Country",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Country",
                table: "Country",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_City_AutoIncrementId",
                table: "City",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_City",
                table: "City",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Address_AutoIncrementId",
                table: "Address",
                column: "AutoIncrementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Address",
                table: "Address",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Address_City_CityId",
                table: "Address",
                column: "CityId",
                principalTable: "City",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_Address_Country_CountryId",
                table: "Address",
                column: "CountryId",
                principalTable: "Country",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_Address_State_StateId",
                table: "Address",
                column: "StateId",
                principalTable: "State",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_City_State_StateId",
                table: "City",
                column: "StateId",
                principalTable: "State",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Person_Country_CountryId",
                table: "Person",
                column: "CountryId",
                principalTable: "Country",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonAddress_Address_AddressId",
                table: "PersonAddress",
                column: "AddressId",
                principalTable: "Address",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonAddress_Person_PersonId",
                table: "PersonAddress",
                column: "PersonId",
                principalTable: "Person",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonDetail_City_CityId",
                table: "PersonDetail",
                column: "CityId",
                principalTable: "City",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_PersonDetail_Person_Id",
                table: "PersonDetail",
                column: "Id",
                principalTable: "Person",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonDetail_State_StateId",
                table: "PersonDetail",
                column: "StateId",
                principalTable: "State",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_PersonExtraFeature_ExtraFeature_ExtraFeatureId",
                table: "PersonExtraFeature",
                column: "ExtraFeatureId",
                principalTable: "ExtraFeature",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonExtraFeature_Person_PersonId",
                table: "PersonExtraFeature",
                column: "PersonId",
                principalTable: "Person",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonPassport_Person_PersonId",
                table: "PersonPassport",
                column: "PersonId",
                principalTable: "Person",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonPhone_Person_PersonId",
                table: "PersonPhone",
                column: "PersonId",
                principalTable: "Person",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonPhone_Phone_PhoneId",
                table: "PersonPhone",
                column: "PhoneId",
                principalTable: "Phone",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonPosition_Person_PersonId",
                table: "PersonPosition",
                column: "PersonId",
                principalTable: "Person",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonPosition_Positions_PositionId",
                table: "PersonPosition",
                column: "PositionId",
                principalTable: "Positions",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PersonUser_Person_PersonId",
                table: "PersonUser",
                column: "PersonId",
                principalTable: "Person",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_State_Country_CountryId",
                table: "State",
                column: "CountryId",
                principalTable: "Country",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UnitAddress_Address_AddressId",
                table: "UnitAddress",
                column: "AddressId",
                principalTable: "Address",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UnitAddress_Units_UnitId",
                table: "UnitAddress",
                column: "UnitId",
                principalTable: "Units",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UnitExtraFeature_ExtraFeature_ExtraFeatureId",
                table: "UnitExtraFeature",
                column: "ExtraFeatureId",
                principalTable: "ExtraFeature",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UnitExtraFeature_Units_UnitId",
                table: "UnitExtraFeature",
                column: "UnitId",
                principalTable: "Units",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UnitPhone_Phone_PhoneId",
                table: "UnitPhone",
                column: "PhoneId",
                principalTable: "Phone",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UnitPhone_Units_UnitId",
                table: "UnitPhone",
                column: "UnitId",
                principalTable: "Units",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Workcenter_Units_UnitId",
                table: "Workcenter",
                column: "UnitId",
                principalTable: "Units",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_Workcenter_Workcenter_ParentId",
                table: "Workcenter",
                column: "ParentId",
                principalTable: "Workcenter",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_WorkcenterAddress_Address_AddressId",
                table: "WorkcenterAddress",
                column: "AddressId",
                principalTable: "Address",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkcenterAddress_Workcenter_WorkcenterId",
                table: "WorkcenterAddress",
                column: "WorkcenterId",
                principalTable: "Workcenter",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkcenterExtraFeature_ExtraFeature_ExtraFeatureId",
                table: "WorkcenterExtraFeature",
                column: "ExtraFeatureId",
                principalTable: "ExtraFeature",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkcenterExtraFeature_Workcenter_WorkcenterId",
                table: "WorkcenterExtraFeature",
                column: "WorkcenterId",
                principalTable: "Workcenter",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkcenterPhone_Phone_PhoneId",
                table: "WorkcenterPhone",
                column: "PhoneId",
                principalTable: "Phone",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkcenterPhone_Workcenter_WorkcenterId",
                table: "WorkcenterPhone",
                column: "WorkcenterId",
                principalTable: "Workcenter",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
