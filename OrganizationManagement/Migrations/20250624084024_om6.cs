﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OrganizationManagement.Migrations
{
    /// <inheritdoc />
    public partial class om6 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Positions_PositionTypeId1",
                table: "Positions");

            migrationBuilder.DropColumn(
                name: "PositionTypeId1",
                table: "Positions");

            migrationBuilder.CreateIndex(
                name: "IX_Positions_PositionTypeId",
                table: "Positions",
                column: "PositionTypeId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Positions_PositionTypeId",
                table: "Positions");

            migrationBuilder.AddColumn<string>(
                name: "PositionTypeId1",
                table: "Positions",
                type: "text",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Positions_PositionTypeId1",
                table: "Positions",
                column: "PositionTypeId1");
        }
    }
}
