﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using OrganizationManagement.DbContexts;

#nullable disable

namespace OrganizationManagement.Migrations
{
    [DbContext(typeof(OrganizationManagementDbContext))]
    [Migration("20250516171059_om3")]
    partial class om3
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Address", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AddressText")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<int?>("CityId")
                        .HasColumnType("integer");

                    b.Property<int?>("CountryId")
                        .HasColumnType("integer");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("StateId")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("CityId");

                    b.HasIndex("CountryId");

                    b.HasIndex("StateId");

                    b.ToTable("Address");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.City", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("StateId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("StateId");

                    b.ToTable("City");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Country", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("IsoCode2")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("IsoCode3")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.ToTable("Country");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.ExtraFeature", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Title")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Value")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.ToTable("ExtraFeature");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Person", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<int>("CountryId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("IdentityNumber")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("MiddleName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Surname")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("TitleId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.HasIndex("TitleId");

                    b.ToTable("Person");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonAddress", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AddressId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("PersonId");

                    b.ToTable("PersonAddress");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonDetail", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<int?>("BloodGroupId")
                        .HasColumnType("integer");

                    b.Property<int?>("CityId")
                        .HasColumnType("integer");

                    b.Property<string>("FatherName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("GenderId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("IdCardAcceptanceDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IdCardSerialNumber")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ManualCityName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ManualStateName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("MaritalStatusId")
                        .HasColumnType("integer");

                    b.Property<string>("MotherName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("PlaceOfBirth")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("StateId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("BloodGroupId");

                    b.HasIndex("CityId");

                    b.HasIndex("GenderId");

                    b.HasIndex("MaritalStatusId");

                    b.HasIndex("StateId");

                    b.ToTable("PersonDetail");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonExtraFeature", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("ExtraFeatureId")
                        .HasColumnType("integer");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExtraFeatureId");

                    b.HasIndex("PersonId");

                    b.ToTable("PersonExtraFeature");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonPassport", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("AcceptanceDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PersonId");

                    b.ToTable("PersonPassport");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonPhone", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer");

                    b.Property<int>("PhoneId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PersonId");

                    b.HasIndex("PhoneId");

                    b.ToTable("PersonPhone");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonPosition", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer");

                    b.Property<int>("PositionId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("PersonId");

                    b.HasIndex("PositionId");

                    b.ToTable("PersonPosition");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PersonId");

                    b.ToTable("PersonUser");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Phone", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("CountryCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.ToTable("Phone");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Position", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Code")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer");

                    b.Property<int>("UnitId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex("UnitId");

                    b.ToTable("Positions");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.State", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<int>("CountryId")
                        .HasColumnType("integer");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.ToTable("State");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Unit", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Code")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer");

                    b.Property<int>("UnitTypeId")
                        .HasColumnType("integer");

                    b.Property<string>("UnitTypeId1")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex("UnitTypeId");

                    b.HasIndex("UnitTypeId1");

                    b.ToTable("Units");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.UnitAddress", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AddressId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<int>("UnitId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UnitId");

                    b.ToTable("UnitAddress");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.UnitExtraFeature", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("ExtraFeatureId")
                        .HasColumnType("integer");

                    b.Property<int>("UnitId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExtraFeatureId");

                    b.HasIndex("UnitId");

                    b.ToTable("UnitExtraFeature");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.UnitPhone", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("PhoneId")
                        .HasColumnType("integer");

                    b.Property<int>("UnitId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PhoneId");

                    b.HasIndex("UnitId");

                    b.ToTable("UnitPhone");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Workcenter", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer");

                    b.Property<int?>("UnitId")
                        .HasColumnType("integer");

                    b.Property<int>("WorkcenterTypeId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex("UnitId");

                    b.HasIndex("WorkcenterTypeId");

                    b.ToTable("Workcenter");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.WorkcenterAddress", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AddressId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<int>("WorkcenterId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("WorkcenterId");

                    b.ToTable("WorkcenterAddress");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.WorkcenterExtraFeature", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("ExtraFeatureId")
                        .HasColumnType("integer");

                    b.Property<int>("WorkcenterId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExtraFeatureId");

                    b.HasIndex("WorkcenterId");

                    b.ToTable("WorkcenterExtraFeature");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.WorkcenterPhone", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<int>("PhoneId")
                        .HasColumnType("integer");

                    b.Property<int>("WorkcenterId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PhoneId");

                    b.HasIndex("WorkcenterId");

                    b.ToTable("WorkcenterPhone");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable((string)null);

                    b.ToView("RlxEnums", (string)null);
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("EnumId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RlxEnumId")
                        .HasColumnType("text");

                    b.Property<int?>("WhichRow")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RlxEnumId");

                    b.ToTable((string)null);

                    b.ToView("RlxEnumValues", (string)null);
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxLocalizationDbContextModels.RlxLocalization", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<string>("Culture")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReferenceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable((string)null);

                    b.ToView("RlxLocalizations", (string)null);
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Address", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.City", "City")
                        .WithMany()
                        .HasForeignKey("CityId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.State", "State")
                        .WithMany()
                        .HasForeignKey("StateId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.Navigation("City");

                    b.Navigation("Country");

                    b.Navigation("State");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.City", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.State", "State")
                        .WithMany("Cities")
                        .HasForeignKey("StateId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("State");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Person", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", "Title")
                        .WithMany()
                        .HasForeignKey("TitleId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.Navigation("Country");

                    b.Navigation("Title");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonAddress", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Person", "Person")
                        .WithMany("PersonAddresses")
                        .HasForeignKey("PersonId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("Person");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonDetail", b =>
                {
                    b.HasOne("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", "BloodGroup")
                        .WithMany()
                        .HasForeignKey("BloodGroupId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.City", "City")
                        .WithMany()
                        .HasForeignKey("CityId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.HasOne("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", "Gender")
                        .WithMany()
                        .HasForeignKey("GenderId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Person", "Person")
                        .WithOne()
                        .HasForeignKey("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonDetail", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", "MaritalStatus")
                        .WithMany()
                        .HasForeignKey("MaritalStatusId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.State", "State")
                        .WithMany()
                        .HasForeignKey("StateId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.Navigation("BloodGroup");

                    b.Navigation("City");

                    b.Navigation("Gender");

                    b.Navigation("MaritalStatus");

                    b.Navigation("Person");

                    b.Navigation("State");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonExtraFeature", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.ExtraFeature", "ExtraFeature")
                        .WithMany()
                        .HasForeignKey("ExtraFeatureId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Person", "Person")
                        .WithMany("PersonExtraFeatures")
                        .HasForeignKey("PersonId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExtraFeature");

                    b.Navigation("Person");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonPassport", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Person", "Person")
                        .WithMany("PersonPassports")
                        .HasForeignKey("PersonId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Person");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonPhone", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Person", "Person")
                        .WithMany("PersonPhones")
                        .HasForeignKey("PersonId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Phone", "Phone")
                        .WithMany()
                        .HasForeignKey("PhoneId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Person");

                    b.Navigation("Phone");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonPosition", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Person", "Person")
                        .WithMany("PersonPositions")
                        .HasForeignKey("PersonId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Position", "Position")
                        .WithMany("PersonPositions")
                        .HasForeignKey("PositionId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Person");

                    b.Navigation("Position");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.PersonUser", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Person", "Person")
                        .WithMany("PersonUsers")
                        .HasForeignKey("PersonId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Person");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Position", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Position", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Unit", "Unit")
                        .WithMany("Positions")
                        .HasForeignKey("UnitId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Parent");

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.State", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Country", "Country")
                        .WithMany("States")
                        .HasForeignKey("CountryId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Unit", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Unit", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.HasOne("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", null)
                        .WithMany()
                        .HasForeignKey("UnitTypeId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", "UnitType")
                        .WithMany()
                        .HasForeignKey("UnitTypeId1");

                    b.Navigation("Parent");

                    b.Navigation("UnitType");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.UnitAddress", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Unit", "Unit")
                        .WithMany("UnitAddresses")
                        .HasForeignKey("UnitId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.UnitExtraFeature", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.ExtraFeature", "ExtraFeature")
                        .WithMany()
                        .HasForeignKey("ExtraFeatureId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Unit", "Unit")
                        .WithMany("UnitExtraFeatures")
                        .HasForeignKey("UnitId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExtraFeature");

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.UnitPhone", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Phone", "Phone")
                        .WithMany()
                        .HasForeignKey("PhoneId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Unit", "Unit")
                        .WithMany("UnitPhones")
                        .HasForeignKey("UnitId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Phone");

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Workcenter", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Workcenter", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Unit", "Unit")
                        .WithMany()
                        .HasForeignKey("UnitId")
                        .HasPrincipalKey("AutoIncrementId");

                    b.HasOne("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", "WorkcenterType")
                        .WithMany()
                        .HasForeignKey("WorkcenterTypeId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Parent");

                    b.Navigation("Unit");

                    b.Navigation("WorkcenterType");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.WorkcenterAddress", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Workcenter", "Workcenter")
                        .WithMany("WorkcenterAddresses")
                        .HasForeignKey("WorkcenterId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("Workcenter");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.WorkcenterExtraFeature", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.ExtraFeature", "ExtraFeature")
                        .WithMany()
                        .HasForeignKey("ExtraFeatureId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Workcenter", "Workcenter")
                        .WithMany("WorkcenterExtraFeatures")
                        .HasForeignKey("WorkcenterId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExtraFeature");

                    b.Navigation("Workcenter");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.WorkcenterPhone", b =>
                {
                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Phone", "Phone")
                        .WithMany()
                        .HasForeignKey("PhoneId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OrganizationManagement.Models.OrganizationManagementDbContextModels.Workcenter", "Workcenter")
                        .WithMany("WorkcenterPhones")
                        .HasForeignKey("WorkcenterId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Phone");

                    b.Navigation("Workcenter");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", b =>
                {
                    b.HasOne("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", "RlxEnum")
                        .WithMany("RlxEnumValues")
                        .HasForeignKey("RlxEnumId");

                    b.Navigation("RlxEnum");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Country", b =>
                {
                    b.Navigation("States");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Person", b =>
                {
                    b.Navigation("PersonAddresses");

                    b.Navigation("PersonExtraFeatures");

                    b.Navigation("PersonPassports");

                    b.Navigation("PersonPhones");

                    b.Navigation("PersonPositions");

                    b.Navigation("PersonUsers");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Position", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("PersonPositions");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.State", b =>
                {
                    b.Navigation("Cities");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Unit", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("Positions");

                    b.Navigation("UnitAddresses");

                    b.Navigation("UnitExtraFeatures");

                    b.Navigation("UnitPhones");
                });

            modelBuilder.Entity("OrganizationManagement.Models.OrganizationManagementDbContextModels.Workcenter", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("WorkcenterAddresses");

                    b.Navigation("WorkcenterExtraFeatures");

                    b.Navigation("WorkcenterPhones");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", b =>
                {
                    b.Navigation("RlxEnumValues");
                });
#pragma warning restore 612, 618
        }
    }
}
