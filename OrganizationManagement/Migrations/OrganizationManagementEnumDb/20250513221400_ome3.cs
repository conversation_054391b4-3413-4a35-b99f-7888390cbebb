﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OrganizationManagement.Migrations.OrganizationManagementEnumDb
{
    /// <inheritdoc />
    public partial class ome3 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RlxEnumValues_RlxEnums_RlxEnumId",
                table: "RlxEnumValues");

            migrationBuilder.DropIndex(
                name: "IX_RlxEnumValues_RlxEnumId",
                table: "RlxEnumValues");

            migrationBuilder.DropColumn(
                name: "RlxEnumId",
                table: "RlxEnumValues");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_RlxEnums_AutoIncrementId",
                table: "RlxEnums",
                column: "AutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_RlxEnumValues_EnumId",
                table: "RlxEnumValues",
                column: "EnumId");

            migrationBuilder.AddForeignKey(
                name: "FK_RlxEnumValues_RlxEnums_EnumId",
                table: "RlxEnumValues",
                column: "EnumId",
                principalTable: "RlxEnums",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RlxEnumValues_RlxEnums_EnumId",
                table: "RlxEnumValues");

            migrationBuilder.DropIndex(
                name: "IX_RlxEnumValues_EnumId",
                table: "RlxEnumValues");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_RlxEnums_AutoIncrementId",
                table: "RlxEnums");

            migrationBuilder.AddColumn<string>(
                name: "RlxEnumId",
                table: "RlxEnumValues",
                type: "text",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_RlxEnumValues_RlxEnumId",
                table: "RlxEnumValues",
                column: "RlxEnumId");

            migrationBuilder.AddForeignKey(
                name: "FK_RlxEnumValues_RlxEnums_RlxEnumId",
                table: "RlxEnumValues",
                column: "RlxEnumId",
                principalTable: "RlxEnums",
                principalColumn: "Id");
        }
    }
}
