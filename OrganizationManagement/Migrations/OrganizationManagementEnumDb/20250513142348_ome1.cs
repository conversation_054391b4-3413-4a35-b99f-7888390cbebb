﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace OrganizationManagement.Migrations.OrganizationManagementEnumDb
{
    /// <inheritdoc />
    public partial class ome1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "RlxEnums",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Code = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RlxEnums", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RlxEnumValues",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EnumId = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Code = table.Column<string>(type: "text", nullable: true),
                    WhichRow = table.Column<int>(type: "integer", nullable: true),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false),
                    RlxEnumId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RlxEnumValues", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RlxEnumValues_RlxEnums_RlxEnumId",
                        column: x => x.RlxEnumId,
                        principalTable: "RlxEnums",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_RlxEnums_AutoIncrementId",
                table: "RlxEnums",
                column: "AutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_RlxEnumValues_AutoIncrementId",
                table: "RlxEnumValues",
                column: "AutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_RlxEnumValues_RlxEnumId",
                table: "RlxEnumValues",
                column: "RlxEnumId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RlxEnumValues");

            migrationBuilder.DropTable(
                name: "RlxEnums");
        }
    }
}
