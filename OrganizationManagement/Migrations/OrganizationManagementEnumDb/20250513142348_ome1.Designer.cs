﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using OrganizationManagement.DbContexts;

#nullable disable

namespace OrganizationManagement.Migrations.OrganizationManagementEnumDb
{
    [DbContext(typeof(OrganizationManagementEnumDbContext))]
    [Migration("20250513142348_ome1")]
    partial class ome1
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Code")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId");

                    b.ToTable("RlxEnums");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("EnumId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("RlxEnumId")
                        .HasColumnType("text");

                    b.Property<int?>("WhichRow")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId");

                    b.HasIndex("RlxEnumId");

                    b.ToTable("RlxEnumValues");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", b =>
                {
                    b.HasOne("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", "RlxEnum")
                        .WithMany("RlxEnumValues")
                        .HasForeignKey("RlxEnumId");

                    b.Navigation("RlxEnum");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", b =>
                {
                    b.Navigation("RlxEnumValues");
                });
#pragma warning restore 612, 618
        }
    }
}
