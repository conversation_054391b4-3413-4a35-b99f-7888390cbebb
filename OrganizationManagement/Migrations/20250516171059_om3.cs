﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace OrganizationManagement.Migrations
{
    /// <inheritdoc />
    public partial class om3 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Positions_Positions_PositionId",
                table: "Positions");

            migrationBuilder.DropForeignKey(
                name: "FK_Positions_Units_UnitId1",
                table: "Positions");

            migrationBuilder.DropForeignKey(
                name: "FK_Units_Units_UnitId",
                table: "Units");

            migrationBuilder.DropIndex(
                name: "IX_Positions_PositionId",
                table: "Positions");

            migrationBuilder.DropIndex(
                name: "IX_Positions_UnitId1",
                table: "Positions");

            migrationBuilder.DropColumn(
                name: "PositionId",
                table: "Positions");

            migrationBuilder.DropColumn(
                name: "UnitId1",
                table: "Positions");

            migrationBuilder.RenameColumn(
                name: "UnitId",
                table: "Units",
                newName: "UnitTypeId1");

            migrationBuilder.RenameIndex(
                name: "IX_Units_UnitId",
                table: "Units",
                newName: "IX_Units_UnitTypeId1");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Units_AutoIncrementId",
                table: "Units",
                column: "AutoIncrementId");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Positions_AutoIncrementId",
                table: "Positions",
                column: "AutoIncrementId");

            migrationBuilder.CreateTable(
                name: "Country",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    IsoCode2 = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    IsoCode3 = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Country", x => x.Id);
                    table.UniqueConstraint("AK_Country_AutoIncrementId", x => x.AutoIncrementId);
                });

            migrationBuilder.CreateTable(
                name: "ExtraFeature",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Title = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Value = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExtraFeature", x => x.Id);
                    table.UniqueConstraint("AK_ExtraFeature_AutoIncrementId", x => x.AutoIncrementId);
                });

            migrationBuilder.CreateTable(
                name: "Phone",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    CountryCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Number = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Phone", x => x.Id);
                    table.UniqueConstraint("AK_Phone_AutoIncrementId", x => x.AutoIncrementId);
                });

            migrationBuilder.CreateTable(
                name: "Workcenter",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ParentId = table.Column<int>(type: "integer", nullable: true),
                    WorkcenterTypeId = table.Column<int>(type: "integer", nullable: false),
                    UnitId = table.Column<int>(type: "integer", nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Workcenter", x => x.Id);
                    table.UniqueConstraint("AK_Workcenter_AutoIncrementId", x => x.AutoIncrementId);
                    table.ForeignKey(
                        name: "FK_Workcenter_Units_UnitId",
                        column: x => x.UnitId,
                        principalTable: "Units",
                        principalColumn: "AutoIncrementId");
                    table.ForeignKey(
                        name: "FK_Workcenter_Workcenter_ParentId",
                        column: x => x.ParentId,
                        principalTable: "Workcenter",
                        principalColumn: "AutoIncrementId");
                });

            migrationBuilder.CreateTable(
                name: "Person",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    IdentityNumber = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Surname = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    MiddleName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    DateOfBirth = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CountryId = table.Column<int>(type: "integer", nullable: false),
                    TitleId = table.Column<int>(type: "integer", nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Person", x => x.Id);
                    table.UniqueConstraint("AK_Person_AutoIncrementId", x => x.AutoIncrementId);
                    table.ForeignKey(
                        name: "FK_Person_Country_CountryId",
                        column: x => x.CountryId,
                        principalTable: "Country",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "State",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Code = table.Column<string>(type: "text", nullable: true),
                    CountryId = table.Column<int>(type: "integer", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_State", x => x.Id);
                    table.UniqueConstraint("AK_State_AutoIncrementId", x => x.AutoIncrementId);
                    table.ForeignKey(
                        name: "FK_State_Country_CountryId",
                        column: x => x.CountryId,
                        principalTable: "Country",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UnitExtraFeature",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    UnitId = table.Column<int>(type: "integer", nullable: false),
                    ExtraFeatureId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UnitExtraFeature", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UnitExtraFeature_ExtraFeature_ExtraFeatureId",
                        column: x => x.ExtraFeatureId,
                        principalTable: "ExtraFeature",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UnitExtraFeature_Units_UnitId",
                        column: x => x.UnitId,
                        principalTable: "Units",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UnitPhone",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    UnitId = table.Column<int>(type: "integer", nullable: false),
                    PhoneId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UnitPhone", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UnitPhone_Phone_PhoneId",
                        column: x => x.PhoneId,
                        principalTable: "Phone",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UnitPhone_Units_UnitId",
                        column: x => x.UnitId,
                        principalTable: "Units",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkcenterExtraFeature",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    WorkcenterId = table.Column<int>(type: "integer", nullable: false),
                    ExtraFeatureId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkcenterExtraFeature", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkcenterExtraFeature_ExtraFeature_ExtraFeatureId",
                        column: x => x.ExtraFeatureId,
                        principalTable: "ExtraFeature",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WorkcenterExtraFeature_Workcenter_WorkcenterId",
                        column: x => x.WorkcenterId,
                        principalTable: "Workcenter",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkcenterPhone",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    WorkcenterId = table.Column<int>(type: "integer", nullable: false),
                    PhoneId = table.Column<int>(type: "integer", nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkcenterPhone", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkcenterPhone_Phone_PhoneId",
                        column: x => x.PhoneId,
                        principalTable: "Phone",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WorkcenterPhone_Workcenter_WorkcenterId",
                        column: x => x.WorkcenterId,
                        principalTable: "Workcenter",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PersonExtraFeature",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PersonId = table.Column<int>(type: "integer", nullable: false),
                    ExtraFeatureId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonExtraFeature", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PersonExtraFeature_ExtraFeature_ExtraFeatureId",
                        column: x => x.ExtraFeatureId,
                        principalTable: "ExtraFeature",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PersonExtraFeature_Person_PersonId",
                        column: x => x.PersonId,
                        principalTable: "Person",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PersonPassport",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PersonId = table.Column<int>(type: "integer", nullable: false),
                    Number = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    AcceptanceDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonPassport", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PersonPassport_Person_PersonId",
                        column: x => x.PersonId,
                        principalTable: "Person",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PersonPhone",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PersonId = table.Column<int>(type: "integer", nullable: false),
                    PhoneId = table.Column<int>(type: "integer", nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonPhone", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PersonPhone_Person_PersonId",
                        column: x => x.PersonId,
                        principalTable: "Person",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PersonPhone_Phone_PhoneId",
                        column: x => x.PhoneId,
                        principalTable: "Phone",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PersonPosition",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PersonId = table.Column<int>(type: "integer", nullable: false),
                    PositionId = table.Column<int>(type: "integer", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonPosition", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PersonPosition_Person_PersonId",
                        column: x => x.PersonId,
                        principalTable: "Person",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PersonPosition_Positions_PositionId",
                        column: x => x.PositionId,
                        principalTable: "Positions",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PersonUser",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PersonId = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonUser", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PersonUser_Person_PersonId",
                        column: x => x.PersonId,
                        principalTable: "Person",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "City",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    StateId = table.Column<int>(type: "integer", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_City", x => x.Id);
                    table.UniqueConstraint("AK_City_AutoIncrementId", x => x.AutoIncrementId);
                    table.ForeignKey(
                        name: "FK_City_State_StateId",
                        column: x => x.StateId,
                        principalTable: "State",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Address",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Title = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AddressText = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    PostalCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    StateId = table.Column<int>(type: "integer", nullable: true),
                    CountryId = table.Column<int>(type: "integer", nullable: true),
                    CityId = table.Column<int>(type: "integer", nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Address", x => x.Id);
                    table.UniqueConstraint("AK_Address_AutoIncrementId", x => x.AutoIncrementId);
                    table.ForeignKey(
                        name: "FK_Address_City_CityId",
                        column: x => x.CityId,
                        principalTable: "City",
                        principalColumn: "AutoIncrementId");
                    table.ForeignKey(
                        name: "FK_Address_Country_CountryId",
                        column: x => x.CountryId,
                        principalTable: "Country",
                        principalColumn: "AutoIncrementId");
                    table.ForeignKey(
                        name: "FK_Address_State_StateId",
                        column: x => x.StateId,
                        principalTable: "State",
                        principalColumn: "AutoIncrementId");
                });

            migrationBuilder.CreateTable(
                name: "PersonDetail",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    MotherName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    FatherName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    PlaceOfBirth = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    GenderId = table.Column<int>(type: "integer", nullable: true),
                    MaritalStatusId = table.Column<int>(type: "integer", nullable: true),
                    BloodGroupId = table.Column<int>(type: "integer", nullable: true),
                    IdCardSerialNumber = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    IdCardAcceptanceDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    StateId = table.Column<int>(type: "integer", nullable: true),
                    CityId = table.Column<int>(type: "integer", nullable: true),
                    ManualCityName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ManualStateName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PersonDetail_City_CityId",
                        column: x => x.CityId,
                        principalTable: "City",
                        principalColumn: "AutoIncrementId");
                    table.ForeignKey(
                        name: "FK_PersonDetail_Person_Id",
                        column: x => x.Id,
                        principalTable: "Person",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PersonDetail_State_StateId",
                        column: x => x.StateId,
                        principalTable: "State",
                        principalColumn: "AutoIncrementId");
                });

            migrationBuilder.CreateTable(
                name: "PersonAddress",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PersonId = table.Column<int>(type: "integer", nullable: false),
                    AddressId = table.Column<int>(type: "integer", nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonAddress", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PersonAddress_Address_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Address",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PersonAddress_Person_PersonId",
                        column: x => x.PersonId,
                        principalTable: "Person",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UnitAddress",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    UnitId = table.Column<int>(type: "integer", nullable: false),
                    AddressId = table.Column<int>(type: "integer", nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UnitAddress", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UnitAddress_Address_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Address",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UnitAddress_Units_UnitId",
                        column: x => x.UnitId,
                        principalTable: "Units",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkcenterAddress",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    WorkcenterId = table.Column<int>(type: "integer", nullable: false),
                    AddressId = table.Column<int>(type: "integer", nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkcenterAddress", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkcenterAddress_Address_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Address",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WorkcenterAddress_Workcenter_WorkcenterId",
                        column: x => x.WorkcenterId,
                        principalTable: "Workcenter",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Units_ParentId",
                table: "Units",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_Units_UnitTypeId",
                table: "Units",
                column: "UnitTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Positions_ParentId",
                table: "Positions",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_Positions_UnitId",
                table: "Positions",
                column: "UnitId");

            migrationBuilder.CreateIndex(
                name: "IX_Address_CityId",
                table: "Address",
                column: "CityId");

            migrationBuilder.CreateIndex(
                name: "IX_Address_CountryId",
                table: "Address",
                column: "CountryId");

            migrationBuilder.CreateIndex(
                name: "IX_Address_StateId",
                table: "Address",
                column: "StateId");

            migrationBuilder.CreateIndex(
                name: "IX_City_StateId",
                table: "City",
                column: "StateId");

            migrationBuilder.CreateIndex(
                name: "IX_Person_CountryId",
                table: "Person",
                column: "CountryId");

            migrationBuilder.CreateIndex(
                name: "IX_Person_TitleId",
                table: "Person",
                column: "TitleId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonAddress_AddressId",
                table: "PersonAddress",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonAddress_PersonId",
                table: "PersonAddress",
                column: "PersonId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonDetail_BloodGroupId",
                table: "PersonDetail",
                column: "BloodGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonDetail_CityId",
                table: "PersonDetail",
                column: "CityId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonDetail_GenderId",
                table: "PersonDetail",
                column: "GenderId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonDetail_MaritalStatusId",
                table: "PersonDetail",
                column: "MaritalStatusId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonDetail_StateId",
                table: "PersonDetail",
                column: "StateId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonExtraFeature_ExtraFeatureId",
                table: "PersonExtraFeature",
                column: "ExtraFeatureId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonExtraFeature_PersonId",
                table: "PersonExtraFeature",
                column: "PersonId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonPassport_PersonId",
                table: "PersonPassport",
                column: "PersonId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonPhone_PersonId",
                table: "PersonPhone",
                column: "PersonId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonPhone_PhoneId",
                table: "PersonPhone",
                column: "PhoneId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonPosition_PersonId",
                table: "PersonPosition",
                column: "PersonId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonPosition_PositionId",
                table: "PersonPosition",
                column: "PositionId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonUser_PersonId",
                table: "PersonUser",
                column: "PersonId");

            migrationBuilder.CreateIndex(
                name: "IX_State_CountryId",
                table: "State",
                column: "CountryId");

            migrationBuilder.CreateIndex(
                name: "IX_UnitAddress_AddressId",
                table: "UnitAddress",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_UnitAddress_UnitId",
                table: "UnitAddress",
                column: "UnitId");

            migrationBuilder.CreateIndex(
                name: "IX_UnitExtraFeature_ExtraFeatureId",
                table: "UnitExtraFeature",
                column: "ExtraFeatureId");

            migrationBuilder.CreateIndex(
                name: "IX_UnitExtraFeature_UnitId",
                table: "UnitExtraFeature",
                column: "UnitId");

            migrationBuilder.CreateIndex(
                name: "IX_UnitPhone_PhoneId",
                table: "UnitPhone",
                column: "PhoneId");

            migrationBuilder.CreateIndex(
                name: "IX_UnitPhone_UnitId",
                table: "UnitPhone",
                column: "UnitId");

            migrationBuilder.CreateIndex(
                name: "IX_Workcenter_ParentId",
                table: "Workcenter",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_Workcenter_UnitId",
                table: "Workcenter",
                column: "UnitId");

            migrationBuilder.CreateIndex(
                name: "IX_Workcenter_WorkcenterTypeId",
                table: "Workcenter",
                column: "WorkcenterTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkcenterAddress_AddressId",
                table: "WorkcenterAddress",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkcenterAddress_WorkcenterId",
                table: "WorkcenterAddress",
                column: "WorkcenterId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkcenterExtraFeature_ExtraFeatureId",
                table: "WorkcenterExtraFeature",
                column: "ExtraFeatureId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkcenterExtraFeature_WorkcenterId",
                table: "WorkcenterExtraFeature",
                column: "WorkcenterId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkcenterPhone_PhoneId",
                table: "WorkcenterPhone",
                column: "PhoneId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkcenterPhone_WorkcenterId",
                table: "WorkcenterPhone",
                column: "WorkcenterId");

            migrationBuilder.AddForeignKey(
                name: "FK_Positions_Positions_ParentId",
                table: "Positions",
                column: "ParentId",
                principalTable: "Positions",
                principalColumn: "AutoIncrementId");

            migrationBuilder.AddForeignKey(
                name: "FK_Positions_Units_UnitId",
                table: "Positions",
                column: "UnitId",
                principalTable: "Units",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Units_Units_ParentId",
                table: "Units",
                column: "ParentId",
                principalTable: "Units",
                principalColumn: "AutoIncrementId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Positions_Positions_ParentId",
                table: "Positions");

            migrationBuilder.DropForeignKey(
                name: "FK_Positions_Units_UnitId",
                table: "Positions");

            migrationBuilder.DropForeignKey(
                name: "FK_Units_Units_ParentId",
                table: "Units");

            migrationBuilder.DropTable(
                name: "PersonAddress");

            migrationBuilder.DropTable(
                name: "PersonDetail");

            migrationBuilder.DropTable(
                name: "PersonExtraFeature");

            migrationBuilder.DropTable(
                name: "PersonPassport");

            migrationBuilder.DropTable(
                name: "PersonPhone");

            migrationBuilder.DropTable(
                name: "PersonPosition");

            migrationBuilder.DropTable(
                name: "PersonUser");

            migrationBuilder.DropTable(
                name: "UnitAddress");

            migrationBuilder.DropTable(
                name: "UnitExtraFeature");

            migrationBuilder.DropTable(
                name: "UnitPhone");

            migrationBuilder.DropTable(
                name: "WorkcenterAddress");

            migrationBuilder.DropTable(
                name: "WorkcenterExtraFeature");

            migrationBuilder.DropTable(
                name: "WorkcenterPhone");

            migrationBuilder.DropTable(
                name: "Person");

            migrationBuilder.DropTable(
                name: "Address");

            migrationBuilder.DropTable(
                name: "ExtraFeature");

            migrationBuilder.DropTable(
                name: "Phone");

            migrationBuilder.DropTable(
                name: "Workcenter");

            migrationBuilder.DropTable(
                name: "City");

            migrationBuilder.DropTable(
                name: "State");

            migrationBuilder.DropTable(
                name: "Country");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Units_AutoIncrementId",
                table: "Units");

            migrationBuilder.DropIndex(
                name: "IX_Units_ParentId",
                table: "Units");

            migrationBuilder.DropIndex(
                name: "IX_Units_UnitTypeId",
                table: "Units");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Positions_AutoIncrementId",
                table: "Positions");

            migrationBuilder.DropIndex(
                name: "IX_Positions_ParentId",
                table: "Positions");

            migrationBuilder.DropIndex(
                name: "IX_Positions_UnitId",
                table: "Positions");

            migrationBuilder.RenameColumn(
                name: "UnitTypeId1",
                table: "Units",
                newName: "UnitId");

            migrationBuilder.RenameIndex(
                name: "IX_Units_UnitTypeId1",
                table: "Units",
                newName: "IX_Units_UnitId");

            migrationBuilder.AddColumn<string>(
                name: "PositionId",
                table: "Positions",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UnitId1",
                table: "Positions",
                type: "text",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Positions_PositionId",
                table: "Positions",
                column: "PositionId");

            migrationBuilder.CreateIndex(
                name: "IX_Positions_UnitId1",
                table: "Positions",
                column: "UnitId1");

            migrationBuilder.AddForeignKey(
                name: "FK_Positions_Positions_PositionId",
                table: "Positions",
                column: "PositionId",
                principalTable: "Positions",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Positions_Units_UnitId1",
                table: "Positions",
                column: "UnitId1",
                principalTable: "Units",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Units_Units_UnitId",
                table: "Units",
                column: "UnitId",
                principalTable: "Units",
                principalColumn: "Id");
        }
    }
}
