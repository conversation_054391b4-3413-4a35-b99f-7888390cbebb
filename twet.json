["real-time AI assistant for calls and meetings", "Adı Bilge Özşar. Tanışmıyoruz. 20'lerin<PERSON> olmal<PERSON>. Videoyu izlerken \"birini biliyorsam terbiyesizim\" dedim. Müzik denince çok az şey dışında son 4-5 yıl yok bende. Önerilerini dinledim. Hiç boş yok. Bizim Çizkolik sergilerindeki genç sanatçılar gibi ondan da yeni şeyler öğrendim", "OMG Cursor and Windsurf is so cooked \n\n\n@kirodotdev\n a new AI IDE just dropped \n>> Uses Claude Sonnet 4\n>> Free to try (50 interactions/mo)\n>> Better UX\n>> Start cooking!\n\nOne-shotted this Airbnb NextJS app under a minute! ", "Take a peek behind the scenes of a senior dev’s awarded portfolio. Clone it, learn from it, build your own.", "headless browser automation in Docker", "Dünden beri \n@Kilo_Code\n kullanıyorum. Bu orchestrator <PERSON><PERSON><PERSON><PERSON><PERSON> neymiş böyle aklım çıktı. \n\nNe istediğinizi anlatıyorsunuz önce architect moduna geçiş kendi kendine plan yapıyor sonra onları tasklara bölüyor code mode’una geçip her şeyi adım adım yaparak gidiyor. <PERSON><PERSON> durmadan hem", "kilo code > 10x cline falan bu arada", "tools for building gen ai agents that connect to databases", "Son zamanlarda ASO ve marketing stratejilerinde en çok verim aldığım ülkelerden birisi Thailand. RPD oranları çok yüksek. Japonya'yı zaten anlatmaya gerek yok. Endonezya ve Malezya büyük fırsat ülkesi. Doğru stratejilerle muazzam acq sağlanabiliyor. Ama yerelleştirmesi çok zor.", "<PERSON>bil uygu<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rme yaparken Endonezce, Japonca, Tayca ve Malayca eklemeyi bir deneyin.\n\nBu bölgenin insanı mobil uygulamaya para harcamayı garip şekilde çok sever. Öğrencisinden tut, en salaş sokak satıcısı bile mobil uygulamaya para", "monitors docker logs for keywords, sends notifications", "Bu aralar app fikri için en sık kullandığımız yöntemlerden biri bu \n\nİnsanlar ihtiyaç duydukları çözümleri aslında açık açık yazıyor. Özellikle Reddit gibi platformlarda.\n\nSadece şunu Google'da aratarak:\n\n site:reddit com \"is there a tool that\"\n\nGerçek kullanıcı ihtiyaçlarına", "some guy at Mastercard prompt injected a job posting and just days later it tricked somebody’s ai ", "open-source browser API for automating web tasks with AI agents", "Automates online income tasks like posting on Twitter, generating YouTube Shorts, and handling affiliate marketing", "promptu yaz, otom<PERSON><PERSON><PERSON> al (<PERSON><PERSON> olmana gerek yok)", "İncelemek isteyenler için ", "App Store'da ilk görseliniz nasıl olmalı?\n\nApp Store’da kullanıcılar kararını genellikle ilk görsele bakarak verir. Bu yüzden bu görsel, ana hikayeyi tek karede anlatmalı.\n\nİyi bir App Store görseli sadece “güzel” olmamalı. İlk birkaç saniyede faydayı anlatmalı ve kullanıcıyı", "N8N’in local ortamda nasıl kurulacağını kısa ve anlaşılır bir şekilde açıklamaya çalıştım.\n\nUmarım faydalı olur ", "microsandbox: self-hosted platform for running untrusted code with isolation", "- Onboarding’ten hemen sonra review gösterin.\n- <PERSON><PERSON><PERSON><PERSON> keywordler b<PERSON><PERSON><PERSON>, uy<PERSON>lama ismi en sona\n- Screenshotlara localization yapın\n- Görsellerde contrast kullanın koskoca Apple OCR’ı okuyamaz sonra \n- Description indexlenmiyor\n- Event isimleri, satın alma isimleri indexleniyor ", "Son bir senedir twitterda paylaşılan aso bilgisi\n- komisyon yüzde 15 e düşüyor small business e katılınca (bu aso işinde bir şeye yaramıyor tabi)\n- uygulama adını sona koy\n-etkinlik ekle hızlı review girsin\nGenelde paylaşılan şeyler hep aynı yeni hiçbir şey yok. Varsa link", "ntfy: send notifications to your phone or desktop using simple HTTP or REST API calls", "If you're a software engineer who wants to learn the core concepts about system design and distributed systems, read these 12 articles: ↓", "Open-source tool for making dashboards from databases and APIs", "Introducing Gemini CLI, a light and powerful open-source AI agent that brings Gemini directly into your terminal. >_\n\nWrite code, debug, and automate tasks with Gemini 2.5 Pro with industry-leading high usage limits at no cost.", "geçen mekanda Yunan salatası olarak yediğim salata aklımdan çıkmayınca ufak bi araştırdım, adı da Horiatiki imiş. Hemen yaptım \n\nmalum havalar sıcak, artık menülerimiz hep böyle salata türleri ya da soğuk mezelerden gidecek \n\nbir sonraki gün öğünleriniz için düzenli", "Guide for running and managing your own software and servers at home or work", "claude code köpe<PERSON><PERSON>z ola<PERSON>k, bunu kaydedin.\n\naşağıdaki vereceğim şeyi yazmak istediğinizi yazıp sonuna ekliyorsunuz ve sadece bekliyorsunuz 20-30 dakika aralıksız şekilde istediğinizi yapacak.\n\n\"before acting on this request, fully scan and understand the entire codebase. gather", "Google, canlı müzik üreten açık kaynak modelini yayınladı!\n\nLink: https://magenta.withgoogle.com/magenta-realtime…", "Python API for getting YouTube video transcripts and subtitles, including auto-generated ones", "Cloud storage for photos, videos, and 2FA codes with end-to-end encryption", "Açık kaynaklı projem <PERSON> MCP, artık güncel yatırım fonu verilerine de erişiyor.\n\nYeni eklediğim araçlar sayesinde LLM modelleri artık,\n\n- Güncel fon bilgilerine ulaşabilir.\n- Fonların performanslarını görüntüleyebilir ve kıyaslayabilir.\n- Fon içeriğininin hem güncel hem de", "MCP server and Chrome extension letting AI apps control your browser", "Eğer app yapmak istiyor ve hâlâ son üç aydır e-ticaret klonu yapmak dışında hiç bir fikir bula<PERSON>z, buyurun sizin için para kazanabileceğiniz 8 farklı kategoride 12 tane app fikri \n– Fitness → AI meal planner\n– Learning → AI study buddy, language coach\n– Productivity →", "Mo<PERSON><PERSON><PERSON><PERSON> ben de severek kullanıyorum. Fakat kendinizi <PERSON><PERSON><PERSON> ile kısıtlamayıp aşağıdaki web sitelerine de göz atmanızı tavsiye ederim \n\nhttp://refero.design\nhttp://nicelydone.club\nhttp://appshots.design\nhttp://pageflows.com\nhttp://uxarchive.com", "Cursor ultra tip\n\nconnect cursor to chrome\n\nask the ai to browse a website\n\nthen ask it to create a clone\n\nget a similar website in minutes", "Next.js ve shadcn/ui ile proje gel<PERSON>, \"keşke şöyle hazır bir component olsaydı da burada hemen kullansaydım\" dediğim anlar için çok güzel bir repo buldum.\n\n\"awesome-shadcn/ui\" reposu, shadcn ekosistemi için özenle seçilmiş yüzlerce component, kütüphane ve aracı bir araya", "Yeni açık kaynaklı projem: Borsa MCP.\n\nBorsa MCP ile LLM modelleri,\n\nBIST'te işlem gören güncel şirket listesi ve kodlarını alabilir,\nŞirketler hakkında kapsamlı bilgiler alabilir,\nŞirketlerin bilançolarını, kar-zarar ve nakit akışı tablolarını yıllık ve çeyreklik olarak", "Manus çıldırdı :)) Bugünden itibaren chat modunu devreye aldı.  Herkese ÜCRETSİZ ve SINIRSIZ :)) \n\nİstediğiniz soruyu sorun ve cevabınızı alın.\nBana gelen \"Abi ücretsiz yapay zeka var mı?\" sorusu da cevaplanmış oldu.\n\nDerin araştırma gerektiren sorularınız için ajanı devreye", "Selamlar Dostlar   Kodlama yapmadan LLM modellerinizi oluşturmanız için açık kaynaklı olarak geliştirip paylaştığım LLMRipper aracımın v2'sini tamamladım  Bu versiyon ile;\n Eğitimlerinizde LoRA veya Full Fine-Tuning seçme desteği geldi. \n Çoklu GPU eğitimi desteği", "You must be careful when you run local MCPs, because you’re essentially executing unknown code on your machine. That’s why you shouldn’t run every npx command claims to be an MCP server.\n\nI think npx should have a sandbox mode, something like <PERSON><PERSON> has.", "Anthropic tarafından yayınlanan interaktif “Prompt Engineering” kursunu daha önce denk gelmemiş olanlar için tekrar paylaşıyorum"]