using Mapster;
using Rlx.Log.Models;

namespace Rlx.Log.Configs;

public static class MapsterConfig
{
    public static void RegisterMappings()
    {
        TypeAdapterConfig<Rlx.Shared.Models.Dtos.RlxEntityLogDto, RlxEntityLog>.NewConfig().TwoWays();
        TypeAdapterConfig<Rlx.Shared.Models.Dtos.RlxSystemLogDto, RlxSystemLog>.NewConfig().TwoWays();
        TypeAdapterConfig<Rlx.Shared.Models.Dtos.RlxRequestLogDto, RlxRequestLog>.NewConfig().TwoWays();
      
    }
}