using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
public class RlxEntityLog
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string? Id { get; set; }
    [BsonElement("date")]
    public DateTime Date { get; set; }
    [BsonElement("user_id")]
    public string? UserId { get; set; }
    [BsonElement("entity_id")]
    public string? EntityId { get; set; }
    // [BsonElement("model_name")]
    // public string? ModelName { get; set; }
    [BsonElement("entity_state")]
    public string? EntityState { get; set; }
    [BsonElement("entity_data")]
    public BsonDocument? EntityData { get; set; }
    [BsonElement("changed_props")]
    public List<EntityPropertyChangeDetail>? ChangedProperties { get; set; }
}


