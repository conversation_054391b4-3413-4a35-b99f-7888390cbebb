using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Rlx.Log.Models;

public class RlxRequestLog
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string? Id { get; set; }
    [BsonElement("start")]
    public DateTime? Start { get; set; }
    [BsonElement("end")]
    public DateTime? End { get; set; }
    [BsonElement("user_id")]
    public string? UserId { get; set; }
    [BsonElement("method")]
    public string? Method { get; set; }
    [BsonElement("path")]
    public string? Path { get; set; }
    [BsonElement("module")]
    public string? Module { get; set; }
    [BsonElement("duration")]
    public TimeSpan? Duration { get; set; }
    [BsonElement("query_string")]
    public string? QueryString { get; set; }
    [BsonElement("status_code")]
    public int? StatusCode { get; set; }

}