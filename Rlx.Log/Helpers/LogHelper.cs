// using MongoDB.Driver;
// using Rlx.Log.Interfaces;
// public class LogHelper : ILogHelper
// {
//     private readonly IMongoDatabase _database;
//     private readonly IConfiguration _configuration;
//     private readonly ILogger<LogHelper> _logger;
//     public LogHelper(ILogger<LogHelper> logger, IConfiguration configuration)
//     {
//         _logger = logger;
//         _configuration = configuration;
//         var client = new MongoClient(_configuration["LogHelper:ConnectionString"]);
//         _database = client.GetDatabase(_configuration["LogHelper:DatabaseName"]);
//     }
//     private IMongoCollection<rlxs> GetCollection(string modelName)
//     {
//         return _database.GetCollection<RlxLog>(modelName);
//     }
//     public async Task Save(string tableName, RlxLog log)
//     {
//         var collection = GetCollection(tableName);
//         await collection.InsertOneAsync(log);
//     }
// }