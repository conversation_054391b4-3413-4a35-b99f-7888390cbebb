// using MongoDB.Driver;
// using Rlx.Log.Interfaces;
// using Rlx.Shared.Factories;
// public class EntityHelper : IEntityHelper
// {
//     private readonly ILogger<LogHelper> _logger;
//     private readonly MongoService<RlxEntity> _service;
//     public EntityHelper(MongoServiceFactory<RlxEntity> factory, ILogger<LogHelper> logger, string collectionName)
//     {
//         _logger = logger;
//         _service = factory.Create("EntityHelper", collectionName);
//     }

//     public async Task Save(RlxEntity entity)
//     {
//         await _service.InsertAsync(entity);
//     }
// }