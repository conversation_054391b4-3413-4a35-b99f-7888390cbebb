<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-Rlx.Log-c3b8c389-b66c-4090-852d-f779b7473d08</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
    <PackageReference Include="MongoDB.Driver" Version="3.2.1" />
    <PackageReference Include="NEST" Version="7.17.5" />
    <PackageReference Include="RabbitMQ.Client" Version="7.0.0" />
    <PackageReference Include="Rlx.Shared" Version="1.0.0" />
    <!-- <Reference Include="Rlx.Shared">
      <HintPath>D:\code_dtx\Rlx\Rlx.Shared\bin\Debug\net8.0\Rlx.Shared.dll</HintPath>
    </Reference> -->
  </ItemGroup>
</Project>