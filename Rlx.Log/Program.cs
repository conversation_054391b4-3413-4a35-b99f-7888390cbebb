using System.Text.Json;
using System.Text.Json.Serialization;
using Rlx.Log;
using Rlx.Log.Configs;
// using Rlx.Log.Interfaces;
using Rlx.Shared.Factories;

var builder = Host.CreateApplicationBuilder(args);

builder.Configuration.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                     .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true);

builder.Configuration.AddEnvironmentVariables();
Console.WriteLine($"Environment: {builder.Environment.EnvironmentName}");
Console.WriteLine($"Environment: {JsonSerializer.Serialize(builder.Environment)}");

MapsterConfig.RegisterMappings();

builder.Services.AddHostedService<Worker>();
// builder.Services.AddSingleton<ILogHelper, LogHelper>();
// builder.Services.AddSingleton<IEntityHelper, EntityHelper>();
builder.Services.AddSingleton(typeof(MongoDbServiceFactory));

var host = builder.Build();
host.Run();
