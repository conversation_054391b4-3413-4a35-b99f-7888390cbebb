using System.Text;
using System.Text.Json;
using Mapster;
using Nest;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Rlx.Log.Models;
using Rlx.Shared.Factories;
namespace Rlx.Log;
public class Worker : BackgroundService
{
    private readonly ILogger<Worker> _logger;
    private readonly IConfiguration _configuration;
    private readonly MongoDbService _entityMongoService;
    private readonly MongoDbService _requestMongoService;
    // private readonly ILogHelper _logHelper;
    private IConnection? _connection;
    private IChannel? _channel;
    public Worker(ILogger<Worker> logger, MongoDbServiceFactory mongoDbServiceFactory, IConfiguration configuration)
    {
        _logger = logger;
        _entityMongoService = mongoDbServiceFactory.Create("EntityLog");
        _requestMongoService = mongoDbServiceFactory.Create("RequestLog");
        _configuration = configuration;
        // _logHelper = logHelper;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var factory = new ConnectionFactory() { HostName = _configuration["RabbitMQ:Host"]!, Port = int.Parse(_configuration["RabbitMQ:Port"]!), UserName = _configuration["RabbitMQ:Username"]!, Password = _configuration["RabbitMQ:Password"]! };
        _connection = await factory.CreateConnectionAsync();
        _channel = await _connection.CreateChannelAsync();
        await _channel.QueueDeclareAsync(queue: "entity_queue",
                                      durable: true,
                                      exclusive: false,
                                      autoDelete: false,
                                      arguments: null);
        await _channel.QueueDeclareAsync(queue: "system_log_queue",
                                      durable: true,
                                      exclusive: false,
                                      autoDelete: false,
                                      arguments: null);

        await _channel.QueueDeclareAsync(queue: "request_queue",
                                      durable: true,
                                      exclusive: false,
                                      autoDelete: false,
                                      arguments: null);

        var entityConsumer = new AsyncEventingBasicConsumer(_channel);
        entityConsumer.ReceivedAsync += ProcessEntityMessage;
        await _channel.BasicConsumeAsync(queue: "entity_queue", autoAck: false, consumer: entityConsumer);

        var logConsumer = new AsyncEventingBasicConsumer(_channel);
        logConsumer.ReceivedAsync += ProcessSystemLogMessage;
        await _channel.BasicConsumeAsync(queue: "system_log_queue", autoAck: false, consumer: logConsumer);

        var reqConsumer = new AsyncEventingBasicConsumer(_channel);
        reqConsumer.ReceivedAsync += ProcessRequestMessage;
        await _channel.BasicConsumeAsync(queue: "request_queue", autoAck: false, consumer: reqConsumer);

        while (!stoppingToken.IsCancellationRequested)
        {
            await Task.Delay(1000, stoppingToken);
        }
    }
    private async Task ProcessEntityMessage(object model, BasicDeliverEventArgs ea)
    {
        try
        {
            var body = ea.Body.ToArray();
            var message = Encoding.UTF8.GetString(body);
            _logger.LogInformation($"Message received: {message}");
            var messageEntity = JsonSerializer.Deserialize<Rlx.Shared.Models.Dtos.RlxEntityLogDto>(message);
            var tableName = (messageEntity?.DbName ?? "nodbname") + "." + (messageEntity?.ModelName ?? "nomodelname");
            var saveEntity = messageEntity.Adapt<RlxEntityLog>();
            await _entityMongoService.InsertAsync(saveEntity, tableName);
            await _channel!.BasicAckAsync(ea.DeliveryTag, multiple: false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Message error.");
            await _channel!.BasicNackAsync(ea.DeliveryTag, multiple: false, requeue: false);
        }
    }

    private async Task ProcessSystemLogMessage(object model, BasicDeliverEventArgs ea)
    {
        try
        {
            var body = ea.Body.ToArray();
            var message = Encoding.UTF8.GetString(body);
            _logger.LogInformation($"Message received: {message}");

            var logDto = JsonSerializer.Deserialize<Rlx.Shared.Models.Dtos.RlxSystemLogDto>(message);
            var log = logDto.Adapt<RlxSystemLog>();
            var defaultIndex = _configuration["SystemLog:DatabaseName"]!;
            var settings = new ConnectionSettings(new Uri(_configuration["SystemLog:ConnectionString"]!))
            .DefaultIndex(defaultIndex);

            var client = new ElasticClient(settings);

            var indexExistsResponse = await client.Indices.ExistsAsync(defaultIndex);

            if (!indexExistsResponse.Exists)
            {
                var createIndexResponse = await client.Indices.CreateAsync(defaultIndex, c => c
                    .Map<RlxSystemLog>(m => m.AutoMap()));
                Console.WriteLine("Index oluşturuldu.");
            }
            else
            {
                Console.WriteLine("Index zaten var.");
            }


            var response = await client.IndexDocumentAsync(log);

            if (response.IsValid)
                _logger.LogInformation("Elasticsearch saved ok.");
            else
                _logger.LogError($"Elasticsearch error: {response.DebugInformation}");

            await _channel!.BasicAckAsync(ea.DeliveryTag, multiple: false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Message error.");
            await _channel!.BasicNackAsync(ea.DeliveryTag, multiple: false, requeue: true);
        }
    }

    private async Task ProcessRequestMessage(object model, BasicDeliverEventArgs ea)
    {
        try
        {
            var body = ea.Body.ToArray();
            var message = Encoding.UTF8.GetString(body);
            _logger.LogInformation($"Message received: {message}");
            var requestLogDto = JsonSerializer.Deserialize<Rlx.Shared.Models.Dtos.RlxRequestLogDto>(message);
            var requestLog = requestLogDto.Adapt<RlxRequestLog>();
            await _requestMongoService.InsertAsync(requestLog, "logs");
            await _channel!.BasicAckAsync(ea.DeliveryTag, multiple: false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Message error.");
            await _channel!.BasicNackAsync(ea.DeliveryTag, multiple: false, requeue: false);
        }
    }
    public async ValueTask DisposeAsync()
    {
        if (_channel != null)
        {
            await _channel.CloseAsync();
            await _channel.DisposeAsync();
        }
        if (_connection != null)
        {
            await _connection.CloseAsync();
            await _connection.DisposeAsync();
        }
    }
}
