using Microsoft.AspNetCore.Identity;
using Rlx.Shared.Helpers;
namespace Rlx.Identity.Models.RlxDbContextModels;
public class RlxUserClaim : IdentityUserClaim<string>
{
 private string? _claimType;
    public override string? ClaimType
    {
        get { return _claimType; }
        set
        {
            _claimType = value;
            RefreshNormalizeAll();
        }
    }
    private string? _claimValue;
    public override string? ClaimValue
    {
        get { return _claimValue; }
        set
        {
            _claimValue = value;
            RefreshNormalizeAll();
        }
    }
    private string? _description;
    public string? Description
    {
        get { return _description; }
        set
        {
            _description = value;
            RefreshNormalizeAll();
        }
    }
    public string? NormalizedAll { get; set; }
    private void RefreshNormalizeAll()
    {
        NormalizedAll = (ClaimType + " " + ClaimValue + " " + (Description ?? "")).RlxNormalize();
    }
}