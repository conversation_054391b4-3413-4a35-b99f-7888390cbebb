using Rlx.Shared.Helpers;
namespace Rlx.Identity.Models.RlxDbContextModels;
public class RlxClaimSchema
{
    public required string Id { get; set; } = Guid.NewGuid().ToString();
    public required int AutoIncrementId { get; set; }
    private string _claimType = string.Empty;
    public required string ClaimType
    {
        get { return _claimType; }
        set
        {
            _claimType = value;
            RefreshNormalizeAll();
        }
    }
    private string _claimValue = string.Empty;
    public required string ClaimValue
    {
        get { return _claimValue; }
        set
        {
            _claimValue = value;
            RefreshNormalizeAll();
        }
    }
    private string? _description;
    public string? Description
    {
        get { return _description; }
        set
        {
            _description = value;
            RefreshNormalizeAll();
        }
    }
    public string? NormalizedAll { get; set; }
    public bool Disabled { get; set; }
    private void RefreshNormalizeAll()
    {
        NormalizedAll = (ClaimType + " " + ClaimValue + " " + (Description ?? "")).RlxNormalize();
    }
}