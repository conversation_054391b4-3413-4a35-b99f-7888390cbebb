using Microsoft.AspNetCore.Identity;
using Rlx.Shared.Helpers;
namespace Rlx.Identity.Models.RlxDbContextModels;
public class RlxUser : IdentityUser<string>
{
    public RlxUser()
    {
        Id = Guid.NewGuid().ToString();
    }
    public int AutoIncrementId { get; set; }
    private string? _fullName;
    [ProtectedPersonalData]
    public string? FullName
    {
        get => _fullName;
        set
        {
            if (!string.IsNullOrEmpty(value))
            {
                _fullName = value;
                NormalizedFullName = value.RlxNormalize();
            }
            else
            {
                _fullName = null;
                NormalizedFullName = null;
            }
        }
    }
    [ProtectedPersonalData]
    public string? NormalizedFullName { get; private set; }
    public bool Disabled { get; set; }
}