using Rlx.Shared.Models.Cos;

namespace Rlx.Identity.Models.Cos;

public class GetClaimSchemasCo
{
    public string? ClaimType { get; set; }
    public string? ClaimTypeContains { get; set; }
    public string? ClaimValue { get; set; }
    public string? ClaimValueContains { get; set; }
    public string? Description { get; set; }
    public string? DescriptionContains { get; set; }
    public string? AutoComplete { get; set; }
    public bool? Disabled { get; set; }
    public List<SortCo>? SortCriterias { get; set; }
}