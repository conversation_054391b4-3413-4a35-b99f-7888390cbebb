using System.ComponentModel.DataAnnotations;
namespace Rlx.Identity.Models.Dtos;
public class RlxRoleCreateDto
{
    [Required]
    [StringLength(256)]
    public required string Name { get; set; }
    public bool Disabled { get; set; }
}
public class RlxRoleUpdateDto : RlxRoleCreateDto
{
    [Required]
    public required string Id { get; set; }
}
public class RlxRoleDto
{
    public string? Id { get; set; }
    public string? Name { get; set; }
    public bool? Disabled { get; set; }
    public IEnumerable<ClaimDto>? RoleClaims { get; set; }
}
public class RlxRoleClaimsSaveDto
{
    [Required]
    public required string RoleId { get; set; }
    public IEnumerable<RlxRoleClaimsSaveClaimDto>? Claims { get; set; }
}
public class RlxRoleClaimsSaveClaimDto
{
    [Required]
    public required string ClaimType { get; set; }
    [Required]
    public required string ClaimValue { get; set; }
    public string? Description { get; set; }
    public bool Delete { get; set; }
}
