using System.ComponentModel.DataAnnotations;
namespace Rlx.Identity.Models.Dtos;
public class RlxUserCreateDto
{
    [Required]
    [StringLength(256)]
    public required string UserName { get; set; }
    [Required]
    public required string FullName { get; set; }
    [Required]
    [EmailAddress]
    [StringLength(256)]
    public required string Email { get; set; }
    // [Required]
    public string? PhoneNumber { get; set; }
    // [Required]
    public string? Password { get; set; }
    public bool Disabled { get; set; }
}
public class RlxUserUpdateDto : RlxUserCreateDto
{
    [Required]
    public required string Id { get; set; }
}
public class RlxUserDto
{
    public string? Id { get; set; }
    public string? UserName { get; set; }
    public string? FullName { get; set; }
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Password { get; set; }
    public bool? Disabled { get; set; }
    public IEnumerable<RlxRoleDto>? Roles { get; set; }
    public IEnumerable<ClaimDto>? UserClaims { get; set; }
}
public class RlxUserRolesSaveDto
{
    [Required]
    public required string UserId { get; set; }
    public IEnumerable<RlxUserRolesSaveRoleDto>? Roles { get; set; }
}
public class RlxUserRolesSaveRoleDto
{
    [Required]
    public required string Name { get; set; }
    public bool Delete { get; set; }
}
public class RlxUserClaimsSaveDto
{
    [Required]
    public required string UserId { get; set; }
    public IEnumerable<RlxUserClaimsSaveClaimDto>? Claims { get; set; }
}
public class RlxUserClaimsSaveClaimDto
{
    [Required]
    public required string ClaimType { get; set; }
    [Required]
    public required string ClaimValue { get; set; }
    public string? Description { get; set; }
    public bool Delete { get; set; }
}