using System.ComponentModel.DataAnnotations;
namespace Rlx.Identity.Models.Dtos;
public class RlxClaimSchemaCreateDto
{
    [Required]
    [StringLength(256)]
    public required string ClaimType { get; set; }
    [Required]
    [StringLength(256)]
    public required string ClaimValue { get; set; }
    [StringLength(256)]
    public string? Description { get; set; }
    public bool Disabled { get; set; }
}
public class RlxClaimSchemaUpdateDto : RlxClaimSchemaCreateDto
{
    [Required]
    public required string Id { get; set; }
}
public class RlxClaimSchemaDto
{
    public string? Id { get; set; }
    public string? ClaimType { get; set; }
    public string? ClaimValue { get; set; }
    public string? Description { get; set; }
    public bool? Disabled { get; set; }
}
public class RlxClaimSchemaForAutoCompleteDto
{
    public string? Id { get; set; }
    public string? ClaimType { get; set; }
    public string? ClaimValue { get; set; }
    public string? Description { get; set; }
}