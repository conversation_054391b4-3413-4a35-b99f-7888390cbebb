using System.ComponentModel.DataAnnotations;
namespace Rlx.Identity.Models.Dtos
{
    public class LoginRequestDto
    {
        [Required]
        public required string Username { get; set; }
        [Required]
        public required string Password { get; set; }
        public bool RememberMe { get; set; }
        public string? ClientId { get; set; }
        public string? ClientSecret { get; set; }
        public string? RedirectUri { get; set; }
        public string? ResponseType { get; set; }
    }
     public class OpenIddictRequestDto
    {
        public bool RememberMe { get; set; }
        public string? client_id { get; set; }
        public string? client_secret { get; set; }
        public string? redirect_uri { get; set; }
        public string? response_type { get; set; }
        public string? scope { get; set; }
    }
}