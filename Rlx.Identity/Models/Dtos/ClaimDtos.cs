using System.ComponentModel.DataAnnotations;
namespace Rlx.Identity.Models.Dtos;
public class ClaimDto
{
    public int? Id { get; set; }
    public string? ClaimType { get; set; }
    public string? ClaimValue { get; set; }
    public string? Description { get; set; }
}
public class ClaimCreateDto
{
    [Required]
    public required string ClaimType { get; set; }
    [Required]
    public required string ClaimValue { get; set; }
}
public class ClaimUpdateDto :ClaimCreateDto
{
    [Required]
    public int Id { get; set; }
}
