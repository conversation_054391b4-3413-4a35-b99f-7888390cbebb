using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Rlx.Identity.Consts;
using Rlx.Identity.Interfaces;
using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Shared.Models.Cos;
namespace Rlx.Identity.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class RlxUserController : ControllerBase
{
    private readonly IRlxUserManager _rlxUserManager;
    public RlxUserController(IRlxUserManager rlxUserManager)
    {
        _rlxUserManager = rlxUserManager;
    }
    [HttpGet]
    public IActionResult Test()
    {
        return Ok("ok");
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.getuser")]
    [HttpPost]
    public async Task<IActionResult> GetUsersAsync(PagedListCo<GetUsersCo> co)
    {
        return Ok(await _rlxUserManager.GetUsersAsync(co));
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.saveuser")]
    [HttpPost]
    public async Task<IActionResult> CreateUserAsync(RlxUserCreateDto dto)
    {
        await _rlxUserManager.CreateUserAsync(dto);
        return Ok();
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.saveuser")]
    [HttpPut]
    public async Task<IActionResult> UpdateUserAsync(RlxUserUpdateDto dto)
    {
        await _rlxUserManager.UpdateUserAsync(dto);
        return Ok();
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.getuser")]
    [HttpGet]
    public async Task<IActionResult> GetUserRolesAsync(string userId)
    {
        return Ok(await _rlxUserManager.GetUserRolesAsync(userId));
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.getuser")]
    [HttpGet]
    public async Task<IActionResult> GetUserClaims(string userId)
    {
        return Ok(await _rlxUserManager.GetUserClaimsAsync(userId));
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.saveuser")]
    [HttpPost]
    public async Task<IActionResult> SaveUserRolesAsync(RlxUserRolesSaveDto dto)
    {
        await _rlxUserManager.SaveUserRolesAsync(dto);
        return Ok();
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.saveuser")]
    [HttpPost]
    public async Task<IActionResult> SaveUserClaimsAsync(RlxUserClaimsSaveDto dto)
    {
        await _rlxUserManager.SaveUserClaimsAsync(dto);
        return Ok();
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.setpassword")]
    [HttpPut]
    public async Task<IActionResult> SetPassword(RlxSetPasswordDto dto)
    {
        await _rlxUserManager.SetPasswordAsync(dto);
        return Ok();
    }
    // [HttpDelete("{id}")]
    // public async Task<IActionResult> DeleteUserAsync(string id)
    // {
    //     await _rlxUserManager.DeleteUserAsync(id);
    //     return Ok();
    // }
}