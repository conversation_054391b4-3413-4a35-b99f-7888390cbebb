using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Rlx.Identity.Consts;
using Rlx.Identity.Interfaces;
using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Shared.Models.Cos;
namespace Rlx.Identity.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class RlxClaimSchemaController : ControllerBase
{
    private readonly IRlxClaimSchemaManager _rlxClaimSchemaManager;
    public RlxClaimSchemaController(IRlxClaimSchemaManager rlxClaimSchemaManager)
    {
        _rlxClaimSchemaManager = rlxClaimSchemaManager;
    }
    [HttpGet]
    public IActionResult Test()
    {
        return Ok("ok");
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.getclaimschema")]
    [HttpPost]
    public async Task<IActionResult> GetClaimSchemasAsync(PagedListCo<GetClaimSchemasCo> co)
    {
        return Ok(await _rlxClaimSchemaManager.GetClaimSchemasAsync(co));
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.saveclaimschema")]
    [HttpPost]
    public async Task<IActionResult> CreateClaimSchemaAsync(RlxClaimSchemaCreateDto dto)
    {
        await _rlxClaimSchemaManager.CreateClaimSchemaAsync(dto);
        return Ok();
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.saveclaimschema")]
    [HttpPut]
    public async Task<IActionResult> UpdateClaimSchemaAsync(RlxClaimSchemaUpdateDto dto)
    {
        await _rlxClaimSchemaManager.UpdateClaimSchemaAsync(dto);
        return Ok();
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.saveclaimschema")]
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteClaimSchemaAsync(string id)
    {
        if (string.IsNullOrWhiteSpace(id))
        {
            throw new ArgumentNullException("Id is required");
        }
        await _rlxClaimSchemaManager.DeleteClaimSchemaAsync(id);
        return Ok();
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.getclaimschema")]
    [HttpGet]
    public async Task<IActionResult> GetClaimSchemasForAutoCompleteAsync(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
        {
            throw new ArgumentNullException("Search text is required");
        }
        return Ok(await _rlxClaimSchemaManager.GetClaimSchemasForAutoCompleteAsync(text));
    }
}