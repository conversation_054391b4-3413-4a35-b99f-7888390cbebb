using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Rlx.Identity.Configs;
using Rlx.Identity.Consts;
using Rlx.Identity.Interfaces;
using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace Rlx.Identity.Controllers;


[Route("[controller]/[action]")]
[ApiController]
public class RlxRoleController : ControllerBase
{
    private readonly IRlxRoleManager _rlxRoleManager;

    public RlxRoleController(IRlxRoleManager rlxRoleManager)
    {
        _rlxRoleManager = rlxRoleManager;
    }

    [HttpGet]
    public IActionResult Test()
    {
        return Ok("ok");
    }


    [Authorize($"{RlxIdentityConst.PermissionActionCore}.getrole")]
    [HttpPost]
    public async Task<IActionResult> GetRolesAsync(PagedListCo<GetRolesCo> co)
    {
        return Ok(await _rlxRoleManager.GetRolesAsync(co));
    }

    [Authorize($"{RlxIdentityConst.PermissionActionCore}.getrole")]
    [HttpPost]
    public async Task<IActionResult> GetRolesForAutoCompleteAsync(PagedListCo<GetRolesCo> co)
    {
        return Ok(await _rlxRoleManager.GetRolesForAutoCompleteAsync(co));
    }

    [Authorize($"{RlxIdentityConst.PermissionActionCore}.saverole")]
    [HttpPost]
    public async Task<IActionResult> CreateRoleAsync(RlxRoleCreateDto dto)
    {
        await _rlxRoleManager.CreateRoleAsync(dto);
        return Ok();
    }

    [Authorize($"{RlxIdentityConst.PermissionActionCore}.saverole")]
    [HttpPut]
    public async Task<IActionResult> UpdateRoleAsync(RlxRoleUpdateDto dto)
    {
        await _rlxRoleManager.UpdateRoleAsync(dto);
        return Ok();
    }

    [Authorize($"{RlxIdentityConst.PermissionActionCore}.getrole")]
    [HttpGet]
    public async Task<IActionResult> GetRoleClaims(string roleId)
    {
        var claims = await _rlxRoleManager.GetRoleClaimsAsync(roleId);
        return Ok(claims);
    }

    [Authorize($"{RlxIdentityConst.PermissionActionCore}.saverole")]
    [HttpPost]
    public async Task<IActionResult> SaveRoleClaims(RlxRoleClaimsSaveDto dto)
    {
        await _rlxRoleManager.SaveRoleClaimsAsync(dto);
        return Ok();
    }

    // [HttpDelete("{id}")]
    // public async Task<IActionResult> DeleteRoleAsync(string id)
    // {
    //     await _rlxRoleManager.DeleteRoleAsync(id);
    //     return Ok();
    // }
}