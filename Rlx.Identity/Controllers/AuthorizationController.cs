using System.Security.Claims;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Abstractions;
using OpenIddict.Server.AspNetCore;
using OpenIddict.Validation.AspNetCore;
using Rlx.Identity.Models.Dtos;
using Rlx.Identity.Models.RlxDbContextModels;
using static OpenIddict.Abstractions.OpenIddictConstants;
namespace Rlx.Identity.Controllers;
[ApiController]
public class AuthorizationController : ControllerBase
{
    private readonly UserManager<RlxUser> _userManager;
    public AuthorizationController(UserManager<RlxUser> userManager)
    {
        _userManager = userManager;
    }
    [HttpGet("/connect/authorize")]
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public async Task<IActionResult> Authorize()
    {
        var request = HttpContext.GetOpenIddictServerRequest() ??
            throw new InvalidOperationException("The OpenID Connect request cannot be retrieved.");
        if (User.Identity == null || !User.Identity.IsAuthenticated)
        {
            return Challenge(new AuthenticationProperties
            {
                RedirectUri = Url.Action("Authorize", new { request.ClientId, request.Scope })
            });
        }
        var claims = new List<Claim>
        {
            new Claim(Claims.Subject, User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty),
            new Claim(Claims.Email, User.FindFirst(ClaimTypes.Email)?.Value ?? string.Empty).SetDestinations(Destinations.IdentityToken),
            new Claim(Claims.Name, User.FindFirst(ClaimTypes.Name)?.Value ?? string.Empty).SetDestinations(Destinations.IdentityToken),
            new Claim("fullname", User.FindFirst("fullname")?.Value ?? string.Empty).SetDestinations(Destinations.IdentityToken),
            new Claim(ClaimTypes.NameIdentifier, User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty)
        };
        var identity = new ClaimsIdentity(claims, Schemes.Bearer);
        // identity.AddClaim(new Claim(OpenIddictConstants.Claims.Email, User.FindFirst(ClaimTypes.Email)?.Value ?? string.Empty).SetDestinations(OpenIddictConstants.Destinations.IdentityToken));
        var principal = new ClaimsPrincipal(identity);
        principal.SetScopes(request.GetScopes());
        return SignIn(principal, OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);
    }
    [HttpPost("/connect/token")]
    public async Task<IActionResult> Exchange()
    {
        var request = HttpContext.GetOpenIddictServerRequest() ??
            throw new InvalidOperationException("The OpenID Connect request cannot be retrieved.");
        if (request.IsAuthorizationCodeGrantType())
        {
            var principal = (await HttpContext.AuthenticateAsync(OpenIddictServerAspNetCoreDefaults.AuthenticationScheme)).Principal;
            if (principal == null)
            {
                return BadRequest(new OpenIddictResponse
                {
                    Error = Errors.InvalidGrant,
                    ErrorDescription = "The token is no longer valid."
                });
            }
            return SignIn(principal, OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);
        }
        else if (request.IsRefreshTokenGrantType())
        {
            var principal = (await HttpContext.AuthenticateAsync(OpenIddictServerAspNetCoreDefaults.AuthenticationScheme)).Principal;
            if (principal == null)
            {
                return BadRequest(new OpenIddictResponse
                {
                    Error = Errors.InvalidGrant,
                    ErrorDescription = "The refresh token is no longer valid or has been revoked."
                });
            }
            return SignIn(principal, OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);
        }
        return BadRequest(new OpenIddictResponse
        {
            Error = Errors.UnsupportedGrantType,
            ErrorDescription = "The specified grant type is not supported."
        });
    }
    [HttpPost("/account/login")]
    public async Task<IActionResult> Login(LoginRequestDto model)
    {
        var user = await _userManager.FindByNameAsync(model.Username);
        if (user == null || !await _userManager.CheckPasswordAsync(user, model.Password))
        {
            return BadRequest("Invalid username or password.");
        }
        var identity = new ClaimsIdentity(OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);
        identity.AddClaim(new Claim(ClaimTypes.NameIdentifier, user.Id));
        identity.AddClaim(new Claim("fullname", user.FullName ?? ""));
        identity.AddClaim(new Claim(ClaimTypes.Name, user.UserName ?? ""));
        identity.AddClaim(new Claim(ClaimTypes.Email, user.Email ?? ""));
        var principal = new ClaimsPrincipal(identity);
        await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal);
        return Ok();
    }
    [HttpGet("~/connect/logout")]
    [HttpPost("~/connect/logout")]
    public async Task<IActionResult> Logout()
    {
        await HttpContext.SignOutAsync(OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme);
        await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
        return NoContent();
    }
    // [HttpGet("/account/testgirisdogrulama")]
    // [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    // public async Task<IActionResult> Test()
    // {
    //     if (User.Identity == null || !User.Identity.IsAuthenticated)
    //     {
    //         return BadRequest("no user");
    //     }
    //     return Ok();
    // }
    // [HttpGet("/account/testgiris")]
    // public async Task<IActionResult> Test2()
    // {
    //     var identity = new ClaimsIdentity(CookieAuthenticationDefaults.AuthenticationScheme);
    //     identity.AddClaim(new Claim("FullName", "fullname1"));
    //     identity.AddClaim(new Claim("Username", "username1"));
    //     var principal = new ClaimsPrincipal(identity);
    //     var authProperties = new AuthenticationProperties
    //     {
    //         IsPersistent = true,
    //         ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(30)
    //     };
    //     await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal, authProperties);
    //     return Ok();
    // }
}
