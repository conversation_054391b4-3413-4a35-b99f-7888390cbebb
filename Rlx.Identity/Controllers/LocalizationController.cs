using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Rlx.Identity.Configs;
using Rlx.Identity.Consts;
using Rlx.Identity.DbContexts;
using Rlx.Identity.Resources;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace Rlx.Identity.Controllers;

[Route("[controller]/[action]/{id?}")]
public class LocalizationController : RlxLocalizationControllerBase<RlxIdentityLocalizationDbContext>
{
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IRlxSystemLogHelper<HomeController> _systemLogHelper;
    public LocalizationController(RlxIdentityLocalizationDbContext dbContext, IRlxLocalizationManager<RlxIdentityLocalizationDbContext> rlxLocalizationManager, ILogger<RlxLocalizationControllerBase<RlxIdentityLocalizationDbContext>> logger, IAuthenticationSchemeProvider schemes, IStringLocalizer<SharedResource> localizer, IRlxSystemLogHelper<HomeController> systemLogHelper) : base(dbContext, rlxLocalizationManager, logger)
    {
        _localizer = localizer;
        _systemLogHelper = systemLogHelper;
    }
    [HttpGet]
    public IActionResult Test()
    {
        return Ok("ok");
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.savelocalization")]
    [HttpPost]
    public override async Task<IActionResult> SaveLocalizations(RlxLocalizationSaveDto[] dto)
    {
        return await base.SaveLocalizations(dto);
    }
    [Authorize($"{RlxIdentityConst.PermissionActionCore}.getlocalization")]
    [HttpPost]
    public override async Task<IActionResult> GetLocalizations(GetRlxLocalizationsCo co)
    {
        return await base.GetLocalizations(co);
    }
}