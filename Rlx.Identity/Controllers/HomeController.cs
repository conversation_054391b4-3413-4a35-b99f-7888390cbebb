using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Rlx.Identity.Configs;
using Rlx.Identity.Consts;
using Rlx.Identity.DbContexts;
using Rlx.Identity.Resources;
using Rlx.Shared.Interfaces;
namespace Rlx.Identity.Controllers;

[ApiController]
[Route("[controller]/[action]")]
public class HomeController : ControllerBase
{
    private readonly IRlxLocalizationManager<RlxIdentityLocalizationDbContext> _rlxLocalizationManager;
    private readonly IAuthenticationSchemeProvider _schemes;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IRlxSystemLogHelper<HomeController> _systemLogHelper;
    public HomeController(IRlxLocalizationManager<RlxIdentityLocalizationDbContext> rlxLocalizationManager, IAuthenticationSchemeProvider schemes, IStringLocalizer<SharedResource> localizer, IRlxSystemLogHelper<HomeController> systemLogHelper)
    {
        _rlxLocalizationManager = rlxLocalizationManager;
        _schemes = schemes;
        _localizer = localizer;
        _systemLogHelper = systemLogHelper;
    }
    [HttpGet]
    public IActionResult Index()
    {
        return new RedirectResult("~/swagger");
    }
    [HttpGet]
    [Authorize(Policy = $"{RlxIdentityConst.PermissionPageCore}.test")]
    public async Task<IActionResult> Test()
    {
        // await _rlxLocalizationManager.CreateRlxLocalizationAsync(new RlxLocalizationCreateDto
        // {
        //     Key = "Test",
        //     Value = "Test",
        //     Culture = "en",
        //     ReferenceId = "xyz"
        // });
        return Ok("test ok");
    }
    [HttpGet]
    public async Task<IActionResult> Test2()
    {
        return Ok("version: 1.0.3");
        // var ds = await _schemes.GetDefaultAuthenticateSchemeAsync();
        // return Ok("test2 ok: v4: " + ds?.Name);
    }
    [Authorize($"{RlxIdentityConst.PermissionPageCore}.all")]
    [HttpGet]
    public async Task<IActionResult> Test3()
    {
        return Ok("test3 ok");
    }
    // [HttpGet]
    // public async Task<IActionResult> TestLocalization()
    // {
    //     var val = _localizer["Test"];
    //     await _systemLogHelper.LogErrorAsync("test localization", new ArgumentNullException("test localization"));
    //     return Ok(val + " localization ok");
    // }
}