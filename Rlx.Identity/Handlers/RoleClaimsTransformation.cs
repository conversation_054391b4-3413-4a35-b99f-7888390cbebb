// using System.Security.Claims;
// using Microsoft.AspNetCore.Authentication;
// using Microsoft.AspNetCore.Identity;
// using Rlx.Identity.Models.RlxDbContextModels;
// using Rlx.Shared.Interfaces;
// using Rlx.Shared.Helpers;
// using OpenIddict.Abstractions;
// using Rlx.Identity.Interfaces;
// using Rlx.Identity.Models.Dtos;
// using Rlx.Identity.Consts;
// public class RoleClaimsTransformation : IClaimsTransformation
// {
//     private readonly IRlxUserManager _rlxUserManager;
//     private readonly IRlxRoleManager _rlxRoleManager;
//     private readonly IRlxCacheService _rlxCacheService;
//     public RoleClaimsTransformation(
//                                     IRlxCacheService rlxCacheService,
//                                     IRlxUserManager rlxUserManager,
//                                     IRlxRoleManager rlxRoleManager)
//     {
//         _rlxCacheService = rlxCacheService;
//         _rlxUserManager = rlxUserManager;
//         _rlxRoleManager = rlxRoleManager;
//     }
//     public async Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
//     {
//         var identity = (ClaimsIdentity?)principal.Identity;
//         if (identity == null) return principal;
//         var id = identity.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;
//         // var user = await _userManager.GetUserAsync(principal);
//         if (id == null) return principal;
//         var roles = await _rlxUserManager.GetUserRolesAsync(id);
//         foreach (var role in roles)
//         {
//             identity.AddClaim(new Claim(ClaimTypes.Role, role.Name!));
//             var roleClaimsJson = await _rlxCacheService.GetAsync(TextHelper.RlxJoin(CacheConst.RoleClaims, role.Name!));
//             var roleClaims = JsonHelper.FromJson<List<ClaimDto>>(roleClaimsJson);
//             if (roleClaims == null || roleClaims.Count == 0)
//             {
//                 // var roleId = await _rlxRoleManager.GetRoleIdByName(role.Id);
//                 roleClaims = (await _rlxRoleManager.GetRoleClaimsAsync(role.Id!))?.ToList();
//                 // roleClaims = await _roleManager.GetClaimsAsync(new RlxRole { Name = role });
//                 if (roleClaims?.Count > 0)
//                 {
//                     await _rlxCacheService.SetAsync(TextHelper.RlxJoin(CacheConst.RoleClaims, role.Name!), JsonHelper.ToJson(roleClaims));
//                 }
//             }
//             if (roleClaims == null || roleClaims.Count == 0)
//             {
//                 continue;
//             }
//             foreach (var claim in roleClaims)
//             {
//                 if (!identity.Claims.Any(c => c.Type == claim.ClaimType && c.Value == claim.ClaimValue))
//                 {
//                     identity.AddClaim(new Claim(claim.ClaimType!, claim.ClaimValue!));
//                 }
//             }
//         }
//         return principal;
//     }
// }
