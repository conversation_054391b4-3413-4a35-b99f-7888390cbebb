using Microsoft.EntityFrameworkCore;
using Rlx.Identity.DbContexts;
using Rlx.Identity.Interfaces;
using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Identity.Models.RlxDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace Rlx.Identity.Stores;

public class RlxUserStore : IRlxUserStore
{
    private readonly RlxIdentityDbContext _dbContext;
    public RlxUserStore(RlxIdentityDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<IEnumerable<ClaimDto>> GetUserClaimsAsync(string userId)
    {
        var userClaims = await (from uc in _dbContext.UserClaims
                                where uc.UserId == userId
                                select new ClaimDto
                                {
                                    Id = uc.Id,
                                    ClaimType = uc.ClaimType,
                                    ClaimValue = uc.ClaimValue,
                                    // Description = uc.Description
                                }).ToListAsync();
        return userClaims;
    }

    public async Task<IEnumerable<RlxRoleDto>> GetUserRolesAsync(string userId)
    {
        var userRoles = await (from ur in _dbContext.UserRoles
                               join r in _dbContext.Roles on ur.RoleId equals r.Id
                               where ur.UserId == userId
                               select new RlxRoleDto
                               {
                                   Id = r.Id,
                                   Name = r.Name
                               }).ToListAsync();
        return userRoles;
    }

    public async Task<PagedListDto<RlxUserDto>> GetUsersAsync(PagedListCo<GetUsersCo> co)
    {
        return await GetUserAsync(co, q => q.Select(x => new RlxUserDto
        {
            Id = x.Id,
            UserName = x.UserName,
            FullName = x.FullName,
            Email = x.Email,
            PhoneNumber = x.PhoneNumber,
            Disabled = x.Disabled
        }));
    }

    public async Task<PagedListDto<RlxUserDto>> GetUsersBasicAsync(PagedListCo<GetUsersCo> co)
    {
        return await GetUserAsync(co, q => q.Select(x => new RlxUserDto
        {
            Id = x.Id,
            UserName = x.UserName,
            FullName = x.FullName,
            PhoneNumber = x.PhoneNumber,
            Email = x.Email
        }));
    }

    public async Task<RlxUser?> GetUserByIdAsync(string userId)
    {
        var user = await _dbContext.Users.Where(x => x.Id == userId).FirstOrDefaultAsync();
        return user;

    }

    private async Task<PagedListDto<T>> GetUserAsync<T>(PagedListCo<GetUsersCo> co, Func<IQueryable<RlxUser>, IQueryable<T>> selector)
    {
        var pl = new PagedListDto<T>
        {
            Page = co.Pager.Page,
            Size = co.Pager.Size
        };
        var query = _dbContext.RlxUsers.AsQueryable();

        if (co.Criteria != null)
        {
            if (!string.IsNullOrEmpty(co.Criteria.UsernameContains))
            {
                query = query.Where(x => x.UserName != null && x.UserName.Contains(co.Criteria.UsernameContains));
            }

            if (!string.IsNullOrEmpty(co.Criteria.FullNameContains))
            {
                query = query.Where(x => x.FullName != null && x.FullName.Contains(co.Criteria.FullNameContains));
            }

            if (!string.IsNullOrEmpty(co.Criteria.EmailContains))
            {
                query = query.Where(x => x.Email != null && x.Email.Contains(co.Criteria.EmailContains));
            }

            if (!string.IsNullOrEmpty(co.Criteria.PhoneNumberContains))
            {
                query = query.Where(x => x.PhoneNumber != null && x.PhoneNumber.Contains(co.Criteria.PhoneNumberContains));
            }

            if (co.Criteria.Disabled.HasValue)
            {
                query = query.Where(x => x.Disabled == co.Criteria.Disabled);
            }
        }

        pl.Count = await query.CountAsync();
        if (pl.Count != 0)
        {
            //todo: sorting
            pl.Data = await selector(query).Skip(co.Pager.Skip).Take(co.Pager.Size).ToListAsync();
        }
        return pl;




    }

    public async Task<int> GetAutoIncrementIdAsync(string id)
    {
        return await _dbContext.RlxUsers.Where(x => x.Id == id).Select(x => x.AutoIncrementId).FirstOrDefaultAsync();
    }

    public async Task<RlxUser?> GetUser(string id)
    {
        return await _dbContext.RlxUsers.Where(x => x.Id == id).FirstOrDefaultAsync();
    }

    // public async Task SaveUserClaimsAsync(RlxUserClaim data)
    // {
    //     return await _dbContext.RlxUsers.Where(x => x.Id == id).FirstOrDefaultAsync();
    // }




}