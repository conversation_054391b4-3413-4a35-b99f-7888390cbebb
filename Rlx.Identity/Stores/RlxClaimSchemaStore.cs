using Microsoft.EntityFrameworkCore;
using Rlx.Identity.DbContexts;
using Rlx.Identity.Interfaces;
using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Identity.Models.RlxDbContextModels;
using Rlx.Shared.Helpers;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace Rlx.Identity.Stores;

public class RlxClaimSchemaStore : IRlxClaimSchemaStore
{
    private readonly RlxIdentityDbContext _dbContext;
    public RlxClaimSchemaStore(RlxIdentityDbContext dbContext)
    {
        _dbContext = dbContext;
    }
    public async Task AddClaimSchemaAsync(RlxClaimSchema data)
    {
        _dbContext.Add(data);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<PagedListDto<RlxClaimSchemaDto>> GetClaimSchemasAsync(PagedListCo<GetClaimSchemasCo> co)
    {
        return await GetClaimSchemasAsync(co, q => q.Select(x => new RlxClaimSchemaDto
        {
            Id = x.Id,
            ClaimType = x.ClaimType,
            ClaimValue = x.ClaimValue,
            Disabled = x.Disabled,
            Description = x.Description
        }));
    }

    public async Task<PagedListDto<RlxClaimSchemaDto>> GetClaimSchemasBasicAsync(PagedListCo<GetClaimSchemasCo> co)
    {
        return await GetClaimSchemasAsync(co, q => q.Select(x => new RlxClaimSchemaDto
        {
            Id = x.Id,
            ClaimType = x.ClaimType,
            ClaimValue = x.ClaimValue,
            Description = x.Description
        }));
    }

    public async Task<PagedListDto<RlxClaimSchemaForAutoCompleteDto>> GetClaimSchemasForAutoCompleteAsync(PagedListCo<GetClaimSchemasCo> co)
    {
        return await GetClaimSchemasAsync(co, q => q.Select(x => new RlxClaimSchemaForAutoCompleteDto
        {
            Id = x.Id,
            ClaimType = x.ClaimType,
            ClaimValue = x.ClaimValue,
            Description = x.Description
        }));
    }

    private async Task<PagedListDto<T>> GetClaimSchemasAsync<T>(PagedListCo<GetClaimSchemasCo> co, Func<IQueryable<RlxClaimSchema>, IQueryable<T>> selector)
    {
        var pl = new PagedListDto<T>
        {
            Page = co.Pager.Page,
            Size = co.Pager.Size
        };
        var query = _dbContext.RlxClaimSchemas.AsQueryable();

        if (co.Criteria != null)
        {
            if (!string.IsNullOrEmpty(co.Criteria.ClaimType))
            {
                query = query.Where(x => x.ClaimType == co.Criteria.ClaimType);
            }

            if (!string.IsNullOrEmpty(co.Criteria.ClaimTypeContains))
            {
                query = query.Where(x => x.ClaimType.Contains(co.Criteria.ClaimTypeContains));
            }

            if (!string.IsNullOrEmpty(co.Criteria.ClaimValue))
            {
                query = query.Where(x => x.ClaimValue == co.Criteria.ClaimValue);
            }

            if (!string.IsNullOrEmpty(co.Criteria.ClaimValueContains))
            {
                query = query.Where(x => x.ClaimValue.Contains(co.Criteria.ClaimValueContains));
            }

            if (!string.IsNullOrEmpty(co.Criteria.Description))
            {
                query = query.Where(x => x.Description == co.Criteria.Description);
            }

            if (!string.IsNullOrEmpty(co.Criteria.DescriptionContains))
            {
                query = query.Where(x => x.Description != null && x.Description.Contains(co.Criteria.DescriptionContains));
            }

            if (!string.IsNullOrEmpty(co.Criteria.AutoComplete))
            {
                query = query.Where(x => x.NormalizedAll != null && x.NormalizedAll.Contains(co.Criteria.AutoComplete.RlxNormalize()));
            }

            if (co.Criteria.Disabled.HasValue)
            {
                query = query.Where(x => x.Disabled == co.Criteria.Disabled);
            }
        }

        pl.Count = await query.CountAsync();
        if (pl.Count != 0)
        {
            //todo: sorting
            pl.Data = await selector(query).Skip(co.Pager.Skip).Take(co.Pager.Size).ToListAsync();
        }
        return pl;
    }

    public async Task<bool> ClaimSchemaExistsAsync(string claimType, string claimValue)
    {
        return await _dbContext.RlxClaimSchemas.AnyAsync(x => x.ClaimType == claimType && x.ClaimValue == claimValue);
    }

    public async Task<bool> ClaimSchemaExistsAsync(string id)
    {
        return await _dbContext.RlxClaimSchemas.AnyAsync(x => x.Id == id);
    }
    public async Task<bool> SchemaExistsWithDifferentIdAsync(string id, string claimType, string claimValue)
    {
        return await _dbContext.RlxClaimSchemas.AnyAsync(x => x.Id != id && x.ClaimType == claimType && x.ClaimValue == claimValue);
    }

    public async Task UpdateClaimSchemaAsync(RlxClaimSchema data)
    {
        _dbContext.RlxClaimSchemas.Update(data);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<int> GetAutoIncrementIdAsync(string id)
    {
        return await _dbContext.RlxClaimSchemas.Where(x => x.Id == id).Select(x => x.AutoIncrementId).FirstOrDefaultAsync();
    }

    public async Task DeleteClaimSchemaAsync(string id)
    {
        var data = await _dbContext.RlxClaimSchemas.FirstAsync(a => a.Id == id);
        _dbContext.RlxClaimSchemas.Remove(data);
        await _dbContext.SaveChangesAsync();

    }


}