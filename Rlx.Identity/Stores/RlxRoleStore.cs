using Microsoft.EntityFrameworkCore;
using Rlx.Identity.DbContexts;
using Rlx.Identity.Interfaces;
using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Identity.Models.RlxDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace Rlx.Identity.Stores;

public class RlxRoleStore : IRlxRoleStore
{
    private readonly RlxIdentityDbContext _dbContext;
    public RlxRoleStore(RlxIdentityDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<IEnumerable<ClaimDto>> GetRoleClaimsAsync(string roleId)
    {
        var userClaims = await (from uc in _dbContext.RoleClaims
                                where uc.RoleId == roleId
                                select new ClaimDto
                                {
                                    Id = uc.Id,
                                    ClaimType = uc.ClaimType,
                                    ClaimValue = uc.ClaimValue
                                }).ToListAsync();
        return userClaims;
    }

    public async Task<string?> GetRoleIdByName(string roleName)
    {
        var id = await (from uc in _dbContext.Roles
                        where uc.Name == roleName
                        select uc.Id).FirstOrDefaultAsync();
        return id;
    }

    public async Task<PagedListDto<RlxRoleDto>> GetRolesAsync(PagedListCo<GetRolesCo> co)
    {
        return await GetRoleAsync(co, q => q.Select(x => new RlxRoleDto
        {
            Id = x.Id,
            Name = x.Name,
            Disabled = x.Disabled
        }));
    }

    public async Task<PagedListDto<RlxRoleDto>> GetRolesBasicAsync(PagedListCo<GetRolesCo> co)
    {
        return await GetRoleAsync(co, q => q.Select(x => new RlxRoleDto
        {
            Id = x.Id,
            Name = x.Name,
        }));
    }

    public async Task<PagedListDto<AutoCompleteDto>> GetRolesForAutoCompleteAsync(PagedListCo<GetRolesCo> co)
    {
        return await GetRoleAsync(co, q => q.Select(x => new AutoCompleteDto
        {
            Id = x.Id,
            Name = x.Name,
        }));
    }

    public async Task<RlxRole?> GetRoleByIdAsync(string roleId)
    {
        var role = await _dbContext.Roles.Where(x => x.Id == roleId).FirstOrDefaultAsync();
        return role;

    }

    private async Task<PagedListDto<T>> GetRoleAsync<T>(PagedListCo<GetRolesCo> co, Func<IQueryable<RlxRole>, IQueryable<T>> selector)
    {
        var pl = new PagedListDto<T>
        {
            Page = co.Pager.Page,
            Size = co.Pager.Size
        };
        var query = _dbContext.RlxRoles.AsQueryable();

        if (co.Criteria != null)
        {
            if (!string.IsNullOrEmpty(co.Criteria.NameContains))
            {
                query = query.Where(x => x.NormalizedName != null && x.NormalizedName.Contains(co.Criteria.NameContains.ToUpperInvariant()));
            }

            if (co.Criteria.Disabled.HasValue)
            {
                query = query.Where(x => x.Disabled == co.Criteria.Disabled);
            }
        }

        pl.Count = await query.CountAsync();
        if (pl.Count != 0)
        {
            //todo: sorting
            pl.Data = await selector(query).Skip(co.Pager.Skip).Take(co.Pager.Size).ToListAsync();
        }
        return pl;




    }

    public async Task<int> GetAutoIncrementIdAsync(string id)
    {
        return await _dbContext.RlxRoles.Where(x => x.Id == id).Select(x => x.AutoIncrementId).FirstOrDefaultAsync();
    }


    public async Task<RlxRole?> GetRlxRole(string id)
    {
        return await _dbContext.RlxRoles.Where(x => x.Id == id).FirstOrDefaultAsync();
    }

    public async Task<bool> HasClaim(string roleId, string claimType, string claimValue)
    {
        return await _dbContext.RoleClaims.AnyAsync(x => x.RoleId == roleId && x.ClaimType == claimType && x.ClaimValue == claimValue);
    }
}