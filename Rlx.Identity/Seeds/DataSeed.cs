using Microsoft.AspNetCore.Identity;
using OpenIddict.Abstractions;
using Rlx.Identity.Interfaces;
using Rlx.Identity.Models.Dtos;
using Rlx.Identity.Models.RlxDbContextModels;
using System.Security.Claims;
using System.Text.Json;
public class DataSeed
{
    public static async Task Initialize(IServiceProvider serviceProvider)
    {
        var roleManager = serviceProvider.GetRequiredService<RoleManager<RlxRole>>();
        var userManager = serviceProvider.GetRequiredService<UserManager<RlxUser>>();
        var rlxClaimSchemaManager = serviceProvider.GetRequiredService<IRlxClaimSchemaManager>();
        var env = serviceProvider.GetRequiredService<IWebHostEnvironment>();
        var config = serviceProvider.GetRequiredService<IConfiguration>();
        #region roles
        if (!await roleManager.RoleExistsAsync("Admin"))
        {
            var adminRole = new RlxRole { Name = "Admin" };
            await roleManager.CreateAsync(adminRole);
            await roleManager.AddClaimAsync(adminRole, new Claim("permission.page.all", "all"));
            await roleManager.AddClaimAsync(adminRole, new Claim("permission.action.all", "all"));
        }
        if (!await roleManager.RoleExistsAsync("Standard User"))
        {
            await roleManager.CreateAsync(new RlxRole { Name = "Standard User" });
        }
        #endregion
        #region users
        var user = await userManager.FindByNameAsync("rlx_super_user");
        if (user == null)
        {
            user = new RlxUser
            {
                UserName = "rlx_super_user",
                Email = "<EMAIL>",
                EmailConfirmed = true
            };
            await userManager.CreateAsync(user, "Admin123!");
            await userManager.AddToRoleAsync(user, "Admin");
            // Claim ekle
            await userManager.AddClaimAsync(user, new Claim("permission.page.all", "all"));
            await userManager.AddClaimAsync(user, new Claim("permission.action.all", "all"));
        }
        #endregion
        #region oidcApps
        var oidcAppManager = serviceProvider.GetRequiredService<IOpenIddictApplicationManager>();
        if (await oidcAppManager.FindByClientIdAsync("logincenter") == null)
        {
            var oidcApp = new OpenIddictApplicationDescriptor
            {
                ClientId = "logincenter",
                ClientSecret = "VYRkVI8G6BMcwk",
                ApplicationType = OpenIddictConstants.ApplicationTypes.Web,
                ConsentType = OpenIddictConstants.ConsentTypes.Implicit,
                DisplayName = "Login Center",
                Permissions =
            {
                OpenIddictConstants.Permissions.Endpoints.Authorization,
                OpenIddictConstants.Permissions.Endpoints.Logout,
                OpenIddictConstants.Permissions.Endpoints.Token,
                OpenIddictConstants.Permissions.GrantTypes.AuthorizationCode,
                OpenIddictConstants.Permissions.GrantTypes.RefreshToken,
                OpenIddictConstants.Permissions.ResponseTypes.Code,
                OpenIddictConstants.Permissions.Scopes.Email,
                OpenIddictConstants.Permissions.Scopes.Profile,
            },
                Requirements =
            {
                OpenIddictConstants.Requirements.Features.ProofKeyForCodeExchange
            },
                ClientType = OpenIddictConstants.ClientTypes.Confidential,
            };
            var redirectUris = config.GetSection("OpenIddict:RedirectUris").Get<string[]>();
            if (redirectUris != null && redirectUris.Length > 0)
                foreach (var uri in redirectUris)
                {
                    if (Uri.TryCreate(uri, UriKind.Absolute, out var redirectUri))
                    {
                        oidcApp.RedirectUris.Add(redirectUri);
                    }
                }
            var postLogoutRedirectUris = config.GetSection("OpenIddict:PostLogoutRedirectUris").Get<string[]>();
            if (postLogoutRedirectUris != null && postLogoutRedirectUris.Length > 0)
                foreach (var uri in postLogoutRedirectUris)
                {
                    if (Uri.TryCreate(uri, UriKind.Absolute, out var postLogoutRedirectUri))
                    {
                        oidcApp.PostLogoutRedirectUris.Add(postLogoutRedirectUri);
                    }
                }
            await oidcAppManager.CreateAsync(oidcApp);
            #endregion
            #region claimSchemas
            var claimSchemaPath = Path.Combine(env.ContentRootPath, "Seeds", "claimSchemas.json");
            if (File.Exists(claimSchemaPath))
            {
                var claimSchemas = JsonSerializer.Deserialize<List<RlxClaimSchemaCreateDto>>(File.ReadAllText(claimSchemaPath));
                if (claimSchemas != null)
                    foreach (var claimSchema in claimSchemas)
                    {
                        if (!await rlxClaimSchemaManager.ClaimSchemaExistsAsync(claimSchema.ClaimType, claimSchema.ClaimValue))
                            await rlxClaimSchemaManager.CreateClaimSchemaAsync(claimSchema);
                    }
            }
            #endregion
        }
    }
}