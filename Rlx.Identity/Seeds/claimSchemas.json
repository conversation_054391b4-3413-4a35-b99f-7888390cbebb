[{"ClaimType": "permission.page.all", "ClaimValue": "all"}, {"ClaimType": "permission.page.core", "ClaimValue": "all"}, {"ClaimType": "permission.action.all", "ClaimValue": "all"}, {"ClaimType": "permission.action.core", "ClaimValue": "all"}, {"ClaimType": "permission.action.core", "ClaimValue": "getuser"}, {"ClaimType": "permission.action.core", "ClaimValue": "saveuser"}, {"ClaimType": "permission.action.core", "ClaimValue": "getclaimschema"}, {"ClaimType": "permission.action.core", "ClaimValue": "saveclaimschema"}, {"ClaimType": "permission.action.core", "ClaimValue": "getrole"}, {"ClaimType": "permission.action.core", "ClaimValue": "saverole"}]