using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using System.Text.Json;
using Mapster;
using Microsoft.AspNetCore.Identity;
using Rlx.Identity.Interfaces;
using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Identity.Models.RlxDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace Rlx.Identity.Managers;

public class RlxUserManager : IRlxUserManager
{
    private readonly UserManager<RlxUser> _userManager;
    private readonly IUserStore<RlxUser> _userStore;
    private readonly IRlxUserStore _rlxUserStore;
    public RlxUserManager(UserManager<RlxUser> userManager, IRlxUserStore rlxUserStore, IUserStore<RlxUser> userStore)
    {
        _userManager = userManager;
        _rlxUserStore = rlxUserStore;
        _userStore = userStore;
    }
    public async Task CreateUserAsync(RlxUserCreateDto dto)
    {
        var user = dto.Adapt<RlxUser>();
        IdentityResult? result;
        if (dto.Password == null)
            result = await _userManager.CreateAsync(user);
        else
            result = await _userManager.CreateAsync(user, dto.Password);
        if (!result.Succeeded)
        {
            throw new ValidationException(JsonSerializer.Serialize(result));
        }
    }
    // public async Task DeleteUserAsync(string id)
    // {
    //     throw new NotImplementedException();
    // }
    public async Task<PagedListDto<RlxUserDto>> GetUsersAsync(PagedListCo<GetUsersCo> co)
    {
        var result = await _rlxUserStore.GetUsersAsync(co);
        return result;
    }
    public async Task<PagedListDto<RlxUserDto>> GetUsersBasicAsync(PagedListCo<GetUsersCo> co)
    {
        var result = await _rlxUserStore.GetUsersBasicAsync(co);
        return result;
    }
    public async Task UpdateUserAsync(RlxUserUpdateDto dto)
    {
        var aiid = await _rlxUserStore.GetAutoIncrementIdAsync(dto.Id);
        if (aiid == 0)
        {
            throw new KeyNotFoundException("User not found");
        }
        var user = await _rlxUserStore.GetUser(dto.Id);
        if (user == null)
        {
            throw new KeyNotFoundException("User not found");
        }
        dto.Adapt(user);
        user.AutoIncrementId = aiid;
        var result = await _userManager.UpdateAsync(user);
        if (!result.Succeeded)
        {
            throw new ValidationException(JsonSerializer.Serialize(result));
        }
    }
    public async Task<IEnumerable<RlxRoleDto>> GetUserRolesAsync(string userId)
    {
        if (string.IsNullOrWhiteSpace(userId))
        {
            throw new ArgumentNullException("UserId");
        }
        var result = await _rlxUserStore.GetUserRolesAsync(userId);
        return result;
    }
    public async Task<IEnumerable<ClaimDto>> GetUserClaimsAsync(string userId)
    {
        if (string.IsNullOrWhiteSpace(userId))
        {
            throw new ArgumentNullException("UserId");
        }
        var result = await _rlxUserStore.GetUserClaimsAsync(userId);
        return result;
    }
    // public async Task<IEnumerable<RlxRoleDto>> GetUserRolesAsync(string userId)
    // {
    //     var roles = await _rlxUserStore.GetUserRolesAsync(userId);
    //     return roles;
    // }
    // public async Task<IEnumerable<ClaimDto>> GetUserClaimsAsync(string userId)
    // {
    //     var claims = await _rlxUserStore.GetUserClaimsAsync(userId);
    //     return claims;
    // }
    public async Task SaveUserRolesAsync(RlxUserRolesSaveDto dto)
    {
        var user = await _rlxUserStore.GetUserByIdAsync(dto.UserId);
        if (user == null)
        {
            throw new KeyNotFoundException("User not found");
        }
        if (dto.Roles?.Any() == true)
        {
            var addList = new List<string>();
            var delList = new List<string>();
            foreach (var role in dto.Roles)
            {
                if (role.Delete)
                {
                    delList.Add(role.Name);
                }
                else
                {
                    addList.Add(role.Name);
                }
            }
            if (delList.Any())
            {
                await _userManager.RemoveFromRolesAsync(user, delList.ToArray());
            }
            if (addList.Any())
            {
                await _userManager.AddToRolesAsync(user, addList.ToArray());
            }
        }
    }
    public async Task SaveUserClaimsAsync(RlxUserClaimsSaveDto dto)
    {
        var user = await _rlxUserStore.GetUserByIdAsync(dto.UserId);
        if (user == null)
        {
            throw new KeyNotFoundException("User not found");
        }
        if (dto.Claims?.Any() == true)
        {
            var addList = new List<Claim>();
            var delList = new List<Claim>();
            foreach (var claim in dto.Claims)
            {
                if (claim.Delete)
                {
                    delList.Add(new Claim(claim.ClaimType, claim.ClaimValue));
                }
                else
                {
                    addList.Add(new Claim(claim.ClaimType, claim.ClaimValue));
                }
            }
            if (delList.Any())
            {
                await _userManager.RemoveClaimsAsync(user, delList.ToArray());
            }
            if (addList.Any())
            {
                await _userManager.AddClaimsAsync(user, addList.ToArray());
            }
        }
    }
    public async Task SetPasswordAsync([Required] RlxSetPasswordDto dto)
    {
        var user = await _rlxUserStore.GetUserByIdAsync(dto.UserId);
        if (user == null)
        {
            throw new KeyNotFoundException("User not found");
        }
        var token = await _userManager.GeneratePasswordResetTokenAsync(user);
        var result = await _userManager.ResetPasswordAsync(user, token, dto.Password);
        if (!result.Succeeded)
        {
            throw new ValidationException(JsonSerializer.Serialize(result));
        }
    }
}