using System.ComponentModel.DataAnnotations;
using Mapster;
using Rlx.Identity.Interfaces;
using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Identity.Models.RlxDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace Rlx.Identity.Managers;
public class RlxClaimSchemaManager : IRlxClaimSchemaManager
{
    private readonly IRlxClaimSchemaStore _rlxClaimSchemaStore;
    public RlxClaimSchemaManager(IRlxClaimSchemaStore rlxClaimSchemaStore)
    {
        _rlxClaimSchemaStore = rlxClaimSchemaStore;
    }
    public async Task CreateClaimSchemaAsync(RlxClaimSchemaCreateDto dto)
    {
        var beforeAdded = await _rlxClaimSchemaStore.ClaimSchemaExistsAsync(dto.ClaimType, dto.ClaimValue);
        if (beforeAdded)
        {
            throw new ValidationException("Claim schema already exists");
        }
        var saveData = dto.Adapt<RlxClaimSchema>();
        await _rlxClaimSchemaStore.AddClaimSchemaAsync(saveData);
    }
    public async Task DeleteClaimSchemaAsync(string id)
    {
        var beforeAdded = await _rlxClaimSchemaStore.ClaimSchemaExistsAsync(id);
        if (!beforeAdded)
        {
            throw new KeyNotFoundException("Claim schema not found");
        }
        await _rlxClaimSchemaStore.DeleteClaimSchemaAsync(id);
    }
    public Task<PagedListDto<RlxClaimSchemaDto>> GetClaimSchemasAsync(PagedListCo<GetClaimSchemasCo> co)
    {
        //co = co ?? new PagedListCo<GetClaimSchemasCo>();
        return _rlxClaimSchemaStore.GetClaimSchemasAsync(co);
    }
    public Task<PagedListDto<RlxClaimSchemaDto>> GetClaimSchemasBasicAsync(PagedListCo<GetClaimSchemasCo> co)
    {
        return _rlxClaimSchemaStore.GetClaimSchemasBasicAsync(co);
    }
    public Task<PagedListDto<RlxClaimSchemaForAutoCompleteDto>> GetClaimSchemasForAutoCompleteAsync(string text)
    {
        return _rlxClaimSchemaStore.GetClaimSchemasForAutoCompleteAsync(new PagedListCo<GetClaimSchemasCo> { Criteria = new GetClaimSchemasCo { AutoComplete = text, Disabled = false } });
    }

    public Task<bool> ClaimSchemaExistsAsync(string claimType, string claimValue)
    {
        return _rlxClaimSchemaStore.ClaimSchemaExistsAsync(claimType, claimValue);
    }

    public async Task UpdateClaimSchemaAsync(RlxClaimSchemaUpdateDto dto)
    {
        var aiid = await _rlxClaimSchemaStore.GetAutoIncrementIdAsync(dto.Id);
        if (aiid == 0)
        {
            throw new KeyNotFoundException("Claim schema not found");
        }
        var beforeAdded = await _rlxClaimSchemaStore.SchemaExistsWithDifferentIdAsync(dto.Id, dto.ClaimType, dto.ClaimValue);
        if (beforeAdded)
        {
            throw new ValidationException("Claim schema already exists");
        }
        var saveData = dto.Adapt<RlxClaimSchema>();
        saveData.AutoIncrementId = aiid;
        await _rlxClaimSchemaStore.UpdateClaimSchemaAsync(saveData);
    }
}