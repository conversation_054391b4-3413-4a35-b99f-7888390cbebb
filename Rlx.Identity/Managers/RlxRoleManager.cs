using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using System.Text.Json;
using Mapster;
using Microsoft.AspNetCore.Identity;
using Rlx.Identity.Consts;
using Rlx.Identity.Interfaces;
using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Identity.Models.RlxDbContextModels;
using Rlx.Shared.Helpers;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace Rlx.Identity.Managers;
public class RlxRoleManager : IRlxRoleManager
{
    private readonly RoleManager<RlxRole> _roleManager;
    private readonly IRoleStore<RlxRole> _roleStore;
    private readonly IRlxRoleStore _rlxRoleStore;
    private readonly IRlxCacheService _rlxCacheService;
    public RlxRoleManager(RoleManager<RlxRole> roleManager, IRlxRoleStore RlxRoleStore, IRoleStore<RlxRole> roleStore, IRlxCacheService rlxCacheService)
    {
        _roleManager = roleManager;
        _rlxRoleStore = RlxRoleStore;
        _roleStore = roleStore;
        _rlxCacheService = rlxCacheService;
    }
    public async Task CreateRoleAsync(RlxRoleCreateDto dto)
    {
        var role = dto.Adapt<RlxRole>();
        var result = await _roleManager.CreateAsync(role);
        if (!result.Succeeded)
        {
            throw new ValidationException(JsonSerializer.Serialize(result));
        }
    }
    // public async Task DeleteRoleAsync(string id)
    // {
    //     throw new NotImplementedException();
    // }
    public async Task<PagedListDto<RlxRoleDto>> GetRolesAsync(PagedListCo<GetRolesCo> co)
    {
        var result = await _rlxRoleStore.GetRolesAsync(co);
        return result;
    }
    public async Task<PagedListDto<RlxRoleDto>> GetRolesBasicAsync(PagedListCo<GetRolesCo> co)
    {
        var result = await _rlxRoleStore.GetRolesBasicAsync(co);
        return result;
    }
    public async Task<PagedListDto<AutoCompleteDto>> GetRolesForAutoCompleteAsync(PagedListCo<GetRolesCo> co)
    {
        var result = await _rlxRoleStore.GetRolesForAutoCompleteAsync(co);
        return result;
    }
    public async Task UpdateRoleAsync(RlxRoleUpdateDto dto)
    {
        var aiid = await _rlxRoleStore.GetAutoIncrementIdAsync(dto.Id);
        if (aiid == 0)
        {
            throw new KeyNotFoundException("Role not found");
        }
        var role = await _rlxRoleStore.GetRlxRole(dto.Id);
        if (role == null)
        {
            throw new KeyNotFoundException("Role not found");
        }
        dto.Adapt(role);
        role.AutoIncrementId = aiid;
        var result = await _roleManager.UpdateAsync(role);
        if (!result.Succeeded)
        {
            throw new ValidationException(JsonSerializer.Serialize(result));
        }
    }
    public async Task<IEnumerable<ClaimDto>> GetRoleClaimsAsync(string roleId)
    {
        if (string.IsNullOrWhiteSpace(roleId))
        {
            throw new ArgumentNullException("RoleId");
        }
        var result = await _rlxRoleStore.GetRoleClaimsAsync(roleId);
        return result;
    }
    // public async Task<IEnumerable<ClaimDto>> GetRoleClaimsAsync(string roleId)
    // {
    //     var claims = await _rlxRoleStore.GetRoleClaimsAsync(roleId);
    //     return claims;
    // }

    public async Task<string> GetRoleIdByName(string roleName)
    {
        if (string.IsNullOrWhiteSpace(roleName))
        {
            throw new ArgumentNullException("RoleName");
        }
        var id = await _rlxRoleStore.GetRoleIdByName(roleName);
        if (id == null)
            throw new KeyNotFoundException("Role not found");
        return id;
    }
    public async Task SaveRoleClaimsAsync(RlxRoleClaimsSaveDto dto)
    {
        var role = await _rlxRoleStore.GetRoleByIdAsync(dto.RoleId);
        if (role == null)
        {
            throw new KeyNotFoundException("Role not found");
        }
        if (dto.Claims?.Any() == true)
        {
            foreach (var claim in dto.Claims)
            {
                var cl = new Claim(claim.ClaimType, claim.ClaimValue);
                if (claim.Delete)
                {
                    await _roleManager.RemoveClaimAsync(role, cl);
                }
                else
                {
                    var hasClaim = await _rlxRoleStore.HasClaim(role.Id!, claim.ClaimType, claim.ClaimValue);
                    if (hasClaim)
                    {
                        continue;
                    }
                    await _roleManager.AddClaimAsync(role, cl);
                }
            }
            await _rlxCacheService.RemoveAsync(TextHelper.RlxJoin(CacheConst.RoleClaims, role.Name!));

        }
    }
}