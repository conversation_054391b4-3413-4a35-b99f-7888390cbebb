using Microsoft.AspNetCore.Authorization;
using Rlx.Identity.Consts;
using Rlx.Shared.Helpers;
namespace Rlx.Identity.Configs;

public static class PolicyConfig
{
    public static void ConfigurePolicies(this AuthorizationOptions options)
    {
        options.AddPage("test");
        // options.AddAction("save");
        // options.AddAction("get");
        options.AddAction("saveuser");
        options.AddAction("getuser");
        options.AddAction("saverole");
        options.AddAction("getrole");
        options.AddAction("saveclaimschema");
        options.AddAction("getclaimschema");
        options.AddAction("savelocalization");
        options.AddAction("getlocalization");
        options.AddAction("setpassword");
    }
    private static void AddPage(this AuthorizationOptions options, string val)
    {
        options.AddRlxPolicy([new Shared.Models.RlxPolicy { ClaimType = RlxIdentityConst.PermissionPageCore, ClaimValue = val },
        new Shared.Models.RlxPolicy { ClaimType = RlxIdentityConst.PermissionPageAll, ClaimValue = "all" },
        new Shared.Models.RlxPolicy { ClaimType = RlxIdentityConst.PermissionPageCore, ClaimValue = "all" }]);
    }
    private static void AddAction(this AuthorizationOptions options, string val)
    {
        options.AddRlxPolicy([new Shared.Models.RlxPolicy { ClaimType = RlxIdentityConst.PermissionActionCore, ClaimValue = val },
        new Shared.Models.RlxPolicy { ClaimType = RlxIdentityConst.PermissionActionAll, ClaimValue = "all" },
        new Shared.Models.RlxPolicy { ClaimType = RlxIdentityConst.PermissionActionCore, ClaimValue = "all" }]);
    }
}