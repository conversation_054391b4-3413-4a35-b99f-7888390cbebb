using Mapster;
using Rlx.Identity.Models.Dtos;
using Rlx.Identity.Models.RlxDbContextModels;
namespace Rlx.Identity.Configs;
public static class MapsterConfig
{
    public static void RegisterMappings()
    {
        TypeAdapterConfig<RlxClaimSchema, RlxClaimSchemaCreateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<RlxClaimSchema, RlxClaimSchemaUpdateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<RlxClaimSchema, RlxClaimSchemaDto>.NewConfig().TwoWays();
        TypeAdapterConfig<RlxRole, RlxRoleCreateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<RlxRole, RlxRoleUpdateDto>.NewConfig().TwoWays();
    }
}