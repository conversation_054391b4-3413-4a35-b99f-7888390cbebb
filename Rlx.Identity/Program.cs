using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using OpenIddict.Abstractions;
using OpenIddict.Validation.AspNetCore;
using Rlx.Identity.Configs;
using Rlx.Identity.DbContexts;
using Rlx.Identity.Interfaces;
using Rlx.Identity.Managers;
using Rlx.Identity.Models.RlxDbContextModels;
using Rlx.Identity.Stores;
using Rlx.Shared.Configs;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Factories;
using Rlx.Shared.Handlers;
using Rlx.Shared.Helpers;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Managers;
using Rlx.Shared.Middlewares;
using Rlx.Shared.Services;
using Rlx.Shared.Stores;
using StackExchange.Redis;
var builder = WebApplication.CreateBuilder(args);
MapsterConfig.RegisterMappings();
RlxLocalizationMapsterConfig.RegisterMappings();
builder.Services.AddLocalization();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddHttpContextAccessor();
builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    // options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.Preserve;
    // options.JsonSerializerOptions.WriteIndented = true;
    // options.JsonSerializerOptions.PropertyNamingPolicy = null;
});
builder.Services.AddDbContext<RlxIdentityDbContext>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("RlxIdentity"));
    options.UseOpenIddict();
});
builder.Services.AddDbContext<RlxIdentityLocalizationDbContext>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("RlxIdentity"));
});
builder.Services.AddDbContext<RlxIdentitySharedDbContext>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("RlxIdentity"));
});
builder.Services.AddIdentity<RlxUser, RlxRole>()
    .AddEntityFrameworkStores<RlxIdentityDbContext>()
    .AddDefaultTokenProviders();
var encryptionCert = new X509Certificate2("Certs/encryption.pfx", "YxDnlZdvPimyg5");
var signingCert = new X509Certificate2("Certs/signing.pfx", "37evwu1vlX5iPl");
builder.Services.AddOpenIddict()
.AddCore(options =>
{
    options.UseEntityFrameworkCore()
           .UseDbContext<RlxIdentityDbContext>();
})
.AddServer(options =>
{
    options.SetTokenEndpointUris("/connect/token")
           .SetAuthorizationEndpointUris("/connect/authorize")
           .SetUserinfoEndpointUris("/connect/userinfo")
           .SetLogoutEndpointUris("/connect/logout");
    options.AllowAuthorizationCodeFlow()
           .AllowRefreshTokenFlow();
    options.AllowImplicitFlow();
    options.AddEncryptionCertificate(encryptionCert)
           .AddSigningCertificate(signingCert);
    options.UseAspNetCore()
           .EnableTokenEndpointPassthrough()
           .EnableLogoutEndpointPassthrough()
           .EnableAuthorizationEndpointPassthrough();
    options.SetRefreshTokenLifetime(TimeSpan.FromDays(30));
    options.RegisterScopes(OpenIddictConstants.Scopes.OpenId, OpenIddictConstants.Scopes.Email, OpenIddictConstants.Scopes.Profile, OpenIddictConstants.Scopes.OfflineAccess);
})
.AddValidation(options =>
{
    options.UseLocalServer();
    options.UseAspNetCore();
});
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigin", b =>
    {
        b.WithOrigins(builder.Configuration.GetSection("CorsOrigins").Get<string[]>()!)
               .AllowCredentials()
               .AllowAnyHeader()
               .AllowAnyMethod();
    });
});
// app.UseCors("AllowSpecificOrigin");
builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
    options.DefaultAuthenticateScheme = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
})
    .AddCookie(CookieAuthenticationDefaults.AuthenticationScheme, options =>
    {
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
        options.Cookie.SameSite = SameSiteMode.None;
        options.Cookie.Domain = builder.Configuration["CookieDomain"]!;
    });
builder.Services.AddAuthorization(options =>
{
    options.ConfigurePolicies();
});
builder.Services.AddTransient<IClaimsTransformation, RoleClaimsTransformation>();
builder.Services.AddScoped<IRlxIdentitySharedManager, RlxIdentitySharedManager>();
builder.Services.AddScoped<IRlxIdentitySharedStore, RlxIdentitySharedStore>();
builder.Services.AddScoped<IRlxRoleManager, RlxRoleManager>();
builder.Services.AddScoped<IRlxClaimSchemaManager, RlxClaimSchemaManager>();
builder.Services.AddScoped<IRlxUserManager, RlxUserManager>();
builder.Services.AddScoped<IRlxClaimSchemaStore, RlxClaimSchemaStore>();
builder.Services.AddScoped<IRlxRoleStore, RlxRoleStore>();
builder.Services.AddScoped<IRlxUserStore, RlxUserStore>();
// builder.Services.AddScoped<ILogHelper, LogHelper>();
// builder.Services.AddScoped<IRlxQueueService, RabbitMQService>();
builder.Services.AddScoped<IEntityChangeLogHelper, EntityChangeLogHelper>();
builder.Services.AddScoped<IRlxLocalizationManager<RlxIdentityLocalizationDbContext>, RlxLocalizationManager<RlxIdentityLocalizationDbContext>>();
builder.Services.AddScoped<IRlxLocalizationStore<RlxIdentityLocalizationDbContext>, RlxLocalizationStore<RlxIdentityLocalizationDbContext>>();
builder.Services.AddScoped<IUserContextHelper, UserContextHelper>();
builder.Services.AddScoped<IConnectionMultiplexer>(sp => { return ConnectionMultiplexer.Connect(builder.Configuration["RedisCache:ConnectionString"]!); });
builder.Services.AddScoped<IRlxCacheService, RedisCacheService>();
builder.Services.AddScoped(typeof(IRlxSystemLogHelper<>), typeof(RlxSystemLogHelper<>));
builder.Services.AddSingleton(typeof(RlxQueueServiceFactory));
builder.Services.AddScoped<ExceptionHandler>();
// builder.Services.AddSingleton(typeof(MongoDbServiceFactory));
// builder.Services.AddSingleton(typeof(MongoCollectionServiceFactory<>));
var app = builder.Build();
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    // ForwardedHeaders = ForwardedHeaders.XForwardedProto | ForwardedHeaders.XForwardedFor,
    // KnownNetworks = 
    // {
    //     new Microsoft.AspNetCore.HttpOverrides.IPNetwork(IPAddress.Parse("***********"), 16) // Docker ağınızı buraya yazın
    // }
    ForwardedHeaders = ForwardedHeaders.All, // Tüm header'ları kabul et
    ForwardLimit = null, // Limit kaldır (birden fazla proxy varsa)
    KnownProxies = { }, // Tüm proxy'lere güven (TEHLİKELİ, production'da kullanmayın!)
    KnownNetworks = { } // Tüm ağlara güven
});
using (var scope = app.Services.CreateScope())
{
    var dbContext = scope.ServiceProvider.GetRequiredService<RlxIdentityDbContext>();
    dbContext.Database.Migrate();
    var localizationDbContext = scope.ServiceProvider.GetRequiredService<RlxIdentityLocalizationDbContext>();
    localizationDbContext.Database.Migrate();
}
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    await DataSeed.Initialize(services);
}
// app.UseMiddleware<ExceptionHandlerMiddleware>();
app.UseMiddleware<RequestLoggingMiddleware>();
app.UseLocalization();
// app.UseMiddleware<LocalizationMiddleware>();
app.UseExceptionHandler(
    errorApp =>
{
    errorApp.Run(async context =>
    {
        var exceptionHandler = app.Services.GetRequiredService<ExceptionHandler>();
        var exceptionFeature = context.Features.Get<IExceptionHandlerPathFeature>();
        if (exceptionFeature?.Error != null)
        {
            await exceptionHandler.TryHandleAsync(context, exceptionFeature.Error, context.RequestAborted);
        }
    });
});
// app.Use(async (context, next) =>
// {
//     var logger = app.Services.GetRequiredService<ILogger<Program>>();
//     logger.LogInformation($"SCHEME: {context.Request.Scheme}");
//     logger.LogInformation($"IS HTTPS: {context.Request.IsHttps}");
//     await next();
// });
// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    // app.UseDeveloperExceptionPage();
}
else
{
    // app.UseExceptionHandler("/Error"); // Production ortamında özel hata sayfası
}
app.UseCors("AllowSpecificOrigin");
// // app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.Run();
