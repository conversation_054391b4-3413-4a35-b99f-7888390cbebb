using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Rlx.Identity.Models.RlxDbContextModels;
using Rlx.Shared.Interfaces;
namespace Rlx.Identity.DbContexts;

public class RlxIdentityDbContext : IdentityDbContext<RlxUser, RlxRole, string, RlxUserClaim, RlxUserRole, RlxUserLogin, RlxRoleClaim, RlxUserToken>
{
    private readonly IEntityChangeLogHelper _entityChangeLogHelper;
    private readonly IConfiguration _configuration;
    public RlxIdentityDbContext(DbContextOptions<RlxIdentityDbContext> options, IEntityChangeLogHelper entityChangeLogHelper, IConfiguration configuration) : base(options)
    {
        _entityChangeLogHelper = entityChangeLogHelper;
        _configuration = configuration;
        ChangeTracker.LazyLoadingEnabled = false;
    }
    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        builder.Entity<RlxClaimSchema>(b =>
        {
            b.<PERSON>(b => b.Id);
            b.Property(u => u.AutoIncrementId).IsRequired().ValueGeneratedOnAdd();
            b.Property(u => u.ClaimType).IsRequired().HasMaxLength(256);
            b.Property(u => u.ClaimValue).IsRequired().HasMaxLength(256);
            b.Property(u => u.Description).HasMaxLength(256);
            b.Property(u => u.NormalizedAll).IsRequired();
        });
        builder.Entity<RlxClaimSchema>().HasIndex(u => new { u.ClaimType, u.ClaimValue }).IsUnique();
        builder.Entity<RlxClaimSchema>().HasIndex(u => u.AutoIncrementId);
        builder.Entity<RlxClaimSchema>().HasIndex(u => u.Disabled);
        builder.Entity<RlxClaimSchema>().HasIndex(u => u.NormalizedAll);
        builder.Entity<RlxUser>().Property(u => u.AutoIncrementId).ValueGeneratedOnAdd();
        builder.Entity<RlxRole>().Property(u => u.AutoIncrementId).ValueGeneratedOnAdd();
        builder.Entity<RlxUser>().HasIndex(u => u.AutoIncrementId);
        builder.Entity<RlxRole>().HasIndex(u => u.AutoIncrementId);
        builder.Entity<RlxUserClaim>().HasIndex(u => u.NormalizedAll);
        builder.Entity<RlxRoleClaim>().HasIndex(u => u.NormalizedAll);
    }
    public DbSet<RlxUser> RlxUsers { get; set; }
    public DbSet<RlxRole> RlxRoles { get; set; }
    public DbSet<RlxClaimSchema> RlxClaimSchemas { get; set; }
    public DbSet<RlxUserClaim> RlxUserClaims { get; set; }
    public DbSet<RlxUserRole> RlxUserRoles { get; set; }
    public DbSet<RlxUserLogin> RlxUserLogins { get; set; }
    public DbSet<RlxRoleClaim> RlxRoleClaims { get; set; }
    public DbSet<RlxUserToken> RlxUserTokens { get; set; }
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        if (_configuration["EntityLog:Enabled"] == "1")
        {
            await _entityChangeLogHelper.AddEntityChangeLogAsync(dbContext: this);
        }
        return await base.SaveChangesAsync(cancellationToken);
    }
}