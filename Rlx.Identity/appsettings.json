{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"RlxIdentityShared": "Host=localhost;Database=RlxIdentity;Username=readonly_user;Password=**************;Port=6007", "RlxIdentity": "Host=localhost;Database=RlxIdentity;Username=postgres;Password=**********************;Port=6007"}, "EntityLog": {"Host": "localhost", "Port": "6004", "Username": "UKjbldeMP7swx72qgIfqtU", "Password": "j8WkyK91Y6yjKS8vHG52lZ", "Enabled": "1", "ExcludeEntities": ["RlxUserLogin", "RlxUserToken", "OpenIddictEntityFrameworkCoreAuthorization", "OpenIddictEntityFrameworkCoreToken"]}, "RequestLog": {"Host": "localhost", "Port": "6004", "Username": "UKjbldeMP7swx72qgIfqtU", "Password": "j8WkyK91Y6yjKS8vHG52lZ", "Enabled": "1", "Module": "Rlx.Identity"}, "SystemLog": {"Host": "localhost", "Port": "6004", "Username": "UKjbldeMP7swx72qgIfqtU", "Password": "j8WkyK91Y6yjKS8vHG52lZ", "Enabled": "1", "Module": "Rlx.Identity"}, "RedisCache": {"ConnectionString": "localhost:6006,password=qkpWm2qLJqzVwN"}, "CorsOrigins": ["https://localhost:3001", "https://localhost:4001", "https://localhost:5001", "http://localhost:6001"], "OpenIddict": {"RedirectUris": ["https://localhost:3001/account/token", "https://local-logincenter.arel.edu.tr:6014/account/token"], "PostLogoutRedirectUris": ["https://localhost:3001/account/logout", "https://local-logincenter.arel.edu.tr:6014/account/logout"]}, "CookieDomain": ".localhost"}