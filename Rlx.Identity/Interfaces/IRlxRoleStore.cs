using System.Security.Claims;
using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Identity.Models.RlxDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace Rlx.Identity.Interfaces;

public interface IRlxRoleStore
{
    Task<PagedListDto<RlxRoleDto>> GetRolesAsync(PagedListCo<GetRolesCo> co);
    Task<PagedListDto<RlxRoleDto>> GetRolesBasicAsync(PagedListCo<GetRolesCo> co);
    Task<PagedListDto<AutoCompleteDto>> GetRolesForAutoCompleteAsync(PagedListCo<GetRolesCo> co);
    Task<IEnumerable<ClaimDto>> GetRoleClaimsAsync(string roleId);
    Task<string?> GetRoleIdByName(string roleName);
    Task<RlxRole?> GetRoleByIdAsync(string roleId);
    Task<int> GetAutoIncrementIdAsync(string id);
    Task<RlxRole?> GetRlxRole(string roleId);
    Task<bool> HasClaim(string roleId,string claimType, string claimValue);

    // Task AddClaimSchemaAsync(RlxClaimSchema data);
    // Task UpdateClaimSchemaAsync(RlxClaimSchema data);
    // Task DeleteClaimSchemaAsync(string id);
    // Task<bool> ClaimSchemaExistsAsync(string claimType, string claimValue);
    // Task<bool> ClaimSchemaExistsAsync(string id);
    // Task<int> GetAutoIncrementIdAsync(string id);
    // Task<bool> SchemaExistsWithDifferentIdAsync(string id, string claimType, string claimValue);
}