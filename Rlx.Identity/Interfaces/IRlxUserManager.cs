using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace Rlx.Identity.Interfaces;

public interface IRlxUserManager
{
    Task<PagedListDto<RlxUserDto>> GetUsersAsync(PagedListCo<GetUsersCo> co);
    Task<PagedListDto<RlxUserDto>> GetUsersBasicAsync(PagedListCo<GetUsersCo> co);
    Task CreateUserAsync(RlxUserCreateDto dto);
    Task UpdateUserAsync(RlxUserUpdateDto dto);
    Task<IEnumerable<RlxRoleDto>> GetUserRolesAsync(string userId);
    Task<IEnumerable<ClaimDto>> GetUserClaimsAsync(string userId);
    Task SaveUserRolesAsync(RlxUserRolesSaveDto dto);
    Task SaveUserClaimsAsync(RlxUserClaimsSaveDto dto);
    Task SetPasswordAsync(RlxSetPasswordDto dto);
    // Task DeleteUserAsync(string id);
}