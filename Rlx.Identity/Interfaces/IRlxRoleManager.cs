using System.Security.Claims;
using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace Rlx.Identity.Interfaces;

public interface IRlxRoleManager
{
    Task<PagedListDto<RlxRoleDto>> GetRolesAsync(PagedListCo<GetRolesCo> co);
    Task<PagedListDto<RlxRoleDto>> GetRolesBasicAsync(PagedListCo<GetRolesCo> co);
    Task<PagedListDto<AutoCompleteDto>> GetRolesForAutoCompleteAsync(PagedListCo<GetRolesCo> co);
    Task CreateRoleAsync(RlxRoleCreateDto dto);
    Task UpdateRoleAsync(RlxRoleUpdateDto dto);
    Task<IEnumerable<ClaimDto>> GetRoleClaimsAsync(string roleId);
    Task<string> GetRoleIdByName(string roleName);
    Task SaveRoleClaimsAsync(RlxRoleClaimsSaveDto dto);
    

    // Task<IEnumerable<RlxRoleDto>> GetRlxRoleRolesAsync(string userId);
    // Task<IEnumerable<ClaimDto>> GetRoleClaimsAsync(string userId);
    // Task SaveRlxRoleRolesAsync(RlxRoleRolesSaveDto dto);
    // Task SaveRoleClaimsAsync(RlxRoleClaimsSaveDto dto);
    // Task DeleteRoleAsync(string id);

}
