using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Identity.Models.RlxDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace Rlx.Identity.Interfaces;

public interface IRlxClaimSchemaStore
{
    Task<PagedListDto<RlxClaimSchemaDto>> GetClaimSchemasAsync(PagedListCo<GetClaimSchemasCo> co);
    Task<PagedListDto<RlxClaimSchemaDto>> GetClaimSchemasBasicAsync(PagedListCo<GetClaimSchemasCo> co);
    Task<PagedListDto<RlxClaimSchemaForAutoCompleteDto>> GetClaimSchemasForAutoCompleteAsync(PagedListCo<GetClaimSchemasCo> co);
    Task AddClaimSchemaAsync(RlxClaimSchema data);
    Task UpdateClaimSchemaAsync(RlxClaimSchema data);
    Task DeleteClaimSchemaAsync(string id);
    Task<bool> ClaimSchemaExistsAsync(string claimType, string claimValue);
    Task<bool> ClaimSchemaExistsAsync(string id);
    Task<int> GetAutoIncrementIdAsync(string id);
    Task<bool> SchemaExistsWithDifferentIdAsync(string id, string claimType, string claimValue);
}