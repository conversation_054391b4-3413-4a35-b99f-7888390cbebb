using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Identity.Models.RlxDbContextModels;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace Rlx.Identity.Interfaces;

public interface IRlxUserStore
{
    Task<PagedListDto<RlxUserDto>> GetUsersAsync(PagedListCo<GetUsersCo> co);
    Task<PagedListDto<RlxUserDto>> GetUsersBasicAsync(PagedListCo<GetUsersCo> co);
    Task<IEnumerable<RlxRoleDto>> GetUserRolesAsync(string userId);
    Task<IEnumerable<ClaimDto>> GetUserClaimsAsync(string userId);
    Task<RlxUser?> GetUserByIdAsync(string userId);
    Task<int> GetAutoIncrementIdAsync(string id);
    Task<RlxUser?> GetUser(string roleId);



    // Task AddClaimSchemaAsync(RlxClaimSchema data);
    // Task UpdateClaimSchemaAsync(RlxClaimSchema data);
    // Task DeleteClaimSchemaAsync(string id);
    // Task<bool> ClaimSchemaExistsAsync(string claimType, string claimValue);
    // Task<bool> ClaimSchemaExistsAsync(string id);
    // Task<int> GetAutoIncrementIdAsync(string id);
    // Task<bool> SchemaExistsWithDifferentIdAsync(string id, string claimType, string claimValue);
}