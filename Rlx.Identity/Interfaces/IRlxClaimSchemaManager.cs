using Rlx.Identity.Models.Cos;
using Rlx.Identity.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace Rlx.Identity.Interfaces;

public interface IRlxClaimSchemaManager
{
    Task<PagedListDto<RlxClaimSchemaDto>> GetClaimSchemasAsync(PagedListCo<GetClaimSchemasCo> co);
    Task<PagedListDto<RlxClaimSchemaDto>> GetClaimSchemasBasicAsync(PagedListCo<GetClaimSchemasCo> co);
    Task<PagedListDto<RlxClaimSchemaForAutoCompleteDto>> GetClaimSchemasForAutoCompleteAsync(string text);
    Task CreateClaimSchemaAsync(RlxClaimSchemaCreateDto dto);
    Task UpdateClaimSchemaAsync(RlxClaimSchemaUpdateDto dto);
    Task DeleteClaimSchemaAsync(string id);
    Task<bool> ClaimSchemaExistsAsync(string claimType, string claimValue);

}