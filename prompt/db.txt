ApdysSolution/
├── Apdys.sln
│
├── src/
│   ├── Apdys.Core.Api/
│   │   ├── Apdys.Core.Api.csproj
│   │   ├── appsettings.json
│   │   ├── appsettings.Development.json
│   │   ├── Program.cs
│   │   ├── Dockerfile
│   │   ├── README.md
│   │   │
│   │   ├── Properties/
│   │   │   └── launchSettings.json
│   │   │
│   │   ├── Controllers/
│   │   │   ├── CriteriaController.cs
│   │   │   ├── FormsController.cs
│   │   │   ├── SubmissionsController.cs
│   │   │   ├── UserController.cs
│   │   │   ├── DepartmentStrategicPerformanceController.cs
│   │   │   ├── StaffCompetencyController.cs
│   │   │   ├── PortfolioControlController.cs
│   │   │   └── GenericDataEntryController.cs
│   │   │
│   │   ├── Models/
│   │   │   ├── Dtos/
│   │   │   │   ├── UserDtos.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [9]
│   │   │   │   │   public class UserProfileDto
│   │   │   │   │   {
│   │   │   │   │       public string UserId { get; set; } // From Identity Server token [cite: 10]
│   │   │   │   │       public string FullName { get; set; } // From Org. Management [cite: 11]
│   │   │   │   │       public string Email { get; set; } // From Org. Management [cite: 12]
│   │   │   │   │       public List<string> ApdysRoles { get; set; } // APDYS-specific role names [cite: 13]
│   │   │   │   │       public string AcademicCadre { get; set; } // From Org. Management [cite: 14]
│   │   │   │   │       public string Department { get; set; } // From Org. Management [cite: 15]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── CriteriaDtos.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [16]
│   │   │   │   │   public class DynamicCriterionTemplateRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(200)]
│   │   │   │   │       public string Name { get; set; } [cite: 17]
│   │   │   │   │       [StringLength(1000)]
│   │   │   │   │       public string Description { get; set; } [cite: 18]
│   │   │   │   │       [Range(0, double.MaxValue)]
│   │   │   │   │       public double? Coefficient { get; set; } [cite: 19]
│   │   │   │   │       [Range(0, int.MaxValue)]
│   │   │   │   │       public int? MaxLimit { get; set; } [cite: 20]
│   │   │   │   │       public List<InputFieldDefinitionDto> InputFields { get; set; } = new(); [cite: 21]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [22]
│   │   │   │   │   public class DynamicCriterionTemplateResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string Id { get; set; } // MongoDB ObjectId as string [cite: 23]
│   │   │   │   │       public string Name { get; set; } [cite: 24]
│   │   │   │   │       public string Description { get; set; } [cite: 25]
│   │   │   │   │       public double? Coefficient { get; set; } [cite: 26]
│   │   │   │   │       public int? MaxLimit { get; set; } [cite: 27]
│   │   │   │   │       public string Status { get; set; } // e.g., "Draft", "Active", "Inactive" [cite: 28]
│   │   │   │   │       public List<InputFieldDefinitionDto> InputFields { get; set; } = new(); [cite: 29]
│   │   │   │   │       public DateTime CreatedAt { get; set; } [cite: 30]
│   │   │   │   │       public DateTime UpdatedAt { get; set; } [cite: 31]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [32]
│   │   │   │   │   public class InputFieldDefinitionDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public string FieldId { get; set; } // Unique ID within the criterion [cite: 33]
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(100)]
│   │   │   │   │       public string Label { get; set; } [cite: 34]
│   │   │   │   │       [Required]
│   │   │   │   │       public string InputType { get; set; } // e.g., "Text", "Number", "Date", "File", "Dropdown" [cite: 35]
│   │   │   │   │       public bool IsMandatory { get; set; } [cite: 36]
│   │   │   │   │       public string PlaceholderText { get; set; } [cite: 37]
│   │   │   │   │       public double? MinValue { get; set; } // For Number type [cite: 38]
│   │   │   │   │       public double? MaxValue { get; set; } // For Number type [cite: 39]
│   │   │   │   │       public string AllowedFileTypes { get; set; } // For File type [cite: 40]
│   │   │   │   │       public int? MaxFileSizeMb { get; set; } // For File type [cite: 41]
│   │   │   │   │       public List<string> DropdownOptions { get; set; } = new(); // For Dropdown type [cite: 42]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [43]
│   │   │   │   │   public class StaticCriterionResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string StaticCriterionSystemId { get; set; } // Predefined system unique key [cite: 44]
│   │   │   │   │       public string Name { get; set; } [cite: 45]
│   │   │   │   │       public string Description { get; set; } [cite: 46]
│   │   │   │   │       public bool IsActive { get; set; } [cite: 47]
│   │   │   │   │       public string DataSourceHint { get; set; } [cite: 48]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [49]
│   │   │   │   │   public class ToggleActivationRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public bool IsActive { get; set; } [cite: 50]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── FormDtos.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [51]
│   │   │   │   │   public class EvaluationFormRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(250)]
│   │   │   │   │       public string Name { get; set; } [cite: 52]
│   │   │   │   │       [Required]
│   │   │   │   │       public List<string> ApplicableAcademicCadres { get; set; } = new(); [cite: 53]
│   │   │   │   │       [Required]
│   │   │   │   │       public DateTime EvaluationPeriodStartDate { get; set; } [cite: 54]
│   │   │   │   │       [Required]
│   │   │   │   │       public DateTime EvaluationPeriodEndDate { get; set; } [cite: 55]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [56]
│   │   │   │   │   public class EvaluationFormResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string Id { get; set; } // Public GUID Id of the EvaluationFormEntity [cite: 57]
│   │   │   │   │       public string Name { get; set; } [cite: 58]
│   │   │   │   │       public List<string> ApplicableAcademicCadres { get; set; } [cite: 59]
│   │   │   │   │       public DateTime EvaluationPeriodStartDate { get; set; } [cite: 60]
│   │   │   │   │       public DateTime EvaluationPeriodEndDate { get; set; } [cite: 61]
│   │   │   │   │       public string Status { get; set; } // e.g., "Draft", "Active", "Archived" [cite: 62]
│   │   │   │   │       public DateTime CreatedAt { get; set; } [cite: 63]
│   │   │   │   │       public DateTime UpdatedAt { get; set; } [cite: 64]
│   │   │   │   │       public List<FormCategoryResponseDto> Categories { get; set; } = new(); [cite: 65]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [66]
│   │   │   │   │   public class FormCategoryRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(150)]
│   │   │   │   │       public string Name { get; set; } [cite: 67]
│   │   │   │   │       [Range(0, 100)]
│   │   │   │   │       public double Weight { get; set; } [cite: 68]
│   │   │   │   │       public int DisplayOrder { get; set; } [cite: 69]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [70]
│   │   │   │   │   public class FormCategoryResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string Id { get; set; } // Public GUID Id of the FormCategoryEntity [cite: 71]
│   │   │   │   │       public string EvaluationFormId { get; set; } // Public GUID Id of the parent EvaluationFormEntity [cite: 72]
│   │   │   │   │       public string Name { get; set; } [cite: 73]
│   │   │   │   │       public double Weight { get; set; } [cite: 74]
│   │   │   │   │       public int DisplayOrder { get; set; } [cite: 75]
│   │   │   │   │       public List<CriterionAssignmentResponseDto> AssignedCriteria { get; set; } = new(); [cite: 76]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [77]
│   │   │   │   │   public class CriterionAssignmentRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public string CriterionId { get; set; } // Can be MongoDB ObjectId (string) for dynamic or StaticCriterionSystemId for static [cite: 78]
│   │   │   │   │       [Required]
│   │   │   │   │       public string CriterionType { get; set; } // "Dynamic" or "Static" [cite: 79]
│   │   │   │   │       public int DisplayOrder { get; set; } [cite: 80]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [81]
│   │   │   │   │   public class CriterionAssignmentResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string LinkId { get; set; } // Public GUID Id of the FormCriterionLinkEntity [cite: 82]
│   │   │   │   │       public string CriterionId { get; set; } // MongoDB ObjectId for dynamic, or StaticCriterionSystemId for static [cite: 83]
│   │   │   │   │       public string CriterionType { get; set; } // "Dynamic" or "Static" [cite: 84]
│   │   │   │   │       public string CriterionName { get; set; } // Fetched for display [cite: 85]
│   │   │   │   │       public string CriterionDescription { get; set; } // Fetched for display [cite: 86]
│   │   │   │   │       public double? CriterionCoefficient { get; set; } // For dynamic criteria [cite: 87]
│   │   │   │   │       public int DisplayOrder { get; set; } [cite: 88]
│   │   │   │   │       public List<InputFieldDefinitionDto> InputFields { get; set; } // For dynamic criteria, from its template [cite: 89]
│   │   │   │   │       public string DataSourceHint { get; set; } // For static criteria, from its definition [cite: 90]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── SubmissionDtos.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [91]
│   │   │   │   │   public class AcademicianDashboardFormDto
│   │   │   │   │   {
│   │   │   │   │       public string FormId { get; set; } // Public GUID Id of the EvaluationFormEntity [cite: 92]
│   │   │   │   │       public string FormName { get; set; } [cite: 93]
│   │   │   │   │       public DateTime EvaluationPeriodStartDate { get; set; } [cite: 94]
│   │   │   │   │       public DateTime EvaluationPeriodEndDate { get; set; } [cite: 95]
│   │   │   │   │       public string SubmissionStatus { get; set; } // e.g., "Not Started", "Draft", "Submitted", "Approved", "Rejected" [cite: 96]
│   │   │   │   │       public string SubmissionId { get; set; } // Public GUID Id of AcademicSubmissionEntity, if a submission exists [cite: 97]
│   │   │   │   │       public DateTime? SubmissionDeadline { get; set; } // Calculated or from form period end date [cite: 98]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [99]
│   │   │   │   │   public class AcademicianSubmissionRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public string FormId { get; set; } // Public GUID Id of the EvaluationFormEntity [cite: 100]
│   │   │   │   │       public List<SubmittedDynamicCriterionEntryDto> DynamicCriterionEntries { get; set; } = new(); [cite: 101]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [102]
│   │   │   │   │   public class SubmittedDynamicCriterionEntryDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public string FormCriterionLinkId { get; set; } // Public GUID Id of the FormCriterionLinkEntity [cite: 103]
│   │   │   │   │       public Dictionary<string, object> Data { get; set; } = new(); // Key: InputFieldDefinition.FieldId [cite: 104]
│   │   │   │   │       public List<string> TempUploadedFileIds { get; set; } = new(); // Internal IDs for newly uploaded files [cite: 105]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [106]
│   │   │   │   │   public class AcademicianSubmissionResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string SubmissionId { get; set; } // Public GUID Id [cite: 107]
│   │   │   │   │       public string FormId { get; set; } // Public GUID Id [cite: 108]
│   │   │   │   │       public string FormName { get; set; } [cite: 109]
│   │   │   │   │       public string AcademicianUserId { get; set; } [cite: 110]
│   │   │   │   │       public string AcademicianName { get; set; } [cite: 111]
│   │   │   │   │       public string Status { get; set; } [cite: 112]
│   │   │   │   │       public DateTime SubmittedAt { get; set; } [cite: 113]
│   │   │   │   │       public DateTime? LastSavedAt { get; set; } [cite: 114]
│   │   │   │   │       public DateTime? ApprovedAt { get; set; } [cite: 115]
│   │   │   │   │       public string ApprovedByControllerUserId { get; set; } [cite: 116]
│   │   │   │   │       public string ApprovalComments { get; set; } [cite: 117]
│   │   │   │   │       public DateTime? RejectedAt { get; set; } [cite: 118]
│   │   │   │   │       public string RejectedByControllerUserId { get; set; } [cite: 119]
│   │   │   │   │       public string RejectionComments { get; set; } [cite: 120]
│   │   │   │   │       public List<AcademicianSubmissionCategoryViewDto> Categories { get; set; } = new(); [cite: 121]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [121]
│   │   │   │   │   public class AcademicianSubmissionCategoryViewDto
│   │   │   │   │   {
│   │   │   │   │       public string CategoryId { get; set; } // Public GUID Id [cite: 122]
│   │   │   │   │       public string CategoryName { get; set; } [cite: 123]
│   │   │   │   │       public double Weight { get; set; } [cite: 124]
│   │   │   │   │       public int DisplayOrder { get; set; } [cite: 125]
│   │   │   │   │       public List<AcademicianCriterionDataViewDto> CriteriaData { get; set; } = new(); [cite: 126]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [126]
│   │   │   │   │   public class AcademicianCriterionDataViewDto
│   │   │   │   │   {
│   │   │   │   │       public string FormCriterionLinkId { get; set; } // Public GUID Id [cite: 127]
│   │   │   │   │       public string CriterionType { get; set; } // "Dynamic" or "Static" [cite: 128]
│   │   │   │   │       public string CriterionName { get; set; } [cite: 129]
│   │   │   │   │       public string CriterionDescription { get; set; } [cite: 130]
│   │   │   │   │       public double? CriterionCoefficient { get; set; } // From dynamic template [cite: 131]
│   │   │   │   │       public List<InputFieldDefinitionDto> InputFields { get; set; } // Definition from template [cite: 132]
│   │   │   │   │       public List<SubmittedCriterionInstanceDataViewDto> SubmittedInstances {get; set;} = new(); // User-entered data [cite: 133]
│   │   │   │   │       public string StaticValue { get; set; } // The fetched/pre-calculated value for this academician [cite: 134]
│   │   │   │   │       public string DataSourceHint { get; set; } // From static criterion definition [cite: 135]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [135]
│   │   │   │   │   public class SubmittedCriterionInstanceDataViewDto
│   │   │   │   │   {
│   │   │   │   │       public string InstanceId { get; set; } // ID for this specific entry (e.g., from MongoDB) [cite: 136]
│   │   │   │   │       public Dictionary<string, object> Data { get; set; } [cite: 137]
│   │   │   │   │       public List<EvidenceFileResponseDto> EvidenceFiles { get; set; } = new(); [cite: 138]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [139]
│   │   │   │   │   public class EvidenceFileResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string Id { get; set; } // Public GUID Id of the EvidenceFileEntity [cite: 140]
│   │   │   │   │       public string FileName { get; set; } [cite: 141]
│   │   │   │   │       public string ContentType { get; set; } [cite: 142]
│   │   │   │   │       public long SizeBytes { get; set; } [cite: 143]
│   │   │   │   │       public DateTime UploadedAt { get; set; } [cite: 144]
│   │   │   │   │       public string DownloadUrl { get; set; } // URL to download the file [cite: 145]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [146]
│   │   │   │   │   public class UploadEvidenceResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string TempUploadedFileId { get; set; } // Temporary ID to associate with submission entries [cite: 147]
│   │   │   │   │       public string FileName { get; set; } [cite: 148]
│   │   │   │   │       public long SizeBytes { get; set; } [cite: 149]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── ControllerDtos.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [150]
│   │   │   │   │   public class ControllerSubmissionSummaryDto
│   │   │   │   │   {
│   │   │   │   │       public string SubmissionId { get; set; } // Public GUID Id [cite: 151]
│   │   │   │   │       public string FormName { get; set; } [cite: 152]
│   │   │   │   │       public string AcademicianName { get; set; } [cite: 153]
│   │   │   │   │       public string AcademicianDepartment { get; set; } [cite: 154]
│   │   │   │   │       public DateTime SubmittedAt { get; set; } [cite: 155]
│   │   │   │   │       public string Status { get; set; } // Should be "Submitted" or "Pending Approval" [cite: 156]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [157]
│   │   │   │   │   public class SubmissionRejectionRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(2000)]
│   │   │   │   │       public string Comments { get; set; } [cite: 158]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [159]
│   │   │   │   │   public class SubmissionApprovalRequestDto
│   │   │   │   │   {
│   │   │   │   │       [StringLength(2000)]
│   │   │   │   │       public string Comments { get; set; } // Optional comments on approval [cite: 160]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── RbacDtos.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [160]
│   │   │   │   │   public class ApdysRoleResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string Id { get; set; } // Public GUID Id [cite: 161]
│   │   │   │   │       public string RoleName { get; set; } // e.g., APDYS_Admin, APDYS_Academician [cite: 162]
│   │   │   │   │       public string Description { get; set; } [cite: 163]
│   │   │   │   │       public List<ApdysPermissionResponseDto> Permissions { get; set; } = new(); [cite: 164]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [164]
│   │   │   │   │   public class ApdysPermissionResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string Id { get; set; } // Public GUID Id [cite: 165]
│   │   │   │   │       public string PermissionName { get; set; } // e.g., CanManageCriteria, CanSubmitPerformanceData [cite: 166]
│   │   │   │   │       public string Description { get; set; } [cite: 167]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [167]
│   │   │   │   │   public class ApdysRoleRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(100)]
│   │   │   │   │       public string RoleName { get; set; } [cite: 168]
│   │   │   │   │       [StringLength(500)]
│   │   │   │   │       public string Description { get; set; } [cite: 169]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [169]
│   │   │   │   │   public class ApdysPermissionRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(150)]
│   │   │   │   │       public string PermissionName { get; set; } [cite: 170]
│   │   │   │   │       [StringLength(500)]
│   │   │   │   │       public string Description { get; set; } [cite: 171]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [171]
│   │   │   │   │   public class AssignPermissionToRoleRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public string PermissionId { get; set; } // Public GUID Id of the permission [cite: 172]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [172]
│   │   │   │   │   public class AssignRoleToUserRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public string UniversityUserId { get; set; } [cite: 173]
│   │   │   │   │       [Required]
│   │   │   │   │       public string RoleId { get; set; } // Public GUID Id of the APDYS Role [cite: 174]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── DepartmentStrategicDtos.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [175]
│   │   │   │   │   public class DepartmentStrategicIndicatorDefinitionRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(100)]
│   │   │   │   │       public string IndicatorSystemId { get; set; } [cite: 176]
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(250)]
│   │   │   │   │       public string Name { get; set; } [cite: 177]
│   │   │   │   │       [StringLength(1000)]
│   │   │   │   │       public string Description { get; set; } [cite: 178]
│   │   │   │   │       [Required]
│   │   │   │   │       public string DataType { get; set; } // e.g., "Percentage", "Number", "Currency", "Narrative" [cite: 179]
│   │   │   │   │       public bool IsHigherBetter { get; set; } = true; [cite: 180]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [180]
│   │   │   │   │   public class DepartmentStrategicIndicatorDefinitionResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string IndicatorSystemId { get; set; } [cite: 181]
│   │   │   │   │       public string Name { get; set; } [cite: 182]
│   │   │   │   │       public string Description { get; set; } [cite: 183]
│   │   │   │   │       public string DataType { get; set; } [cite: 184]
│   │   │   │   │       public bool IsHigherBetter { get; set; } [cite: 185]
│   │   │   │   │       public bool IsActive { get; set; } = true; [cite: 186]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [187]
│   │   │   │   │   public class DepartmentStrategicPerformanceDataRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public string DepartmentId { get; set; } // Identifier for the department from Org. Management [cite: 188]
│   │   │   │   │       [Required]
│   │   │   │   │       public string AssessmentPeriodId { get; set; } // e.g., "2025-AcademicYear", "2025-Q1" [cite: 189]
│   │   │   │   │       [Required]
│   │   │   │   │       public List<IndicatorPerformanceEntryDto> PerformanceEntries { get; set; } = new(); [cite: 190]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [190]
│   │   │   │   │   public class IndicatorPerformanceEntryDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public string IndicatorSystemId { get; set; } // FK to DepartmentStrategicIndicatorDefinitionEntity [cite: 191]
│   │   │   │   │       public string ActualValue { get; set; } // Stored as string, parsed based on Indicator's DataType [cite: 192]
│   │   │   │   │       public string TargetValue { get; set; } // Optional [cite: 193]
│   │   │   │   │       public string Notes { get; set; } [cite: 194]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [194]
│   │   │   │   │   public class DepartmentStrategicPerformanceDataResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string Id { get; set; } // Public GUID Id of the main data entry record [cite: 195]
│   │   │   │   │       public string DepartmentId { get; set; } [cite: 196]
│   │   │   │   │       public string DepartmentName { get; set; } // Fetched for context [cite: 197]
│   │   │   │   │       public string AssessmentPeriodId { get; set; } [cite: 198]
│   │   │   │   │       public List<IndicatorPerformanceResponseDto> PerformanceEntries { get; set; } = new(); [cite: 199]
│   │   │   │   │       public DateTime SubmittedAt { get; set; } [cite: 199]
│   │   │   │   │       public string SubmittedByUserId { get; set; } [cite: 200]
│   │   │   │   │       public DateTime LastUpdatedAt { get; set; } [cite: 201]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [201]
│   │   │   │   │   public class IndicatorPerformanceResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string IndicatorSystemId { get; set; } [cite: 202]
│   │   │   │   │       public string IndicatorName { get; set; } // Fetched for context [cite: 203]
│   │   │   │   │       public string ActualValue { get; set; } [cite: 204]
│   │   │   │   │       public string TargetValue { get; set; } [cite: 205]
│   │   │   │   │       public string Notes { get; set; } [cite: 206]
│   │   │   │   │       public string DataType { get; set; } // Fetched for context [cite: 207]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── StaffCompetencyDtos.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [208]
│   │   │   │   │   public class StaffCompetencyDefinitionRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(100)]
│   │   │   │   │       public string CompetencySystemId { get; set; } [cite: 209]
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(250)]
│   │   │   │   │       public string Name { get; set; } [cite: 210]
│   │   │   │   │       [StringLength(1000)]
│   │   │   │   │       public string Description { get; set; } [cite: 211]
│   │   │   │   │       public List<string> RatingScale { get; set; } = new(); // e.g., ["Below Expectations", "Meets Expectations", "Exceeds Expectations"] [cite: 212]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [212]
│   │   │   │   │   public class StaffCompetencyDefinitionResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string CompetencySystemId { get; set; } [cite: 213]
│   │   │   │   │       public string Name { get; set; } [cite: 214]
│   │   │   │   │       public string Description { get; set; } [cite: 215]
│   │   │   │   │       public List<string> RatingScale { get; set; } [cite: 216]
│   │   │   │   │       public bool IsActive { get; set; } = true; [cite: 217]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [218]
│   │   │   │   │   public class StaffCompetencyEvaluationRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public string AcademicianUniveristyUserId { get; set; } // The staff member being evaluated [cite: 219]
│   │   │   │   │       [Required]
│   │   │   │   │       public string EvaluationContextId { get; set; } // e.g., "AnnualReview-2025", "ProjectAlpha-Review" [cite: 220]
│   │   │   │   │       public List<CompetencyRatingEntryDto> CompetencyRatings { get; set; } = new(); [cite: 221]
│   │   │   │   │       public string OverallComments { get; set; } [cite: 222]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [222]
│   │   │   │   │   public class CompetencyRatingEntryDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public string CompetencySystemId { get; set; } // FK to StaffCompetencyDefinitionEntity [cite: 223]
│   │   │   │   │       [Required]
│   │   │   │   │       public string Rating { get; set; } // Must be one of the defined RatingScale values [cite: 224]
│   │   │   │   │       public string Comments { get; set; } // Specific comments for this competency [cite: 225]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [225]
│   │   │   │   │   public class StaffCompetencyEvaluationResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string Id { get; set; } // Public GUID Id of the evaluation record [cite: 226]
│   │   │   │   │       public string AcademicianUniveristyUserId { get; set; } [cite: 227]
│   │   │   │   │       public string AcademicianName { get; set; } // Fetched for context [cite: 228]
│   │   │   │   │       public string EvaluatingManagerUserId { get; set; } // From UserContextCo [cite: 229]
│   │   │   │   │       public string EvaluatingManagerName { get; set; } // Fetched for context [cite: 230]
│   │   │   │   │       public string EvaluationContextId { get; set; } [cite: 231]
│   │   │   │   │       public List<CompetencyRatingResponseDto> CompetencyRatings { get; set; } = new(); [cite: 232]
│   │   │   │   │       public string OverallComments { get; set; } [cite: 232]
│   │   │   │   │       public DateTime SubmittedAt { get; set; } [cite: 233]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [233]
│   │   │   │   │   public class CompetencyRatingResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string CompetencySystemId { get; set; } [cite: 234]
│   │   │   │   │       public string CompetencyName { get; set; } // Fetched for context [cite: 235]
│   │   │   │   │       public string Rating { get; set; } [cite: 236]
│   │   │   │   │       public string Comments { get; set; } [cite: 237]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── PortfolioControlDtos.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [238]
│   │   │   │   │   public class PortfolioChecklistItemDefinitionRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(100)]
│   │   │   │   │       public string ItemSystemId { get; set; } [cite: 239]
│   │   │   │   │       [Required]
│   │   │   │   │       [StringLength(250)]
│   │   │   │   │       public string Name { get; set; } [cite: 240]
│   │   │   │   │       [StringLength(1000)]
│   │   │   │   │       public string Description { get; set; } [cite: 241]
│   │   │   │   │       public string ApplicableCadreHint { get; set; } // e.g., "All", "PhD Candidates" [cite: 242]
│   │   │   │   │       public string ExpectedLocationInEbysHint { get; set; } [cite: 243]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [243]
│   │   │   │   │   public class PortfolioChecklistItemDefinitionResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string ItemSystemId { get; set; } [cite: 244]
│   │   │   │   │       public string Name { get; set; } [cite: 245]
│   │   │   │   │       public string Description { get; set; } [cite: 246]
│   │   │   │   │       public string ApplicableCadreHint { get; set; } [cite: 247]
│   │   │   │   │       public string ExpectedLocationInEbysHint { get; set; } [cite: 248]
│   │   │   │   │       public bool IsActive { get; set; } = true; [cite: 249]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [250]
│   │   │   │   │   public class PortfolioVerificationUpdateRequestDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public string AcademicianUniveristyUserId { get; set; } [cite: 251]
│   │   │   │   │       [Required]
│   │   │   │   │       public string ItemSystemId { get; set; } // FK to PortfolioChecklistItemDefinitionEntity [cite: 252]
│   │   │   │   │       [Required]
│   │   │   │   │       public string VerificationStatus { get; set; } // e.g., "VerifiedInEbys", "Missing", "DiscrepancyFound", "NotApplicable" [cite: 253]
│   │   │   │   │       public string ArchivistComments { get; set; } [cite: 254]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [254]
│   │   │   │   │   public class PortfolioVerificationResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string Id { get; set; } // Public GUID Id of the verification record [cite: 255]
│   │   │   │   │       public string AcademicianUniveristyUserId { get; set; } [cite: 256]
│   │   │   │   │       public string AcademicianName { get; set; } // Fetched [cite: 257]
│   │   │   │   │       public string ItemSystemId { get; set; } [cite: 258]
│   │   │   │   │       public string ItemName { get; set; } // Fetched [cite: 259]
│   │   │   │   │       public string VerificationStatus { get; set; } [cite: 260]
│   │   │   │   │       public string ArchivistComments { get; set; } [cite: 261]
│   │   │   │   │       public DateTime LastVerifiedAt { get; set; } [cite: 262]
│   │   │   │   │       public string LastVerifiedByArchivistUserId { get; set; } [cite: 263]
│   │   │   │   │   }

│   │   │   │   │   // Source: data-models.txt [264]
│   │   │   │   │   public class AcademicianPortfolioStatusResponseDto
│   │   │   │   │   {
│   │   │   │   │       public string AcademicianUniveristyUserId { get; set; } [cite: 265]
│   │   │   │   │       public string AcademicianName { get; set; } [cite: 266]
│   │   │   │   │       public string Department { get; set; } [cite: 267]
│   │   │   │   │       public int TotalItems { get; set; } [cite: 268]
│   │   │   │   │       public int VerifiedItems { get; set; } [cite: 269]
│   │   │   │   │       public int MissingItems { get; set; } [cite: 270]
│   │   │   │   │       public string OverallPortfolioStatus { get; set; } // e.g., "PendingReview", "InReview", "CompletedWithIssues", "Completed" [cite: 271]
│   │   │   │   │       public List<PortfolioVerificationResponseDto> VerificationDetails { get; set; } = new(); [cite: 272]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   └── GenericDataEntryDtos.cs
│   │   │   │       ```csharp
│   │   │   │       // Source: data-models.txt [273]
│   │   │   │       public class GenericDataEntryDefinitionRequestDto
│   │   │   │       {
│   │   │   │           [Required]
│   │   │   │           [StringLength(100)]
│   │   │   │           public string EntryTypeSystemId { get; set; } [cite: 274]
│   │   │   │           [Required]
│   │   │   │           [StringLength(250)]
│   │   │   │           public string Name { get; set; } [cite: 275]
│   │   │   │           [StringLength(1000)]
│   │   │   │           public string Description { get; set; } [cite: 276]
│   │   │   │           public string ApplicableRoleHint { get; set; } // e.g., "APDYS_DataEntry_Library", "APDYS_DataEntry_TTO" [cite: 277]
│   │   │   │           [Required]
│   │   │   │           public string DataFieldLabel { get; set; } // e.g., "Outstanding Fine Amount", "Number of Patents Filed" [cite: 278]
│   │   │   │           [Required]
│   │   │   │           public string DataFieldType { get; set; } // "Number", "Text", "Boolean", "Currency" [cite: 279]
│   │   │   │       }

│   │   │   │       // Source: data-models.txt [279]
│   │   │   │       public class GenericDataEntryDefinitionResponseDto
│   │   │   │       {
│   │   │   │           public string EntryTypeSystemId { get; set; } [cite: 280]
│   │   │   │           public string Name { get; set; } [cite: 281]
│   │   │   │           public string Description { get; set; } [cite: 282]
│   │   │   │           public string ApplicableRoleHint { get; set; } [cite: 283]
│   │   │   │           public string DataFieldLabel { get; set; } [cite: 284]
│   │   │   │           public string DataFieldType { get; set; } [cite: 285]
│   │   │   │           public bool IsActive { get; set; } = true; [cite: 286]
│   │   │   │       }

│   │   │   │       // Source: data-models.txt [287]
│   │   │   │       public class GenericDataEntryRequestDto
│   │   │   │       {
│   │   │   │           [Required]
│   │   │   │           public string AcademicianUniveristyUserId { get; set; } // The academician this data pertains to [cite: 288]
│   │   │   │           [Required]
│   │   │   │           public string EntryTypeSystemId { get; set; } // FK to GenericDataEntryDefinitionEntity [cite: 289]
│   │   │   │           [Required]
│   │   │   │           public string Value { get; set; } // Stored as string, parsed based on DataFieldType [cite: 290]
│   │   │   │           public string AssessmentPeriodId { get; set; } // Optional, e.g., "2025" [cite: 291]
│   │   │   │           public string Notes { get; set; } [cite: 292]
│   │   │   │       }

│   │   │   │       // Source: data-models.txt [292]
│   │   │   │       public class GenericDataEntryResponseDto
│   │   │   │       {
│   │   │   │           public string Id { get; set; } // Public GUID Id of the entry [cite: 293]
│   │   │   │           public string AcademicianUniveristyUserId { get; set; } [cite: 294]
│   │   │   │           public string AcademicianName { get; set; } // Fetched [cite: 295]
│   │   │   │           public string EntryTypeSystemId { get; set; } [cite: 296]
│   │   │   │           public string EntryTypeName { get; set; } // Fetched [cite: 297]
│   │   │   │           public string Value { get; set; } [cite: 298]
│   │   │   │           public string AssessmentPeriodId { get; set; } [cite: 299]
│   │   │   │           public string Notes { get; set; } [cite: 300]
│   │   │   │           public DateTime SubmittedAt { get; set; } [cite: 301]
│   │   │   │           public string SubmittedByUserId { get; set; } // User from data entry role [cite: 302]
│   │   │   │       }
│   │   │   │       ```
│   │   │   │   ├── NotificationDtos.cs // For NotificationMessageDto from section 5.1
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [516]
│   │   │   │   │   public class NotificationMessageDto
│   │   │   │   │   {
│   │   │   │   │       [Required]
│   │   │   │   │       public string NotificationType { get; set; } // e.g., "SubmissionReceived", "SubmissionApproved" [cite: 517]
│   │   │   │   │       [Required]
│   │   │   │   │       public string RecipientUserId { get; set; } // University User ID of the recipient [cite: 518]
│   │   │   │   │       public string RecipientEmail { get; set; } // Optional: Can be fetched by NotificationService [cite: 519]
│   │   │   │   │       public Dictionary<string, string> TemplateData { get; set; } = new(); // Key-value pairs for email template [cite: 520]
│   │   │   │   │       public DateTime Timestamp { get; set; } = DateTime.UtcNow; [cite: 521]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   ├── Cos/
│   │   │   │   ├── UserContextCo.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [304]
│   │   │   │   │   public class UserContextCo
│   │   │   │   │   {
│   │   │   │   │       public string UserId { get; set; } // University User ID [cite: 305]
│   │   │   │   │       public List<string> ApdysRoleNames { get; set; } // APDYS specific role names [cite: 306]
│   │   │   │   │       public List<string> ApdysPermissions { get; set; } // APDYS specific permission names [cite: 307]
│   │   │   │   │       public string AcademicCadre { get; set; } [cite: 308]
│   │   │   │   │       public string Department { get; set; } [cite: 309]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── FetchSubmissionsQueryCo.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [310]
│   │   │   │   │   public class FetchSubmissionsQueryCo
│   │   │   │   │   {
│   │   │   │   │       public string AcademicianUserId { get; set; } [cite: 311]
│   │   │   │   │       public string ControllerUserId { get; set; } // For controller's view [cite: 312]
│   │   │   │   │       public List<string> Statuses { get; set; } [cite: 313]
│   │   │   │   │       public string FormId { get; set; } // Public GUID Id [cite: 314]
│   │   │   │   │       public int PageNumber { get; set; } = 1; [cite: 315]
│   │   │   │   │       public int PageSize { get; set; } = 20; [cite: 315]
│   │   │   │   │       public string SortBy { get; set; } // e.g., "SubmittedAt", "AcademicianName" [cite: 316]
│   │   │   │   │       public string SortOrder { get; set; } // "asc" or "desc" [cite: 317]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   └── ProcessStaticCriterionValueCo.cs
│   │   │   │       ```csharp
│   │   │   │       // Source: data-models.txt [318]
│   │   │   │       public class ProcessStaticCriterionValueCo
│   │   │   │       {
│   │   │   │           public string AcademicianUserId { get; set; } [cite: 319]
│   │   │   │           public string StaticCriterionSystemId { get; set; } [cite: 320]
│   │   │   │           public string DataSourceHint { get; set; } [cite: 321]
│   │   │   │           public DateTime EvaluationDateContext { get; set; } // To fetch period-specific static data [cite: 322]
│   │   │   │       }
│   │   │   │       ```
│   │   │   ├── Entities/
│   │   │   │   ├── ApdysRoleEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [324]
│   │   │   │   │   public class ApdysRoleEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 325]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 326]
│   │   │   │   │       [Required] [StringLength(100)] public string RoleName { get; set; } // Unique Index [cite: 327]
│   │   │   │   │       [StringLength(500)] public string Description { get; set; } [cite: 328]
│   │   │   │   │       public DateTime CreatedAt { get; set; } = DateTime.UtcNow; [cite: 329]
│   │   │   │   │       public virtual ICollection<ApdysRolePermissionEntity> RolePermissions { get; set; } = new List<ApdysRolePermissionEntity>(); [cite: 329]
│   │   │   │   │       public virtual ICollection<UserApdysRoleMappingEntity> UserMappings { get; set; } = new List<UserApdysRoleMappingEntity>(); [cite: 330]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── ApdysPermissionEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [330]
│   │   │   │   │   public class ApdysPermissionEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 331]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 332]
│   │   │   │   │       [Required] [StringLength(150)] public string PermissionName { get; set; } // Unique Index [cite: 333]
│   │   │   │   │       [StringLength(500)] public string Description { get; set; } [cite: 334]
│   │   │   │   │       public DateTime CreatedAt { get; set; } = DateTime.UtcNow; [cite: 335]
│   │   │   │   │       public virtual ICollection<ApdysRolePermissionEntity> RolePermissions { get; set; } = new List<ApdysRolePermissionEntity>(); [cite: 336]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── ApdysRolePermissionEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [336]
│   │   │   │   │   public class ApdysRolePermissionEntity
│   │   │   │   │   {
│   │   │   │   │       public int ApdysRoleAutoIncrementId { get; set; } // Composite PK part 1, FK [cite: 337]
│   │   │   │   │       public int ApdysPermissionAutoIncrementId { get; set; } // Composite PK part 2, FK [cite: 338]
│   │   │   │   │       public DateTime AssignedAt { get; set; } = DateTime.UtcNow; [cite: 339]
│   │   │   │   │       public virtual ApdysRoleEntity ApdysRole { get; set; } [cite: 340]
│   │   │   │   │       public virtual ApdysPermissionEntity ApdysPermission { get; set; } [cite: 341]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── UserApdysRoleMappingEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [342]
│   │   │   │   │   public class UserApdysRoleMappingEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 343]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 344]
│   │   │   │   │       [Required] public string UniversityUserId { get; set; } // Indexed [cite: 345]
│   │   │   │   │       public int ApdysRoleAutoIncrementId { get; set; } // FK [cite: 346]
│   │   │   │   │       public DateTime AssignedAt { get; set; } = DateTime.UtcNow; [cite: 347]
│   │   │   │   │       public virtual ApdysRoleEntity ApdysRole { get; set; } [cite: 348]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── StaticCriterionDefinitionEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [348]
│   │   │   │   │   public class StaticCriterionDefinitionEntity
│   │   │   │   │   {
│   │   │   │   │       [Key]
│   │   │   │   │       [StringLength(100)]
│   │   │   │   │       public string StaticCriterionSystemId { get; set; } [cite: 349]
│   │   │   │   │       [Required] [StringLength(200)] public string Name { get; set; } [cite: 350]
│   │   │   │   │       [StringLength(1000)] public string Description { get; set; } [cite: 351]
│   │   │   │   │       public bool IsActive { get; set; } = true; [cite: 352]
│   │   │   │   │       [StringLength(250)] public string DataSourceHint { get; set; } [cite: 353]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── EvaluationFormEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [353]
│   │   │   │   │   public class EvaluationFormEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 354]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 355]
│   │   │   │   │       [Required] [StringLength(250)] public string Name { get; set; } [cite: 356]
│   │   │   │   │       public string ApplicableAcademicCadresJson { get; set; } // Store as JSON string [cite: 357]
│   │   │   │   │       public DateTime EvaluationPeriodStartDate { get; set; } [cite: 358]
│   │   │   │   │       public DateTime EvaluationPeriodEndDate { get; set; } [cite: 359]
│   │   │   │   │       [Required] [StringLength(50)] public string Status { get; set; } // "Draft", "Active", "Archived" [cite: 360]
│   │   │   │   │       public DateTime CreatedAt { get; set; } = DateTime.UtcNow; [cite: 361]
│   │   │   │   │       public string CreatedByUserId { get; set; } [cite: 362]
│   │   │   │   │       public DateTime UpdatedAt { get; set; } = DateTime.UtcNow; [cite: 363]
│   │   │   │   │       public string UpdatedByUserId { get; set; } [cite: 363]
│   │   │   │   │       public virtual ICollection<FormCategoryEntity> Categories { get; set; } = new List<FormCategoryEntity>(); [cite: 364]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── FormCategoryEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [364]
│   │   │   │   │   public class FormCategoryEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 365]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 366]
│   │   │   │   │       public int EvaluationFormAutoIncrementId { get; set; } // FK [cite: 367]
│   │   │   │   │       [Required] [StringLength(150)] public string Name { get; set; } [cite: 368]
│   │   │   │   │       public double Weight { get; set; } // e.g., 0.4 for 40% [cite: 369]
│   │   │   │   │       public int DisplayOrder { get; set; } [cite: 370]
│   │   │   │   │       public DateTime CreatedAt { get; set; } = DateTime.UtcNow; [cite: 371]
│   │   │   │   │       public string CreatedByUserId { get; set; } [cite: 371]
│   │   │   │   │       public DateTime UpdatedAt { get; set; } = DateTime.UtcNow; [cite: 372]
│   │   │   │   │       public string UpdatedByUserId { get; set; } [cite: 373]
│   │   │   │   │       public virtual EvaluationFormEntity EvaluationForm { get; set; } [cite: 374]
│   │   │   │   │       public virtual ICollection<FormCriterionLinkEntity> CriterionLinks { get; set; } = new List<FormCriterionLinkEntity>(); [cite: 375]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── FormCriterionLinkEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [375]
│   │   │   │   │   public class FormCriterionLinkEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 376]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 377]
│   │   │   │   │       public int FormCategoryAutoIncrementId { get; set; } // FK [cite: 378]
│   │   │   │   │       [Required] [StringLength(50)] public string CriterionType { get; set; } // "Dynamic" or "Static" [cite: 379]
│   │   │   │   │       [StringLength(100)] public string DynamicCriterionTemplateId { get; set; } // Nullable, MongoDB ObjectId string [cite: 380]
│   │   │   │   │       [StringLength(100)] public string StaticCriterionSystemId { get; set; } // Nullable, FK [cite: 381]
│   │   │   │   │       public int DisplayOrder { get; set; } [cite: 382]
│   │   │   │   │       public DateTime CreatedAt { get; set; } = DateTime.UtcNow; [cite: 383]
│   │   │   │   │       public string CreatedByUserId { get; set; } [cite: 383]
│   │   │   │   │       public virtual FormCategoryEntity FormCategory { get; set; } [cite: 384]
│   │   │   │   │       public virtual StaticCriterionDefinitionEntity StaticCriterionDefinition { get; set; } [cite: 385]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── AcademicSubmissionEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [385]
│   │   │   │   │   public class AcademicSubmissionEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 386]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 387]
│   │   │   │   │       public int EvaluationFormAutoIncrementId { get; set; } // FK [cite: 388]
│   │   │   │   │       [Required] public string AcademicianUniveristyUserId { get; set; } [cite: 389]
│   │   │   │   │       [Required] [StringLength(50)] public string Status { get; set; } [cite: 390]
│   │   │   │   │       public DateTime CreatedAt { get; set; } = DateTime.UtcNow; [cite: 391]
│   │   │   │   │       public DateTime UpdatedAt { get; set; } = DateTime.UtcNow; [cite: 391]
│   │   │   │   │       public DateTime? SubmittedAt { get; set; } [cite: 392]
│   │   │   │   │       public DateTime? ApprovedAt { get; set; } [cite: 393]
│   │   │   │   │       public string ApprovedByControllerUserId { get; set; } [cite: 394]
│   │   │   │   │       [StringLength(2000)] public string ApprovalComments { get; set; } [cite: 395]
│   │   │   │   │       public DateTime? RejectedAt { get; set; } [cite: 396]
│   │   │   │   │       public string RejectedByControllerUserId { get; set; } [cite: 397]
│   │   │   │   │       [StringLength(2000)] public string RejectionComments { get; set; } [cite: 398]
│   │   │   │   │       public virtual EvaluationFormEntity EvaluationForm { get; set; } [cite: 399]
│   │   │   │   │       public virtual ICollection<EvidenceFileEntity> EvidenceFiles { get; set; } = new List<EvidenceFileEntity>(); [cite: 400]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── EvidenceFileEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [400]
│   │   │   │   │   public class EvidenceFileEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 401]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 402]
│   │   │   │   │       public int AcademicSubmissionAutoIncrementId { get; set; } // FK [cite: 403]
│   │   │   │   │       [Required] public string SubmittedDynamicDataInstanceId { get; set; } // FK (string): Links file to specific MongoDB dynamic data entry instance [cite: 404]
│   │   │   │   │       [Required] [StringLength(255)] public string FileName { get; set; } [cite: 405]
│   │   │   │   │       [Required] [StringLength(100)] public string ContentType { get; set; } [cite: 406]
│   │   │   │   │       public long SizeBytes { get; set; } [cite: 407]
│   │   │   │   │       [Required] public string StoredFilePath { get; set; } [cite: 408]
│   │   │   │   │       public DateTime UploadedAt { get; set; } = DateTime.UtcNow; [cite: 409]
│   │   │   │   │       [Required] public string UploadedByUniveristyUserId { get; set; } [cite: 410]
│   │   │   │   │       public virtual AcademicSubmissionEntity AcademicSubmission { get; set; } [cite: 411]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── DepartmentStrategicIndicatorDefinitionEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [411]
│   │   │   │   │   public class DepartmentStrategicIndicatorDefinitionEntity
│   │   │   │   │   {
│   │   │   │   │       [Key]
│   │   │   │   │       [StringLength(100)]
│   │   │   │   │       public string IndicatorSystemId { get; set; } [cite: 412]
│   │   │   │   │       [Required] [StringLength(250)] public string Name { get; set; } [cite: 413]
│   │   │   │   │       [StringLength(1000)] public string Description { get; set; } [cite: 414]
│   │   │   │   │       [Required] [StringLength(50)] public string DataType { get; set; } [cite: 415]
│   │   │   │   │       public bool IsHigherBetter { get; set; } = true; [cite: 416]
│   │   │   │   │       public bool IsActive { get; set; } = true; [cite: 416]
│   │   │   │   │       public DateTime CreatedAt { get; set; } = DateTime.UtcNow; [cite: 417]
│   │   │   │   │       public string CreatedByUserId { get; set; } [cite: 417]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── DepartmentStrategicPerformanceDataEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [417]
│   │   │   │   │   public class DepartmentStrategicPerformanceDataEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 418]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 419]
│   │   │   │   │       [Required] [StringLength(100)] public string DepartmentId { get; set; } [cite: 420]
│   │   │   │   │       [Required] [StringLength(100)] public string AssessmentPeriodId { get; set; } [cite: 421]
│   │   │   │   │       [Required] [StringLength(100)] public string IndicatorSystemId { get; set; } // FK [cite: 422]
│   │   │   │   │       public string ActualValue { get; set; } [cite: 423]
│   │   │   │   │       public string TargetValue { get; set; } [cite: 424]
│   │   │   │   │       [StringLength(2000)] public string Notes { get; set; } [cite: 425]
│   │   │   │   │       public DateTime SubmittedAt { get; set; } = DateTime.UtcNow; [cite: 426]
│   │   │   │   │       [Required] public string SubmittedByUserId { get; set; } [cite: 427]
│   │   │   │   │       public virtual DepartmentStrategicIndicatorDefinitionEntity IndicatorDefinition { get; set; } [cite: 428]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── StaffCompetencyDefinitionEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [428]
│   │   │   │   │   public class StaffCompetencyDefinitionEntity
│   │   │   │   │   {
│   │   │   │   │       [Key]
│   │   │   │   │       [StringLength(100)]
│   │   │   │   │       public string CompetencySystemId { get; set; } [cite: 429]
│   │   │   │   │       [Required] [StringLength(250)] public string Name { get; set; } [cite: 430]
│   │   │   │   │       [StringLength(1000)] public string Description { get; set; } [cite: 431]
│   │   │   │   │       public string RatingScaleJson { get; set; } // Store as JSON string [cite: 432]
│   │   │   │   │       public bool IsActive { get; set; } = true; [cite: 433]
│   │   │   │   │       public DateTime CreatedAt { get; set; } = DateTime.UtcNow; [cite: 433]
│   │   │   │   │       public string CreatedByUserId { get; set; } [cite: 434]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── StaffCompetencyEvaluationEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [434]
│   │   │   │   │   public class StaffCompetencyEvaluationEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 435]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 436]
│   │   │   │   │       [Required] public string AcademicianUniveristyUserId { get; set; } [cite: 437]
│   │   │   │   │       [Required] public string EvaluatingManagerUserId { get; set; } [cite: 438]
│   │   │   │   │       [Required] [StringLength(100)] public string EvaluationContextId { get; set; } [cite: 439]
│   │   │   │   │       [StringLength(4000)] public string OverallComments { get; set; } [cite: 440]
│   │   │   │   │       public DateTime SubmittedAt { get; set; } = DateTime.UtcNow; [cite: 441]
│   │   │   │   │       public virtual ICollection<CompetencyRatingEntity> CompetencyRatings { get; set; } = new List<CompetencyRatingEntity>(); [cite: 442]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── CompetencyRatingEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [442]
│   │   │   │   │   public class CompetencyRatingEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 443]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 444]
│   │   │   │   │       public int StaffCompetencyEvaluationAutoIncrementId { get; set; } // FK [cite: 445]
│   │   │   │   │       [Required] [StringLength(100)] public string CompetencySystemId { get; set; } // FK [cite: 446]
│   │   │   │   │       [Required] [StringLength(100)] public string Rating { get; set; } [cite: 447]
│   │   │   │   │       [StringLength(1000)] public string Comments { get; set; } [cite: 448]
│   │   │   │   │       public virtual StaffCompetencyEvaluationEntity Evaluation { get; set; } [cite: 449]
│   │   │   │   │       public virtual StaffCompetencyDefinitionEntity CompetencyDefinition { get; set; } [cite: 450]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── PortfolioChecklistItemDefinitionEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [450]
│   │   │   │   │   public class PortfolioChecklistItemDefinitionEntity
│   │   │   │   │   {
│   │   │   │   │       [Key]
│   │   │   │   │       [StringLength(100)]
│   │   │   │   │       public string ItemSystemId { get; set; } [cite: 451]
│   │   │   │   │       [Required] [StringLength(250)] public string Name { get; set; } [cite: 452]
│   │   │   │   │       [StringLength(1000)] public string Description { get; set; } [cite: 453]
│   │   │   │   │       [StringLength(100)] public string ApplicableCadreHint { get; set; } [cite: 454]
│   │   │   │   │       [StringLength(250)] public string ExpectedLocationInEbysHint { get; set; } [cite: 455]
│   │   │   │   │       public bool IsActive { get; set; } = true; [cite: 456]
│   │   │   │   │       public DateTime CreatedAt { get; set; } = DateTime.UtcNow; [cite: 456]
│   │   │   │   │       public string CreatedByUserId { get; set; } [cite: 457]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── PortfolioVerificationLogEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [457]
│   │   │   │   │   public class PortfolioVerificationLogEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 458]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 459]
│   │   │   │   │       [Required] public string AcademicianUniveristyUserId { get; set; } [cite: 460]
│   │   │   │   │       [Required] [StringLength(100)] public string ItemSystemId { get; set; } // FK [cite: 461]
│   │   │   │   │       [Required] [StringLength(100)] public string VerificationStatus { get; set; } [cite: 462]
│   │   │   │   │       [StringLength(2000)] public string ArchivistComments { get; set; } [cite: 463]
│   │   │   │   │       public DateTime LastVerifiedAt { get; set; } = DateTime.UtcNow; [cite: 464]
│   │   │   │   │       [Required] public string LastVerifiedByArchivistUserId { get; set; } [cite: 465]
│   │   │   │   │       public virtual PortfolioChecklistItemDefinitionEntity ChecklistItemDefinition { get; set; } [cite: 466]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── GenericDataEntryDefinitionEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [466]
│   │   │   │   │   public class GenericDataEntryDefinitionEntity
│   │   │   │   │   {
│   │   │   │   │       [Key]
│   │   │   │   │       [StringLength(100)]
│   │   │   │   │       public string EntryTypeSystemId { get; set; } [cite: 467]
│   │   │   │   │       [Required] [StringLength(250)] public string Name { get; set; } [cite: 468]
│   │   │   │   │       [StringLength(1000)] public string Description { get; set; } [cite: 469]
│   │   │   │   │       [StringLength(100)] public string ApplicableRoleHint { get; set; } [cite: 470]
│   │   │   │   │       [Required] [StringLength(150)] public string DataFieldLabel { get; set; } [cite: 471]
│   │   │   │   │       [Required] [StringLength(50)] public string DataFieldType { get; set; } [cite: 472]
│   │   │   │   │       public bool IsActive { get; set; } = true; [cite: 473]
│   │   │   │   │       public DateTime CreatedAt { get; set; } = DateTime.UtcNow; [cite: 473]
│   │   │   │   │       public string CreatedByUserId { get; set; } [cite: 474]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   ├── GenericDataEntryRecordEntity.cs
│   │   │   │   │   ```csharp
│   │   │   │   │   // Source: data-models.txt [474]
│   │   │   │   │   public class GenericDataEntryRecordEntity
│   │   │   │   │   {
│   │   │   │   │       public int AutoIncrementId { get; set; } // PK [cite: 475]
│   │   │   │   │       public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 476]
│   │   │   │   │       [Required] public string AcademicianUniveristyUserId { get; set; } [cite: 477]
│   │   │   │   │       [Required] [StringLength(100)] public string EntryTypeSystemId { get; set; } // FK [cite: 478]
│   │   │   │   │       [Required] public string Value { get; set; } [cite: 479]
│   │   │   │   │       [StringLength(100)] public string AssessmentPeriodId { get; set; } [cite: 480]
│   │   │   │   │       [StringLength(2000)] public string Notes { get; set; } [cite: 481]
│   │   │   │   │       public DateTime SubmittedAt { get; set; } = DateTime.UtcNow; [cite: 482]
│   │   │   │   │       [Required] public string SubmittedByUserId { get; set; } [cite: 483]
│   │   │   │   │       public virtual GenericDataEntryDefinitionEntity EntryDefinition { get; set; } [cite: 484]
│   │   │   │   │   }
│   │   │   │   │   ```
│   │   │   │   └── NotificationQueueLogEntity.cs // Optional, as per data-models.txt [522, 523]
│   │   │   │       ```csharp
│   │   │   │       // Source: data-models.txt [524]
│   │   │   │       public class NotificationQueueLogEntity
│   │   │   │       {
│   │   │   │           public int AutoIncrementId { get; set; } // PK [cite: 525]
│   │   │   │           public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index [cite: 526]
│   │   │   │           public string NotificationType { get; set; } [cite: 527]
│   │   │   │           public string RecipientUserId { get; set; } [cite: 528]
│   │   │   │           public string PayloadJson { get; set; } // Serialized NotificationMessageDto [cite: 529]
│   │   │   │           public string Status { get; set; } // e.g., "PendingSend", "SentToBroker", "Failed" [cite: 530]
│   │   │   │           public int RetryCount { get; set; } = 0; [cite: 531]
│   │   │   │           public DateTime CreatedAt { get; set; } = DateTime.UtcNow; [cite: 531]
│   │   │   │           public DateTime? LastAttemptAt { get; set; } [cite: 532]
│   │   │   │           public string ErrorMessage { get; set; } [cite: 533]
│   │   │   │       }
│   │   │   │       ```
│   │   │   └── MongoDocuments/
│   │   │       ├── DynamicCriterionTemplateDoc.cs
│   │   │       │   ```csharp
│   │   │       │   // Source: data-models.txt [486]
│   │   │       │   public class DynamicCriterionTemplateDoc
│   │   │       │   {
│   │   │       │       [BsonId]
│   │   │       │       [BsonRepresentation(BsonType.ObjectId)]
│   │   │       │       public string Id { get; set; } [cite: 487]
│   │   │       │       [BsonRequired] public string Name { get; set; } [cite: 488]
│   │   │       │       public string Description { get; set; } [cite: 489]
│   │   │       │       public double? Coefficient { get; set; } [cite: 490]
│   │   │       │       public int? MaxLimit { get; set; } [cite: 491]
│   │   │       │       [BsonRequired] public string Status { get; set; } // "Draft", "Active", "Inactive" [cite: 492]
│   │   │       │       public List<InputFieldDefinitionDoc> InputFields { get; set; } = new(); [cite: 493]
│   │   │       │       public DateTime CreatedAt { get; set; } [cite: 494]
│   │   │       │       public string CreatedByUserId { get; set; } [cite: 495]
│   │   │       │       public DateTime UpdatedAt { get; set; } [cite: 496]
│   │   │       │       public string UpdatedByUserId { get; set; } [cite: 497]
│   │   │       │   }

│   │   │       │   // Source: data-models.txt [497]
│   │   │       │   public class InputFieldDefinitionDoc // Embedded document
│   │   │       │   {
│   │   │       │       [BsonRequired] public string FieldId { get; set; } [cite: 498]
│   │   │       │       [BsonRequired] public string Label { get; set; } [cite: 499]
│   │   │       │       [BsonRequired] public string InputType { get; set; } [cite: 500]
│   │   │       │       public bool IsMandatory { get; set; } [cite: 501]
│   │   │       │       public string PlaceholderText { get; set; } [cite: 502]
│   │   │       │       public double? MinValue { get; set; } [cite: 503]
│   │   │       │       public double? MaxValue { get; set; } [cite: 504]
│   │   │       │       public string AllowedFileTypes { get; set; } [cite: 505]
│   │   │       │       public int? MaxFileSizeMb { get; set; } [cite: 506]
│   │   │       │       public List<string> DropdownOptions { get; set; } = new(); [cite: 507]
│   │   │       │   }
│   │   │       │   ```
│   │   │       └── SubmittedDynamicDataDoc.cs
│   │   │           ```csharp
│   │   │           // Source: data-models.txt [508]
│   │   │           public class SubmittedDynamicDataDoc
│   │   │           {
│   │   │               [BsonId]
│   │   │               [BsonRepresentation(BsonType.ObjectId)]
│   │   │               public string Id { get; set; } // Unique ID for this specific data entry instance [cite: 509]
│   │   │               [BsonRequired] public string AcademicSubmissionId { get; set; } // Public GUID Id of the parent AcademicSubmissionEntity [cite: 510]
│   │   │               [BsonRequired] public string FormCriterionLinkId { get; set; } // Public GUID Id of the FormCriterionLinkEntity (Dynamic type) [cite: 511]
│   │   │               [BsonRequired] public string AcademicianUniveristyUserId { get; set; } [cite: 512]
│   │   │               public Dictionary<string, object> Data { get; set; } = new(); // Key: InputFieldDefinitionDoc.FieldId [cite: 513]
│   │   │               public DateTime CreatedAt { get; set; } = DateTime.UtcNow; [cite: 514]
│   │   │               public DateTime UpdatedAt { get; set; } = DateTime.UtcNow; [cite: 515]
│   │   │           }
│   │   │           ```
│   │   ├── Database/
│   │   │   ├── DbContexts/
│   │   │   │   └── ApdysCoreDbContext.cs
│   │   │   └── Migrations/
│   │   │
│   │   ├── Managers/
│   │   │   ├── ICriteriaManager.cs
│   │   │   ├── CriteriaManager.cs
│   │   │   └── (Other managers...)
│   │   │
│   │   ├── Stores/
│   │   │   ├── ICriteriaStore.cs
│   │   │   ├── CriteriaStore.cs
│   │   │   └── (Other stores...)
│   │   │
│   │   ├── Services/
│   │   │   ├── IOrganizationManagementClient.cs
│   │   │   ├── OrganizationManagementClient.cs
│   │   │   ├── IFileStorageService.cs
│   │   │   └── FileStorageService.cs
│   │   │
│   │   ├── Extensions/
│   │   │   ├── ServiceCollectionExtensions.cs
│   │   │   └── WebApplicationExtensions.cs
│   │   │
│   │   ├── Mappings/
│   │   │   └── ApdysMappingProfile.cs
│   │   │
│   │   └── Constants/
│   │       └── ApdysConstants.cs
│   │
│   ├── Apdys.Notification.Api/
│   │   ├── Apdys.Notification.Api.csproj
│   │   ├── Program.cs
│   │   ├── Models/
│   │   │   └── Dtos/
│   │   │       └── NotificationMessageDto.cs // Already defined under Core.Api/Models/Dtos for this example
│   │   ├── Workers/
│   │   │   └── EmailDispatchWorker.cs
│   │   └── Services/
│   │       ├── IEmailSender.cs
│   │       └── EmailSender.cs
│
└── tests/
    ├── Apdys.Core.Api.UnitTests/
    ├── Apdys.Core.Api.IntegrationTests/
    ├── Apdys.Notification.Api.UnitTests/
    └── Apdys.Notification.Api.IntegrationTests/