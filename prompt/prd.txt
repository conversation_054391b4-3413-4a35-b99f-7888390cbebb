
# Academic Performance Evaluation System (APDYS) Product Requirements Document (PRD)

## Intro

Arel University requires a comprehensive and dynamic system, APDYS (Academic Performance Evaluation System), to measure and manage the performance of its academic staff. This document outlines the requirements for the Minimum Viable Product (MVP) of APDYS, which will serve as a backend microservices application built on .NET 8. The system aims to replace manual or fragmented evaluation processes with a structured, digital, transparent, auditable, and efficient solution that is adaptable to evolving academic roles and evaluation metrics. APDYS will integrate with existing university systems for identity management and organizational data to ensure consistency and streamline processes.

## Goals and Context

- **Project Objectives:**
  * Establish a transparent, efficient, and adaptable digital system for academic performance evaluation.
  * Enable administrators to dynamically create and manage performance criteria, forms, and categories.
  * Allow academicians to submit performance data and supporting evidence.
  * Facilitate a review and approval workflow for submitted data by controllers.
  * Integrate with existing university Identity Server and Organization Management modules.
  * Support specialized evaluation workflows for different university roles.
  * Ensure evaluations are consistent, auditable, and adaptable.

- **Measurable Outcomes:**
  * Successful integration and data synchronization with Identity Server and Organization Management module.
  * Admins can create and configure new performance evaluation forms without code changes.
  * Academicians can successfully submit their performance data and evidence through the system.
  * Controllers can successfully review and action (approve/reject) submissions.
  * The system accurately reflects the defined RBAC for all user roles.
  * Notification system reliably informs users of relevant events (e.g., submission status changes).

- **Success Criteria:**
  * All MVP features related to admin configuration, academician submission, and controller review are functional.
  * Integration with Identity Server and Organization Management is stable and performs as expected.
  * The system correctly implements the defined role-based access controls.
  * The specialized workflows for Department Strategic Performance, Academic Staff Competency, and Portfolio Control are operational.
  * The notification system is functional and delivers timely alerts for critical workflow actions.

- **Key Performance Indicators (KPIs):**
  * Percentage of academic staff actively submitting data within evaluation periods.
  * Average turnaround time from submission to controller review.
  * Admin satisfaction with criteria/form management tools.
  * Reliability and uptime of integrations with Identity Server and Organization Management.
  * Number of successful data submissions and reviews processed.
  * Notification delivery success rate.

## Scope and Requirements (MVP / Current Version)

### Functional Requirements (High-Level)

- **User Authentication and Authorization**
  * Integrate with the existing Identity Server for token-based authentication.
  * Retrieve user details, roles, cadres, and department information from the Organization Management module.
  * Implement internal RBAC to manage permissions for different user roles (Admin, Academician, Controller, Strategic Management Office Staff, Manager, Archivist, Other Data Entry Roles).

- **Criteria Management (Admin)**
  * Admins can create, edit, activate, and deactivate dynamic performance criteria templates.
  * Admins can define custom input types for criteria (text, number, date, file).
  * Admins can manage static criteria.

- **Form and Category Management (Admin)**
  * Admins can build criteria forms and link them to specific academic cadres.
  * Admins can create and manage weighted categories within these forms.
  * Admins can assign criteria to categories with drag‑and‑drop ordering.

- **Academician Data Submission**
  * Academicians can log in and view a personalized dashboard with their assigned form and categories.
  * Academicians can input performance data for each criterion and upload supporting evidence.
  * Academicians can save submissions as drafts and make final submissions.

- **Controller Data Verification**
  * Controllers can view a dashboard of submissions pending their review.
  * Controllers can review submitted data and preview/download supporting evidence.
  * Controllers can approve or reject submissions, providing mandatory comments for rejections.

- **Specialized Evaluation Workflows**
  * Strategic Management Office Staff can enter Department Strategic Performance data.
  * Managers (Deans/HoDs) can evaluate staff for Academic Staff Competency.
  * Archivists can verify portfolio submissions for Portfolio Control, tracking EBYS check status manually.
  * Other designated roles (Library, TTO, Erasmus, etc.) can submit relevant data through dedicated workflows.

- **User Notification System**
  * Implement in‑app alerts (and email if feasible) to inform users of critical events.
  * Notifications for Academicians: submission confirmation, status changes.
  * Notifications for Controllers: new submission awaiting review.
  * Notifications for specialized roles: task assignments or status changes.

- **Audit Logging**
  * Implement audit logging for key actions (criteria creation, form configuration, data submission, approval/rejection).

### Non‑Functional Requirements (NFRs)

- **Performance** – Responsive APIs, efficient during peak periods.
- **Scalability** – Microservices can scale independently.
- **Reliability/Availability** – High availability, robust error handling, data integrity.
- **Security** – Token authentication, granular RBAC, OWASP compliance.
- **Maintainability** – Clean .NET 8 codebase, documented APIs, modular architecture.
- **Usability/Accessibility** – Consistent API responses and error messages.
- **Other Constraints** – Backend: .NET 8; Databases: PostgreSQL & MongoDB; EBYS verification remains manual.

### Integration Requirements

- Identity Server for authentication.
- Organization Management Module for user/org data.

### Testing Requirements

Manual testing by university staff; APIs must be easily testable (e.g., Postman collections).

## Epic Overview

1. **Core Setup & Auth Integration**
2. **Admin – Criteria & Form Management**
3. **Academician – Data Submission Workflow**
4. **Controller – Data Verification Workflow**
5. **Specialized Evaluation Workflows**
6. **User Notification System**
7. **Audit Logging & System Essentials**

## Key Reference Documents

`docs/project-brief.md`, `docs/architecture.md`, `docs/epic*.md`, `docs/tech-stack.md`, `docs/api-reference.md`, `docs/data-models.md`, `docs/testing-strategy.md`

## Post‑MVP / Future Enhancements

- Reporting aggregation service & dashboards.
- Direct API integration with EBYS.
- Advanced notification preferences.
- Workflow versioning.
- Public API for other university systems.

## Change Log

| Change | Date | Version | Description | Author |
| --- | --- | --- | --- | --- |
| Initial Draft | 2025‑05‑15 | 0.1 | First version of the PRD. | 1‑pm AI |
| Updated tech stack & notifications | 2025‑05‑15 | 0.2 | Incorporated user feedback. | 1‑pm AI |

## Initial Architect Prompt

### Technical Infrastructure

- **Starter Template** – Follow .NET 8 microservice best practices.
- **Hosting** – On‑premise servers (Arel University).
- **Backend Platform** – .NET 8 microservices.
- **Databases** – PostgreSQL (relational) & MongoDB (dynamic).

### Technical Constraints

- Integration with Identity Server & Organization Management.
- Microservices architecture.
- Dynamic criteria & input model ensuring query performance.
- Manual EBYS workflow for MVP.
- Distinct, permission‑driven workflows for all roles.

### Deployment Considerations

- On‑premise CI/CD (dev, staging, prod) defined with Arel IT.
- Independent deployment of services.

### Local Development & Testing

- Provide clear API contracts & predictable responses.
- Supply Postman collections for manual testing.

### Other Technical Considerations

- **Security** – Robust authentication/authorization, protect sensitive data.
- **Data Integrity** – Handle evolving criteria & form structures.
- **User Notifications** – Design for reliability; email optional for MVP.
- **API Design** – RESTful, versioned, well‑documented.
- **Scalability & Performance** – Efficient querying, especially during peak periods.
- **Audit Logging** – Capture sufficient detail for future reporting.
