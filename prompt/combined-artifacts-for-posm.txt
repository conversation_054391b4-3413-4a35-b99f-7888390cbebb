
---

### Enhancing Epic 1: Core Setup, Authentication & User Management Integration

**Overall Epic Technical Notes:**
* Ensure all `Rlx.Shared` components (logging, user context, base classes if applicable for controllers/managers/stores, RabbitMQ helpers, OIDC helpers) are properly integrated and utilized from the start.
* Focus on clear separation of concerns when implementing authentication, user data retrieval, and RBAC logic.
* All API endpoints introduced in this epic must adhere to the defined API Reference standards (versioning, error handling, etc.).

---

**Story 1.1: Project Scaffolding and Microservice Boilerplate**

* **User Story / Goal:** As a Developer, I need the initial .NET 8 microservice project(s) set up with appropriate boilerplate, configuration management, and logging framework, so that development can begin on a consistent and structured codebase.
* **Technical Enhancements/Considerations:**
    * Project structure should align with the `project-structure.md` document.
    * Configuration management should leverage `appsettings.json`, environment-specific `appsettings.{Env}.json`, and environment variables as outlined in `environment-vars.md`. Strongly-typed options classes should be used.
    * Logging integration must use `RlxSystemLogHelper` from `Rlx.Shared`.
    * Ensure `.editorconfig` is set up to enforce coding standards from `coding-standards.md`.
    * Include basic Swagger/OpenAPI setup via Swashbuckle.
    * Initial Dockerfile should be optimized for build caching and minimal image size.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** A new .NET 8 solution containing `Apdys.Core.Api` and `Apdys.Notification.Api` projects can be built and run (locally).
    * **AC2:** Application configuration (e.g., a sample database connection string from `appsettings.Development.json`) can be loaded and accessed via `IConfiguration` and strongly-typed options.
    * **AC3:** Basic application startup, shutdown, and a sample error event are logged using `RlxSystemLogHelper`.
    * **AC4:** A Docker image can be successfully built for `Apdys.Core.Api` using its `Dockerfile`.
    * **AC5 (New):** Basic Swagger UI is accessible for `Apdys.Core.Api` when run locally.
    * **AC6 (New):** `Rlx.Shared` (version 1.0.3) is added as a NuGet package reference to relevant projects.
* **Technical Notes for Developers:**
    * Refer to `project-structure.md`, `environment-vars.md`, `coding-standards.md`, `tech-stack.md`.
    * Consider creating shared `Directory.Build.props` / `Directory.Build.targets` files for common project settings (e.g., LangVersion, Nullable, TreatWarningsAsErrors, common package versions like `Rlx.Shared`).

---

**Story 1.2: Identity Server Integration for Authentication**

* **User Story / Goal:** As a User, I want the system to use Arel University's Identity Server for authentication, so that I can log in with my existing university credentials securely.
* **Technical Enhancements/Considerations:**
    * Implementation must use `Microsoft.AspNetCore.Authentication.JwtBearer`.
    * Configuration for `Authority` (Identity Server URL) and `Audience` must come from `environment-vars.md` / `appsettings.json`.
    * Ensure token validation parameters (issuer, lifetime, signature (via JWKS endpoint)) are correctly configured.
    * `Rlx.Shared` might provide OIDC helper configurations or constants; these should be used.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** An unauthenticated request to a protected sample API endpoint in `Apdys.Core.Api` returns a `401 Unauthorized` HTTP status code.
    * **AC2:** A request with a valid JWT token (obtained manually from Arel Identity Server for testing) to the protected sample API endpoint grants access and returns a `200 OK`.
    * **AC3:** User identifier (e.g., `sub` claim or specific user ID claim) and any standard OIDC claims can be successfully extracted from the validated token and are accessible via `HttpContext.User`.
    * **AC4:** An expired token, a token with an invalid signature, or a token with an incorrect audience/issuer results in a `401 Unauthorized` response.
* **Technical Notes for Developers:**
    * The "sample endpoint" can be a simple GET endpoint that returns the authenticated user's claims.
    * Coordinate with Arel IT for correct Identity Server metadata endpoint, client configuration (if APDYS needs to be registered as a client for any reason, though typically for resource server validation, it just needs issuer info), and sample tokens for testing.

---

**Story 1.3: Organization Management Module Integration for User Data**

* **User Story / Goal:** As the System, I need to retrieve user details (like name, email, roles, academic cadre, department) from the Organization Management module after authentication, so that APDYS has the necessary context for user operations and RBAC.
* **Technical Enhancements/Considerations:**
    * Implement a dedicated client service (e.g., `OrganizationManagementClient`) for this, following patterns in `Rlx.Shared` if available.
    * The client needs to be configurable (endpoint/connection details from `environment-vars.md`).
    * Implement caching for fetched user details (e.g., using `IMemoryCache` or Redis if `Rlx.Shared` provides helpers and it's deemed necessary for performance early on) to avoid excessive calls. Cache keys should be based on user ID. Define a reasonable cache expiration policy.
    * Error handling for the client is critical (e.g., Org. Mgmt. service unavailable, user not found).
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Given a valid user identifier (from JWT), the `OrganizationManagementClient` successfully retrieves the user's full name and email.
    * **AC2:** The client can retrieve the user's Arel University roles, academic cadre, and department.
    * **AC3:** If the Organization Management module is unavailable or the user is not found, the error is logged via `RlxSystemLogHelper`, and the system handles it gracefully (e.g., by denying further access if critical info is missing, or using cached data with a staleness indicator if configured).
    * **AC4:** Retrieved user data is correctly mapped to an internal APDYS user context object (e.g., `UserContextCo`).
    * **AC5 (New):** Basic caching for user details is implemented with a configurable expiration time (e.g., 5 minutes).
* **Technical Notes for Developers:**
    * Clarify with Arel IT the exact API contract or database schema/query mechanism for the Organization Management module.
    * Determine if any specific authentication/authorization is needed for APDYS to access the Organization Management module.

---

**Story 1.4: Initial RBAC Data Model and Permission Structure**

* **User Story / Goal:** As an Admin, I need a foundational Role-Based Access Control (RBAC) system defined with core roles and permissions, so that system functionalities can be secured based on user roles.
* **Technical Enhancements/Considerations:**
    * Entities (`ApdysRoleEntity`, `ApdysPermissionEntity`, `ApdysRolePermissionEntity`, `UserApdysRoleMappingEntity`) should be created in PostgreSQL as per `data-models.md`.
    * Implement `Stores` (Repositories) for managing these RBAC entities.
    * Implement `Managers` for RBAC logic (e.g., assigning permissions to roles, checking user permissions).
    * The mechanism to map Org. Management roles/attributes to APDYS roles needs to be robust and configurable (e.g., via a configuration file or database table if complex).
    * Authorization policies (e.g., `[Authorize(Policy = "CanManageCriteria")]`) should be defined and registered in `Program.cs`. These policies will use the RBAC manager to check permissions based on the `UserContextCo`.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** `ApdysRoleEntity`, `ApdysPermissionEntity`, and `ApdysRolePermissionEntity` can be created, read, updated, and deleted (CRUD) via their respective Stores/Managers. (API for this might be admin-only and limited for MVP).
    * **AC2:** A clear mapping logic (configurable or code-based for MVP) exists to derive APDYS roles (e.g., `APDYS_Academician`) from Arel University roles/attributes retrieved from the Organization Management module. The `UserContextCo` is populated with these APDYS roles and their associated APDYS permissions.
    * **AC3:** A sample API endpoint protected by a custom APDYS permission (e.g., requiring `CanViewDashboardPermission`) correctly grants access to users with that permission (via their APDYS role) and denies access (403 Forbidden) to users without it.
    * **AC4:** Initial APDYS roles (e.g., `APDYS_Admin`, `APDYS_Academician`, `APDYS_Controller`) and core permissions (e.g., `SubmitPerformanceData`, `ApproveSubmissions`, `ManageSystemConfiguration`) are seeded into the database at application startup or via a seeding script. Role-permission mappings for these defaults are also seeded.
* **Technical Notes for Developers:**
    * See `data-models.md` for entity structures.
    * Permissions should be granular. Consider a naming convention like `Resource.Action` (e.g., `Forms.Create`, `Submissions.Approve`).
    * The `UserContextCo` should contain the resolved list of APDYS permissions for the current user to make checks efficient.

---

**Story 1.5: Basic User Profile Service**

* **User Story / Goal:** As an Authenticated User, I want to be able to retrieve my basic profile information relevant to APDYS (e.g., name, email, APDYS roles, academic cadre), so that I know how the system identifies me.
* **Technical Enhancements/Considerations:**
    * The API endpoint `/api/v1/users/me` should be implemented.
    * The service logic should aggregate data from the JWT claims (User ID), data fetched via `OrganizationManagementClient`, and APDYS roles/permissions derived from the RBAC system. This aggregated data should form the `UserContextCo` and then be mapped to `UserProfileDto`.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** An authenticated user making a `GET` request to `/api/v1/users/me` receives a `200 OK` response with a `UserProfileDto` payload.
    * **AC2:** The `UserProfileDto` contains the correct:
        * `UserId` (from JWT `sub` or user ID claim).
        * `FullName` and `Email` (from Organization Management module).
        * `ApdysRoles` (list of APDYS role names assigned to the user).
        * `AcademicCadre` and `Department` (from Organization Management module).
    * **AC3:** An unauthenticated request to `/api/v1/users/me` results in a `401 Unauthorized` error.
    * **AC4:** (Implicitly covered by `/me` endpoint) The endpoint only ever returns data for the currently authenticated user.
* **Technical Notes for Developers:**
    * The controller action for this endpoint should be decorated with `[Authorize]` attribute.
    * The manager/service handling this request should have access to the current `UserContextCo` to populate the DTO.

---

Okay, we've successfully enhanced Epic 1. Let's proceed to **Epic 2: Admin - Criteria & Form Management**.

This epic is crucial as it defines the dynamic nature of the evaluation system, heavily involving MongoDB for dynamic criteria and PostgreSQL for form structures.

---

### Enhancing Epic 2: Admin - Criteria & Form Management

**Overall Epic Technical Notes:**
* All operations in this epic are restricted to users with the `APDYS_Admin` role. Robust authorization checks must be in place for all API endpoints.
* Data validation for all DTOs is critical, using .NET validation attributes.
* Leverage `RlxSystemLogHelper` for detailed audit logging of all create, update, and status change operations on criteria, forms, categories, and assignments. Include user ID, timestamp, entity IDs, and changed values where appropriate.
* Consider the implications of updates to criteria or forms that are already in use or have submissions against them. For MVP, restrictions on editing active/in-use items are acceptable.

---

**Story 2.1: Define and Manage Performance Criteria Templates (Dynamic Criteria)**

* **User Story / Goal:** As an Admin, I want to create, view, update, and manage the lifecycle (activate/deactivate) of dynamic performance criteria templates, so that I can define the building blocks for evaluations.
* **Technical Enhancements/Considerations:**
    * Criteria templates will be stored as `DynamicCriterionTemplateDoc` in MongoDB.
    * The `Status` field (Draft, Active, Inactive) is key for lifecycle management.
    * "Associated permissions/visibility" in requirements likely means which academic cadres this criterion *could* apply to, not a direct permission check on the criterion itself. Actual assignment to forms (which are linked to cadres) controls visibility to academicians.
    * For updates (PUT), clearly define which fields can be updated based on the criterion's status (e.g., if `Status` is "Active" and it's used in forms with data, only `Description` or `MaxLimit` might be editable, not `Coefficient` or `InputFields` without versioning – for MVP, stricter limitations are fine).
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Admin can successfully `POST` a `DynamicCriterionTemplateRequestDto` to create a new template. The system saves it to MongoDB with a unique `Id` (ObjectId) and `Status` "Draft" by default. `CreatedAt`, `UpdatedAt`, `CreatedByUserId`, `UpdatedByUserId` fields are populated.
    * **AC2:** Admin can `GET` a list of all `DynamicCriterionTemplateResponseDto`, with optional filtering by `Status`.
    * **AC3:** Admin can `PUT` to update a `DynamicCriterionTemplateRequestDto` for an existing template.
        * **AC3.1 (New):** If the template `Status` is "Draft", all fields can be updated.
        * **AC3.2 (New):** If the template `Status` is "Active" or "Inactive", define MVP restrictions (e.g., only `Name`, `Description`, `MaxLimit` can be updated, or no updates allowed).
    * **AC4:** Admin can `PUT` to change the `Status` of a criterion template (e.g., via a dedicated endpoint like `/api/v1/criteria-templates/{id}/status` with a body `{"status": "Active"}`).
        * **AC4.1 (New):** Changing status to "Active" is only possible if all required fields for active criteria (e.g., `InputFields` are defined if any are needed) are present and valid.
    * **AC5:** API endpoints enforce that input DTOs meet validation rules (e.g., `Name`, `Coefficient` if not nullable, `InputFields` structure).
    * **AC6 (New):** All CRUD operations are audited using `RlxSystemLogHelper`.
* **Technical Notes for Developers:**
    * Use the MongoDB C# Driver for interactions.
    * Implement a `CriteriaTemplateStore` for MongoDB operations.
    * The `CriteriaTemplateManager` will handle business logic, status transitions, and validation.
    * Define the `Status` enum/constants clearly.

---

**Story 2.2: Define Custom Input Types for Dynamic Criteria**

* **User Story / Goal:** As an Admin, when creating/editing a dynamic criterion template, I want to define the specific input types and constraints for the data academicians will submit, so that data collection is structured and validated.
* **Technical Enhancements/Considerations:**
    * `InputFields` will be an embedded list within the `DynamicCriterionTemplateDoc` in MongoDB, as `List<InputFieldDefinitionDoc>`.
    * The structure of `InputFieldDefinitionDoc` (and its DTO `InputFieldDefinitionDto`) needs to robustly support all specified types and their constraints.
    * Validation of these definitions themselves is important (e.g., `DropdownOptions` must be present if `InputType` is "Dropdown").
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1 - AC6 (combined & refined):** When creating or updating a `DynamicCriterionTemplateDoc` via the API, an Admin can successfully define and persist various `InputFieldDefinitionDoc` types within it:
        * Text (single/multi-line - perhaps a `IsMultiLine` boolean property).
        * Number (with optional `MinValue`, `MaxValue` constraints).
        * Date (with optional future date range constraints – for MVP, just date type).
        * File Upload (with optional `AllowedFileTypes` string like "pdf,docx" and `MaxFileSizeMb` integer).
        * Dropdown (with a non-empty `DropdownOptions` list of strings).
    * **AC7 (New):** Admin can mark any defined input field as `IsMandatory`.
    * **AC8 (New):** The API validates the structure of submitted `InputFieldDefinitionDto`s (e.g., if `InputType` is "Dropdown", `DropdownOptions` must not be empty).
    * **AC9 (New):** An `InputFieldDefinitionDto` must have a unique `FieldId` within its parent criterion template.
* **Technical Notes for Developers:**
    * The `FieldId` in `InputFieldDefinitionDoc` will be used as the key when academicians submit data for this field.
    * Consider how the frontend might render these definitions to create a form builder UI.
    * The `InputType` should be an enum or a set of well-defined string constants.

---

**Story 2.3: Manage Static Criteria**

* **User Story / Goal:** As an Admin, I want to activate or deactivate predefined static criteria within the system, so that standard, non-configurable criteria can be included in evaluations where appropriate.
* **Technical Enhancements/Considerations:**
    * `StaticCriterionDefinitionEntity` will be stored in PostgreSQL and likely seeded. Admins only toggle `IsActive`.
    * The "predefined list" will be managed via seed data in the database.
    * Data sourcing for these is external to APDYS data entry (as discussed). The entity should have a `DataSourceHint`.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Admin can `GET` a list of all predefined `StaticCriterionResponseDto`s, showing their `StaticCriterionSystemId`, `Name`, `Description`, `DataSourceHint`, and current `IsActive` status.
    * **AC2:** Admin can `PUT` to an endpoint like `/api/v1/static-criteria/{staticCriterionSystemId}/activation` with a `ToggleActivationRequestDto` body to change the `IsActive` status of a specific static criterion.
    * **AC3:** (Covered by Story 2.6 AC5) Only static criteria with `IsActive = true` can be selected for assignment to form categories.
    * **AC4 (New):** Changes to `IsActive` status are audited.
* **Technical Notes for Developers:**
    * Implement `StaticCriterionStore` for PostgreSQL access.
    * `StaticCriterionManager` handles retrieval and status updates.
    * The `StaticCriterionSystemId` (e.g., "YEARS_OF_SERVICE") will be the key used for management and linking.

---

**Story 2.4: Create and Manage Evaluation Forms**

* **User Story / Goal:** As an Admin, I want to create and manage evaluation forms, linking them to specific academic cadres and evaluation periods, so that tailored performance reviews can be conducted.
* **Technical Enhancements/Considerations:**
    * `EvaluationFormEntity` stored in PostgreSQL.
    * `Status` field (Draft, Active, Archived) is key.
    * `ApplicableAcademicCadres` will likely be stored as a JSON string or in a separate link table if querying by individual cadre becomes complex. For MVP, a JSON string is simpler.
    * Restrictions on updates if `Status` is "Active" and submissions exist (e.g., cannot change period or cadres).
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Admin can `POST` an `EvaluationFormRequestDto` to create a new form. System saves `EvaluationFormEntity` with `Status` "Draft" by default. `Id` (GUID), `CreatedAt`, `UpdatedAt`, `CreatedByUserId`, `UpdatedByUserId` are populated.
    * **AC2:** Admin can `GET` a list of all `EvaluationFormResponseDto`s, with optional filtering by `Status` or `ApplicableAcademicCadres`.
    * **AC3:** Admin can `PUT` to update an `EvaluationFormRequestDto` for a form.
        * **AC3.1 (New):** If `Status` is "Draft", most fields can be updated.
        * **AC3.2 (New):** If `Status` is "Active", define MVP restrictions (e.g., `Name` can be updated, but not period or cadres if submissions exist).
    * **AC4:** Form status transitions (e.g., via `/api/v1/evaluation-forms/{id}/status` with body `{"status": "Active"}`) are managed.
        * **AC4.1 (New):** A form can only be moved to "Active" if it has at least one category and one criterion defined (or business rule allows empty active forms).
        * **AC4.2 (New):** Archiving a form might be restricted if it has active, non-finalized submissions.
    * **AC5 (New):** All CRUD and status operations are audited.
* **Technical Notes for Developers:**
    * Implement `FormStore` and `FormManager`.
    * `EvaluationPeriodEndDate` should be validated to be after `EvaluationPeriodStartDate`.
    * Consider how "Archived" forms are handled in listings and data retrieval for active submissions.

---

**Story 2.5: Manage Weighted Categories within Forms**

* **User Story / Goal:** As an Admin, I want to define and manage weighted categories within an evaluation form, so that criteria can be grouped logically and contribute appropriately to the form's total score.
* **Technical Enhancements/Considerations:**
    * `FormCategoryEntity` stored in PostgreSQL, linked to `EvaluationFormEntity`.
    * Sum of category weights within a form should ideally be 100% (or some other defined total). This validation should be implemented.
    * `DisplayOrder` is important.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Admin can `POST` a `FormCategoryRequestDto` to `/api/v1/evaluation-forms/{formId}/categories` to add a new category to a 'Draft' or editable 'Active' form.
    * **AC2:** Admin can `GET` all `FormCategoryResponseDto`s for a given form, ordered by `DisplayOrder`.
    * **AC3:** Admin can `PUT` to update a `FormCategoryRequestDto` (name, weight, order) for an existing category in an editable form.
    * **AC4:** Admin can `DELETE` a category from a form (if it contains no criteria OR if business rules allow deleting categories with criteria – for MVP, likely only if empty or form is 'Draft').
    * **AC5:** System validates that the sum of `Weight` for all categories in a given form does not exceed 100 (or is exactly 100 before activating the form). This validation occurs upon adding/updating categories or when attempting to activate the form.
    * **AC6 (New):** All CRUD operations on categories are audited.
* **Technical Notes for Developers:**
    * Implement `CategoryStore` and `CategoryManager`.
    * When a category is deleted, ensure related `FormCriterionLinkEntity` records are handled (e.g., cascade delete or prevent deletion if links exist, depending on rules).

---

**Story 2.6: Assign Criteria to Categories in a Form**

* **User Story / Goal:** As an Admin, I want to assign active dynamic and static criteria to specific categories within an evaluation form, and define their order, so that the form structure is complete for academician input.
* **Technical Enhancements/Considerations:**
    * `FormCriterionLinkEntity` stored in PostgreSQL, linking `FormCategoryEntity` to either a `DynamicCriterionTemplateId` (MongoDB ObjectId string) or a `StaticCriterionSystemId`.
    * Only 'Active' dynamic criteria and 'Active' static criteria should be available for assignment.
    * `DisplayOrder` of criteria within a category is important.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Admin can `POST` a `CriterionAssignmentRequestDto` (with `CriterionType="Dynamic"`) to `/api/v1/evaluation-forms/{formId}/categories/{categoryId}/criteria` to assign an 'Active' dynamic criterion to a category in an editable form.
    * **AC2:** Admin can `POST` a `CriterionAssignmentRequestDto` (with `CriterionType="Static"`) to the same endpoint to assign an 'Active' static criterion.
    * **AC3:** Admin can `PUT` to update the `DisplayOrder` of an assigned criterion within a category (e.g., via a dedicated reordering endpoint or by updating the link entity).
    * **AC4:** Admin can `DELETE` a criterion assignment (a `FormCriterionLinkEntity`) from a category (if no data submitted against it on this specific form, or form is 'Draft').
    * **AC5:** The API/backend logic prevents assignment of criteria that are not 'Active'.
    * **AC6 (New):** All assignment/unassignment operations are audited.
    * **AC7 (New):** When fetching a form's structure for display (e.g., in `EvaluationFormResponseDto`), the `CriterionAssignmentResponseDto` correctly populates `CriterionName`, `Description`, `InputFields` (for dynamic) or `DataSourceHint` (for static) by fetching details from MongoDB or the static definitions table.
* **Technical Notes for Developers:**
    * Implement `FormCriterionLinkStore` and `FormCriterionLinkManager`.
    * The manager will need to interact with both PostgreSQL (for static criteria definitions) and MongoDB (for dynamic criteria template details) to validate activeness and fetch details for display.
    * Consider API design for listing available active criteria for assignment.

---

---

### Enhancing Epic 3: Academician - Data Submission Workflow

**Overall Epic Technical Notes:**
* All operations here are performed by authenticated users with the `APDYS_Academician` role (or equivalent derived from Org. Management roles).
* The system must clearly distinguish between dynamic criteria (requiring data input and storage in MongoDB via `SubmittedDynamicDataDoc`) and static criteria (values fetched/displayed, not input by academician).
* File uploads for evidence need robust handling: secure storage, linking to submission entries, and validation (type, size).
* "Draft" functionality is critical, allowing academicians to save and resume work. This means `AcademicSubmissionEntity` and related `SubmittedDynamicDataDoc` entries might exist in a "Draft" state before final submission.
* Audit logging should cover all submission events (draft save, final submit, data entry).

---

**Story 3.1: Academician Dashboard - View Assigned Evaluation Forms & Submissions**

* **User Story / Goal:** As an Academician, I want to see a personalized dashboard listing my active evaluation forms and the status of my submissions (e.g., Not Started, Draft, Submitted, Approved, Rejected), so that I can easily track my tasks and progress.
* **Technical Enhancements/Considerations:**
    * The backend needs to determine which `EvaluationFormEntity` records are "active" for the current academician based on their `ApplicableAcademicCadres` and the form's `EvaluationPeriodStartDate`/`EndDate` and `Status` ("Active").
    * For each assigned form, the system needs to check if an `AcademicSubmissionEntity` exists for the current user and that form.
    * Submission deadlines should be clearly derived from `EvaluationFormEntity.EvaluationPeriodEndDate`.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Authenticated academician calling `GET /api/v1/academician/dashboard/forms` receives a list of `AcademicianDashboardFormDto`.
        * **AC1.1 (New):** The list only includes forms whose `Status` is "Active" and for which the current date falls within their `EvaluationPeriodStartDate` and `EvaluationPeriodEndDate`.
        * **AC1.2 (New):** The list only includes forms where one of the form's `ApplicableAcademicCadres` matches the logged-in academician's cadre (from `UserContextCo`).
    * **AC2:** Each `AcademicianDashboardFormDto` correctly displays `FormId`, `FormName`, `EvaluationPeriodStartDate`, `EvaluationPeriodEndDate`. The `SubmissionStatus` is accurately determined ("Not Started" if no `AcademicSubmissionEntity` exists for the user/form; otherwise, the `Status` from the `AcademicSubmissionEntity`). `SubmissionId` is populated if a submission exists.
    * **AC3 (Clarified):** The DTO should provide enough information (like `FormId` and `SubmissionId`) for a client to construct links/actions to "Start New Submission" (if status is "Not Started") or "Open/View/Edit Submission."
    * **AC4:** `SubmissionDeadline` in the DTO is correctly populated from `EvaluationFormEntity.EvaluationPeriodEndDate`.
* **Technical Notes for Developers:**
    * The `AcademicianManager` will need to combine data from `EvaluationFormStore`, `AcademicSubmissionStore`, and the user's context.
    * Optimize queries to efficiently fetch this dashboard data.

---

**Story 3.2: View Form Details and Criteria Structure**

* **User Story / Goal:** As an Academician, when I select an evaluation form, I want to view its detailed structure, including all categories and the specific criteria (dynamic and static) within each category, along with their descriptions and any defined coefficients or limits, so that I understand what information I need to provide.
* **Technical Enhancements/Considerations:**
    * When an academician opens a form for submission (or to view an existing submission), the API needs to return a detailed structure resembling the `AcademicianSubmissionResponseDto` (even for a new submission, it would show the form structure with empty data sections).
    * This involves fetching the `EvaluationFormEntity`, its `FormCategoryEntity` children, and their linked `FormCriterionLinkEntity` records.
    * For each `FormCriterionLinkEntity`:
        * If "Dynamic", fetch the `DynamicCriterionTemplateDoc` from MongoDB to get its details and `InputFields`.
        * If "Static", fetch the `StaticCriterionDefinitionEntity` from PostgreSQL. Then, (as part of this story or a subsequent one for data population) the actual static *value* for the academician needs to be determined and displayed.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Academician can call `GET /api/v1/academician/submissions/form/{formId}` (or a similar endpoint to get form structure for a new submission, or `GET /api/v1/academician/submissions/{submissionId}` for an existing one). The response is a detailed `AcademicianSubmissionResponseDto` (or a DTO closely representing the form structure with criteria details).
    * **AC2:** Categories and criteria within the response are ordered correctly by their `DisplayOrder`. Details like `CriterionName`, `CriterionDescription`, `CriterionCoefficient` are populated.
    * **AC3:** For each dynamic criterion in the response, the `InputFields` (from its template) are correctly included in the DTO.
    * **AC4:** For each static criterion in the response, its `CriterionName`, `Description`, and `DataSourceHint` are included.
        * **AC4.1 (New):** The *actual value* for the static criterion (e.g., "Years of Service: 10") for the current academician is fetched from the relevant source (as per `DataSourceHint` and `ProcessStaticCriterionValueCo`) and displayed in the `AcademicianCriterionDataViewDto.StaticValue` field.
* **Technical Notes for Developers:**
    * The `SubmissionManager` or a dedicated `FormDisplayManager` will be responsible for assembling this complex DTO.
    * This involves orchestrating calls to multiple stores (PostgreSQL for forms/categories/links/static-defs, MongoDB for dynamic-defs) and potentially external services for static data values.
    * Consider performance implications if a form is very large.

---

**Story 3.3: Input Performance Data for Criteria**

* **User Story / Goal:** As an Academician, I want to input my performance data for each criterion according to the defined input types (text, number, date, dropdown selections), so that I can accurately record my achievements.
* **Technical Enhancements/Considerations:**
    * This story primarily concerns data input for *dynamic criteria*. Static criteria values are not input here by academicians.
    * Data is submitted via `AcademicianSubmissionRequestDto` which contains a list of `SubmittedDynamicCriterionEntryDto`.
    * Each `SubmittedDynamicCriterionEntryDto.Data` dictionary will store key-value pairs where keys are `InputFieldDefinition.FieldId` from the criterion template.
    * Backend validation must check that submitted data types match the `InputType` defined in the `InputFieldDefinitionDoc` (e.g., a string for a number field should be rejected or parsed). Constraints like min/max for numbers also need validation.
    * Multiple entries for a single criterion: The data model `AcademicianCriterionDataViewDto.SubmittedInstances` (a list of `SubmittedCriterionInstanceDataViewDto`) allows for this. Each instance would be a separate `SubmittedDynamicDataDoc` in MongoDB.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Academician can submit data (via `POST` or `PUT` to `/api/v1/academician/submissions`) where `SubmittedDynamicCriterionEntryDto.Data` contains values that are valid for the `InputType` of each corresponding `InputFieldDefinition` (text, number, date).
    * **AC2:** Academician can submit data where `SubmittedDynamicCriterionEntryDto.Data` includes selections for `Dropdown` input types, and the selected value is one of the `DropdownOptions`.
    * **AC3:** Server-side validation rejects submissions if:
        * Data types in `SubmittedDynamicCriterionEntryDto.Data` do not match the criterion's `InputFieldDefinition.InputType`.
        * Numeric data violates `MinValue`/`MaxValue` constraints.
        * Date data is not in a valid format.
        * (Future: Date range constraints).
    * **AC4:** If an `InputFieldDefinition` is marked `IsMandatory`, the corresponding key must be present in `SubmittedDynamicCriterionEntryDto.Data` with a non-empty/non-null value upon *final submission* (drafts might allow missing mandatory fields).
    * **AC5:** The system correctly creates/updates multiple `SubmittedDynamicDataDoc` records in MongoDB if an academician provides multiple entries for a dynamic criterion that allows it (e.g., "Published Journal Article"). Each `SubmittedDynamicDataDoc` is linked to the parent `AcademicSubmissionEntity.Id`.
* **Technical Notes for Developers:**
    * The `SubmissionManager` will parse `AcademicianSubmissionRequestDto`.
    * For each entry in `DynamicCriterionEntries`, it will validate the data against the `DynamicCriterionTemplateDoc.InputFields` definition (fetched from MongoDB).
    * Valid data is saved as `SubmittedDynamicDataDoc` documents.
    * Consider how "multiple entries for a single criterion" is indicated by the admin in the criterion template (e.g., a flag "AllowMultipleInstances").

---

**Story 3.4: Upload Supporting Evidence for Criteria**

* **User Story / Goal:** As an Academician, I want to upload supporting evidence files (e.g., PDFs, images) for relevant criteria, so that I can substantiate my performance claims.
* **Technical Enhancements/Considerations:**
    * Implement a dedicated API endpoint (e.g., `POST /api/v1/evidence/upload`) for file uploads. This endpoint should ideally handle the file stream directly.
    * The upload endpoint should return a temporary identifier (`TempUploadedFileId`) for the uploaded file. This ID is then included in the `SubmittedDynamicCriterionEntryDto.TempUploadedFileIds` list when the academician saves or submits their form data.
    * Upon saving/submitting the main submission data, the backend will finalize these temporary files: move them to permanent storage on the Network File Share, create `EvidenceFileEntity` records in PostgreSQL, and link them to both the `AcademicSubmissionEntity` and the specific `SubmittedDynamicDataDoc` instance.
    * Define supported file types and max size in configuration (`environment-vars.md`).
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Academician can successfully `POST` a file (e.g., PDF, DOCX, JPG) to `/api/v1/evidence/upload`. The response is an `UploadEvidenceResponseDto` containing a `TempUploadedFileId`.
    * **AC2:** System rejects files that exceed the configured `MaxFileSizeMb` or are not in the `AllowedFileTypes` list, returning an appropriate error response.
    * **AC3:** When an `AcademicianSubmissionRequestDto` (containing `TempUploadedFileIds` within its `SubmittedDynamicCriterionEntryDto` items) is processed:
        * Corresponding `EvidenceFileEntity` records are created in PostgreSQL, linking to the correct `AcademicSubmissionEntity` and `SubmittedDynamicDataDoc.Id`.
        * Files are moved from temporary upload location to the permanent Network File Share path defined in `EvidenceFileEntity.StoredFilePath`.
    * **AC4:** An academician viewing their submission (`AcademicianSubmissionResponseDto`) can see a list of `EvidenceFileResponseDto`s associated with each relevant `SubmittedCriterionInstanceDataViewDto`.
    * **AC5:** (If editing a draft) Academician can request removal of an evidence file linked to a draft submission. This should delete the `EvidenceFileEntity` and the physical file.
* **Technical Notes for Developers:**
    * Consider using streaming for file uploads to handle large files efficiently.
    * The temporary upload location needs to be secure and cleaned up regularly if files are not finalized.
    * Ensure unique file naming on the Network File Share to prevent collisions. `EvidenceFileEntity.StoredFilePath` should store this unique path.
    * The `DownloadUrl` in `EvidenceFileResponseDto` should be a secure API endpoint that streams the file content after authorization checks.

---

**Story 3.5: Save Submission as Draft**

* **User Story / Goal:** As an Academician, I want to save my performance data submission as a draft at any point, so that I can complete it over multiple sessions without losing my work.
* **Technical Enhancements/Considerations:**
    * A `PUT` or `POST` request to `/api/v1/academician/submissions` (or a dedicated `/draft` sub-resource) with an `AcademicianSubmissionRequestDto` will trigger this.
    * If no `AcademicSubmissionEntity` exists for the user/form, one is created with `Status` "Draft". If one exists and is "Draft", it's updated.
    * Submitted dynamic data (`SubmittedDynamicDataDoc`) is saved/updated in MongoDB.
    * Evidence files (linked via `TempUploadedFileIds`) are processed and `EvidenceFileEntity` records are created/updated.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Academician can submit an `AcademicianSubmissionRequestDto` (potentially with partial data for dynamic criteria).
        * An `AcademicSubmissionEntity` is created/updated with `Status` "Draft".
        * Associated `SubmittedDynamicDataDoc` entries are created/updated in MongoDB.
        * Uploaded evidence files are processed and linked.
    * **AC2:** The academician's dashboard (`AcademicianDashboardFormDto`) reflects the `SubmissionStatus` as "Draft" and includes the `SubmissionId`.
    * **AC3:** Academician can later retrieve this draft submission (using `GET /api/v1/academician/submissions/{submissionId}`), and all previously saved data and evidence links are present.
    * **AC4:** No notification is sent to Controllers when a submission is saved as "Draft".
* **Technical Notes for Developers:**
    * The `SubmissionManager` handles the logic for creating or updating draft submissions.
    * Ensure atomicity if multiple database operations are involved (e.g., saving submission header, dynamic data, evidence links). Consider transactions where appropriate for PostgreSQL operations.

---

**Story 3.6: Final Submit of Evaluation Form**

* **User Story / Goal:** As an Academician, I want to make a final submission of my completed evaluation form, so that it can be reviewed by the designated Controller.
* **Technical Enhancements/Considerations:**
    * This involves changing the `Status` of an existing `AcademicSubmissionEntity` (which might be in "Draft" or "Rejected" state) to "Submitted".
    * All mandatory field validations (for dynamic criteria) must pass before final submission.
    * Once submitted, the academician should no longer be able to edit the data (API should enforce this).
    * This action triggers a notification to the relevant Controller(s) via RabbitMQ.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Academician can make a request (e.g., `POST /api/v1/academician/submissions/{submissionId}/submit`) to finalize their submission.
    * **AC2:** The system performs final validation:
        * All mandatory fields in dynamic criteria are filled.
        * All data constraints (type, range, etc.) are met.
        * Submission is prevented with a clear error message if validation fails.
    * **AC3:** Upon successful submission, the `AcademicSubmissionEntity.Status` is updated to "Submitted", and `SubmittedAt` timestamp is set. The data associated with this submission (dynamic data in MongoDB, evidence links) becomes effectively read-only for the academician.
    * **AC4:** (Covered by AC3 - status update)
    * **AC5:** A `NotificationMessageDto` (type `NewSubmissionForController`) is published to RabbitMQ containing necessary details (SubmissionId, AcademicianName, FormName) for the Notification Service to alert the relevant Controller(s).
* **Technical Notes for Developers:**
    * The `SubmissionManager` will handle the status change and trigger the notification event.
    * Determine how "relevant Controller(s)" are identified (e.g., based on academician's department/faculty, form type, or a pre-configured assignment). This logic needs to be implemented.
    * The read-only enforcement means subsequent `PUT` requests from the same academician for this submission ID should be rejected if the status is "Submitted" or further along.

---


---

### Enhancing Epic 4: Controller - Data Verification Workflow

**Overall Epic Technical Notes:**
* All operations in this epic are performed by authenticated users with the `APDYS_Controller` role (or equivalent derived from Org. Management roles). Authorization checks are paramount.
* Controllers need a clear, comprehensive view of the submitted data, including dynamic entries and any fetched static criteria values, plus all associated evidence.
* The process of assigning submissions to specific controllers or controller pools needs to be clearly defined. For MVP, it might be based on the academician's department or faculty, or a simpler "all controllers see all submissions within their purview" model.
* Audit logging (`RlxSystemLogHelper`) for all approval/rejection actions is critical, capturing who actioned what and when, along with any comments.
* Notifications to academicians upon approval or rejection must be triggered.

---

**Story 4.1: Controller Dashboard - View Pending Submissions**

* **User Story / Goal:** As a Controller, I want to see a dashboard listing all academician submissions that are pending my review, so that I can manage my workload and address submissions in a timely manner.
* **Technical Enhancements/Considerations:**
    * The API for this dashboard needs to fetch `AcademicSubmissionEntity` records with `Status` "Submitted" (or "Pending Approval").
    * Filtering/sorting capabilities (by submission date, academician name, department) are important for usability.
    * "Purview" logic: How does the system determine which submissions a *specific* controller is responsible for or allowed to see? This needs to be implemented (e.g., controller is linked to specific departments/faculties via Org. Management data, or there are defined "controller pools"). For MVP, this might be simplified (e.g., controllers in a certain Arel-level role see all).
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Authenticated controller calling `GET /api/v1/controller/submissions` receives a list of `ControllerSubmissionSummaryDto` for submissions matching their purview and having a status of "Submitted".
    * **AC2:** Each `ControllerSubmissionSummaryDto` correctly displays `SubmissionId`, `AcademicianName`, `AcademicianDepartment` (from Org. Mgmt via user lookup), `FormName` (from linked `EvaluationFormEntity`), and `SubmittedAt`.
    * **AC3:** The API supports query parameters for sorting the list by `SubmittedAt` (default descending) and `AcademicianName`. Basic filtering by `AcademicianName` (contains), `FormName` (contains), or `AcademicianDepartment` should be considered.
    * **AC4:** The "purview" logic is correctly applied, ensuring controllers only see submissions they are authorized to review. This logic must be documented.
    * **AC5 (New):** Pagination is implemented for this endpoint.
* **Technical Notes for Developers:**
    * The `ControllerManager` (or `SubmissionManager` with controller-specific methods) will handle fetching this data.
    * The query to `AcademicSubmissionEntity` will need to join or lookup `EvaluationFormEntity` (for FormName) and potentially user details from Org. Mgmt. (for AcademicianName, Department) if not denormalized.
    * Clearly define and implement the "purview" logic. This might involve checking the controller's own attributes (department, faculty) against the academician's attributes.

---

**Story 4.2: Review Submitted Data and Evidence**

* **User Story / Goal:** As a Controller, when I select a submission for review, I want to view all the data entered by the Academician, structured by categories and criteria, and be ableable to easily access/preview any uploaded supporting evidence, so that I can thoroughly assess the submission.
* **Technical Enhancements/Considerations:**
    * The API endpoint (e.g., `GET /api/v1/controller/submissions/{submissionId}`) should return a DTO similar to `AcademicianSubmissionResponseDto`, as it contains the full form structure, dynamic data entries, fetched static values, and evidence file details.
    * For evidence files, the `DownloadUrl` in `EvidenceFileResponseDto` must be a secure API endpoint that streams the file after an authorization check.
    * "Preview common file types" is a stretch goal. For MVP, providing download links is sufficient.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Controller can call `GET /api/v1/controller/submissions/{submissionId}` and receive a detailed `AcademicianSubmissionResponseDto` (or a controller-specific version if needed).
        * **AC1.1 (New):** The DTO includes all categories, criteria (dynamic and static), user-entered data for dynamic criteria (from `SubmittedDynamicDataDoc`), fetched values for static criteria, and associated evidence file metadata.
    * **AC2:** The DTO lists all `EvidenceFileResponseDto`s associated with each `SubmittedCriterionInstanceDataViewDto`, including `FileName`, `ContentType`, `SizeBytes`, and a valid `DownloadUrl`.
    * **AC3:** Controller can use the `DownloadUrl` (e.g., `GET /api/v1/evidence/{evidenceFileId}/download`) to successfully download any evidence file associated with the submission, after passing authorization checks.
    * **AC4:** (Stretch, Post-MVP) Controller can preview common evidence file types (PDF, images) directly within a frontend UI (this implies the API would support streaming these files with appropriate content types).
* **Technical Notes for Developers:**
    * The `SubmissionManager` will assemble this DTO, fetching data from `AcademicSubmissionEntity`, `EvaluationFormEntity` and its children, `SubmittedDynamicDataDoc` (MongoDB), and `EvidenceFileEntity`. It will also orchestrate fetching static criteria values.
    * The evidence download endpoint needs careful implementation to ensure only authorized users can access files and to handle large files efficiently (streaming).

---

**Story 4.3: Approve Submission**

* **User Story / Goal:** As a Controller, I want to approve a submission once I have verified its accuracy and completeness, so that the evaluation process can move forward for that Academician.
* **Technical Enhancements/Considerations:**
    * API endpoint (e.g., `POST /api/v1/controller/submissions/{submissionId}/approve` or `PUT /api/v1/controller/submissions/{submissionId}/status`).
    * Update `AcademicSubmissionEntity.Status` to "Approved", set `ApprovedAt`, `ApprovedByControllerUserId` (from `UserContextCo`), and store optional `ApprovalComments`.
    * The submission should become read-only for further edits by the academician or controller (except perhaps for adding more comments by an admin later, if such a flow exists).
    * Trigger notification to the Academician.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Controller can make a request to approve a "Submitted" `AcademicSubmissionEntity`.
    * **AC2:** `AcademicSubmissionEntity.Status` is updated to "Approved". `ApprovedAt` and `ApprovedByControllerUserId` are recorded. Optional `ApprovalComments` (from `SubmissionApprovalRequestDto`) are stored.
    * **AC3:** The submission is now considered finalized and further attempts by the Academician or this Controller to modify the core data (via submission update APIs) are rejected.
    * **AC4:** A `NotificationMessageDto` (type `SubmissionApproved`) is published to RabbitMQ for the Academician, including any approval comments.
    * **AC5 (New):** The approval action is audited with `RlxSystemLogHelper`.
* **Technical Notes for Developers:**
    * The `ControllerManager` or `SubmissionManager` handles this state transition.
    * Ensure only submissions in "Submitted" (or potentially "Rejected" if re-approval after minor fixes is a flow, though not detailed yet) state can be approved.

---

**Story 4.4: Reject Submission with Mandatory Comments**

* **User Story / Goal:** As a Controller, I want to reject a submission if it is inaccurate or incomplete, providing mandatory comments explaining the reasons for rejection, so that the Academician can make necessary corrections and resubmit.
* **Technical Enhancements/Considerations:**
    * API endpoint (e.g., `POST /api/v1/controller/submissions/{submissionId}/reject` or `PUT /api/v1/controller/submissions/{submissionId}/status`).
    * `SubmissionRejectionRequestDto` containing mandatory `Comments` is used.
    * Update `AcademicSubmissionEntity.Status` to "Rejected", set `RejectedAt`, `RejectedByControllerUserId`, and store `RejectionComments`.
    * The submission should be "unlocked" for the Academician to edit and resubmit. This means they can again `PUT` updates to their submission.
    * Trigger notification to the Academician including rejection comments.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Controller can make a request to reject a "Submitted" `AcademicSubmissionEntity`.
    * **AC2:** The `SubmissionRejectionRequestDto.Comments` field is validated as mandatory; rejection fails if comments are missing.
    * **AC3:** `AcademicSubmissionEntity.Status` is updated to "Rejected". `RejectedAt`, `RejectedByControllerUserId`, and `RejectionComments` are recorded.
    * **AC4:** Rejection comments are stored and are visible to the Academician when they view their rejected submission.
    * **AC5:** An Academician whose submission has "Rejected" status can subsequently update and resubmit their submission (i.e., relevant APIs from Epic 3 are re-enabled for this submission).
    * **AC6:** A `NotificationMessageDto` (type `SubmissionRejected`) is published to RabbitMQ for the Academician, including the `RejectionComments`.
    * **AC7 (New):** The rejection action is audited with `RlxSystemLogHelper`.
* **Technical Notes for Developers:**
    * The `ControllerManager` or `SubmissionManager` handles this state transition.
    * Ensure only submissions in "Submitted" state can be rejected.
    * When an academician resubmits a previously "Rejected" submission, the status should transition back to "Submitted," and previous rejection/approval fields should ideally be cleared or versioned if a history is needed (for MVP, clearing is simpler).

---


This epic covers distinct data entry and processing for several specific roles (Strategic Management Office, Managers for Staff Competency, Archivists for Portfolio Control, and other designated data entry staff). The data models for these were defined previously. Now, we'll focus on the technical enhancements and ACs for the stories themselves.

---

### Enhancing Epic 5: Specialized Evaluation Workflows Implementation

**Overall Epic Technical Notes:**
* Each sub-epic/workflow will likely involve a dedicated set of API endpoints and corresponding Manager/Store logic within the Core APDYS Service.
* Authorization is critical: ensure that only users with the correct APDYS role (e.g., `APDYS_StrategicMgmt`, `APDYS_Manager_Dean`, `APDYS_Archivist`, `APDYS_DataEntry_Library`) can access and perform actions within their respective workflows. These APDYS roles will need to be mapped from Arel University roles.
* Data validation specific to each workflow's DTOs is essential.
* Audit logging (`RlxSystemLogHelper`) should capture all data submissions and modifications within these specialized workflows.
* Consider if `Rlx.Shared` provides any base DTOs, manager patterns, or store patterns that can be leveraged for these more straightforward data entry tasks.
* For MVP, the "definition" stories (e.g., defining strategic indicators, competency areas) might involve seeding data rather than building full admin UIs for their management, unless specified as dynamic by an Admin. The current stories imply these definitions are managed by an Admin/System Architect, which usually translates to seed data or a simple admin interface.

---

#### Sub‑Epic 5.A: Department Strategic Performance Workflow (Strategic Management Office)

**Story 5.A.1: Define Data Structure for Department Strategic Performance**

* **User Story / Goal:** As an Admin/System Architect, I need a defined data structure for collecting Department Strategic Performance indicators, so that Strategic Management Office staff can input relevant data.
* **Technical Enhancements/Considerations:**
    * `DepartmentStrategicIndicatorDefinitionEntity` will be stored in PostgreSQL (as per `data-models.md`).
    * These definitions are likely seeded into the database initially rather than managed via a dynamic Admin UI for MVP.
    * The `IndicatorSystemId` should be a unique, human-readable key.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** `DepartmentStrategicIndicatorDefinitionEntity` schema is implemented in PostgreSQL.
    * **AC2:** The entity supports `IndicatorSystemId` (PK), `Name`, `Description`, `DataType` (e.g., "Percentage", "Number", "Currency", "Narrative"), `IsHigherBetter`, `IsActive`, `CreatedAt`, `CreatedByUserId`.
    * **AC3:** A set of initial strategic performance indicators can be successfully seeded into the database.
    * **AC4 (New):** An API endpoint (e.g., `GET /api/v1/admin/strategic-indicator-definitions`) allows Admins to view all defined (and active) indicators. (Optional for MVP if purely seed-driven).
* **Technical Notes for Developers:**
    * `DataType` will inform how `ActualValue` and `TargetValue` are interpreted and potentially validated later.

**Story 5.A.2: Data Entry for Department Strategic Performance**

* **User Story / Goal:** As a Strategic Management Office Staff member, I want to access a dedicated interface to input and manage Department Strategic Performance data for various departments and assessment periods, so that this information is centrally recorded.
* **Technical Enhancements/Considerations:**
    * API endpoints (e.g., `POST /api/v1/department-strategic-performance`, `GET /api/v1/department-strategic-performance?departmentId=X&periodId=Y`).
    * `DepartmentStrategicPerformanceDataEntity` stores each entry.
    * `DepartmentId` will come from Arel Org. Management. System might need a way to list valid Department IDs.
    * `AssessmentPeriodId` needs a consistent format (e.g., "2025-AY", "2025-Q1").
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** An authorized Strategic Management Office Staff member can `POST` a `DepartmentStrategicPerformanceDataRequestDto` to submit data for multiple indicators for a specific department and assessment period. Each entry is saved as a `DepartmentStrategicPerformanceDataEntity` record.
    * **AC2:** Submitted data is correctly associated with the `DepartmentId`, `AssessmentPeriodId`, and the relevant `IndicatorSystemId`. `SubmittedAt` and `SubmittedByUserId` are recorded.
    * **AC3:** Basic validation is performed on the DTO: `DepartmentId`, `AssessmentPeriodId`, and at least one `PerformanceEntry` are required. The `ActualValue` format should be checked against the `IndicatorDefinitionEntity.DataType` (e.g., parseable as a number if DataType is "Number").
    * **AC4:** Authorized users can `GET` previously submitted strategic performance data, filterable by `DepartmentId` and `AssessmentPeriodId`.
    * **AC5 (New):** Submitted data can be updated via a `PUT` request by authorized staff.
    * **AC6 (New):** All data entry and updates are audited.
* **Technical Notes for Developers:**
    * Ensure the API provides a way to list available `DepartmentStrategicIndicatorDefinitionEntity` records so the frontend/API client knows which indicators to provide data for.

---

#### Sub‑Epic 5.B: Academic Staff Competency Evaluation Workflow (Managers)

**Story 5.B.1: Define Data Structure for Academic Staff Competency**

* **User Story / Goal:** As an Admin/System Architect, I need a defined data structure for Academic Staff Competency evaluations, so that Managers can record their assessments.
* **Technical Enhancements/Considerations:**
    * `StaffCompetencyDefinitionEntity` in PostgreSQL, likely seeded for MVP.
    * `RatingScaleJson` stores the available rating options.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** `StaffCompetencyDefinitionEntity` schema is implemented in PostgreSQL.
    * **AC2:** Entity supports `CompetencySystemId` (PK), `Name`, `Description`, `RatingScaleJson`, `IsActive`, `CreatedAt`, `CreatedByUserId`.
    * **AC3:** Initial competency definitions (e.g., "Teaching Skills", "Communication") and their rating scales are seeded.
    * **AC4 (New):** An API endpoint (e.g., `GET /api/v1/admin/staff-competency-definitions`) allows viewing of active competency definitions. (Optional for MVP if purely seed-driven).
* **Technical Notes for Developers:**
    * The client application will need to parse `RatingScaleJson` to display options to the Manager.

**Story 5.B.2: Data Entry for Academic Staff Competency by Managers**

* **User Story / Goal:** As a Manager (Dean/HoD), I want to access a dedicated interface to evaluate the competencies of academic staff I supervise, providing ratings and comments, so that their performance can be assessed from a managerial perspective.
* **Technical Enhancements/Considerations:**
    * API endpoints (e.g., `POST /api/v1/staff-competency-evaluations`).
    * `StaffCompetencyEvaluationEntity` (header) and `CompetencyRatingEntity` (details) are used.
    * The system needs to verify that the authenticated manager is indeed authorized to evaluate the specified `AcademicianUniveristyUserId` (supervisory relationship from Org. Management data).
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** An authorized Manager can `POST` a `StaffCompetencyEvaluationRequestDto` for an academician they supervise.
        * A `StaffCompetencyEvaluationEntity` record is created.
        * Multiple `CompetencyRatingEntity` records are created, linked to the evaluation header.
        * `EvaluatingManagerUserId` (from `UserContextCo`) and `SubmittedAt` are recorded.
    * **AC2:** Submitted data is correctly associated with the `AcademicianUniveristyUserId`, `EvaluatingManagerUserId`, and `EvaluationContextId`.
    * **AC3:** Managers can `GET` previously submitted competency evaluations they performed for their staff.
    * **AC4 (New):** Validation ensures that submitted `Rating` values in `CompetencyRatingEntryDto` are valid entries from the `StaffCompetencyDefinitionEntity.RatingScaleJson` for the respective competency.
    * **AC5 (New):** Logic to verify supervisory relationship is implemented and enforced.
    * **AC6 (New):** All submissions are audited.
* **Technical Notes for Developers:**
    * The `EvaluationContextId` provides flexibility (e.g., "Annual Review 2025", "Post-Probation Review").
    * How supervisory relationships are determined from Org. Management data needs to be clearly defined.

---

#### Sub‑Epic 5.C: Portfolio Control Workflow (Archivists)

**Story 5.C.1: Define Data Structure for Portfolio Control Checklist**

* **User Story / Goal:** As an Admin/System Architect, I need a data structure for portfolio control items that Archivists will verify, so that the verification process is standardized.
* **Technical Enhancements/Considerations:**
    * `PortfolioChecklistItemDefinitionEntity` in PostgreSQL, likely seeded.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** `PortfolioChecklistItemDefinitionEntity` schema is implemented in PostgreSQL.
    * **AC2:** Entity supports `ItemSystemId` (PK), `Name`, `Description`, `ApplicableCadreHint`, `ExpectedLocationInEbysHint`, `IsActive`, `CreatedAt`, `CreatedByUserId`.
    * **AC3 (New):** Initial portfolio checklist item definitions are seeded.
    * **AC4 (New):** An API (e.g., `GET /api/v1/admin/portfolio-checklist-items`) allows viewing of active checklist items. (Optional for MVP if purely seed-driven).
* **Technical Notes for Developers:**
    * `ItemSystemId` should be a unique, descriptive key.

**Story 5.C.2: Portfolio Item Verification by Archivists**

* **User Story / Goal:** As an Archivist, I want to view a list of Academicians or portfolio items requiring verification, access relevant information, and record the EBYS check status and any comments for each item, so that portfolio compliance is tracked.
* **Technical Enhancements/Considerations:**
    * API endpoints (e.g., `PUT /api/v1/portfolio-verifications` to update status, `GET /api/v1/portfolio-verifications?academicianId=X` to view status).
    * `PortfolioVerificationLogEntity` stores the verification status for each item per academician. This acts as an "upsert" log; only the latest status for an academician/item pair is typically relevant for current state, but a log could be kept if history is needed (for MVP, latest state is fine).
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** An authorized Archivist can `GET` a list of academicians and their portfolio items needing verification (e.g., by querying for academicians who have items not yet logged in `PortfolioVerificationLogEntity` or items with a "Pending" status). The response could be `AcademicianPortfolioStatusResponseDto`.
    * **AC2:** An authorized Archivist can `PUT` a `PortfolioVerificationUpdateRequestDto` to record/update the verification status for a specific `ItemSystemId` and `AcademicianUniveristyUserId`. This creates or updates a `PortfolioVerificationLogEntity`.
    * **AC3:** `LastVerifiedAt` and `LastVerifiedByArchivistUserId` (from `UserContextCo`) are recorded in `PortfolioVerificationLogEntity`.
    * **AC4:** (Clarified) An "overall portfolio review" status for an academician (e.g., "Completed", "Pending") could be derived dynamically or have a separate tracking mechanism if needed, rather than a direct "Complete" flag on the log itself. For MVP, individual item statuses are primary.
    * **AC5 (New):** All verification updates are audited.
* **Technical Notes for Developers:**
    * The "list of Academicians or portfolio items requiring verification" might involve complex query logic (e.g., find all active academicians, cross-reference with active checklist items, then check against `PortfolioVerificationLogEntity`).
    * `VerificationStatus` should be a set of predefined string constants or an enum.

---

#### Sub‑Epic 5.D: Generic Data Entry Workflow for Other Roles (Library, TTO, Erasmus, etc.)

**Story 5.D.1: Define Data Structure(s) for Other Data Entry Roles**

* **User Story / Goal:** As an Admin/System Architect, I need to define data structures for data to be entered by roles like Library Staff, TTO Staff, so that diverse performance‑related information can be captured.
* **Technical Enhancements/Considerations:**
    * `GenericDataEntryDefinitionEntity` in PostgreSQL, seeded for each type of generic data.
    * For MVP, this is a simplified model with one data field per entry type. If more complex, per-role forms are needed, it might leverage a simplified version of the dynamic form engine, but that's likely post-MVP.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** `GenericDataEntryDefinitionEntity` schema is implemented in PostgreSQL.
    * **AC2:** Entity supports `EntryTypeSystemId` (PK), `Name`, `Description`, `ApplicableRoleHint`, `DataFieldLabel`, `DataFieldType`, `IsActive`, `CreatedAt`, `CreatedByUserId`.
    * **AC3:** Initial generic data entry type definitions (e.g., for Library, TTO) are seeded.
    * **AC4 (New):** An API (e.g., `GET /api/v1/admin/generic-entry-definitions`) allows viewing of active definitions. (Optional for MVP).
* **Technical Notes for Developers:**
    * `EntryTypeSystemId` provides a unique key for each type of generic data.
    * `ApplicableRoleHint` helps in authorization and UI presentation.

**Story 5.D.2: Data Entry Interface for Other Roles**

* **User Story / Goal:** As a designated Data Entry Staff member, I want to access a dedicated interface to input specific data related to academic performance, so that this information is included in APDYS.
* **Technical Enhancements/Considerations:**
    * API endpoints (e.g., `POST /api/v1/generic-data-entries`, `GET /api/v1/generic-data-entries?academicianId=X&entryType=Y`).
    * `GenericDataEntryRecordEntity` stores each submitted record.
    * Authorization will check if the logged-in user's APDYS role matches the `ApplicableRoleHint` for the `EntryTypeSystemId` they are submitting against.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** An authorized Data Entry Staff member (e.g., with role `APDYS_DataEntry_Library`) can `POST` a `GenericDataEntryRequestDto` for an `EntryTypeSystemId` relevant to their role. A `GenericDataEntryRecordEntity` is created.
    * **AC2:** Submitted data is correctly associated with `AcademicianUniveristyUserId`, `EntryTypeSystemId`, and `AssessmentPeriodId` (if provided). `SubmittedAt` and `SubmittedByUserId` are recorded.
    * **AC3:** Basic validation is performed: `AcademicianUniveristyUserId`, `EntryTypeSystemId`, and `Value` are required. The `Value` format should be checked against `GenericDataEntryDefinitionEntity.DataFieldType`.
    * **AC4 (New):** Authorized users can `GET` previously submitted generic data entries, filterable by `AcademicianUniveristyUserId`, `EntryTypeSystemId`, and `AssessmentPeriodId`.
    * **AC5 (New):** Submitted entries can be updated via `PUT` by authorized staff.
    * **AC6 (New):** All data entries and updates are audited.
* **Technical Notes for Developers:**
    * Ensure the API lists available `GenericDataEntryDefinitionEntity` records relevant to the logged-in data entry staff's role.

---

This epic focuses on the Notification Service and its interaction with the Core APDYS Service via RabbitMQ.

---

### Enhancing Epic 6: User Email Notification System

**Overall Epic Technical Notes:**
* The Notification Service (`Apdys.Notification.Api`) is a separate microservice.
* It consumes messages from RabbitMQ (published by `Apdys.Core.Api`) using helpers from `Rlx.Shared`.
* It uses MailKit to send emails via Arel University's SMTP server.
* Email content should be templated. For MVP, basic text templates are fine. HTML templates can be a future enhancement. Templates should be configurable or embedded resources.
* All email sending activities (successes, failures) must be logged using `RlxSystemLogHelper`.
* Ensure secure handling of SMTP credentials (from `environment-vars.md`).
* The `NotificationMessageDto` (defined in `data-models.md`) will be the contract for messages on RabbitMQ.

---

**Story 6.1: Notification Service Core Implementation (Email Focused)**

* **User Story / Goal:** As a Developer, I need a core notification service/module that can queue and process email notification requests, so that APDYS can reliably send email notifications for various system events.
* **Technical Enhancements/Considerations:**
    * This story describes the `Apdys.Notification.Api` service.
    * The "queue" is RabbitMQ, as established. `Rlx.Shared` provides helpers for publishing (from Core service) and consuming (in Notification service).
    * The "interface or event structure" is the `NotificationMessageDto`.
    * SMTP server details from `environment-vars.md`.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** A `NotificationMessageDto` can be successfully published by the Core APDYS Service to a designated RabbitMQ queue (e.g., `apdys.notifications.email`) using `Rlx.Shared` helpers. The Notification Service's RabbitMQ consumer (worker) successfully receives this message.
    * **AC2 (Clarified):** The Notification Service worker can deserialize the `NotificationMessageDto` and trigger the email sending logic.
    * **AC3:** Email dispatch attempts, successes, and failures (including reasons for failure like SMTP connection issues, invalid recipient address if determinable) are logged by the Notification Service using `RlxSystemLogHelper`.
    * **AC4:** The service is configured to connect to RabbitMQ and the SMTP server using details from environment variables/configuration.
* **Technical Notes for Developers:**
    * Focus on the RabbitMQ consumer (likely a `BackgroundService` in ASP.NET Core) within `Apdys.Notification.Api`.
    * Implement robust error handling for the consumer (e.g., dead-letter queue (DLQ) configuration for RabbitMQ if messages repeatedly fail processing, or if `Rlx.Shared` helpers manage this).

---

**Story 6.2: Email Notification Dispatcher**

* **User Story / Goal:** As the System, I need to be able to send email notifications to users, so that they receive important updates.
* **Technical Enhancements/Considerations:**
    * Implement an `EmailSender` service within `Apdys.Notification.Api` using MailKit.
    * Email templates: For MVP, these can be simple string templates embedded as resources or in configuration. Key placeholders (e.g., `{{UserName}}`, `{{FormName}}`, `{{Status}}`, `{{Comments}}`) will be replaced using data from `NotificationMessageDto.TemplateData`.
    * User email addresses: The `NotificationMessageDto` should contain `RecipientUserId`. The Notification Service will need to query the Organization Management Module (via a client, potentially using a helper from `Rlx.Shared` or a direct client if specific to this service's needs) to get the email address for the `RecipientUserId` if `RecipientEmail` is not already in the DTO. Caching these lookups should be considered.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** The `EmailSender` service can successfully connect to the configured SMTP server and send a test email (with a predefined template and recipient) to a specified address.
    * **AC2:** SMTP connection errors or email sending failures (e.g., invalid recipient format before sending, SMTP server rejection) are logged comprehensively by `RlxSystemLogHelper`. The message is requeued or sent to a DLQ based on error type and retry strategy.
    * **AC3:** If `NotificationMessageDto.RecipientEmail` is not provided, the service attempts to retrieve the recipient's email address from the Organization Management module using `RecipientUserId`. If the email cannot be found, this is logged, and the notification is handled as undeliverable (e.g., moved to DLQ).
    * **AC4:** Email content is generated by replacing placeholders in a predefined template with values from `NotificationMessageDto.TemplateData`. Emails clearly state they are from "Arel University APDYS" and are system communications.
* **Technical Notes for Developers:**
    * Design a simple templating mechanism.
    * Clarify if the `NotificationMessageDto` *must* always include `RecipientEmail`, or if the Notification Service is *always* responsible for looking it up. For resilience, having the publisher (Core Service) look it up and include it might be better, with the Notification Service as a fallback. For MVP, the Notification service doing the lookup is acceptable if it has access to the Org. Mgmt. Client.
    * Consider "From" address and display name configuration (`Smtp__FromAddress`, `Smtp__FromName` from `environment-vars.md`).

---

**Story 6.3: Notifications for Academician Submission Workflow (Email Only)**

* **User Story / Goal:** As an Academician, I want to receive email notifications when my submission is successfully submitted, approved, or rejected, so that I am kept informed of its status.
* **Technical Enhancements/Considerations:**
    * The Core APDYS Service (specifically `SubmissionManager` or `ControllerManager`) will be responsible for publishing `NotificationMessageDto` instances to RabbitMQ when these events occur.
    * Define distinct `NotificationType` values (e.g., `ACADEMICIAN_SUBMISSION_FINALIZED`, `ACADEMICIAN_SUBMISSION_APPROVED`, `ACADEMICIAN_SUBMISSION_REJECTED`).
    * Ensure all necessary data for the email templates (e.g., Form Name, Submission ID, Rejection Comments) is included in `NotificationMessageDto.TemplateData`.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** When an academician successfully makes a final submission (Story 3.6), the Core APDYS Service publishes a `NotificationMessageDto` (type `ACADEMICIAN_SUBMISSION_FINALIZED`) to RabbitMQ. The Notification Service consumes this and sends a confirmation email to the academician.
    * **AC2:** When a controller approves a submission (Story 4.3), the Core APDYS Service publishes a `NotificationMessageDto` (type `ACADEMICIAN_SUBMISSION_APPROVED`) to RabbitMQ. The Notification Service consumes this and sends an approval email to the academician, including any approval comments if provided.
    * **AC3:** When a controller rejects a submission (Story 4.4), the Core APDYS Service publishes a `NotificationMessageDto` (type `ACADEMICIAN_SUBMISSION_REJECTED`) to RabbitMQ. The Notification Service consumes this and sends a rejection email to the academician, which MUST include the controller's rejection comments.
    * **AC4 (New):** Email content for each notification type is clear, correctly populates placeholders (Form Name, Status, Comments), and identifies the source as APDYS.
* **Technical Notes for Developers:**
    * Create specific email template content for each notification type.
    * Ensure the Core Service reliably gets the Academician's UserID for the `RecipientUserId` field in the DTO.

---

**Story 6.4: Notifications for Controller Workflow (Email Only)**

* **User Story / Goal:** As a Controller, I want to receive email notifications when a new academician submission requires my review, so that I can act on it promptly.
* **Technical Enhancements/Considerations:**
    * When an academician's submission status changes to "Submitted" (Story 3.6), the Core APDYS Service needs to identify the relevant controller(s) and publish `NotificationMessageDto` instances for each.
    * Logic for identifying "relevant Controller(s)" is key (see notes for Story 4.1).
    * `NotificationType` could be `CONTROLLER_NEW_SUBMISSION_PENDING_REVIEW`.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** When an academician's submission status becomes "Submitted", the Core APDYS Service identifies the relevant controller(s) based on the defined purview logic. For each relevant controller, a `NotificationMessageDto` (type `CONTROLLER_NEW_SUBMISSION_PENDING_REVIEW`) is published to RabbitMQ. The Notification Service consumes these and sends an email to each controller.
    * **AC2:** The email notification to the controller includes the submitting Academician's Name, the Form Name, and Submission Date. It should also provide a direct link (if a frontend exists, otherwise just IDs) to the submission in APDYS.
* **Technical Notes for Developers:**
    * The logic for determining controller purview and fetching their UserIDs/emails is critical and must be implemented in the Core APDYS Service.
    * Consider batching notifications if many controllers need to be notified about the same submission (though individual emails are fine for MVP).

---

**Story 6.5: Notifications for Specialized Workflows (Email Only)**

* **User Story / Goal:** As a User involved in a specialized workflow (Strategic Management, Manager, Archivist, Other Data Entry), I want to receive email notifications relevant to tasks assigned to me or status changes in data I manage, so that I can stay on top of my responsibilities.
* **Technical Enhancements/Considerations:**
    * This is a more open-ended story. For each specialized workflow in Epic 5, identify specific events that warrant notifications.
    * Examples provided are good starting points:
        * Strategic Management Office: "Department strategic data submission confirmation."
        * Manager: "Staff competency evaluation submitted by you (confirmation)."
        * Archivist: "Notification that new academician portfolios are ready for verification (summary/digest if possible, or individual if few)."
        * Other Data Entry Roles: "Generic data entry submission confirmation."
    * The Core APDYS Service (relevant Managers for Epic 5 workflows) will publish these `NotificationMessageDto`s.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1 (Per identified event):** For each defined critical notification point in an Epic 5 workflow (e.g., after a Strategic Management user submits department data), the Core APDYS Service publishes an appropriate `NotificationMessageDto` to RabbitMQ. The Notification Service consumes this and sends a tailored email to the relevant user(s).
    * **AC2:** Email notification content is specific and relevant to the specialized workflow event (e.g., "Your Department Strategic Data for Q1 2025 has been successfully recorded.").
* **Technical Notes for Developers:**
    * Collaborate with the PM or stakeholders to pinpoint the exact notification triggers and recipients for each specialized workflow.
    * Create distinct `NotificationType` values and email templates for each.

---


This epic covers critical non-functional aspects like audit trails, standardized error handling, and basic operational endpoints.

---

### Enhancing Epic 7: Audit Logging & System Essentials

**Overall Epic Technical Notes:**
* This epic heavily relies on `Rlx.Shared` for logging (`RlxSystemLogHelper` which likely utilizes or aligns with the `rlx.log` package mentioned) and potentially for base error handling structures or health check components.
* Consistency across services (Core APDYS and Notification Service) for these essentials is key.
* Ensure that configuration for these (e.g., log levels, health check details) is manageable.

---

**Story 7.1: Basic Audit Logging Implementation with `rlx.log` (via `RlxSystemLogHelper`)**

* **User Story / Goal:** As an Administrator/System Operator, I need key system actions to be logged (who, what, when) using the university's standard `rlx.log` NuGet package (assumed to be implemented via `RlxSystemLogHelper` from `Rlx.Shared`), so that there is an audit trail for security, troubleshooting, and compliance purposes.
* **Technical Enhancements/Considerations:**
    * The `RlxSystemLogHelper` from `Rlx.Shared` will be the primary interface for logging audit events.
    * Ensure it's integrated into a common "Audit Service" or directly within Managers where actions occur.
    * The "who" should be the `UniversityUserId` from `UserContextCo`.
    * "What" includes the action type (e.g., "CRITERIA_CREATED") and relevant entity IDs (public GUIDs).
    * "When" is the UTC timestamp.
    * `RlxSystemLogHelper` is expected to forward logs via RabbitMQ to a central store. The APDYS services are producers of these logs.
    * The "internal API for authorized personnel to query these logs" would likely be part of the central logging infrastructure provided by Arel University, not built within APDYS itself, unless `Rlx.Shared` provides a client library for this.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Successful and failed login attempts (the latter if detectable before full context, otherwise successful logins) are logged via `RlxSystemLogHelper` by the Core APDYS Service.
        * Log includes: Timestamp, UserID (if available), EventType (`LOGIN_SUCCESS`/`LOGIN_FAILURE`), IP Address (if available from request).
    * **AC2:** Creation, modification, and status changes of key entities (Criteria Templates, Evaluation Forms, Form Categories, Criterion Assignments) by an Admin are logged.
        * Log includes: Timestamp, Admin UserID, EventType (e.g., `FORM_CREATED`, `CRITERION_STATUS_CHANGED`), Entity Public GUID ID, key changed data (e.g., old/new status, name change).
    * **AC3:** Submission lifecycle events (Draft Saved, Final Submitted, Approved, Rejected) by Academicians and Controllers are logged.
        * Log includes: Timestamp, UserID (Academician or Controller), EventType (e.g., `SUBMISSION_SUBMITTED`, `SUBMISSION_APPROVED`), Submission Public GUID ID, Form Public GUID ID.
    * **AC4 (Clarified):** All audit logs generated by APDYS services include Timestamp, authenticated UserID (if available), a clearly defined EventType string, relevant Entity Public GUID IDs, a brief description of the action, and outcome (Success/Failure).
    * **AC5:** Audit logs are successfully published to the RabbitMQ exchange designated by `RlxSystemLogHelper` for central processing.
* **Technical Notes for Developers:**
    * Create a standardized list of `EventType` strings for auditable actions.
    * Ensure `UserContextCo` is available to populate user details in audit logs.
    * The structure of the "key details" or "changed data" needs careful consideration to be useful without being overly verbose or logging sensitive data inappropriately (e.g., avoid logging full PII in every audit log unless specifically required for that event type).

---

**Story 7.2: Standardized API Error Handling and Response Format**

* **User Story / Goal:** As an API Consumer, I need consistent and predictable error responses from all APDYS APIs, so that I can reliably handle errors.
* **Technical Enhancements/Considerations:**
    * Implement global exception handling middleware in both Core APDYS and Notification Service (if it exposes APIs).
    * The JSON error response structure defined in `api-reference.md` must be strictly followed.
    * `Rlx.Shared` might provide a base error handling middleware or standard error DTOs/models; these should be used.
    * Include a `traceId` (correlation ID) in all error responses, which should also be logged with the error details. This can be generated by middleware at the start of each request.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** All API error responses (4xx, 5xx) strictly adhere to the JSON structure defined in `api-reference.md` (including `type`, `title`, `status`, `traceId`, and optional `errors` object).
    * **AC2:** Unhandled exceptions in the request pipeline are caught by global error handling middleware, logged with `RlxSystemLogHelper` (including `traceId` and full exception details), and result in a generic `500 Internal Server Error` response following the standard JSON format (without exposing internal stack traces in the response body).
    * **AC3 (Covered by Story 7.3):** Validation errors from DTO attributes result in a `400 Bad Request` with the standard JSON error format, including detailed field-specific messages in the `errors` object.
    * **AC4:** Requests for non-existent resources (e.g., `GET /api/v1/forms/non-existent-guid`) return a `404 Not Found` with the standard JSON error format. Unauthenticated requests return `401 Unauthorized`, and unauthorized (but authenticated) requests return `403 Forbidden`, both with the standard JSON error format.
    * **AC5 (New):** A `traceId` (correlation ID) is generated for each request and included in all error responses and corresponding server-side logs.
* **Technical Notes for Developers:**
    * Investigate if `Rlx.Shared` provides `ProblemDetails` compliant error handling or a base middleware.
    * Ensure the `traceId` is consistently logged with both the request and any errors generated during its processing.

---

**Story 7.3: Consistent API Validation Message Patterns using .NET Attributes**

* **User Story / Goal:** As an API Consumer, when I submit invalid data, I need clear validation error messages based on .NET attributes.
* **Technical Enhancements/Considerations:**
    * All API Request DTOs must be decorated with appropriate .NET validation attributes (`[Required]`, `[StringLength]`, `[Range]`, `[EmailAddress]`, `[RegularExpression]`, etc.).
    * ASP.NET Core's automatic model state validation (triggered by `[ApiController]` attribute on controllers) will handle this. The errors will then be formatted by the global exception handling middleware (from Story 7.2) into the standard JSON error response.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** Submitting a request DTO with missing fields marked `[Required]` results in a `400 Bad Request` response. The `errors` object in the JSON response contains a key for each missing field with a corresponding error message (e.g., "The {FieldName} field is required.").
    * **AC2:** Submitting a request DTO with data that violates format or range constraints (e.g., `[StringLength(10)]`, `[Range(1, 100)]`, `[EmailAddress]`) results in a `400 Bad Request`. The `errors` object contains field-specific messages detailing the violation (e.g., "The field {FieldName} must be a string with a maximum length of 10.").
    * **AC3 (New):** Custom validation attributes, if created for more complex cross-field validation, also integrate with model state and produce errors in the standard format.
* **Technical Notes for Developers:**
    * Ensure all DTOs in `Models/Dtos/` are thoroughly annotated.
    * The global error handler (TS2) must correctly transform `ModelStateDictionary` errors into the `errors` field of the standard JSON error response.

---

**Story 7.4: Basic Health Check Endpoint**

* **User Story / Goal:** As a System Operator, I need a `/health` endpoint per microservice.
* **Technical Enhancements/Considerations:**
    * Use `Microsoft.Extensions.Diagnostics.HealthChecks` for implementing health checks.
    * For the Core APDYS Service, health checks should verify connectivity to PostgreSQL, MongoDB, and RabbitMQ (publisher side).
    * For the Notification Service, health checks should verify connectivity to RabbitMQ (consumer side) and the SMTP server (e.g., a basic connection test).
    * `Rlx.Shared` might provide common health check patterns or publishers.
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** A `GET` request to `/health` on both `Apdys.Core.Api` and `Apdys.Notification.Api` returns a `200 OK` HTTP status and a response body indicating "Healthy" status when the service and all its critical dependencies are operational.
    * **AC2:** If a critical dependency (e.g., PostgreSQL for Core service, RabbitMQ for Notification service) is down or unresponsive, the `/health` endpoint returns a non-`200` status (e.g., `503 Service Unavailable`) and a response body indicating "Unhealthy" status with details about which dependency check failed.
    * **AC3 (New):** Health check responses conform to a standard format (e.g., as defined by `Microsoft.Extensions.Diagnostics.HealthChecks` JSON output).
* **Technical Notes for Developers:**
    * Configure appropriate timeouts for dependency checks to prevent the `/health` endpoint itself from becoming unresponsive.
    * Differentiate between "degraded" (non-critical dependency issue) and "unhealthy" (critical dependency issue) if needed, although "Healthy/Unhealthy" is often sufficient for load balancer checks.

---

**Story 7.5: API Documentation Generation (Swagger/OpenAPI)**

* **User Story / Goal:** As a Developer, I need autogenerated Swagger/OpenAPI docs.
* **Technical Enhancements/Considerations:**
    * Configure Swashbuckle.AspNetCore in both services (primarily Core APDYS, but Notification if it has any utility APIs).
    * Ensure XML documentation comments (`<summary>`, `<remarks>`, `<param>`, `<response>`) on controller actions and DTOs are generated and included in the Swagger output.
    * Configure Swagger UI to correctly display authentication requirements (e.g., Bearer token input for OIDC).
* **Acceptance Criteria (ACs) Refinements:**
    * **AC1:** The generated `swagger.json` (OpenAPI specification) accurately reflects all API endpoints, HTTP methods, request/response DTOs (including validation attributes as schema metadata), and security schemes for the Core APDYS Service.
    * **AC2:** The Swagger UI (`/swagger` endpoint) is accessible in development environments and correctly renders the API documentation, allowing interactive exploration and testing of endpoints.
    * **AC3 (New):** XML documentation comments from controller actions and DTO properties are visible in the Swagger UI, providing descriptions for endpoints, parameters, and schema fields.
    * **AC4 (New):** Swagger UI includes a mechanism to authorize requests using a JWT Bearer token for testing protected endpoints.
* **Technical Notes for Developers:**
    * Enable XML documentation file generation in the `.csproj` files for relevant projects.
    

---

This concludes the enhancements for Epic 7. These foundational elements are vital for the system's quality and operability.


