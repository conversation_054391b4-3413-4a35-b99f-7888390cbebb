# AcademicPerformance Authorization Dokümantasyonu

## 📋 İçindekiler
1. [Authorization Genel Bakış](#authorization-genel-bakış)
2. [Policy Tanımları](#policy-tanımları)
3. [Controller Authorization Durumu](#controller-authorization-durumu)
4. [Test Endpoint'leri](#test-endpointleri)
5. [<PERSON><PERSON><PERSON><PERSON>](#kullanım-örnekleri)
6. [Troubleshooting](#troubleshooting)

## 🔐 Authorization Genel Bakış

AcademicPerformance projesi, **Rlx.Identity** sistemi ile entegre olarak çalışan **claim-based authorization** kullanır.

### Temel Bileşenler:
- **OpenIddict Validation**: JWT token doğrulama
- **RoleClaimsTransformation**: Kullanıcı claim'lerini otomatik yükleme
- **Policy-based Authorization**: Granüler yetkilendirme kontrolü
- **APConsts.Policies**: Merkezi policy tanımları

### Authorization Flow:
1. <PERSON><PERSON><PERSON><PERSON><PERSON> JWT token ile istek yapar
2. OpenIddict token'ı doğrular
3. RoleClaimsTransformation kullanıcı claim'lerini yükler
4. Policy engine claim'leri kontrol eder
5. Endpoint'e erişim verilir/reddedilir

## 📜 Policy Tanımları

### Core Policies (APConsts.Policies):

| Policy | Claim Pattern | Açıklama |
|--------|---------------|----------|
| `AllAccess` | `permission.action.ap.allaccess` | Genel erişim (authenticated kullanıcılar) |
| `AccessReporting` | `permission.action.ap.accessreporting` | Raporlama dashboard'ına erişim |
| `AccessAP` | `permission.page.ap.ap` | AcademicPerformance sistem erişimi |
| `ManageCriteria` | `permission.action.ap.managecriteria` | Kriter şablonları yönetimi |
| `SubmitData` | `permission.action.ap.submitdata` | Performans verisi gönderme |
| `ApproveSubmissions` | `permission.action.ap.approvesubmissions` | Gönderileri onaylama/reddetme |
| `ManageForms` | `permission.action.ap.manageforms` | Değerlendirme formları yönetimi |
| `ViewReports` | `permission.action.ap.viewreports` | Raporları görüntüleme |
| `RequireAdminRole` | `permission.action.ap.requireadminrole` | Admin rolü gerektiren işlemler |
| `ManageFiles` | `permission.action.ap.managefiles` | Dosya yönetimi (upload/download/delete) |
| `UploadFiles` | `permission.action.ap.uploadfiles` | Dosya yükleme |
| `DownloadFiles` | `permission.action.ap.downloadfiles` | Dosya indirme |
| `DeleteFiles` | `permission.action.ap.deletefiles` | Dosya silme |
| `Test` | `permission.action.ap.test` | Test/development amaçlı |

### Policy Claim Logic:
Bir kullanıcı şu claim'lerden **herhangi birine** sahipse policy'yi geçer:
- `permission.action.ap.{actionName}` = `{actionName}`
- `permission.action.all` = `"all"`
- `permission.action.ap` = `"all"`

## 🎯 Controller Authorization Durumu

### ✅ Production Ready Controllers:

#### ReportingController
- **Authorization**: `[Authorize(APConsts.Policies.AccessReporting)]`
- **Endpoint Sayısı**: ~25 endpoint
- **Açıklama**: Tüm reporting endpoint'leri korunuyor

#### FileUploadController
- **Authorization**: `[Authorize(APConsts.Policies.ManageFiles)]`
- **Endpoint Sayısı**: ~50+ endpoint
- **Açıklama**: File upload/download operations korunuyor

#### FileUploadVerificationController
- **Authorization**: `[Authorize(APConsts.Policies.ManageFiles)]`
- **Endpoint Sayısı**: ~15 endpoint
- **Açıklama**: File verification test'leri korunuyor

#### FormController
- **Authorization**: Mixed policies
  - `AllAccess`: Genel form görüntüleme
  - `ManageForms`: Form oluşturma/düzenleme
  - `RequireAdminRole`: Admin işlemleri
- **Endpoint Sayısı**: ~20 endpoint

#### AcademicianController
- **Authorization**: Mixed policies
  - `SubmitData`: Akademisyen veri gönderme
  - `AccessAP`: Genel erişim
- **Endpoint Sayısı**: ~25 endpoint

#### DataVerificationController
- **Authorization**: `[Authorize(APConsts.Policies.ApproveSubmissions)]`
- **Endpoint Sayısı**: ~15 endpoint

#### CriteriaController
- **Authorization**: Mixed policies
  - `ManageCriteria`: Kriter yönetimi
  - `ViewStaticData`: Statik veri görüntüleme
- **Endpoint Sayısı**: ~10 endpoint

### 🧪 Test Controllers:

#### AuthTestController
- **Authorization**: Test amaçlı çeşitli policy'ler
- **Endpoint Sayısı**: 8 endpoint
- **Açıklama**: Authorization test'leri için

#### UserController (Test Endpoint)
- **Authorization**: `[Authorize(APConsts.Policies.Test)]`
- **Endpoint**: `GetTestProfile()`

#### ControllerDashboardController (Test Endpoints)
- **Authorization**: `[Authorize(APConsts.Policies.Test)]`
- **Endpoint'ler**: `TestDashboard()`, `TestPendingSubmissions()`

## 🧪 Test Endpoint'leri

### AuthTestController Endpoint'leri:

```csharp
// Anonymous erişim - Authorization gerektirmez
GET /AuthTest/TestAnonymous

// AllAccess policy test'i
GET /AuthTest/TestAllAccess
[Authorize(APConsts.Policies.AllAccess)]

// AccessReporting policy test'i
GET /AuthTest/TestAccessReporting
[Authorize(APConsts.Policies.AccessReporting)]

// ManageFiles policy test'i
GET /AuthTest/TestManageFiles
[Authorize(APConsts.Policies.ManageFiles)]

// RequireAdminRole policy test'i
GET /AuthTest/TestRequireAdminRole
[Authorize(APConsts.Policies.RequireAdminRole)]

// ManageForms policy test'i
GET /AuthTest/TestManageForms
[Authorize(APConsts.Policies.ManageForms)]

// Test policy test'i
GET /AuthTest/TestTestPolicy
[Authorize(APConsts.Policies.Test)]

// User context bilgilerini getir
GET /AuthTest/GetUserContext
[Authorize(APConsts.Policies.AllAccess)]
```

### Test Response Format:
```json
{
  "status": "Success",
  "message": {
    "Policy": "AllAccess",
    "UserId": "rlx_super_user",
    "UserRoles": ["Admin", "SuperUser"],
    "Message": "AllAccess policy test başarılı",
    "Timestamp": "2025-01-16T10:30:00Z"
  }
}
```

## 💡 Kullanım Örnekleri

### 1. Controller'da Policy Kullanımı:

```csharp
[ApiController]
[Route("[controller]/[action]")]
[Authorize(APConsts.Policies.AccessReporting)] // Class-level authorization
public class ReportingController : BaseApiController
{
    [HttpGet]
    [Authorize(APConsts.Policies.ViewReports)] // Method-level authorization
    public async Task<IActionResult> GetReport(string id)
    {
        // Implementation
    }
}
```

### 2. Mixed Authorization:

```csharp
[ApiController]
[Route("[controller]/[action]")]
public class FormController : BaseApiController
{
    [HttpGet]
    [Authorize(APConsts.Policies.AllAccess)] // Genel erişim
    public async Task<IActionResult> GetForms() { }

    [HttpPost]
    [Authorize(APConsts.Policies.ManageForms)] // Form yönetimi
    public async Task<IActionResult> CreateForm() { }

    [HttpDelete]
    [Authorize(APConsts.Policies.RequireAdminRole)] // Admin gerekli
    public async Task<IActionResult> DeleteForm() { }
}
```

### 3. User Context Kullanımı:

```csharp
public async Task<IActionResult> GetUserData()
{
    var userId = _userContextHelper.GetUserId();
    var userRoles = User.Claims
        .Where(c => c.Type == ClaimTypes.Role)
        .Select(c => c.Value)
        .ToList();
    var allClaims = User.Claims
        .Select(c => new { c.Type, c.Value })
        .ToList();
    
    // Implementation
}
```

## 🔧 Troubleshooting

### Yaygın Sorunlar:

#### 1. 401 Unauthorized
- **Sebep**: Token eksik veya geçersiz
- **Çözüm**: JWT token'ı Authorization header'ında gönder
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" http://localhost:5000/api/endpoint
```

#### 2. 403 Forbidden
- **Sebep**: Kullanıcı gerekli claim'e sahip değil
- **Çözüm**: Kullanıcıya uygun role/claim ata

#### 3. Policy Bulunamadı
- **Sebep**: Policy tanımlanmamış
- **Çözüm**: PolicyConfig.cs'de policy'yi tanımla

### Debug İpuçları:

1. **User Claims Kontrol**:
```csharp
var claims = User.Claims.Select(c => $"{c.Type}: {c.Value}").ToList();
_logger.LogInformation("User claims: {Claims}", string.Join(", ", claims));
```

2. **Policy Test**:
```csharp
var authResult = await _authorizationService.AuthorizeAsync(User, APConsts.Policies.AllAccess);
if (!authResult.Succeeded)
{
    _logger.LogWarning("Authorization failed for policy: {Policy}", APConsts.Policies.AllAccess);
}
```

3. **Token Debug**:
```bash
# JWT token decode için
echo "YOUR_JWT_TOKEN" | base64 -d
```

## 📝 Notlar

- **Production'da**: Test endpoint'leri (`AuthTestController`) silinmeli
- **Security**: Sensitive endpoint'ler için ek validation eklenebilir
- **Performance**: Claim'ler cache'lenebilir
- **Monitoring**: Authorization failure'ları log'lanmalı

---

**Son Güncelleme**: 16 Ocak 2025  
**Versiyon**: 1.0  
**Epic**: 3 - Authorization Production Ready Geçişi
