# Epic 2: Admin - Criteria & Form Management

**Goal:** Enable APDYS Administrators to dynamically define and manage all aspects of performance evaluation criteria (both dynamic and static), construct evaluation forms tailored to specific academic cadres, and organize criteria within these forms using weighted categories. This epic is crucial for the system's flexibility and adaptability.

## Story List

### Story 2.1: Define and Manage Performance Criteria Templates (Dynamic Criteria)

- **User Story / Goal:** As an Admin, I want to create, view, update, and manage the lifecycle (activate/deactivate) of dynamic performance criteria templates, so that I can define the building blocks for evaluations.
- **Detailed Requirements:**
  - <PERSON><PERSON> can define a new criterion template with properties such as:
    - Name/Title (e.g., "Published Journal Article")
    - Description
    - Coefficient/Weight (numerical value for scoring)
    - Maximum Limit (e.g., max number of articles, max points)
    - Status (Draft, Active, Inactive)
    - Associated permissions/visibility (e.g., which academic cadres this might apply to - high level, form assignment is separate)
  - Admins can list all existing criteria templates with their current status.
  - <PERSON>mins can update the properties of an existing criterion template (considering versioning implications if already in use - for MVP, updates might be restricted for active criteria in ongoing evaluation periods).
  - <PERSON><PERSON> can activate or deactivate criteria templates. Deactivated criteria cannot be added to new forms but may remain on existing forms where data has been submitted.
  - Store criteria definitions in MongoDB for schema flexibility.
- **Acceptance Criteria (ACs):**
  - **AC1:** Admin can successfully create a new dynamic criterion template with all specified properties via an API.
  - **AC2:** Admin can retrieve a list of all criteria templates.
  - **AC3:** Admin can update a non-active criterion template's properties.
  - **AC4:** Admin can change the status of a criterion template (Active/Inactive).
  - **AC5:** Validation is in place for required fields (e.g., Name, Coefficient).
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API for CRUD operations on criteria templates.
  - [ ] Implement MongoDB schema and repository for criteria templates.
  - [ ] Implement service logic for managing criteria templates.

---

### Story 2.2: Define Custom Input Types for Dynamic Criteria

- **User Story / Goal:** As an Admin, when creating/editing a dynamic criterion template, I want to define the specific input types and constraints for the data academicians will submit, so that data collection is structured and validated.
- **Detailed Requirements:**
  - For each dynamic criterion, Admins can associate one or more input fields.
  - Supported input field types:
    - Text (single line, multi-line)
    - Number (integer, decimal, with optional min/max constraints)
    - Date (with optional date range constraints)
    - File Upload (with constraints on file type, max size – to be defined)
    - Dropdown (with admin-defined options)
  - Admins can mark input fields as mandatory or optional.
  - Admins can define labels and placeholder text for each input field.
  - This input definition should be part of the criterion template (MongoDB).
- **Acceptance Criteria (ACs):**
  - **AC1:** Admin can associate a 'Text' input field (single/multi-line) to a criterion template.
  - **AC2:** Admin can associate a 'Number' input field with min/max constraints to a criterion template.
  - **AC3:** Admin can associate a 'Date' input field to a criterion template.
  - **AC4:** Admin can associate a 'File Upload' input field specification (e.g., allowed types, max size placeholder) to a criterion template.
  - **AC5:** Admin can associate a 'Dropdown' input field with a list of options.
  - **AC6:** Admin can mark any defined input field as mandatory.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Extend criteria template model in MongoDB to include input field definitions.
  - [ ] Implement API endpoints for managing input field definitions within a criterion.
  - [ ] Add validation logic for input field properties.

---

### Story 2.3: Manage Static Criteria

- **User Story / Goal:** As an Admin, I want to activate or deactivate predefined static criteria within the system, so that standard, non-configurable criteria can be included in evaluations where appropriate.
- **Detailed Requirements:**
  - The system will have a predefined list of static criteria (e.g., "Years of Service", "Highest Degree Held" - specific list TBD).
  - Admins can view this list of static criteria.
  - Admins can activate or deactivate these static criteria. Only active static criteria can be added to forms.
  - Data for static criteria might be partially auto-populated from Organization Management or require specific input. (This story focuses on activation; data sourcing TBD in later stories/epics).
- **Acceptance Criteria (ACs):**
  - **AC1:** Admin can retrieve the list of all predefined static criteria and their current status (Active/Inactive).
  - **AC2:** Admin can change the status of a static criterion.
  - **AC3:** Only 'Active' static criteria can be selected for inclusion in forms (enforced in Story 2.5).
- **Tasks (Optional Initial Breakdown):**
  - [ ] Define initial list and properties of static criteria (can be seeded).
  - [ ] Implement API for listing and toggling status of static criteria.

---

### Story 2.4: Create and Manage Evaluation Forms

- **User Story / Goal:** As an Admin, I want to create and manage evaluation forms, linking them to specific academic cadres and evaluation periods, so that tailored performance reviews can be conducted.
- **Detailed Requirements:**
  - Admins can create a new evaluation form with properties:
    - Form Name/Title
    - Applicable Academic Cadre(s) (from Organization Management data)
    - Evaluation Period (Start Date, End Date)
    - Status (Draft, Active, Archived)
  - Admins can list all existing forms with their details.
  - Admins can update the properties of a form (restrictions apply if Active and submissions exist).
  - Store form definitions in PostgreSQL, linking to MongoDB criteria where applicable.
- **Acceptance Criteria (ACs):**
  - **AC1:** Admin can create a new evaluation form, specifying its name, applicable cadre(s), and evaluation period.
  - **AC2:** Admin can retrieve a list of all evaluation forms.
  - **AC3:** Admin can update a form that is in 'Draft' status.
  - **AC4:** Form status transitions (Draft -> Active -> Archived) are managed.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API for CRUD operations on evaluation forms.
  - [ ] Implement PostgreSQL schema and repository for forms.
  - [ ] Implement service logic for form management.

---

### Story 2.5: Manage Weighted Categories within Forms

- **User Story / Goal:** As an Admin, I want to define and manage weighted categories within an evaluation form, so that criteria can be grouped logically and contribute appropriately to the overall evaluation.
- **Detailed Requirements:**
  - Within a specific evaluation form, Admins can create categories (e.g., "Research Activities," "Teaching Excellence," "Administrative Duties").
  - Each category can be assigned a weight (e.g., percentage) that contributes to the form's total score. The sum of weights for categories in a form should typically sum to 100% (or be validated accordingly).
  - Admins can add, edit, reorder (drag-and-drop ideally, or order property), and delete categories within a form.
  - Categories are stored in PostgreSQL, linked to their parent form.
- **Acceptance Criteria (ACs):**
  - **AC1:** Admin can add a new category with a name and weight to an evaluation form.
  - **AC2:** Admin can retrieve all categories for a given form, including their weights and order.
  - **AC3:** Admin can update the name, weight, and order of categories in a form.
  - **AC4:** Admin can delete a category from a form (if no criteria are assigned or rules allow).
  - **AC5:** System validates category weights within a form (e.g., sum to 100%).
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API for managing categories within forms.
  - [ ] Implement PostgreSQL schema and repository for categories.
  - [ ] Implement validation for category weights.

---

### Story 2.6: Assign Criteria to Categories in a Form

- **User Story / Goal:** As an Admin, I want to assign active dynamic and static criteria to specific categories within an evaluation form, and define their order, so that the form structure is complete for academician input.
- **Detailed Requirements:**
  - Admins can select from the list of 'Active' dynamic criteria templates and 'Active' static criteria.
  - Admins can assign selected criteria to a category within a chosen evaluation form.
  - Admins can define the display order of criteria within each category (drag-and-drop ideally, or order property).
  - A criterion can potentially be used in multiple categories or forms if the logic allows, or it might be a one-to-one mapping within a form context (assume flexible for now).
  - This mapping (Form-Category-Criterion) is stored in PostgreSQL.
- **Acceptance Criteria (ACs):**
  - **AC1:** Admin can successfully assign an active dynamic criterion to a category within a form.
  - **AC2:** Admin can successfully assign an active static criterion to a category within a form.
  - **AC3:** Admin can set and update the order of criteria within a category.
  - **AC4:** Admin can remove a criterion from a category in a form (if no data submitted against it or rules allow).
  - **AC5:** Only active criteria can be assigned to form categories.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API for assigning/managing criteria within form categories.
  - [ ] Implement PostgreSQL schema for Form-Category-Criterion mapping.
  - [ ] Implement logic to fetch active criteria for assignment.

## Change Log

| Change | Date | Version | Description | Author |
| ------ | ---- | ------- | ----------- | ------ |
| Initial Draft | 2025-05-15 | 0.1 | First draft of Epic 2 stories. | 1-pm AI |
