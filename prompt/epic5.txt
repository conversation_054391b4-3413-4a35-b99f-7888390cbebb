# Epic 5: Specialized Evaluation Workflows Implementation

**Goal:** Implement the distinct data entry, processing, and (where applicable) verification workflows for specialized user roles within APDYS, including Strategic Management Office staff for Department Strategic Performance, Managers for Academic Staff Competency, Archivists for Portfolio Control, and other designated data entry staff (e.g., Library, TTO, Erasmus). This ensures that all required performance-related data from various university units can be captured effectively.

## Story List

---

### Sub‑Epic 5.A: Department Strategic Performance Workflow (Strategic Management Office)

**Sub‑Goal:** Enable Strategic Management Office Staff to input and manage data related to Department Strategic Performance, which may feed into overall academic evaluations or university planning.

#### Story 5.A.1: Define Data Structure for Department Strategic Performance

- **User Story / Goal:** As an Admin/System Architect, I need a defined data structure (potentially using dynamic forms or a fixed schema) for collecting Department Strategic Performance indicators, so that Strategic Management Office staff can input relevant data.
- **Detailed Requirements:**
  - Define the types of data to be collected (e.g., departmental goals, KPIs, achievement levels, narrative reports). This might leverage parts of the dynamic criteria system (Epic 2) or require a more specific model.
  - Determine if these forms/data points are configurable by an Admin or are fixed. For MVP, a simpler, more fixed structure might be prioritized.
  - Data to be stored in PostgreSQL or MongoDB, depending on flexibility needs.
- **Acceptance Criteria (ACs):**
  - **AC1:** The data model for Department Strategic Performance indicators is defined and implemented in the database.
  - **AC2:** The model supports key fields like Department ID, Performance Indicator Name/ID, Target Value, Actual Value, Assessment Period, and supporting notes/evidence links.
  - **AC3:** Admins (or developers via seed) can define the specific indicators to be collected.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Analyze and finalize data fields required for Department Strategic Performance.
  - [ ] Design and implement database schema.

#### Story 5.A.2: Data Entry for Department Strategic Performance

- **User Story / Goal:** As a Strategic Management Office Staff member, I want to access a dedicated interface to input and manage Department Strategic Performance data for various departments and assessment periods, so that this information is centrally recorded.
- **Detailed Requirements:**
  - Strategic Management Office Staff can select a department and an assessment period.
  - They can input data for the predefined strategic performance indicators for that department.
  - Ability to save drafts, submit final data, and potentially view/edit past entries (subject to permissions).
  - Interface for this will be API‑driven.
- **Acceptance Criteria (ACs):**
  - **AC1:** Strategic Management Office Staff (with correct role) can successfully input and save data for department strategic performance indicators via API.
  - **AC2:** Submitted data is correctly associated with the specified department and assessment period.
  - **AC3:** Basic validation is in place for required fields.
  - **AC4:** Users can view previously submitted strategic performance data for departments.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API endpoints for CRUD operations on Department Strategic Performance data.
  - [ ] Implement service logic for data entry and retrieval.

---

### Sub‑Epic 5.B: Academic Staff Competency Evaluation Workflow (Managers)

**Sub‑Goal:** Enable Managers (Deans/HoDs) to evaluate the competency of academic staff they supervise, based on predefined competency frameworks or criteria.

#### Story 5.B.1: Define Data Structure for Academic Staff Competency

- **User Story / Goal:** As an Admin/System Architect, I need a defined data structure for Academic Staff Competency evaluations, so that Managers can record their assessments.
- **Detailed Requirements:**
  - Define competency areas and rating scales (e.g., “Leadership: Meets Expectations, Exceeds Expectations”). This might also use the dynamic criteria system or a specific model.
  - The structure should link the evaluation to a specific Academician, the evaluating Manager, and an evaluation period/context.
  - Space for qualitative comments per competency or overall.
- **Acceptance Criteria (ACs):**
  - **AC1:** Data model for Academic Staff Competency evaluations is defined and implemented in PostgreSQL.
  - **AC2:** Model supports linking to Academician, Manager, evaluation period, competency items, ratings, and comments.
  - **AC3:** Admins (or developers via seed) can define the competency items and rating scales.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Analyze and finalize data fields required for Staff Competency.
  - [ ] Design and implement database schema.

#### Story 5.B.2: Data Entry for Academic Staff Competency by Managers

- **User Story / Goal:** As a Manager (Dean/HoD), I want to access a dedicated interface to evaluate the competencies of academic staff I supervise, providing ratings and comments, so that their performance can be assessed from a managerial perspective.
- **Detailed Requirements:**
  - Managers can select an Academician they supervise and an evaluation context/period.
  - They can input ratings and comments for each defined competency area for that Academician.
  - Ability to save drafts and submit final competency evaluations.
- **Acceptance Criteria (ACs):**
  - **AC1:** Managers (with correct role and supervisory relationship) can input and save competency evaluations for their staff via API.
  - **AC2:** Submitted competency data is correctly associated with the Academician, Manager, and period.
  - **AC3:** Managers can view previously submitted competency evaluations for their staff.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API endpoints for managing Staff Competency evaluations.
  - [ ] Implement service logic, including checks for supervisory relationships.

---

### Sub‑Epic 5.C: Portfolio Control Workflow (Archivists)

**Sub‑Goal:** Enable Archivists to verify academic portfolio submissions against EBYS (Electronic Document Management System) records and other requirements, tracking the verification status within APDYS.

#### Story 5.C.1: Define Data Structure for Portfolio Control Checklist

- **User Story / Goal:** As an Admin/System Architect, I need a data structure for portfolio control items (e.g., required course documents, thesis submissions) that Archivists will verify, so that the verification process is standardized.
- **Detailed Requirements:**
  - Define what constitutes a “portfolio item” that needs verification by an Archivist.
  - For each item, the Archivist needs to record verification status (e.g., Verified in EBYS, Missing, Discrepancy Found) and optional notes.
  - Structure must indicate which Academician’s portfolio is being checked and for what period. 
- **Acceptance Criteria (ACs):**
  - **AC1:** Data model for portfolio control items and their verification status is defined and implemented in PostgreSQL.
  - **AC2:** Model supports linking to an Academician, item being checked, EBYS status, verification date, Archivist ID, and comments.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Define portfolio items and verification statuses.
  - [ ] Design and implement database schema.

#### Story 5.C.2: Portfolio Item Verification by Archivists

- **User Story / Goal:** As an Archivist, I want to view a list of Academicians or portfolio items requiring verification, access relevant information, and record the EBYS check status and any comments for each item, so that portfolio compliance is tracked.
- **Detailed Requirements:**
  - Archivists can access a list of portfolios needing review.
  - For each, they see a checklist of items to verify.
  - Archivists manually check EBYS and update status in APDYS for each item.
  - Ability to save progress and mark a portfolio review as complete.
- **Acceptance Criteria (ACs):**
  - **AC1:** Archivists can view a list of portfolio items requiring verification via API.
  - **AC2:** Archivists can update verification status and add comments.
  - **AC3:** Verification date and Archivist ID are recorded.
  - **AC4:** A portfolio review can be marked “Complete”.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API endpoints for listing items and updating verification status.
  - [ ] Implement service logic for portfolio verification.

---

### Sub‑Epic 5.D: Generic Data Entry Workflow for Other Roles (Library, TTO, Erasmus, etc.)

**Sub‑Goal:** Provide a flexible mechanism for other designated university staff to input specific data relevant to academic performance.

#### Story 5.D.1: Define Data Structure(s) for Other Data Entry Roles

- **User Story / Goal:** As an Admin/System Architect, I need to define data structures for data to be entered by roles like Library Staff, TTO Staff, so that diverse performance‑related information can be captured.
- **Detailed Requirements:**
  - Identify the types of data each role needs to submit.
  - Determine if these data input forms can reuse the dynamic form builder or require bespoke models.
  - Data should link to relevant Academician(s) and period.
- **Acceptance Criteria (ACs):**
  - **AC1:** For each “Other Data Entry Role,” a data model/form definition is in place.
  - **AC2:** Models support linking data to an Academician and period.
  - **AC3:** Admins can configure these forms or they are implemented as defined.
- **Tasks (Optional Initial Breakdown):**
  - [ ] For each role, list specific data points.
  - [ ] Design and implement data models or dynamic form configurations.

#### Story 5.D.2: Data Entry Interface for Other Roles

- **User Story / Goal:** As a designated Data Entry Staff member, I want to access a dedicated interface to input specific data related to academic performance, so that this information is included in APDYS.
- **Detailed Requirements:**
  - Each role will have a specific data entry API endpoint.
  - They should be able to select an Academician and an assessment period.
  - Input data according to the fields defined in Story 5.D.1.
  - Ability to save, submit, and view/edit past entries.
- **Acceptance Criteria (ACs):**
  - **AC1:** Each designated role can input and save their specific data via their API.
  - **AC2:** Submitted data is correctly associated with the relevant Academician(s) and period.
  - **AC3:** Basic validation is in place for required fields.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API endpoints for each role’s data submission.
  - [ ] Implement service logic for data entry and retrieval.

## Change Log

| Change | Date | Version | Description | Author |
| ------ | ---- | ------- | ----------- | ------ |
| Initial Draft | 2025‑05‑15 | 0.1 | First draft of Epic 5 stories. | 1‑pm AI |
