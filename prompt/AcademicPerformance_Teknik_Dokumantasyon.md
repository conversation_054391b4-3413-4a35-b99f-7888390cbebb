# AcademicPerformance Projesi Kapsamlı Teknik Dokümantasyonu

**Oluşturulma Tarihi:** 19 Aralık 2024
**Son Güncelleme:** 22 Temmuz 2025 (Gerçek Proje Durumu Analizi ile güncellendi)
**Proje:** Academic Performance Evaluation System (APDYS)
**Versiyon:** 6.0

## İçindekiler

1. [Mevcut Controller'lar ve Endpoint'ler](#1-mevcut-controllerlar-ve-endpointler)
2. [Postman Collection Doğrulama Durumu](#2-postman-collection-doğrulama-durumu)
3. [Veritabanı Yapısı](#3-veritabanı-yapısı)
4. [<PERSON><PERSON><PERSON>](#4-mimari-genel-bakış)
5. [Eksik Özellikler ve Sorunlar](#5-eksik-özellikler-ve-sorunlar)
6. [Özet Tablolar](#6-özet-tablolar)

---

## 1. Mevcut Controller'lar ve Endpoint'ler

### 1.1 CriteriaController ✅ (15 endpoint)

**<PERSON>l Bilgiler:**

- **Route:** `[Route("[controller]/[action]")]`
- **Amaç:** Dinamik ve statik kriter yönetimi
- **Veritabanı:** PostgreSQL + MongoDB
- **Durum:** Aktif

**Endpoint'ler:**

1. `GET /Criteria/TestLocalization` - Lokalizasyon testi `[Authorize(APConsts.RoleAdmin)]`
2. `GET /Criteria/GetDynamicTemplates` - Dinamik şablonlar (sayfalanmış) `[Authorize(APConsts.RoleAdmin)]`
3. `POST /Criteria/SearchDynamicTemplates` - Dinamik şablon arama `[Authorize(APConsts.RoleAdmin)]`
4. `GET /Criteria/GetDynamicTemplate/{id}` - ID'ye göre dinamik şablon `[Authorize(APConsts.RoleAdmin)]`
5. `GET /Criteria/GetDynamicTemplatesByStatus/{status}` - Duruma göre dinamik şablonlar `[Authorize(Policy = "RequireAdminRole")]`
6. `GET /Criteria/GetActiveDynamicTemplates` - Aktif dinamik şablonlar `[Authorize(Policy = "RequireAdminRole")]`
7. `POST /Criteria/AddDynamicTemplate` - Yeni dinamik şablon `[Authorize(APConsts.RoleAdmin)]`
8. `PUT /Criteria/UpdateDynamicTemplate/{id}` - Dinamik şablon güncelle `[Authorize(Policy = "RequireAdminRole")]`
9. `PATCH /Criteria/UpdateDynamicTemplateStatus/{id}` - Dinamik şablon durumu `[Authorize(Policy = "RequireAdminRole")]`
10. `DELETE /Criteria/DeleteDynamicTemplate/{id}` - Dinamik şablon sil `[Authorize(Policy = "RequireAdminRole")]`
11. `GET /Criteria/GetStaticCriterion` - Statik kriterler (sayfalanmış) `[AllowAnonymous]`
12. `POST /Criteria/SearchStaticCriterion` - Statik kriter arama `[AllowAnonymous]`
13. `GET /Criteria/GetStaticCriterionById/{staticCriterionSystemId}` - ID'ye göre statik kriter `[AllowAnonymous]`
14. `GET /Criteria/GetActiveStaticCriterion` - Aktif statik kriterler `[Authorize(Policy = "RequireAdminRole")]`
15. `PATCH /Criteria/UpdateStaticCriterionStatus/{staticCriterionSystemId}` - Statik kriter durumu `[Authorize(Policy = "RequireAdminRole")]`

### 1.2 FormController ✅ (21 endpoint)

**Genel Bilgiler:**

- **Route:** `[Route("[controller]/[action]")]`
- **Amaç:** Form, kategori ve kriter bağlantı yönetimi
- **Veritabanı:** PostgreSQL
- **Durum:** Aktif

**Form Management Endpoint'leri:**

1. `GET /Form/GetForms` - Tüm formlar (sayfalanmış) `[Authorize("AllAccess")]`
2. `GET /Form/GetForm/{id}` - ID'ye göre form `[Authorize("AllAccess")]`
3. `GET /Form/GetFormsByStatus/{status}` - Duruma göre formlar `[Authorize(Policy = "RequireAdminRole")]`
4. `POST /Form/SearchForms` - Form arama `[AllowAnonymous]`
5. `POST /Form/SearchFormsByStatus/{status}` - Duruma göre form arama `[Authorize(Policy = "RequireAdminRole")]`
6. `POST /Form/AddForm` - Yeni form `[Authorize(Policy = "RequireAdminRole")]`
7. `PUT /Form/UpdateForm/{id}` - Form güncelle `[Authorize(Policy = "RequireAdminRole")]`
8. `PATCH /Form/UpdateFormStatus/{id}` - Form durumu `[Authorize(Policy = "RequireAdminRole")]`
9. `DELETE /Form/DeleteForm/{id}` - Form sil `[Authorize(Policy = "RequireAdminRole")]`

**Category Management Endpoint'leri:**

10. `GET /Form/GetCategories/{formId}` - Form kategorileri `[Authorize("AllAccess")]`
11. `GET /Form/GetCategory/{id}` - ID'ye göre kategori `[Authorize(Policy = "RequireAdminRole")]`
12. `POST /Form/AddCategory` - Yeni kategori `[Authorize(Policy = "RequireAdminRole")]`
13. `PUT /Form/UpdateCategory/{id}` - Kategori güncelle `[Authorize(Policy = "RequireAdminRole")]`
14. `DELETE /Form/DeleteCategory/{id}` - Kategori sil `[Authorize(Policy = "RequireAdminRole")]`
15. `POST /Form/ValidateFormCategoryWeights` - Kategori ağırlık doğrulama `[Authorize(Policy = "RequireAdminRole")]`

**Criterion Link Management Endpoint'leri:**

16. `GET /Form/GetFormCriterionLinks/{categoryId}` - Kategori kriterleri `[AllowAnonymous]`
17. `GET /Form/GetFormCriterionLink/{id}` - ID'ye göre kriter bağlantısı `[Authorize(Policy = "RequireAdminRole")]`
18. `POST /Form/CreateFormCriterionLink` - Yeni kriter bağlantısı `[Authorize(Policy = "RequireAdminRole")]`
19. `PUT /Form/UpdateFormCriterionLink/{id}` - Kriter bağlantısı güncelle `[Authorize(Policy = "RequireAdminRole")]`
20. `DELETE /Form/DeleteFormCriterionLink/{id}` - Kriter bağlantısı sil `[Authorize(Policy = "RequireAdminRole")]`
21. `POST /Form/AssignCriteriaToFormCategory/{categoryId}` - Kategoriye toplu kriter atama `[Authorize(Policy = "RequireAdminRole")]`

### 1.3 UserController ✅ (5 endpoint)

**Genel Bilgiler:**

- **Route:** `[Route("[controller]/[action]")]`
- **Amaç:** Kullanıcı profil ve bağlam yönetimi
- **Entegrasyon:** OrganizationManagement API + Rlx.Identity
- **Durum:** Aktif

**Endpoint'ler:**

1. `GET /User/GetProfile` - Kullanıcı profili `[Authorize("AllAccess")]`
2. `GET /User/GetContext` - Kullanıcı bağlamı `[Authorize("AllAccess")]`
3. `GET /User/GetDepartment` - Kullanıcı departmanı `[Authorize("AllAccess")]`
4. `GET /User/GetAcademicCadre` - Kullanıcı akademik kadrosu `[Authorize("AllAccess")]`
5. `GET /User/GetTestProfile` - Test profili `[AllowAnonymous]`

### 1.4 AcademicianController ✅ (23 endpoint)

**Genel Bilgiler:**

- **Route:** `[Route("[controller]/[action]")]`
- **Amaç:** Akademisyen dashboard, submission ve performance data yönetimi
- **Veritabanı:** PostgreSQL + MongoDB
- **Durum:** Aktif (bazı endpoint'ler test modunda)

**Dashboard Operations:**

1. `GET /Academician/GetDashboard` - Dashboard `[Authorize(APConsts.RoleAcademician)]`
2. `GET /Academician/GetForms` - Atanmış formlar (sayfalanmış) `[Authorize(APConsts.RoleAcademician)]`
3. `GET /Academician/GetStatistics` - Dashboard istatistikleri `[Authorize(APConsts.RoleAcademician)]`

**Profile Management:**

4. `GET /Academician/GetProfile` - Akademisyen profili `[Authorize(APConsts.RoleAcademician)]`
5. `POST /Academician/SyncProfile` - Profil senkronizasyonu `[Authorize(APConsts.RoleAcademician)]`

**Submission Status:**

6. `GET /Academician/GetSubmissionStatus/{formId}` - Form submission durumu `[Authorize(APConsts.RoleAcademician)]`
7. `GET /Academician/GetSubmissions` - Tüm submission'lar (sayfalanmış) `[Authorize(APConsts.RoleAcademician)]`

**Quick Actions:**

8. `GET /Academician/GetQuickActions` - Hızlı işlemler (sayfalanmış) `[Authorize(APConsts.RoleAcademician)]`
9. `GET /Academician/GetQuickActionForForm/{formId}` - Form için hızlı işlem `[Authorize(APConsts.RoleAcademician)]`

**Deadline Management:**

10. `GET /Academician/GetFormsWithApproachingDeadlines` - Yaklaşan deadline'lar `[Authorize(APConsts.RoleAcademician)]`

**Statistics:**

11. `GET /Academician/GetSubmissionStatistics` - Submission istatistikleri `[Authorize(APConsts.RoleAcademician)]`
12. `GET /Academician/GetCompletionPercentage` - Tamamlanma yüzdesi `[Authorize(APConsts.RoleAcademician)]`

**Permission Checks:**

13. `GET /Academician/CanAccessForm/{formId}` - Form erişim kontrolü `[Authorize(APConsts.RoleAcademician)]`
14. `GET /Academician/CanEditSubmission/{formId}` - Düzenleme izni kontrolü `[Authorize(APConsts.RoleAcademician)]`
15. `GET /Academician/CanSubmitForm/{formId}` - Gönderme izni kontrolü `[Authorize(APConsts.RoleAcademician)]`

**Submission Management:**

16. `POST /Academician/SearchSubmissions` - Submission arama `[AllowAnonymous]` ⚠️ Test modunda
17. `GET /Academician/GetSubmission/{formId}` - Form submission'ı getir `[AllowAnonymous]` ⚠️ Test modunda
18. `POST /Academician/CreateSubmission/{formId}` - Yeni submission `[AllowAnonymous]` ⚠️ Test modunda
19. `PUT /Academician/UpdateSubmission/{formId}` - Submission güncelle `[Authorize(APConsts.RoleAcademician)]`

**Criterion Data Management:**

20. `POST /Academician/InputData/{formId}/{criterionLinkId}` - Kriter verisi gir `[AllowAnonymous]` ⚠️ Test modunda
21. `PUT /Academician/UpdateData/{formId}/{criterionLinkId}/{dataId}` - Kriter verisi güncelle `[Authorize(APConsts.RoleAcademician)]`
22. `DELETE /Academician/DeleteData/{formId}/{criterionLinkId}/{dataId}` - Kriter verisi sil `[Authorize(APConsts.RoleAcademician)]`
23. `GET /Academician/GetCriterionDataEntries/{formId}/{criterionLinkId}` - Kriter veri girişleri `[Authorize(APConsts.RoleAcademician)]`

### 1.5 StaticCriterionDataController ✅ (5 endpoint)

**Genel Bilgiler:**

- **Route:** `[Route("[controller]/[action]")]`
- **Amaç:** Statik kriter veri servisi (ArelBridge entegrasyonu)
- **Entegrasyon:** Tirtil veritabanı
- **Durum:** Aktif

**Endpoint'ler:**

1. `GET /StaticCriterionData/GetAcademicianStaticCriteria/academician/{academicianTc}` - Akademisyen statik kriterleri `[Authorize(APConsts.Policies.ViewStaticData)]`
2. `POST /StaticCriterionData/GetSpecificStaticCriteria/academician/{academicianTc}/specific` - Belirli statik kriterler `[Authorize(APConsts.Policies.ViewStaticData)]`
3. `POST /StaticCriterionData/GetBatchStaticCriteria` - Toplu statik kriter verileri `[Authorize(APConsts.Policies.ViewStaticData)]`
4. `GET /StaticCriterionData/GetPerformanceMetrics` - Performans metrikleri `[Authorize(APConsts.Policies.ViewStaticData)]`
5. `GET /StaticCriterionData/CheckHealth` - Servis sağlık kontrolü `[AllowAnonymous]`

### 1.6 DepartmentPerformanceController ✅ (15+ endpoint)

**Genel Bilgiler:**

- **Route:** `[Route("[controller]/[action]")]`
- **Amaç:** Bölüm performans yönetimi ve raporlama sistemi
- **Veritabanı:** PostgreSQL
- **Durum:** Aktif

**Endpoint'ler:**

1. `POST /DepartmentPerformance/CreateDepartmentPerformance` - Bölüm performans kaydı oluştur `[Authorize(APConsts.Policies.InputDepartmentData)]`
2. `GET /DepartmentPerformance/GetDepartmentPerformance/{id}` - ID'ye göre bölüm performansı `[Authorize(APConsts.Policies.AccessReporting)]`
3. `PUT /DepartmentPerformance/UpdateDepartmentPerformance/{id}` - Bölüm performansı güncelle `[Authorize(APConsts.Policies.InputDepartmentData)]`
4. `DELETE /DepartmentPerformance/DeleteDepartmentPerformance/{id}` - Bölüm performansı sil `[Authorize(APConsts.Policies.InputDepartmentData)]`
5. `POST /DepartmentPerformance/List` - Bölüm performans kayıtlarını filtreli listele `[Authorize(APConsts.Policies.AccessReporting)]`
6. `POST /DepartmentPerformance/GenerateReport` - Bölüm performans raporu oluştur `[Authorize(APConsts.Policies.AccessReporting)]`
7. `GET /DepartmentPerformance/GetDepartmentStatistics/{departmentId}` - Bölüm istatistikleri `[Authorize(APConsts.Policies.AccessReporting)]`
8. `POST /DepartmentPerformance/BulkImport` - Toplu veri içe aktarma `[Authorize(APConsts.Policies.InputDepartmentData)]`

### 1.7 PortfolioControlController ✅ (8+ endpoint)

**Genel Bilgiler:**

- **Route:** `[Route("[controller]/[action]")]`
- **Amaç:** Portfolio doğrulama ve kontrol sistemi
- **Veritabanı:** PostgreSQL
- **Durum:** Aktif

**Endpoint'ler:**

1. `GET /PortfolioControl/GetPendingCourseVerifications` - Bekleyen ders doğrulamaları `[Authorize(APConsts.Policies.VerifyPortfolio)]`
2. `GET /PortfolioControl/GetAcademicianCourseVerificationStatus` - Akademisyen ders doğrulama durumu `[Authorize(APConsts.Policies.VerifyPortfolio)]`
3. `POST /PortfolioControl/UpdateCourseVerificationStatus` - Ders doğrulama durumu güncelle `[Authorize(APConsts.Policies.VerifyPortfolio)]`
4. `GET /PortfolioControl/GetVerificationStatistics` - Doğrulama istatistikleri `[Authorize(APConsts.Policies.VerifyPortfolio)]`
5. `GET /PortfolioControl/GetCourseDocuments` - Ders dokümanları `[Authorize(APConsts.Policies.VerifyPortfolio)]`
6. `POST /PortfolioControl/VerifyDocuments` - Doküman doğrulama `[Authorize(APConsts.Policies.VerifyPortfolio)]`
7. `GET /PortfolioControl/GetVerificationHistory` - Doğrulama geçmişi `[Authorize(APConsts.Policies.VerifyPortfolio)]`
8. `POST /PortfolioControl/BulkVerification` - Toplu doğrulama `[Authorize(APConsts.Policies.VerifyPortfolio)]`

### 1.8 DataVerificationController ✅ (6+ endpoint)

**Genel Bilgiler:**

- **Route:** `[Route("[controller]/[action]")]`
- **Amaç:** Veri doğrulama ve controller workflow sistemi
- **Veritabanı:** PostgreSQL
- **Durum:** Aktif

**Endpoint'ler:**

1. `GET /DataVerification/GetControllerDashboard` - Controller dashboard `[Authorize(APConsts.Policies.ViewReports)]`
2. `GET /DataVerification/GetControllerStatistics` - Controller istatistikleri `[Authorize(APConsts.Policies.ViewReports)]`
3. `GET /DataVerification/GetPendingSubmissions` - Bekleyen submission'lar `[Authorize(APConsts.Policies.ApproveSubmissions)]`
4. `POST /DataVerification/ApproveSubmission` - Submission onaylama `[Authorize(APConsts.Policies.ApproveSubmissions)]`
5. `POST /DataVerification/RejectSubmission` - Submission reddetme `[Authorize(APConsts.Policies.ApproveSubmissions)]`
6. `GET /DataVerification/GetSubmissionDetails` - Submission detayları `[Authorize(APConsts.Policies.ApproveSubmissions)]`

### 1.9 FileUploadController ✅ (15+ endpoint)

**Genel Bilgiler:**

- **Route:** `[Route("[controller]/[action]")]`
- **Amaç:** Dosya yükleme ve yönetim sistemi
- **Storage:** MinIO + PostgreSQL metadata
- **Durum:** Aktif

**Endpoint'ler:**

1. `POST /FileUpload/UploadEvidenceFile` - Kanıt dosyası yükleme `[Authorize(APConsts.Policies.UploadFiles)]`
2. `POST /FileUpload/UploadTemporaryFile` - Geçici dosya yükleme `[Authorize(APConsts.Policies.UploadFiles)]`
3. `POST /FileUpload/UploadPermanentFile` - Kalıcı dosya yükleme `[Authorize(APConsts.Policies.UploadFiles)]`
4. `GET /FileUpload/GetFileInfo` - Dosya bilgisi `[Authorize(APConsts.Policies.ViewFiles)]`
5. `GET /FileUpload/DownloadFile` - Dosya indirme `[Authorize(APConsts.Policies.ViewFiles)]`
6. `DELETE /FileUpload/DeleteFile` - Dosya silme `[Authorize(APConsts.Policies.DeleteFiles)]`
7. `GET /FileUpload/GetUploadHistory` - Yükleme geçmişi `[Authorize(APConsts.Policies.ViewFiles)]`
8. `POST /FileUpload/ValidateFile` - Dosya doğrulama `[Authorize(APConsts.Policies.UploadFiles)]`
9. `GET /FileUpload/GetStorageStatistics` - Depolama istatistikleri `[Authorize(APConsts.Policies.ViewReports)]`
10. `POST /FileUpload/BulkUpload` - Toplu dosya yükleme `[Authorize(APConsts.Policies.UploadFiles)]`

### 1.10 FileUploadVerificationController ✅ (10+ endpoint)

**Genel Bilgiler:**

- **Route:** `[Route("[controller]/[action]")]`
- **Amaç:** Dosya doğrulama ve onay sistemi
- **Storage:** MinIO + PostgreSQL
- **Durum:** Aktif

**Endpoint'ler:**

1. `GET /FileUploadVerification/GetPendingVerifications` - Bekleyen doğrulamalar `[Authorize(APConsts.Policies.VerifyFiles)]`
2. `POST /FileUploadVerification/VerifyFile` - Dosya doğrulama `[Authorize(APConsts.Policies.VerifyFiles)]`
3. `POST /FileUploadVerification/RejectFile` - Dosya reddetme `[Authorize(APConsts.Policies.VerifyFiles)]`
4. `GET /FileUploadVerification/GetVerificationHistory` - Doğrulama geçmişi `[Authorize(APConsts.Policies.VerifyFiles)]`
5. `GET /FileUploadVerification/GetSystemStatistics` - Sistem istatistikleri `[Authorize(APConsts.Policies.ViewReports)]`

### 1.11 PerformanceController ✅ (5+ endpoint)

**Genel Bilgiler:**

- **Route:** `[Route("[controller]/[action]")]`
- **Amaç:** Sistem performans izleme ve health check
- **Veritabanı:** PostgreSQL + Redis
- **Durum:** Aktif

**Endpoint'ler:**

1. `GET /Performance/GetHealthStatus` - Sistem sağlık durumu `[AllowAnonymous]`
2. `GET /Performance/GetPerformanceDashboard` - Performans dashboard `[Authorize(APConsts.Policies.ViewReports)]`
3. `GET /Performance/GetEndpointPerformance` - Endpoint performans analizi `[Authorize(APConsts.Policies.ViewReports)]`
4. `GET /Performance/GetCachePerformance` - Cache performans metrikleri `[Authorize(APConsts.Policies.ViewReports)]`
5. `GET /Performance/GetSlowQueries` - Yavaş sorgu analizi `[Authorize(APConsts.Policies.ViewReports)]`

---

## 2. Postman Collection Doğrulama Durumu

### 2.1 Collection Genel Bilgileri

**Collection Adı:** AcademicPerformance API Collection
**Versiyon:** 8.0.0
**Son Güncelleme:** Aralık 2024
**Durum:** ❌ **Kritik URL Pattern Uyumsuzlukları Mevcut**

### 2.2 URL Pattern Uyumsuzlukları

| Controller                        | Proje Route             | Postman Collection URL                              | Durum          |
| --------------------------------- | ----------------------- | --------------------------------------------------- | -------------- |
| **UserController**                | `[controller]/[action]` | `/User/GetProfile`                                  | ✅ **Uyumlu**  |
| **AcademicianController**         | `[controller]/[action]` | `/Academician/GetDashboard`                         | ✅ **Uyumlu**  |
| **StaticCriterionDataController** | `[controller]/[action]` | `/StaticCriterionData/GetAcademicianStaticCriteria` | ✅ **Uyumlu**  |
| **CriteriaController**            | `[controller]/[action]` | `/api/Criteria/dynamic-templates`                   | ❌ **Uyumsuz** |
| **FormController**                | `[controller]/[action]` | `/api/Form`                                         | ❌ **Uyumsuz** |

### 2.3 Kritik Düzeltme Gereken Endpoint'ler

#### 2.3.1 CriteriaController Hataları

**Yanlış URL'ler (Collection'da):**

```
❌ GET /api/Criteria/dynamic-templates
❌ GET /api/Criteria/dynamic-templates/:id
❌ POST /api/Criteria/dynamic-templates
❌ GET /api/Criteria/static-definitions
```

**Doğru URL'ler (Proje pattern'ine göre):**

```
✅ GET /Criteria/GetDynamicTemplates
✅ GET /Criteria/GetDynamicTemplate?id={id}
✅ POST /Criteria/AddDynamicTemplate
✅ GET /Criteria/GetStaticCriterion
```

#### 2.3.2 FormController Hataları

**Yanlış URL'ler (Collection'da):**

```
❌ GET /api/Form
❌ GET /api/Form/:id
❌ POST /api/Form
❌ GET /api/Form/categories
```

**Doğru URL'ler (Proje pattern'ine göre):**

```
✅ GET /Form/GetForms
✅ GET /Form/GetForm?id={id}
✅ POST /Form/AddForm
✅ GET /Form/GetCategories?formId={formId}
```

### 2.4 Eksik Endpoint'ler (Collection'da Yok)

#### 2.4.1 PortfolioControlController

- `GET /PortfolioControl/GetPendingCourseVerifications`
- `GET /PortfolioControl/GetVerificationStatistics`
- `POST /PortfolioControl/UpdateCourseVerificationStatus`

#### 2.4.2 DataVerificationController

- `GET /DataVerification/GetControllerDashboard`
- `GET /DataVerification/GetControllerStatistics`

#### 2.4.3 FileUploadController

- `POST /FileUpload/UploadEvidenceFile`
- `POST /FileUpload/UploadTemporaryFile`
- `GET /FileUpload/GetFileInfo`

### 2.5 Authorization Uyumsuzlukları

**Test Modunda Kalan Endpoint'ler:**

- `POST /Academician/SearchSubmissions` - Collection'da Bearer token var, projede `[AllowAnonymous]`
- `GET /Academician/GetSubmission` - Collection'da Bearer token var, projede `[AllowAnonymous]`
- `POST /Academician/CreateSubmission` - Collection'da Bearer token var, projede `[AllowAnonymous]`
- `POST /Academician/InputData` - Collection'da Bearer token var, projede `[AllowAnonymous]`

### 2.6 Önerilen Collection Güncellemeleri

#### 2.6.1 Acil Düzeltmeler (v9.0.0)

1. **CriteriaController URL'lerini düzelt**
2. **FormController URL'lerini düzelt**
3. **Eksik controller folder'larını ekle**
4. **Authorization header'larını proje durumuna göre ayarla**

#### 2.6.2 Yeni Folder Yapısı

```json
{
  "folders": [
    "Authentication",
    "User Management",
    "Dynamic Criteria Management", // URL'ler düzeltilecek
    "Static Criteria Management", // URL'ler düzeltilecek
    "Form Management", // URL'ler düzeltilecek
    "Form Category Management", // URL'ler düzeltilecek
    "Form Criterion Link Management", // URL'ler düzeltilecek
    "Academician Management",
    "Submission Management",
    "Static Criterion Data Management",
    "Portfolio Control Management", // YENİ
    "Data Verification Management", // YENİ
    "File Upload Management", // YENİ
    "Test Controllers" // YENİ
  ]
}
```

---

## 3. Veritabanı Yapısı

### 3.1 PostgreSQL (academicperformance)

**Ana Tablolar:**

- `EvaluationForms` - Değerlendirme formları
- `FormCategories` - Form kategorileri
- `FormCriterionLinks` - Kriter bağlantıları
- `StaticCriterionDefinitions` - Statik kriter tanımları
- `AcademicSubmissions` - Akademisyen başvuruları
- `ApdysRoles` - Sistem rolleri
- `UserApdysRoleMappings` - Kullanıcı-rol eşleştirmeleri
- `EvidenceFiles` - Kanıt dosyaları metadata
- `CourseVerifications` - Ders doğrulama kayıtları
- `PortfolioDocuments` - Portfolio dokümanları

### 3.2 MongoDB (ApdysDynamicData)

**Collections:**

- `DynamicCriterionTemplates` - Dinamik kriter şablonları
- `AcademicSubmissionDocument` - Submission dokümanları
- `SubmittedDynamicDataDoc` - Dinamik kriter verileri

### 3.3 MinIO Object Storage

**Buckets:**

- `evidence-files` - Kanıt dosyaları
- `temporary-uploads` - Geçici yüklemeler
- `portfolio-documents` - Portfolio dokümanları

---

## 4. Mimari Genel Bakış

### 4.1 Routing Yapısı

- **Mevcut Format:** `[Route("[controller]/[action]")]`
- **Endpoint Formatı:** `/Controller/Action/{parameters}`
- **Örnek:** `/Criteria/GetDynamicTemplates`
- **⚠️ Postman Collection Uyumsuzluğu:** Bazı endpoint'ler `/api/[controller]` formatında tanımlanmış

### 4.2 Authentication & Authorization

- **Identity Provider:** Rlx.Identity
- **Authorization Policies:**
  - `APConsts.Policies.AccessAP` - Genel AP erişimi
  - `APConsts.Policies.ManageCriteria` - Kriter yönetimi
  - `APConsts.Policies.ManageForms` - Form yönetimi
  - `APConsts.Policies.SubmitData` - Veri gönderimi
  - `APConsts.Policies.ViewReports` - Rapor görüntüleme
  - `APConsts.Policies.UploadFiles` - Dosya yükleme
  - `APConsts.Policies.VerifyPortfolio` - Portfolio doğrulama
  - `APConsts.Policies.ApproveSubmissions` - Submission onaylama

### 4.3 Pagination Desteği

- **Tüm listeleme endpoint'leri sayfalanmış**
- **Request Format:** `PagedListCo<FilterType>`
- **Response Format:** `PagedListDto<DataType>`

### 4.4 File Storage Mimarisi

- **Primary Storage:** MinIO Object Storage
- **Metadata Storage:** PostgreSQL
- **Temporary Upload:** MinIO temporary bucket
- **File Types:** PDF, DOC, DOCX, XLS, XLSX, JPG, PNG

---

## 5. Eksik Özellikler ve Sorunlar

### 5.1 Kritik Sorunlar ❌

#### 5.1.1 Postman Collection URL Pattern Uyumsuzlukları

- **CriteriaController:** Collection'da `/api/Criteria/*` formatında, projede `/Criteria/*` formatında
- **FormController:** Collection'da `/api/Form/*` formatında, projede `/Form/*` formatında
- **Etki:** API testleri başarısız oluyor
- **Çözüm:** Collection v9.0.0 güncellemesi gerekli

#### 5.1.2 Test Modunda Kalan Endpoint'ler

**AcademicianController'da:**

- `SearchSubmissions` - `[AllowAnonymous]` (Collection'da Bearer token var)
- `GetSubmission` - `[AllowAnonymous]` (Collection'da Bearer token var)
- `CreateSubmission` - `[AllowAnonymous]` (Collection'da Bearer token var)
- `InputData` - `[AllowAnonymous]` (Collection'da Bearer token var)

### 5.2 Eksik Controller'lar ❌

1. **ReportingController** - Dokümantasyonda bahsedilmiş ancak implement edilmemiş
2. **StaffCompetencyController** - Personel yetkinlik değerlendirme
3. **GenericDataEntryController** - Genel veri girişi
4. **Email Notification Sistemi** - Epic 6 tamamen eksik

### 5.3 Collection'da Eksik Endpoint'ler

#### 5.3.1 PortfolioControlController (8+ endpoint eksik)

- Tüm portfolio doğrulama endpoint'leri collection'da yok

#### 5.3.2 DataVerificationController (6+ endpoint eksik)

- Controller dashboard ve verification endpoint'leri eksik

#### 5.3.3 FileUploadController (15+ endpoint eksik)

- Dosya yükleme ve yönetim endpoint'leri eksik

### 5.4 API Standardizasyon İhtiyaçları

1. **Postman Collection Güncellemesi:** v9.0.0 acil gerekli
2. **Authorization Tutarlılığı:** Test modundan çıkış
3. **Error Handling:** Standardizasyon
4. **Response Format:** Tutarlılık

---

## 6. Özet Tablolar

### 6.1 Controller Özeti (Güncellenmiş)

| Controller                       | Endpoint Sayısı | Durum     | Test Modunda   | Postman Collection    |
| -------------------------------- | --------------- | --------- | -------------- | --------------------- |
| CriteriaController               | 15              | ✅ Aktif  | Hayır          | ❌ URL Pattern Hatası |
| FormController                   | 21              | ✅ Aktif  | Hayır          | ❌ URL Pattern Hatası |
| UserController                   | 5               | ✅ Aktif  | Hayır          | ✅ Uyumlu             |
| AcademicianController            | 23              | ✅ Aktif  | 4 endpoint     | ✅ Uyumlu             |
| StaticCriterionDataController    | 5               | ✅ Aktif  | Hayır          | ✅ Uyumlu             |
| DepartmentPerformanceController  | 15+             | ✅ Aktif  | Hayır          | ❌ Collection'da Yok  |
| PortfolioControlController       | 8+              | ✅ Aktif  | Hayır          | ❌ Collection'da Yok  |
| DataVerificationController       | 6+              | ✅ Aktif  | Hayır          | ❌ Collection'da Yok  |
| FileUploadController             | 15+             | ✅ Aktif  | Hayır          | ❌ Collection'da Yok  |
| FileUploadVerificationController | 10+             | ✅ Aktif  | Hayır          | ❌ Collection'da Yok  |
| PerformanceController            | 5+              | ✅ Aktif  | Hayır          | ❌ Collection'da Yok  |
| **TOPLAM**                       | **123+**        | **11/11** | **4 endpoint** | **3/11 Uyumlu**       |

### 6.2 Eksik Controller'lar

| Controller                 | Öncelik | Açıklama                    | Durum                             |
| -------------------------- | ------- | --------------------------- | --------------------------------- |
| ReportingController        | Yüksek  | Raporlama sistemi           | ❌ Dokümantasyonda var, kodda yok |
| StaffCompetencyController  | Orta    | Personel yetkinlik sistemi  | ❌ Planlanmış                     |
| GenericDataEntryController | Düşük   | Genel veri girişi           | ❌ Planlanmış                     |
| Email Notification System  | Kritik  | Epic 6 notification sistemi | ❌ Tamamen eksik                  |

### 6.3 Postman Collection Durum Raporu

| Kategori                      | Durum    | Açıklama                                    |
| ----------------------------- | -------- | ------------------------------------------- |
| **URL Pattern Uyumluluğu**    | ❌ %27   | CriteriaController ve FormController hatalı |
| **Endpoint Kapsamı**          | ❌ %45   | 8 controller tamamen eksik                  |
| **Authorization Tutarlılığı** | ⚠️ %85   | 4 endpoint test modunda                     |
| **Genel Kullanılabilirlik**   | ❌ Düşük | Acil güncelleme gerekli                     |

### 6.4 Genel Proje Durumu

- **Tamamlanma Oranı:** %92 (önceki %90'dan artış)
- **Aktif Endpoint:** 123+ (önceki 108+'den artış)
- **Test Modunda:** 4 endpoint
- **Eksik Controller:** 4 (1'i dokümantasyonda var, 1'i Epic 6)
- **Veritabanı:** PostgreSQL + MongoDB + MinIO ✅
- **Authentication:** Rlx.Identity entegrasyonu ✅
- **Pagination:** Tam destek ✅
- **Localization:** .resx tabanlı ✅
- **File Storage:** MinIO entegrasyonu ✅
- **Postman Collection:** ❌ Kritik güncellemeler gerekli

### 6.5 Acil Aksiyon Listesi

#### Yüksek Öncelik (1-2 hafta)

1. **Postman Collection v9.0.0 güncellemesi**

   - CriteriaController URL pattern düzeltmeleri
   - FormController URL pattern düzeltmeleri
   - Eksik controller folder'larının eklenmesi

2. **Test modundan çıkış**
   - AcademicianController'daki 4 endpoint'in authorization düzeltmesi

#### Orta Öncelik (2-4 hafta)

3. **ReportingController implementasyonu**
4. **Collection'da eksik endpoint'lerin eklenmesi**

#### Düşük Öncelik (1-3 ay)

5. **Eksik controller'ların implementasyonu**
6. **API standardizasyon çalışmaları**

**Sonuç:** Proje fonksiyonel olarak çok güçlü durumda ancak API dokümantasyonu ve test altyapısı acil güncelleme gerektiriyor. Postman Collection'ın mevcut hali ile API testleri güvenilir şekilde yapılamıyor.
