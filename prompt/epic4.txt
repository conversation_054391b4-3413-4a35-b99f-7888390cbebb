# Epic 4: Controller - Data Verification Workflow

**Goal:** Enable Controllers to efficiently review performance data submissions from Academicians, verify the provided information and evidence, and make a decision to approve or reject the submission with appropriate feedback, ensuring the integrity of the evaluation process.

## Story List

### Story 4.1: Controller Dashboard - View Pending Submissions

- **User Story / Goal:** As a Controller, I want to see a dashboard listing all academician submissions that are pending my review, so that I can manage my workload and address submissions in a timely manner.
- **Detailed Requirements:**
  - Upon login, a Controller should see a list of submissions assigned to them or to a pool they are part of, which are in a "Submitted" or "Pending Approval" status.
  - The list should display key information for each submission, such as Academician Name, Form Name, Submission Date, and possibly Department/Cadre.
  - Controllers should be able to sort or filter this list (e.g., by submission date, academician name).
  - The dashboard should clearly indicate the source of the submission (e.g., which academician from which department).
- **Acceptance Criteria (ACs):**
  - **AC1:** Controller can view a list of all submissions awaiting their review.
  - **AC2:** Each item in the list shows Academician Name, Form Name, and Submission Date.
  - **AC3:** Controller can sort the list of pending submissions by date and academician name.
  - **AC4:** The system correctly identifies and displays submissions that fall under the Controller's purview.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API for fetching pending submissions for a controller.
  - [ ] Implement logic for assigning submissions to controllers or controller pools.
  - [ ] Implement sorting and basic filtering capabilities.

---

### Story 4.2: Review Submitted Data and Evidence

- **User Story / Goal:** As a Controller, when I select a submission for review, I want to view all the data entered by the Academician, structured by categories and criteria, and be able to easily access/preview any uploaded supporting evidence, so that I can thoroughly assess the submission.
- **Detailed Requirements:**
  - When a Controller opens a submission, they should see the data in the same form structure (categories, criteria, input fields) that the Academician used.
  - All data entered by the Academician for each criterion should be clearly displayed.
  - For each criterion entry with uploaded evidence, the Controller must be able to view a list of associated files.
  - Controllers should be able to preview common file types (e.g., PDF, images) directly within the review interface if feasible, or at least download them easily.
  - The interface should clearly distinguish between Academician-provided data and system-defined criteria information (like coefficients).
- **Acceptance Criteria (ACs):**
  - **AC1:** Controller can select a submission and view all submitted data, organized by categories and criteria.
  - **AC2:** Controller can view the names of all evidence files uploaded for each criterion entry.
  - **AC3:** Controller can download any evidence file.
  - **AC4:** (Stretch) Controller can preview common evidence file types.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API to fetch a specific submission's full details, including data and evidence links.
  - [ ] Implement logic to retrieve and display submitted data and evidence files.
  - [ ] Investigate/implement file preview capabilities (if feasible).

---

### Story 4.3: Approve Submission

- **User Story / Goal:** As a Controller, I want to approve a submission once I have verified its accuracy and completeness, so that the evaluation process can move forward for that Academician.
- **Detailed Requirements:**
  - Provide an "Approve" action for the Controller.
  - Upon approval, the submission status is updated to "Approved."
  - A notification should be triggered to the Academician (Epic 6).
  - Controller may add an optional comment upon approval.
- **Acceptance Criteria (ACs):**
  - **AC1:** Controller can click an "Approve" button.
  - **AC2:** Submission status is updated to "Approved."
  - **AC3:** Approved submission becomes read‑only.
  - **AC4:** Notification to Academician is triggered.
  - **AC5:** Controller can add an optional comment.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API endpoint for approving a submission.
  - [ ] Implement logic to update status and store comments.
  - [ ] Integrate with notification system.

---

### Story 4.4: Reject Submission with Mandatory Comments

- **User Story / Goal:** As a Controller, I want to reject a submission if it is inaccurate or incomplete, providing mandatory comments explaining the reasons for rejection, so that the Academician can make necessary corrections and resubmit.
- **Detailed Requirements:**
  - Provide a "Reject" action.
  - Controller must enter comments explaining rejection; field is mandatory.
  - Submission status is updated to "Rejected" (or "Needs Revision").
  - Academician must be able to edit and resubmit based on feedback (resubmission flow TBD).
  - Notification with comments is sent to the Academician (Epic 6).
- **Acceptance Criteria (ACs):**
  - **AC1:** Controller can click a "Reject" button.
  - **AC2:** Comment field is required; rejection cannot proceed without it.
  - **AC3:** Submission status is updated to "Rejected."
  - **AC4:** Rejection comments are stored and visible to the Academician.
  - **AC5:** Academician can edit/resubmit after rejection.
  - **AC6:** Notification with comments is sent.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API endpoint for rejecting a submission.
  - [ ] Implement logic to update status, store comments, and unlock for resubmission.
  - [ ] Ensure comment requirement is enforced.
  - [ ] Integrate with notification system.

## Change Log

| Change | Date | Version | Description | Author |
| ------ | ---- | ------- | ----------- | ------ |
| Initial Draft | 2025-05-15 | 0.1 | First draft of Epic 4 stories. | 1-pm AI |
