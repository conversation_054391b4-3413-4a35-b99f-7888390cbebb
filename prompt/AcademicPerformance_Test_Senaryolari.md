# 🎯 AcademicPerformance Projesi - Test Senaryoları Rehberi

## 📖 Giriş

Bu dokümantasyon, AcademicPerformance (Akademik Performans Değerlendirme Sistemi) projesinin test senaryolarını teknik olmayan bir dille açıklamaktadır. Her test senary<PERSON>u, gerç<PERSON> kullanıcıların sistemde yapacağı işlemleri adım adım anlatmaktadır.

---

## 👥 Sistem Kullanıcıları

### 🔧 **Admin (Sistem Yöneticisi)**
- Sistemi kuran ve yöneten kişi
- Formları oluşturur ve düzenler
- Kriterleri belirler
- Kullanıcı yetkilerini yönetir

### 👨‍🏫 **Akademisyen (Öğretim Üyesi)**
- Performans verilerini giren kişi
- Belgelerini yükler
- Başvurularını gönderir

### ✅ **Controller (Onaylayıcı)**
- Başvuruları inceleyen kişi
- Onay/red kararı veren
- Portföy kontrolü yapan

---

## 🧪 Test Senaryoları

### **1. ADMIN KULLANICI TEST SENARYOLARI**

#### **1.1 Form Yönetimi**

**TS-001: Yeni Değerlendirme Formu Oluşturma**
- Admin sisteme giriş yapar
- "Yeni Form Oluştur" butonuna tıklar
- Form adını, açıklamasını ve dönemini girer
- Formu kaydeder
- Sistem başarı mesajı gösterir

**TS-002: Mevcut Formları Listeleme**
- Admin "Formlar" sayfasına gider
- Tüm formları listeler
- Durum, tarih ve isme göre filtreler
- Arama yaparak belirli formu bulur

**TS-003: Form Durumunu Güncelleme**
- Admin bir formu seçer
- Durumunu "Taslak"tan "Aktif"e çevirir
- Değişikliği kaydeder
- Akademisyenler artık bu formu görebilir

**TS-004: Forma Kategori Ekleme**
- Admin bir formu açar
- "A - Eğitim Faaliyetleri" kategorisini ekler
- Kategori ağırlığını %40 olarak belirler
- Kategorinin sırasını ayarlar

**TS-005: Kriter Tanımlama**
- Admin bir kategoriye kriter ekler
- "Verilen Ders Sayısı" kriterini oluşturur
- Kriterin zorunlu olup olmadığını belirler
- Kriter ağırlığını ayarlar

#### **1.2 Sistem Yönetimi**

**TS-006: Kullanıcı Rol Atama**
- Admin kullanıcı listesini açar
- Bir akademisyeni seçer
- "Controller" rolünü atar
- Kullanıcı artık başvuruları onaylayabilir

**TS-007: Sistem Loglarını İnceleme**
- Admin "Sistem Logları" sayfasına gider
- Son 24 saatteki işlemleri görür
- Hata loglarını filtreler
- Problemi tespit eder

### **2. AKADEMİSYEN KULLANICI TEST SENARYOLARI**

#### **2.1 İlk Giriş ve Profil**

**TS-008: İlk Giriş ve Profil Senkronizasyonu**
- Akademisyen sisteme ilk kez giriş yapar
- Sistem otomatik olarak profil bilgilerini çeker
- Ad, soyad, departman bilgileri güncellenir
- Profil tamamlanır

**TS-009: Dashboard Görüntüleme**
- Akademisyen ana sayfaya girer
- Aktif formları görür
- Tamamlanan/bekleyen başvuru sayılarını kontrol eder
- Son tarihleri takip eder

#### **2.2 Form Doldurma Süreci**

**TS-010: Yeni Başvuru Oluşturma**
- Akademisyen aktif bir formu seçer
- "Yeni Başvuru Oluştur" butonuna tıklar
- Sistem boş bir form açar
- Taslak olarak kaydedilir

**TS-011: A Kategorisi Veri Girişi (Eğitim)**
- Akademisyen A kategorisini açar
- "Verilen Ders Sayısı" alanına "5" yazar
- "Danışmanlık Yapılan Öğrenci Sayısı"na "12" girer
- Verileri kaydeder

**TS-012: Kanıt Dosyası Yükleme**
- Akademisyen bir kritere kanıt ekler
- "Dosya Yükle" butonuna tıklar
- Bilgisayarından PDF dosyasını seçer
- Dosya başarıyla yüklenir

**TS-013: Başvuruyu Gönderme**
- Akademisyen tüm alanları doldurur
- Gerekli belgeleri yükler
- "Başvuruyu Gönder" butonuna tıklar
- Sistem onay mesajı gösterir

#### **2.3 Başvuru Takibi**

**TS-014: Başvuru Durumunu Kontrol Etme**
- Akademisyen "Başvurularım" sayfasına gider
- Gönderdiği başvurunun durumunu görür
- "İnceleniyor" durumunda olduğunu fark eder

**TS-015: Red Edilen Başvuruyu Düzenleme**
- Akademisyen red edilen başvurusunu açar
- Controller'ın yorumlarını okur
- Gerekli düzeltmeleri yapar
- Başvuruyu yeniden gönderir

### **3. CONTROLLER (ONAYLAYICI) TEST SENARYOLARI**

#### **3.1 Onay Süreci**

**TS-016: Controller Dashboard**
- Controller sisteme giriş yapar
- Bekleyen başvuru sayısını görür
- İstatistikleri inceler
- İş yükü dağılımını kontrol eder

**TS-017: Başvuru İnceleme**
- Controller bekleyen bir başvuruyu açar
- Akademisyenin girdiği verileri inceler
- Yüklenen belgeleri kontrol eder
- Verilerin doğruluğunu değerlendirir

**TS-018: Başvuru Onaylama**
- Controller başvuruyu uygun bulur
- "Onayla" butonuna tıklar
- Onay yorumu yazar
- Başvuru onaylanır

**TS-019: Başvuru Reddetme**
- Controller eksik/hatalı başvuruyu görür
- "Reddet" butonunu seçer
- Red gerekçesini detaylı yazar
- Akademisyene bildirim gönderilir

#### **3.2 Portföy Kontrolü**

**TS-020: Portföy Dokümanları İnceleme**
- Controller portföy sayfasını açar
- Akademisyenin yüklediği dokümanları listeler
- Her dokümanı tek tek inceler
- Eksik dokümanları tespit eder

**TS-021: Doküman Doğrulama**
- Controller bir dokümanı açar
- İçeriğini kontrol eder
- "Doğrulandı" olarak işaretler
- Doğrulama notunu ekler

### **4. HATA DURUMU TEST SENARYOLARI**

#### **4.1 Sistem Hataları**

**TS-022: İnternet Bağlantısı Kesilmesi**
- Kullanıcı form doldururken internet kesilir
- Sistem otomatik kaydetme yapar
- Bağlantı geri geldiğinde veriler korunur
- Kullanıcı kaldığı yerden devam eder

**TS-023: Büyük Dosya Yükleme Hatası**
- Akademisyen 50MB'lık dosya yüklemeye çalışır
- Sistem "Dosya çok büyük" hatası verir
- Maksimum boyut bilgisi gösterilir
- Kullanıcı daha küçük dosya yükler

**TS-024: Yetkisiz Erişim Denemesi**
- Akademisyen admin sayfasına girmeye çalışır
- Sistem "Yetkiniz yok" mesajı gösterir
- Ana sayfaya yönlendirilir
- Güvenlik logu oluşturulur

#### **4.2 Veri Doğrulama Hataları**

**TS-025: Zorunlu Alan Boş Bırakma**
- Kullanıcı zorunlu alanı boş bırakır
- "Bu alan zorunludur" uyarısı çıkar
- Form gönderilmez
- Kullanıcı alanı doldurur

**TS-026: Geçersiz Veri Girişi**
- Akademisyen sayı alanına harf girer
- Sistem "Geçersiz format" uyarısı verir
- Doğru format örneği gösterilir
- Kullanıcı düzeltme yapar

### **5. PERFORMANS TEST SENARYOLARI**

#### **5.1 Yoğun Kullanım**

**TS-027: Çok Kullanıcılı Test**
- 100 akademisyen aynı anda sisteme girer
- Herkes form doldurmaya başlar
- Sistem yavaşlamadan çalışır
- Tüm işlemler başarıyla tamamlanır

**TS-028: Büyük Veri Yükleme**
- Akademisyen 20 adet belge yükler
- Her biri 5MB boyutunda
- Sistem tüm dosyaları işler
- Yükleme süresi makul kalır

### **6. RAPORLAMA TEST SENARYOLARI**

#### **6.1 Rapor Oluşturma**

**TS-029: Akademisyen Performans Raporu**
- Admin rapor sayfasını açar
- Belirli bir akademisyeni seçer
- Son 3 yılın verilerini görür
- Raporu PDF olarak indirir

**TS-030: Departman Karşılaştırma Raporu**
- Admin departman raporunu oluşturur
- Tüm departmanları karşılaştırır
- Grafik ve tablolar görür
- Excel formatında export eder

---

## 📊 Test Sonuçları Değerlendirme

### ✅ **Başarılı Test Kriterleri**
- İşlem 3 saniye içinde tamamlanır
- Hata mesajı çıkmaz
- Veri doğru kaydedilir
- Kullanıcı bilgilendirilir

### ❌ **Başarısız Test Kriterleri**
- İşlem 10 saniyeden uzun sürer
- Sistem hatası oluşur
- Veri kaybolur
- Kullanıcı bilgilendirilmez

### 📈 **Performans Metrikleri**
- Sayfa yüklenme süresi: < 2 saniye
- Dosya yükleme hızı: > 1MB/saniye
- Eşzamanlı kullanıcı kapasitesi: 500 kişi
- Sistem uptime: %99.9

---

## 🔄 Test Döngüsü

1. **Hazırlık:** Test ortamını kurma
2. **Uygulama:** Senaryoları çalıştırma  
3. **Kayıt:** Sonuçları belgeleme
4. **Analiz:** Hataları inceleme
5. **Düzeltme:** Problemleri çözme
6. **Tekrar:** Testi yeniden çalıştırma

Bu test senaryoları, sistemin tüm özelliklerinin doğru çalıştığından emin olmak için tasarlanmıştır. Her senaryo, gerçek kullanıcıların yaşayabileceği durumları simüle eder.

---

## 📋 Ek Test Senaryoları

### **7. ENTEGRASYON TEST SENARYOLARI**

#### **7.1 Dış Sistem Bağlantıları**

**TS-031: OrganizationManagement API Bağlantısı**
- Sistem akademisyen bilgilerini dış API'den çeker
- Departman ve unvan bilgileri güncellenir
- API'den gelen veriler doğru işlenir
- Bağlantı hatası durumunda uyarı verilir

**TS-032: Dosya Depolama Sistemi**
- Yüklenen dosyalar güvenli sunucuda saklanır
- Dosya erişim linkleri doğru çalışır
- Silinen dosyalar sistemden tamamen kaldırılır
- Yedekleme işlemi otomatik çalışır

#### **7.2 Veritabanı Entegrasyonu**

**TS-033: PostgreSQL Bağlantısı**
- Ana veriler PostgreSQL'de saklanır
- Veri bütünlüğü korunur
- Backup işlemleri düzenli çalışır
- Performans optimizasyonu sağlanır

**TS-034: MongoDB Bağlantısı**
- Dinamik form verileri MongoDB'de saklanır
- Esnek veri yapısı desteklenir
- Arama işlemleri hızlı çalışır
- Veri senkronizasyonu sağlanır

### **8. GÜVENLİK TEST SENARYOLARI**

#### **8.1 Kimlik Doğrulama**

**TS-035: Güvenli Giriş**
- Kullanıcı doğru kimlik bilgileriyle giriş yapar
- Şifre güvenlik kurallarına uygun olmalı
- Başarısız giriş denemeleri sınırlanır
- Oturum süresi otomatik kontrol edilir

**TS-036: Yetkilendirme Kontrolü**
- Her kullanıcı sadece yetkili olduğu sayfaları görür
- Akademisyen admin işlemlerini yapamaz
- Controller sadece kendi alanındaki başvuruları görür
- Yetkisiz erişim denemeleri loglanır

#### **8.2 Veri Güvenliği**

**TS-037: Kişisel Veri Koruması**
- Akademisyen verileri şifrelenerek saklanır
- Sadece yetkili kişiler verilere erişebilir
- Veri silme işlemleri geri alınamaz şekilde yapılır
- KVKK uyumluluğu sağlanır

**TS-038: Dosya Güvenliği**
- Yüklenen dosyalar virüs taramasından geçer
- Zararlı dosya türleri engellenir
- Dosya erişim logları tutulur
- Yetkisiz indirme engellenir

### **9. KULLANICI DENEYİMİ TEST SENARYOLARI**

#### **9.1 Arayüz Testleri**

**TS-039: Responsive Tasarım**
- Sistem masaüstü bilgisayarda düzgün görünür
- Tablet ekranında kullanılabilir
- Mobil telefonda erişilebilir
- Tüm butonlar dokunmatik ekranda çalışır

**TS-040: Erişilebilirlik**
- Görme engelli kullanıcılar ekran okuyucu kullanabilir
- Klavye ile navigasyon mümkündür
- Renk körlüğü olan kullanıcılar sistemi kullanabilir
- Font boyutu ayarlanabilir

#### **9.2 Kullanım Kolaylığı**

**TS-041: Sezgisel Navigasyon**
- Menüler anlaşılır şekilde düzenlenmiş
- Kullanıcı kaybolmadan sayfalar arası geçiş yapar
- Geri dönüş butonları her sayfada mevcut
- Breadcrumb navigasyonu çalışır

**TS-042: Yardım ve Rehberlik**
- Her sayfada yardım butonları bulunur
- Tooltip'ler açıklayıcı bilgi verir
- Hata mesajları anlaşılır dilde yazılmış
- Video rehberler erişilebilir

### **10. YEDEKLEME VE KURTARMA TEST SENARYOLARI**

#### **10.1 Veri Yedekleme**

**TS-043: Otomatik Yedekleme**
- Sistem her gece otomatik yedekleme yapar
- Yedekler güvenli sunucuda saklanır
- 30 günlük yedek geçmişi tutulur
- Yedekleme başarısızlığında uyarı verilir

**TS-044: Manuel Yedekleme**
- Admin istediği zaman yedekleme başlatabilir
- Belirli tarih aralığı yedeklenebilir
- Yedekleme durumu takip edilebilir
- Yedek dosyaları indirilebilir

#### **10.2 Sistem Kurtarma**

**TS-045: Veri Geri Yükleme**
- Silinen veriler yedekten geri yüklenebilir
- Belirli tarihten geri yükleme yapılabilir
- Kurtarma işlemi loglanır
- Veri bütünlüğü korunur

**TS-046: Felaket Kurtarma**
- Sistem çökmesi durumunda hızlı kurtarma
- Alternatif sunucuya geçiş yapılabilir
- Kullanıcılar minimum kesinti yaşar
- Tüm veriler korunur

### **11. PERFORMANS İYİLEŞTİRME TEST SENARYOLARI**

#### **11.1 Hız Optimizasyonu**

**TS-047: Sayfa Yükleme Hızı**
- Ana sayfa 2 saniyede yüklenir
- Form sayfaları 3 saniyede açılır
- Raporlar 5 saniyede oluşturulur
- Dosya indirme hızlı başlar

**TS-048: Veritabanı Performansı**
- Sorgular optimize edilmiş
- İndeksler doğru kullanılmış
- Büyük veri setlerinde yavaşlama yok
- Eşzamanlı erişimde problem yok

#### **11.2 Kaynak Kullanımı**

**TS-049: Bellek Yönetimi**
- Sistem belleği verimli kullanır
- Bellek sızıntısı (memory leak) yok
- Büyük dosyalar işlenirken sistem stabil
- Garbage collection düzgün çalışır

**TS-050: CPU Kullanımı**
- İşlemci kullanımı optimize
- Ağır işlemler arka planda çalışır
- Kullanıcı arayüzü donmaz
- Multi-threading doğru kullanılmış

---

## 🎯 Test Stratejisi ve Planlama

### **Test Aşamaları**

1. **Birim Testler (Unit Tests)**
   - Her fonksiyon ayrı ayrı test edilir
   - Kod kalitesi kontrol edilir
   - Hata yakalama mekanizmaları test edilir

2. **Entegrasyon Testleri**
   - Farklı modüller birlikte test edilir
   - API bağlantıları kontrol edilir
   - Veri akışı doğrulanır

3. **Sistem Testleri**
   - Tüm sistem bir bütün olarak test edilir
   - Gerçek kullanım senaryoları uygulanır
   - Performans ölçümleri yapılır

4. **Kabul Testleri**
   - Son kullanıcılarla birlikte test edilir
   - İş gereksinimleri karşılanıyor mu kontrol edilir
   - Kullanıcı memnuniyeti ölçülür

### **Test Ortamları**

- **Geliştirme Ortamı:** Geliştiricilerin test ettiği ortam
- **Test Ortamı:** QA ekibinin test ettiği ortam
- **Staging Ortamı:** Canlıya çıkmadan önceki son test ortamı
- **Canlı Ortam:** Gerçek kullanıcıların kullandığı ortam

### **Test Metrikleri**

- **Test Kapsamı:** %95 kod kapsamı hedeflenir
- **Hata Oranı:** 1000 satır kodda maksimum 1 hata
- **Performans:** Sayfa yükleme süresi < 3 saniye
- **Kullanılabilirlik:** Kullanıcı memnuniyet skoru > 4.5/5

Bu kapsamlı test senaryoları, AcademicPerformance sisteminin güvenilir, hızlı ve kullanıcı dostu olmasını sağlamak için tasarlanmıştır.
