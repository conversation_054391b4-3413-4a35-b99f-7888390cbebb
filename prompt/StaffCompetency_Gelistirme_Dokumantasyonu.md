# StaffCompetency Modülü Kapsamlı Geliştirme Dokümantasyonu

## 📋 Genel Bakış

Bu dokümantasyon, AcademicPerformance projesindeki StaffCompetency modülünün eksik implementasyonlarının tamamlanması için kapsamlı bir rehber sunmaktadır. <PERSON><PERSON><PERSON><PERSON>, akademik personelin yetkinlik değerlendirmelerini yönetmek, analiz etmek ve raporlamak için tasarlanmıştır.

## 🎯 Proje Durumu

### Tamamlanmış Bileşenler ✅

- Entity modelleri (StaffCompetencyEvaluationEntity, CompetencyRatingEntity, StaffCompetencyDefinitionEntity)
- DbContext konfigürasyonu
- Controller endpoint'leri (StaffCompetencyController)
- Manager ka<PERSON><PERSON> (StaffCompetencyManager)
- Interface tanımları (IStaffCompetencyStore, IStaffCompetencyManager)
- Temel CRUD operasyonları

### Eksik Implementasyonlar ❌

- **StaffCompetencyStore.cs**: 22 adet placeholder method
- İstatistiksel analiz fonksiyonları
- Trend analizi ve benchmark hesaplamaları
- Validation ve business logic
- Performance optimizasyonları

## 🔧 Teknik Gereksinimler

### 1. Placeholder Method'ların Listesi

StaffCompetencyStore.cs dosyasında aşağıdaki 22 method gerçek implementasyon gerektirir:

```csharp
// İstatistiksel Operasyonlar (5 method)
1. CalculateDepartmentCompetencyStatisticsAsync()
2. GetCompetencyTrendAnalysisAsync()
3. CompareStaffCompetenciesAsync()
4. CalculateCompetencyBenchmarkAsync()
5. CalculateAverageCompetencyScoresAsync()

// Query Operasyonları (4 method)
6. GetStaffCompetenciesByStaffIdAsync()
7. GetStaffCompetenciesByEvaluatorAsync()
8. GetStaffCompetenciesByStatusAsync()
9. GetTopPerformersAsync()

// Aggregation Operasyonları (2 method)
10. CalculateOverallCompetencyScoreAsync()
11. CalculateCompetencyGrowthRateAsync()

// Form ve Rapor Operasyonları (3 method)
12. GetEvaluationFormsForStaffAsync()
13. ApproveEvaluationFormAsync()
14. GenerateCompetencyReportAsync()

// Validation Operasyonları (3 method)
15. ValidateStaffCompetencyAsync()
16. CheckDuplicateEvaluationAsync()
17. ValidateCompetencyScoresAsync()

// Utility Operasyonları (5 method)
18. SynchronizeStaffCompetencyDataAsync()
19. ClearStaffCompetencyCacheAsync()
20. GetStaffCompetencyCountAsync()
21. BulkUpdateStatusAsync()
22. ArchiveOldEvaluationsAsync()
```

## 📊 Detaylı Implementasyon Rehberi

### 1. İstatistiksel Operasyonlar (8 saat)

#### 1.1 CalculateDepartmentCompetencyStatisticsAsync Implementation

```csharp
public async Task<List<CompetencyAreaSummaryDto>> CalculateDepartmentCompetencyStatisticsAsync(
    string departmentId, string period)
{
    try
    {
        _logger.LogInformation("Bölüm yetkinlik istatistikleri hesaplanıyor - Department: {DepartmentId}, Period: {Period}",
            departmentId, period);

        // Get all evaluations for the department in the specified period
        var evaluations = await _context.StaffCompetencyEvaluations
            .Include(e => e.CompetencyRatings)
            .ThenInclude(r => r.CompetencyDefinition)
            .Where(e => e.DepartmentId == departmentId
                     && e.EvaluationContextId == period
                     && !e.Disabled)
            .ToListAsync();

        if (!evaluations.Any())
        {
            return new List<CompetencyAreaSummaryDto>();
        }

        // Group by competency areas and calculate statistics
        var competencyStats = evaluations
            .SelectMany(e => e.CompetencyRatings)
            .Where(r => r.CompetencyDefinition != null)
            .GroupBy(r => r.CompetencySystemId)
            .Select(g => new CompetencyAreaSummaryDto
            {
                CompetencySystemId = g.Key,
                CompetencyName = g.First().CompetencyDefinition?.Name ?? "Unknown",
                TotalEvaluations = g.Count(),
                AverageScore = g.Average(r => ConvertRatingToNumeric(r.Rating)),
                MinScore = g.Min(r => ConvertRatingToNumeric(r.Rating)),
                MaxScore = g.Max(r => ConvertRatingToNumeric(r.Rating)),
                StandardDeviation = CalculateStandardDeviation(g.Select(r => ConvertRatingToNumeric(r.Rating))),
                DistributionByRating = g.GroupBy(r => r.Rating)
                    .ToDictionary(rg => rg.Key, rg => rg.Count())
            })
            .OrderByDescending(s => s.AverageScore)
            .ToList();

        _logger.LogInformation("Bölüm yetkinlik istatistikleri hesaplandı - {Count} yetkinlik alanı",
            competencyStats.Count);

        return competencyStats;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Bölüm yetkinlik istatistikleri hesaplanırken hata - Department: {DepartmentId}",
            departmentId);
        throw;
    }
}

// Helper method for standard deviation calculation
private double CalculateStandardDeviation(IEnumerable<double> values)
{
    var valuesList = values.ToList();
    if (valuesList.Count <= 1) return 0;

    var average = valuesList.Average();
    var sumOfSquaresOfDifferences = valuesList.Select(val => (val - average) * (val - average)).Sum();
    return Math.Sqrt(sumOfSquaresOfDifferences / valuesList.Count);
}
```

#### 1.2 GetCompetencyTrendAnalysisAsync Implementation

```csharp
public async Task<List<CompetencyTrendDataDto>> GetCompetencyTrendAnalysisAsync(
    string staffId, int periodCount)
{
    try
    {
        _logger.LogInformation("Personel yetkinlik trend analizi yapılıyor - Staff: {StaffId}, PeriodCount: {PeriodCount}",
            staffId, periodCount);

        // Get the last N periods of evaluations for the staff member
        var evaluations = await _context.StaffCompetencyEvaluations
            .Include(e => e.CompetencyRatings)
            .ThenInclude(r => r.CompetencyDefinition)
            .Where(e => e.AcademicianUniveristyUserId == staffId && !e.Disabled)
            .OrderByDescending(e => e.SubmittedAt)
            .Take(periodCount)
            .ToListAsync();

        if (!evaluations.Any())
        {
            return new List<CompetencyTrendDataDto>();
        }

        var trendData = new List<CompetencyTrendDataDto>();

        // Group by competency areas
        var competencyGroups = evaluations
            .SelectMany(e => e.CompetencyRatings.Select(r => new {
                Evaluation = e,
                Rating = r
            }))
            .GroupBy(x => x.Rating.CompetencySystemId);

        foreach (var competencyGroup in competencyGroups)
        {
            var competencyName = competencyGroup.First().Rating.CompetencyDefinition?.Name ?? "Unknown";

            var periodScores = competencyGroup
                .GroupBy(x => x.Evaluation.EvaluationContextId)
                .Select(pg => new CompetencyPeriodScoreDto
                {
                    Period = pg.Key,
                    Score = pg.Average(x => ConvertRatingToNumeric(x.Rating.Rating)),
                    EvaluationDate = pg.First().Evaluation.SubmittedAt
                })
                .OrderBy(ps => ps.EvaluationDate)
                .ToList();

            // Calculate trend direction
            var trendDirection = CalculateTrendDirection(periodScores.Select(ps => ps.Score));
            var growthRate = CalculateGrowthRate(periodScores.Select(ps => ps.Score));

            trendData.Add(new CompetencyTrendDataDto
            {
                CompetencySystemId = competencyGroup.Key,
                CompetencyName = competencyName,
                PeriodScores = periodScores,
                TrendDirection = trendDirection, // "Increasing", "Decreasing", "Stable"
                GrowthRate = growthRate,
                CurrentScore = periodScores.LastOrDefault()?.Score ?? 0,
                InitialScore = periodScores.FirstOrDefault()?.Score ?? 0
            });
        }

        _logger.LogInformation("Personel yetkinlik trend analizi tamamlandı - {Count} yetkinlik alanı",
            trendData.Count);

        return trendData.OrderByDescending(t => t.GrowthRate).ToList();
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Personel yetkinlik trend analizi hatası - Staff: {StaffId}", staffId);
        throw;
    }
}

// Helper methods for trend analysis
private string CalculateTrendDirection(IEnumerable<double> scores)
{
    var scoresList = scores.ToList();
    if (scoresList.Count < 2) return "Stable";

    var firstHalf = scoresList.Take(scoresList.Count / 2).Average();
    var secondHalf = scoresList.Skip(scoresList.Count / 2).Average();

    var difference = secondHalf - firstHalf;

    return difference switch
    {
        > 0.2 => "Increasing",
        < -0.2 => "Decreasing",
        _ => "Stable"
    };
}

private double CalculateGrowthRate(IEnumerable<double> scores)
{
    var scoresList = scores.ToList();
    if (scoresList.Count < 2) return 0;

    var initialScore = scoresList.First();
    var finalScore = scoresList.Last();

    return initialScore == 0 ? 0 : ((finalScore - initialScore) / initialScore) * 100;
}
```

### 2. Query Operasyonları (2 saat)

#### 2.1 GetStaffCompetenciesByStaffIdAsync Implementation

```csharp
public async Task<List<StaffCompetencyEvaluationEntity>> GetStaffCompetenciesByStaffIdAsync(
    string staffId, string? period = null, int? limit = null)
{
    try
    {
        _logger.LogInformation("Personel yetkinlik değerlendirmeleri getiriliyor - Staff: {StaffId}, Period: {Period}",
            staffId, period);

        var query = _context.StaffCompetencyEvaluations
            .Include(e => e.CompetencyRatings)
            .ThenInclude(r => r.CompetencyDefinition)
            .Where(e => e.AcademicianUniveristyUserId == staffId && !e.Disabled);

        // Apply period filter if specified
        if (!string.IsNullOrEmpty(period))
        {
            query = query.Where(e => e.EvaluationContextId == period);
        }

        // Order by submission date (most recent first)
        query = query.OrderByDescending(e => e.SubmittedAt);

        // Apply limit if specified
        if (limit.HasValue && limit.Value > 0)
        {
            query = query.Take(limit.Value);
        }

        var evaluations = await query.ToListAsync();

        _logger.LogInformation("Personel yetkinlik değerlendirmeleri getirildi - {Count} adet", evaluations.Count);

        return evaluations;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Personel yetkinlik değerlendirmeleri getirme hatası - Staff: {StaffId}", staffId);
        throw;
    }
}
```

## 🗄️ DbContext Konfigürasyonu

### Entity Konfigürasyonu Örnekleri

```csharp
// AcademicPerformanceDbContext.cs içinde ConfigureStaffCompetencyEntities method'u
private void ConfigureStaffCompetencyEntities(ModelBuilder builder)
{
    // StaffCompetencyDefinitionEntity configuration
    builder.Entity<StaffCompetencyDefinitionEntity>(e =>
    {
        e.HasKey(e2 => e2.CompetencySystemId);
        e.Property(e2 => e2.CompetencySystemId).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.Name).HasMaxLength(250).IsRequired();
        e.Property(e2 => e2.Description).HasMaxLength(1000);
        e.Property(e2 => e2.RatingScaleJson).HasColumnType("jsonb"); // PostgreSQL JSON support
        e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);

        // Indexes for performance
        e.HasIndex(e2 => e2.CompetencySystemId).IsUnique();
        e.HasIndex(e2 => e2.IsActive);
        e.HasIndex(e2 => e2.Name);
    });

    // StaffCompetencyEvaluationEntity configuration
    builder.Entity<StaffCompetencyEvaluationEntity>(e =>
    {
        e.HasKey(e2 => e2.AutoIncrementId);
        e.Property(e2 => e2.Id).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.AcademicianUniveristyUserId).HasMaxLength(256).IsRequired();
        e.Property(e2 => e2.EvaluatingManagerUserId).HasMaxLength(256).IsRequired();
        e.Property(e2 => e2.EvaluationContextId).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.OverallComments).HasMaxLength(4000);
        e.Property(e2 => e2.Status).HasMaxLength(50).HasDefaultValue("Draft");
        e.Property(e2 => e2.DepartmentId).HasMaxLength(100);

        // Performance indexes
        e.HasIndex(e2 => e2.Id).IsUnique();
        e.HasIndex(e2 => e2.AcademicianUniveristyUserId);
        e.HasIndex(e2 => e2.EvaluatingManagerUserId);
        e.HasIndex(e2 => e2.EvaluationContextId);
        e.HasIndex(e2 => e2.Status);
        e.HasIndex(e2 => e2.DepartmentId);
        e.HasIndex(e2 => e2.SubmittedAt);
        e.HasIndex(e2 => new { e2.AcademicianUniveristyUserId, e2.EvaluationContextId, e2.EvaluatingManagerUserId })
            .HasDatabaseName("IX_StaffCompetency_Unique_Evaluation");
    });
}
```

## ⏱️ Tahmini Geliştirme Süreleri

| Kategori                       | Method Sayısı | Tahmini Süre | Detay                                |
| ------------------------------ | ------------- | ------------ | ------------------------------------ |
| **İstatistiksel Operasyonlar** | 5             | 8 saat       | Karmaşık hesaplamalar ve aggregation |
| **Query Operasyonları**        | 4             | 2 saat       | Basit CRUD ve filtreleme             |
| **Validation Operasyonları**   | 3             | 1 saat       | Business logic validation            |
| **Utility Operasyonları**      | 5             | 1 saat       | Basit utility method'lar             |
| **DbContext Konfigürasyonu**   | -             | 1 saat       | Index ve relationship tanımları      |
| **Test Yazımı**                | -             | 2 saat       | Unit ve integration testler          |
| **Dokümantasyon**              | -             | 1 saat       | Code comments ve API docs            |
| **TOPLAM**                     | **22**        | **16 saat**  | **2 günlük geliştirme süresi**       |

### 2.2 GetStaffCompetenciesByStatusAsync Implementation

```csharp
public async Task<List<StaffCompetencyEvaluationEntity>> GetStaffCompetenciesByStatusAsync(
    string status, string? departmentId = null)
{
    try
    {
        _logger.LogInformation("Durum bazında yetkinlik değerlendirmeleri getiriliyor - Status: {Status}, Department: {DepartmentId}",
            status, departmentId);

        var query = _context.StaffCompetencyEvaluations
            .Include(e => e.CompetencyRatings)
            .ThenInclude(r => r.CompetencyDefinition)
            .Where(e => e.Status == status && !e.Disabled);

        // Apply department filter if specified
        if (!string.IsNullOrEmpty(departmentId))
        {
            query = query.Where(e => e.DepartmentId == departmentId);
        }

        var evaluations = await query
            .OrderByDescending(e => e.SubmittedAt)
            .ToListAsync();

        _logger.LogInformation("Durum bazında yetkinlik değerlendirmeleri getirildi - {Count} adet", evaluations.Count);

        return evaluations;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Durum bazında yetkinlik değerlendirmeleri getirme hatası - Status: {Status}", status);
        throw;
    }
}
```

### 3. Validation Operasyonları (1 saat)

#### 3.1 CheckDuplicateEvaluationAsync Implementation

```csharp
public async Task<bool> CheckDuplicateEvaluationAsync(
    string staffId, string period, string evaluatorId, string? excludeId = null)
{
    try
    {
        _logger.LogInformation("Duplicate değerlendirme kontrolü - Staff: {StaffId}, Period: {Period}, Evaluator: {EvaluatorId}",
            staffId, period, evaluatorId);

        var query = _context.StaffCompetencyEvaluations
            .Where(e => e.AcademicianUniveristyUserId == staffId
                     && e.EvaluationContextId == period
                     && e.EvaluatingManagerUserId == evaluatorId
                     && !e.Disabled);

        // Exclude specific ID if provided (for update scenarios)
        if (!string.IsNullOrEmpty(excludeId))
        {
            query = query.Where(e => e.Id != excludeId);
        }

        var exists = await query.AnyAsync();

        _logger.LogInformation("Duplicate değerlendirme kontrolü tamamlandı - Duplicate: {Exists}", exists);

        return exists;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Duplicate değerlendirme kontrolü hatası - Staff: {StaffId}", staffId);
        throw;
    }
}
```

#### 3.2 ValidateCompetencyScoresAsync Implementation

```csharp
public async Task<bool> ValidateCompetencyScoresAsync(StaffCompetencyEvaluationEntity entity)
{
    try
    {
        _logger.LogInformation("Yetkinlik skorları doğrulanıyor - Evaluation: {EvaluationId}", entity.Id);

        if (entity.CompetencyRatings == null || !entity.CompetencyRatings.Any())
        {
            _logger.LogWarning("Yetkinlik değerlendirmesinde rating bulunamadı - Evaluation: {EvaluationId}", entity.Id);
            return false;
        }

        // Get all active competency definitions
        var activeCompetencies = await _context.StaffCompetencyDefinitions
            .Where(d => d.IsActive)
            .ToListAsync();

        // Validate each rating
        foreach (var rating in entity.CompetencyRatings)
        {
            // Check if competency exists and is active
            var competencyDef = activeCompetencies.FirstOrDefault(c => c.CompetencySystemId == rating.CompetencySystemId);
            if (competencyDef == null)
            {
                _logger.LogWarning("Geçersiz yetkinlik ID'si - CompetencyId: {CompetencyId}", rating.CompetencySystemId);
                return false;
            }

            // Validate rating value
            if (!IsValidRating(rating.Rating))
            {
                _logger.LogWarning("Geçersiz rating değeri - Rating: {Rating}", rating.Rating);
                return false;
            }

            // Validate rating scale if defined in competency definition
            if (!string.IsNullOrEmpty(competencyDef.RatingScaleJson))
            {
                if (!IsRatingInScale(rating.Rating, competencyDef.RatingScaleJson))
                {
                    _logger.LogWarning("Rating ölçek dışında - Rating: {Rating}, CompetencyId: {CompetencyId}",
                        rating.Rating, rating.CompetencySystemId);
                    return false;
                }
            }
        }

        _logger.LogInformation("Yetkinlik skorları doğrulandı - Evaluation: {EvaluationId}", entity.Id);
        return true;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Yetkinlik skorları doğrulama hatası - Evaluation: {EvaluationId}", entity.Id);
        return false;
    }
}

// Helper methods for validation
private bool IsValidRating(string rating)
{
    var validRatings = new[] { "poor", "below_average", "average", "good", "excellent", "1", "2", "3", "4", "5" };
    return validRatings.Contains(rating?.ToLower());
}

private bool IsRatingInScale(string rating, string ratingScaleJson)
{
    try
    {
        // Parse JSON rating scale and validate
        var ratingScale = JsonSerializer.Deserialize<Dictionary<string, object>>(ratingScaleJson);
        return ratingScale?.ContainsKey(rating.ToLower()) == true;
    }
    catch
    {
        // If JSON parsing fails, use default validation
        return IsValidRating(rating);
    }
}
```

### 4. Utility Operasyonları (1 saat)

#### 4.1 BulkUpdateStatusAsync Implementation

```csharp
public async Task<int> BulkUpdateStatusAsync(List<string> ids, string newStatus, string updatedByUserId)
{
    try
    {
        _logger.LogInformation("Toplu durum güncelleme başlatılıyor - {Count} kayıt, Yeni durum: {Status}",
            ids.Count, newStatus);

        var evaluations = await _context.StaffCompetencyEvaluations
            .Where(e => ids.Contains(e.Id) && !e.Disabled)
            .ToListAsync();

        if (!evaluations.Any())
        {
            _logger.LogWarning("Güncellenecek kayıt bulunamadı");
            return 0;
        }

        var updatedCount = 0;
        var updateTime = DateTime.UtcNow;

        foreach (var evaluation in evaluations)
        {
            evaluation.Status = newStatus;
            evaluation.UpdatedAt = updateTime;
            evaluation.UpdatedByUserId = updatedByUserId;
            updatedCount++;
        }

        await _context.SaveChangesAsync();

        _logger.LogInformation("Toplu durum güncelleme tamamlandı - {UpdatedCount} kayıt güncellendi", updatedCount);

        return updatedCount;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Toplu durum güncelleme hatası");
        throw;
    }
}
```

## 🔗 Proje Entegrasyonu

### 1. Email Notification Sistemi Entegrasyonu

```csharp
// StaffCompetencyManager.cs içinde notification entegrasyonu
public async Task<StaffCompetencyDto> CreateStaffCompetencyAsync(
    StaffCompetencyCreateDto dto, string createdByUserId)
{
    try
    {
        // ... existing implementation ...

        var createdEntity = await _staffCompetencyStore.CreateStaffCompetencyAsync(entity);

        // Send notification to staff member about new evaluation
        _ = Task.Run(async () =>
        {
            try
            {
                await _notificationService.SendStaffCompetencyEvaluationNotificationAsync(
                    createdEntity.Id,
                    createdEntity.AcademicianUniveristyUserId,
                    createdEntity.EvaluatingManagerUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yetkinlik değerlendirme notification gönderim hatası");
            }
        });

        return MapEntityToDto(createdEntity);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Personel yetkinlik değerlendirmesi oluşturma hatası");
        throw;
    }
}
```

### 2. Reporting Modülü Entegrasyonu

```csharp
// ReportingManager.cs içinde StaffCompetency raporları
public async Task<StaffCompetencyReportDto> GenerateStaffCompetencyReportAsync(
    string reportType, string scopeId, string period)
{
    try
    {
        var report = new StaffCompetencyReportDto
        {
            ReportType = reportType,
            ScopeId = scopeId,
            Period = period,
            GeneratedAt = DateTime.UtcNow
        };

        switch (reportType.ToLower())
        {
            case "individual":
                report.IndividualData = await _staffCompetencyStore.GetStaffCompetenciesByStaffIdAsync(scopeId, period);
                report.TrendAnalysis = await _staffCompetencyStore.GetCompetencyTrendAnalysisAsync(scopeId, 12);
                break;

            case "department":
                report.DepartmentStatistics = await _staffCompetencyStore.CalculateDepartmentCompetencyStatisticsAsync(scopeId, period);
                report.TopPerformers = await _staffCompetencyStore.GetTopPerformersAsync(scopeId, period, null, 10);
                break;
        }

        return report;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "StaffCompetency raporu oluşturma hatası");
        throw;
    }
}
```

### 3. Authorization Sistemi Uyumluluğu

```csharp
// StaffCompetencyController.cs içinde authorization örnekleri
[HttpGet]
[Authorize(APConsts.Policies.ViewData)]
public async Task<IActionResult> GetStaffCompetencies([FromQuery] PagedListCo<StaffCompetencyFilterDto> co)
{
    // Implementation
}

[HttpPost]
[Authorize(APConsts.Policies.ManageStaffEvaluations)]
public async Task<IActionResult> CreateStaffCompetency([FromBody] StaffCompetencyCreateDto dto)
{
    // Implementation
}

[HttpGet]
[Authorize(APConsts.Policies.ViewReports)]
public async Task<IActionResult> GetCompetencyStatistics(string departmentId, string period)
{
    // Implementation
}
```

## 📈 Performance Considerations

### 1. Database Indexleme Stratejisi

```sql
-- Kritik performans indexleri
CREATE INDEX CONCURRENTLY IX_StaffCompetencyEvaluation_StaffId_Period
ON "StaffCompetencyEvaluations" ("AcademicianUniveristyUserId", "EvaluationContextId");

CREATE INDEX CONCURRENTLY IX_StaffCompetencyEvaluation_Department_Period
ON "StaffCompetencyEvaluations" ("DepartmentId", "EvaluationContextId");

CREATE INDEX CONCURRENTLY IX_CompetencyRating_CompetencyId_Rating
ON "CompetencyRatings" ("CompetencySystemId", "Rating");

CREATE INDEX CONCURRENTLY IX_StaffCompetencyEvaluation_SubmittedAt
ON "StaffCompetencyEvaluations" ("SubmittedAt" DESC);
```

### 2. Caching Stratejisi

```csharp
// StaffCompetencyStore.cs içinde caching implementasyonu
private readonly IMemoryCache _cache;
private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(30);

public async Task<List<CompetencyAreaSummaryDto>> CalculateDepartmentCompetencyStatisticsAsync(
    string departmentId, string period)
{
    var cacheKey = $"dept_competency_stats_{departmentId}_{period}";

    if (_cache.TryGetValue(cacheKey, out List<CompetencyAreaSummaryDto> cachedStats))
    {
        return cachedStats;
    }

    var stats = await CalculateDepartmentCompetencyStatisticsInternalAsync(departmentId, period);

    _cache.Set(cacheKey, stats, _cacheExpiration);

    return stats;
}
```

## 🧪 Test Senaryoları

### 1. Unit Test Örnekleri

```csharp
[Test]
public async Task CalculateDepartmentCompetencyStatisticsAsync_ValidInput_ReturnsStatistics()
{
    // Arrange
    var departmentId = "DEPT001";
    var period = "2024-2025";

    // Act
    var result = await _staffCompetencyStore.CalculateDepartmentCompetencyStatisticsAsync(departmentId, period);

    // Assert
    Assert.IsNotNull(result);
    Assert.IsTrue(result.Any());
    Assert.IsTrue(result.All(s => s.AverageScore >= 0 && s.AverageScore <= 5));
}

[Test]
public async Task CheckDuplicateEvaluationAsync_DuplicateExists_ReturnsTrue()
{
    // Arrange
    var staffId = "STAFF001";
    var period = "2024-2025";
    var evaluatorId = "EVAL001";

    // Act
    var result = await _staffCompetencyStore.CheckDuplicateEvaluationAsync(staffId, period, evaluatorId);

    // Assert
    Assert.IsTrue(result);
}
```

### 2. Integration Test Senaryoları

```csharp
[Test]
public async Task StaffCompetencyWorkflow_EndToEnd_Success()
{
    // 1. Create competency evaluation
    var createDto = new StaffCompetencyCreateDto { /* test data */ };
    var created = await _manager.CreateStaffCompetencyAsync(createDto, "user123");

    // 2. Verify statistics calculation
    var stats = await _store.CalculateDepartmentCompetencyStatisticsAsync("DEPT001", "2024-2025");
    Assert.IsTrue(stats.Any());

    // 3. Verify trend analysis
    var trends = await _store.GetCompetencyTrendAnalysisAsync("STAFF001", 12);
    Assert.IsNotNull(trends);

    // 4. Verify notification sent
    // Mock verification for notification service
}
```

## ⏱️ Tahmini Geliştirme Süreleri

| Kategori                       | Method Sayısı | Tahmini Süre | Detay                                |
| ------------------------------ | ------------- | ------------ | ------------------------------------ |
| **İstatistiksel Operasyonlar** | 5             | 8 saat       | Karmaşık hesaplamalar ve aggregation |
| **Query Operasyonları**        | 4             | 2 saat       | Basit CRUD ve filtreleme             |
| **Validation Operasyonları**   | 3             | 1 saat       | Business logic validation            |
| **Utility Operasyonları**      | 5             | 1 saat       | Basit utility method'lar             |
| **DbContext Konfigürasyonu**   | -             | 1 saat       | Index ve relationship tanımları      |
| **Test Yazımı**                | -             | 2 saat       | Unit ve integration testler          |
| **Dokümantasyon**              | -             | 1 saat       | Code comments ve API docs            |
| **TOPLAM**                     | **22**        | **16 saat**  | **2 günlük geliştirme süresi**       |

## ✅ Acceptance Criteria

### Fonksiyonel Kriterler:

- [ ] Tüm 22 placeholder method gerçek implementasyona sahip
- [ ] İstatistiksel hesaplamalar doğru sonuç veriyor
- [ ] Trend analizi çalışıyor ve anlamlı veriler üretiyor
- [ ] Validation kuralları business logic'e uygun
- [ ] Performance optimizasyonları uygulanmış

### Teknik Kriterler:

- [ ] Database indexleri tanımlanmış
- [ ] Caching stratejisi implementasyonu
- [ ] Error handling ve logging standartlara uygun
- [ ] Email notification entegrasyonu çalışıyor
- [ ] Authorization policies doğru uygulanmış

### Test Kriterleri:

- [ ] Unit testler %80+ code coverage
- [ ] Integration testler end-to-end senaryoları kapsıyor
- [ ] Performance testleri geçiyor
- [ ] API endpoint'leri Postman collection'da test edilmiş

Bu dokümantasyon, StaffCompetency modülünün eksik implementasyonlarının tamamlanması için gereken tüm teknik detayları, kod örneklerini ve rehberleri içermektedir. Geliştirme süreci boyunca bu dokümantasyon referans alınarak, modülün production-ready hale getirilmesi hedeflenmektedir.
