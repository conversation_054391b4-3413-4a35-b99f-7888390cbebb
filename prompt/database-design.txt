# APDYS Database Design

This document outlines the comprehensive database design for the Academic Performance Evaluation System (APDYS). The design follows the established patterns from the OrganizationManagement project and integrates seamlessly with the university's existing infrastructure while leveraging the Rlx.Shared package patterns.

## 1. Database Architecture Overview

APDYS employs a hybrid database architecture utilizing both PostgreSQL for relational data and MongoDB for dynamic, schema-flexible data:

- **PostgreSQL**: Stores structured relational data including forms, submissions metadata, RBAC, and specialized workflow data
- **MongoDB**: Handles dynamic criteria definitions and submitted performance data with varying field structures
- **Integration**: Both databases integrate with the OrganizationManagement system for user and organizational data

### 1.1 Database Integration Points

**OrganizationManagement Integration:**
- User data sourced from `Person`, `PersonUser`, and `PersonPosition` entities
- Department information from `Unit` entities with hierarchical structure
- Academic cadre information from `RlxEnumValue` entities
- Follows the same dual ID pattern (GUID + AutoIncrement) for consistency

**Rlx.Shared Integration:**
- Inherits from `EntityBaseModel`, `EntityBaseLiteModel`, and `EntityBaseIdModel` base classes
- Utilizes `RlxEnum` and `RlxEnumValue` for standardized lookup data
- Implements entity change logging via `IEntityChangeLogHelper`
- Follows established naming conventions and patterns

### 1.2 Entity Base Class Hierarchy

All APDYS entities follow the Rlx.Shared base class hierarchy:

```csharp
// Base hierarchy from Rlx.Shared
public class EntityBaseIdLiteModel
{
    public string Id { get; set; } = Guid.NewGuid().ToString(); // Public GUID identifier
}

public class EntityBaseIdModel : EntityBaseIdLiteModel
{
    public int AutoIncrementId { get; set; } // Internal primary key
}

public class EntityBaseLiteModel : EntityBaseIdModel
{
    public bool Disabled { get; set; } // Soft disable flag
}

public class EntityBaseModel : EntityBaseLiteModel
{
    public bool Deleted { get; set; } // Soft delete flag
}
```

**APDYS Entity Categories:**
- **Full Entities**: Inherit from `EntityBaseModel` (most entities with full lifecycle)
- **Lookup Entities**: Inherit from `EntityBaseLiteModel` (reference data)
- **System Entities**: Use custom keys for system-defined entities (Static Criteria, etc.)

## 2. PostgreSQL Schema Design

### 2.1 Entity Framework DbContext Configuration

The APDYS DbContext follows the OrganizationManagement pattern with Rlx.Shared integration:

```csharp
public class ApdysCoreDbContext : DbContext
{
    private readonly IEntityChangeLogHelper _entityChangeLogHelper;
    private readonly IConfiguration _configuration;

    public ApdysCoreDbContext(DbContextOptions<ApdysCoreDbContext> options,
        IEntityChangeLogHelper entityChangeLogHelper,
        IConfiguration configuration) : base(options)
    {
        _entityChangeLogHelper = entityChangeLogHelper;
        _configuration = configuration;
        ChangeTracker.LazyLoadingEnabled = false;
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        ConfigureRbacEntities(builder);
        ConfigureFormEntities(builder);
        ConfigureSubmissionEntities(builder);
        ConfigureSpecializedWorkflowEntities(builder);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        if (_configuration["EntityLog:Enabled"] == "1")
        {
            await _entityChangeLogHelper.AddEntityChangeLogAsync(dbContext: this);
        }
        return await base.SaveChangesAsync(cancellationToken);
    }
}
```

### 2.2 RBAC Entity Configurations

**ApdysRoleEntity Configuration:**
```csharp
private void ConfigureRbacEntities(ModelBuilder builder)
{
    builder.Entity<ApdysRoleEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.RoleName).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.Description).HasMaxLength(500);

        // Indexes
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.RoleName).IsUnique();

        // Relationships
        e.HasMany(e2 => e2.RolePermissions)
            .WithOne(rp => rp.ApdysRole)
            .HasForeignKey(rp => rp.ApdysRoleAutoIncrementId)
            .HasPrincipalKey(e2 => e2.AutoIncrementId);
    });

    builder.Entity<ApdysPermissionEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.PermissionName).HasMaxLength(150).IsRequired();
        e.Property(e2 => e2.Description).HasMaxLength(500);

        // Indexes
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.PermissionName).IsUnique();
    });

    builder.Entity<ApdysRolePermissionEntity>(e =>
    {
        e.HasKey(e2 => new { e2.ApdysRoleAutoIncrementId, e2.ApdysPermissionAutoIncrementId });

        e.HasOne(e2 => e2.ApdysRole)
            .WithMany(r => r.RolePermissions)
            .HasForeignKey(e2 => e2.ApdysRoleAutoIncrementId)
            .HasPrincipalKey(r => r.AutoIncrementId);

        e.HasOne(e2 => e2.ApdysPermission)
            .WithMany(p => p.RolePermissions)
            .HasForeignKey(e2 => e2.ApdysPermissionAutoIncrementId)
            .HasPrincipalKey(p => p.AutoIncrementId);
    });

    builder.Entity<UserApdysRoleMappingEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.UniversityUserId).HasMaxLength(256).IsRequired();

        // Indexes
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.UniversityUserId);
        e.HasIndex(e2 => new { e2.UniversityUserId, e2.ApdysRoleAutoIncrementId }).IsUnique();

        e.HasOne(e2 => e2.ApdysRole)
            .WithMany(r => r.UserMappings)
            .HasForeignKey(e2 => e2.ApdysRoleAutoIncrementId)
            .HasPrincipalKey(r => r.AutoIncrementId);
    });
}
```

### 2.3 Form Management Entity Configurations

**Complete Form Entity Configuration:**
```csharp
private void ConfigureFormEntities(ModelBuilder builder)
{
    // EvaluationFormEntity Configuration
    builder.Entity<EvaluationFormEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.Name).HasMaxLength(250).IsRequired();
        e.Property(e2 => e2.ApplicableAcademicCadresJson).HasColumnType("jsonb");
        e.Property(e2 => e2.Status).HasMaxLength(50).IsRequired();
        e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
        e.Property(e2 => e2.UpdatedByUserId).HasMaxLength(256);

        // Indexes for performance
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.Status);
        e.HasIndex(e2 => new { e2.EvaluationPeriodStartDate, e2.EvaluationPeriodEndDate });
        e.HasIndex(e2 => e2.CreatedByUserId);
        e.HasIndex(e2 => e2.Disabled);
        e.HasIndex(e2 => e2.Deleted);

        // Check constraints
        e.HasCheckConstraint("CK_EvaluationForm_Status",
            "Status IN ('Draft', 'Active', 'Archived')");
        e.HasCheckConstraint("CK_EvaluationForm_Period",
            "EvaluationPeriodEndDate > EvaluationPeriodStartDate");
    });

    // FormCategoryEntity Configuration
    builder.Entity<FormCategoryEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.Name).HasMaxLength(150).IsRequired();
        e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
        e.Property(e2 => e2.UpdatedByUserId).HasMaxLength(256);

        // Indexes
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.EvaluationFormAutoIncrementId);
        e.HasIndex(e2 => new { e2.EvaluationFormAutoIncrementId, e2.DisplayOrder });

        // Relationships
        e.HasOne(e2 => e2.EvaluationForm)
            .WithMany(f => f.Categories)
            .HasForeignKey(e2 => e2.EvaluationFormAutoIncrementId)
            .HasPrincipalKey(f => f.AutoIncrementId)
            .OnDelete(DeleteBehavior.Cascade);

        // Check constraints
        e.HasCheckConstraint("CK_FormCategory_Weight", "Weight >= 0 AND Weight <= 1");
        e.HasCheckConstraint("CK_FormCategory_DisplayOrder", "DisplayOrder >= 0");
    });

    // FormCriterionLinkEntity Configuration
    builder.Entity<FormCriterionLinkEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.CriterionType).HasMaxLength(50).IsRequired();
        e.Property(e2 => e2.DynamicCriterionTemplateId).HasMaxLength(100);
        e.Property(e2 => e2.StaticCriterionSystemId).HasMaxLength(100);
        e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);

        // Indexes
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.FormCategoryAutoIncrementId);
        e.HasIndex(e2 => e2.DynamicCriterionTemplateId);
        e.HasIndex(e2 => e2.StaticCriterionSystemId);
        e.HasIndex(e2 => new { e2.FormCategoryAutoIncrementId, e2.DisplayOrder });

        // Relationships
        e.HasOne(e2 => e2.FormCategory)
            .WithMany(c => c.CriterionLinks)
            .HasForeignKey(e2 => e2.FormCategoryAutoIncrementId)
            .HasPrincipalKey(c => c.AutoIncrementId)
            .OnDelete(DeleteBehavior.Cascade);

        e.HasOne(e2 => e2.StaticCriterionDefinition)
            .WithMany()
            .HasForeignKey(e2 => e2.StaticCriterionSystemId)
            .HasPrincipalKey(s => s.StaticCriterionSystemId)
            .OnDelete(DeleteBehavior.Restrict);

        // Check constraints
        e.HasCheckConstraint("CK_FormCriterionLink_Type",
            "CriterionType IN ('Dynamic', 'Static')");
        e.HasCheckConstraint("CK_FormCriterionLink_Reference",
            "(CriterionType = 'Dynamic' AND DynamicCriterionTemplateId IS NOT NULL AND StaticCriterionSystemId IS NULL) OR " +
            "(CriterionType = 'Static' AND StaticCriterionSystemId IS NOT NULL AND DynamicCriterionTemplateId IS NULL)");
    });

    // StaticCriterionDefinitionEntity Configuration
    builder.Entity<StaticCriterionDefinitionEntity>(e =>
    {
        e.HasKey(e2 => e2.StaticCriterionSystemId);
        e.Property(e2 => e2.StaticCriterionSystemId).HasMaxLength(100);
        e.Property(e2 => e2.Name).HasMaxLength(200).IsRequired();
        e.Property(e2 => e2.Description).HasMaxLength(1000);
        e.Property(e2 => e2.DataSourceHint).HasMaxLength(250);

        // Indexes
        e.HasIndex(e2 => e2.IsActive);
        e.HasIndex(e2 => e2.Name);
    });
}
```

### 2.4 Submission Management Entity Configurations

**Complete Submission Entity Configuration:**
```csharp
private void ConfigureSubmissionEntities(ModelBuilder builder)
{
    // AcademicSubmissionEntity Configuration
    builder.Entity<AcademicSubmissionEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.AcademicianUniveristyUserId).HasMaxLength(256).IsRequired();
        e.Property(e2 => e2.Status).HasMaxLength(50).IsRequired();
        e.Property(e2 => e2.ApprovedByControllerUserId).HasMaxLength(256);
        e.Property(e2 => e2.ApprovalComments).HasMaxLength(2000);
        e.Property(e2 => e2.RejectedByControllerUserId).HasMaxLength(256);
        e.Property(e2 => e2.RejectionComments).HasMaxLength(2000);

        // Indexes for performance
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.AcademicianUniveristyUserId);
        e.HasIndex(e2 => e2.EvaluationFormAutoIncrementId);
        e.HasIndex(e2 => e2.Status);
        e.HasIndex(e2 => new { e2.AcademicianUniveristyUserId, e2.EvaluationFormAutoIncrementId }).IsUnique();
        e.HasIndex(e2 => e2.SubmittedAt);
        e.HasIndex(e2 => e2.ApprovedByControllerUserId);
        e.HasIndex(e2 => e2.RejectedByControllerUserId);

        // Relationships
        e.HasOne(e2 => e2.EvaluationForm)
            .WithMany()
            .HasForeignKey(e2 => e2.EvaluationFormAutoIncrementId)
            .HasPrincipalKey(f => f.AutoIncrementId)
            .OnDelete(DeleteBehavior.Restrict);

        // Check constraints
        e.HasCheckConstraint("CK_AcademicSubmission_Status",
            "Status IN ('Draft', 'Submitted', 'Approved', 'Rejected')");
        e.HasCheckConstraint("CK_AcademicSubmission_Approval",
            "(Status = 'Approved' AND ApprovedAt IS NOT NULL AND ApprovedByControllerUserId IS NOT NULL) OR " +
            "(Status != 'Approved')");
        e.HasCheckConstraint("CK_AcademicSubmission_Rejection",
            "(Status = 'Rejected' AND RejectedAt IS NOT NULL AND RejectedByControllerUserId IS NOT NULL) OR " +
            "(Status != 'Rejected')");
    });

    // EvidenceFileEntity Configuration
    builder.Entity<EvidenceFileEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.SubmittedDynamicDataInstanceId).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.FileName).HasMaxLength(255).IsRequired();
        e.Property(e2 => e2.ContentType).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.StoredFilePath).IsRequired();
        e.Property(e2 => e2.UploadedByUniveristyUserId).HasMaxLength(256).IsRequired();

        // Indexes
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.AcademicSubmissionAutoIncrementId);
        e.HasIndex(e2 => e2.SubmittedDynamicDataInstanceId);
        e.HasIndex(e2 => e2.UploadedByUniveristyUserId);
        e.HasIndex(e2 => e2.UploadedAt);

        // Relationships
        e.HasOne(e2 => e2.AcademicSubmission)
            .WithMany(s => s.EvidenceFiles)
            .HasForeignKey(e2 => e2.AcademicSubmissionAutoIncrementId)
            .HasPrincipalKey(s => s.AutoIncrementId)
            .OnDelete(DeleteBehavior.Cascade);

        // Check constraints
        e.HasCheckConstraint("CK_EvidenceFile_Size", "SizeBytes > 0");
    });
}
```

## 3. MongoDB Collection Design

### 3.1 Dynamic Criteria Collection

**DynamicCriterionTemplateDoc**
```javascript
{
  "_id": ObjectId,
  "name": "string",
  "description": "string",
  "coefficient": "number?",
  "maxLimit": "number?",
  "status": "string", // "Draft", "Active", "Inactive"
  "inputFields": [
    {
      "fieldId": "string",
      "label": "string",
      "inputType": "string", // "Text", "Number", "Date", "File", "Dropdown"
      "isMandatory": "boolean",
      "placeholderText": "string?",
      "minValue": "number?",
      "maxValue": "number?",
      "allowedFileTypes": "string?",
      "maxFileSizeMb": "number?",
      "dropdownOptions": ["string"]
    }
  ],
  "createdAt": "ISODate",
  "createdByUserId": "string",
  "updatedAt": "ISODate",
  "updatedByUserId": "string"
}
```

### 3.2 Submitted Data Collection

**SubmittedDynamicDataDoc**
```javascript
{
  "_id": ObjectId,
  "academicSubmissionId": "string", // GUID from PostgreSQL
  "formCriterionLinkId": "string", // GUID from PostgreSQL
  "academicianUniveristyUserId": "string",
  "data": {
    // Dynamic key-value pairs based on InputFieldDefinition.FieldId
    "fieldId1": "value1",
    "fieldId2": "value2"
  },
  "createdAt": "ISODate",
  "updatedAt": "ISODate"
}
```

### 2.5 Specialized Workflow Entity Configurations

**Complete Specialized Workflow Configuration:**
```csharp
private void ConfigureSpecializedWorkflowEntities(ModelBuilder builder)
{
    // Department Strategic Performance Entities
    builder.Entity<DepartmentStrategicIndicatorDefinitionEntity>(e =>
    {
        e.HasKey(e2 => e2.IndicatorSystemId);
        e.Property(e2 => e2.IndicatorSystemId).HasMaxLength(100);
        e.Property(e2 => e2.Name).HasMaxLength(250).IsRequired();
        e.Property(e2 => e2.Description).HasMaxLength(1000);
        e.Property(e2 => e2.DataType).HasMaxLength(50).IsRequired();
        e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);

        // Indexes
        e.HasIndex(e2 => e2.IsActive);
        e.HasIndex(e2 => e2.DataType);
        e.HasIndex(e2 => e2.Name);

        // Check constraints
        e.HasCheckConstraint("CK_StrategicIndicator_DataType",
            "DataType IN ('Percentage', 'Number', 'Currency', 'Narrative')");
    });

    builder.Entity<DepartmentStrategicPerformanceDataEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.DepartmentId).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.AssessmentPeriodId).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.IndicatorSystemId).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.Notes).HasMaxLength(2000);
        e.Property(e2 => e2.SubmittedByUserId).HasMaxLength(256).IsRequired();

        // Indexes
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.DepartmentId);
        e.HasIndex(e2 => e2.AssessmentPeriodId);
        e.HasIndex(e2 => new { e2.DepartmentId, e2.AssessmentPeriodId, e2.IndicatorSystemId }).IsUnique();
        e.HasIndex(e2 => e2.SubmittedByUserId);

        // Relationships
        e.HasOne(e2 => e2.IndicatorDefinition)
            .WithMany()
            .HasForeignKey(e2 => e2.IndicatorSystemId)
            .HasPrincipalKey(i => i.IndicatorSystemId)
            .OnDelete(DeleteBehavior.Restrict);
    });

    // Staff Competency Entities
    builder.Entity<StaffCompetencyDefinitionEntity>(e =>
    {
        e.HasKey(e2 => e2.CompetencySystemId);
        e.Property(e2 => e2.CompetencySystemId).HasMaxLength(100);
        e.Property(e2 => e2.Name).HasMaxLength(250).IsRequired();
        e.Property(e2 => e2.Description).HasMaxLength(1000);
        e.Property(e2 => e2.RatingScaleJson).HasColumnType("jsonb");
        e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);

        // Indexes
        e.HasIndex(e2 => e2.IsActive);
        e.HasIndex(e2 => e2.Name);
    });

    builder.Entity<StaffCompetencyEvaluationEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.AcademicianUniveristyUserId).HasMaxLength(256).IsRequired();
        e.Property(e2 => e2.EvaluatingManagerUserId).HasMaxLength(256).IsRequired();
        e.Property(e2 => e2.EvaluationContextId).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.OverallComments).HasMaxLength(4000);

        // Indexes
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.AcademicianUniveristyUserId);
        e.HasIndex(e2 => e2.EvaluatingManagerUserId);
        e.HasIndex(e2 => e2.EvaluationContextId);
        e.HasIndex(e2 => new { e2.AcademicianUniveristyUserId, e2.EvaluationContextId, e2.EvaluatingManagerUserId });
    });

    builder.Entity<CompetencyRatingEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.CompetencySystemId).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.Rating).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.Comments).HasMaxLength(1000);

        // Indexes
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.StaffCompetencyEvaluationAutoIncrementId);
        e.HasIndex(e2 => e2.CompetencySystemId);

        // Relationships
        e.HasOne(e2 => e2.Evaluation)
            .WithMany(ev => ev.CompetencyRatings)
            .HasForeignKey(e2 => e2.StaffCompetencyEvaluationAutoIncrementId)
            .HasPrincipalKey(ev => ev.AutoIncrementId)
            .OnDelete(DeleteBehavior.Cascade);

        e.HasOne(e2 => e2.CompetencyDefinition)
            .WithMany()
            .HasForeignKey(e2 => e2.CompetencySystemId)
            .HasPrincipalKey(cd => cd.CompetencySystemId)
            .OnDelete(DeleteBehavior.Restrict);
    });

    // Portfolio Control Entities
    builder.Entity<PortfolioChecklistItemDefinitionEntity>(e =>
    {
        e.HasKey(e2 => e2.ItemSystemId);
        e.Property(e2 => e2.ItemSystemId).HasMaxLength(100);
        e.Property(e2 => e2.Name).HasMaxLength(250).IsRequired();
        e.Property(e2 => e2.Description).HasMaxLength(1000);
        e.Property(e2 => e2.ApplicableCadreHint).HasMaxLength(100);
        e.Property(e2 => e2.ExpectedLocationInEbysHint).HasMaxLength(250);
        e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);

        // Indexes
        e.HasIndex(e2 => e2.IsActive);
        e.HasIndex(e2 => e2.Name);
        e.HasIndex(e2 => e2.ApplicableCadreHint);
    });

    builder.Entity<PortfolioVerificationLogEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.AcademicianUniveristyUserId).HasMaxLength(256).IsRequired();
        e.Property(e2 => e2.ItemSystemId).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.VerificationStatus).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.ArchivistComments).HasMaxLength(2000);
        e.Property(e2 => e2.LastVerifiedByArchivistUserId).HasMaxLength(256).IsRequired();

        // Indexes
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.AcademicianUniveristyUserId);
        e.HasIndex(e2 => e2.ItemSystemId);
        e.HasIndex(e2 => new { e2.AcademicianUniveristyUserId, e2.ItemSystemId }).IsUnique();
        e.HasIndex(e2 => e2.VerificationStatus);
        e.HasIndex(e2 => e2.LastVerifiedByArchivistUserId);

        // Relationships
        e.HasOne(e2 => e2.ChecklistItemDefinition)
            .WithMany()
            .HasForeignKey(e2 => e2.ItemSystemId)
            .HasPrincipalKey(ci => ci.ItemSystemId)
            .OnDelete(DeleteBehavior.Restrict);

        // Check constraints
        e.HasCheckConstraint("CK_PortfolioVerification_Status",
            "VerificationStatus IN ('VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
    });

    // Generic Data Entry Entities
    builder.Entity<GenericDataEntryDefinitionEntity>(e =>
    {
        e.HasKey(e2 => e2.EntryTypeSystemId);
        e.Property(e2 => e2.EntryTypeSystemId).HasMaxLength(100);
        e.Property(e2 => e2.Name).HasMaxLength(250).IsRequired();
        e.Property(e2 => e2.Description).HasMaxLength(1000);
        e.Property(e2 => e2.ApplicableRoleHint).HasMaxLength(100);
        e.Property(e2 => e2.DataFieldLabel).HasMaxLength(150).IsRequired();
        e.Property(e2 => e2.DataFieldType).HasMaxLength(50).IsRequired();
        e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);

        // Indexes
        e.HasIndex(e2 => e2.IsActive);
        e.HasIndex(e2 => e2.ApplicableRoleHint);
        e.HasIndex(e2 => e2.DataFieldType);

        // Check constraints
        e.HasCheckConstraint("CK_GenericDataEntry_FieldType",
            "DataFieldType IN ('Number', 'Text', 'Boolean', 'Currency')");
    });

    builder.Entity<GenericDataEntryRecordEntity>(e =>
    {
        e.HasKey(e2 => e2.Id);
        e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
        e.Property(e2 => e2.AcademicianUniveristyUserId).HasMaxLength(256).IsRequired();
        e.Property(e2 => e2.EntryTypeSystemId).HasMaxLength(100).IsRequired();
        e.Property(e2 => e2.Value).IsRequired();
        e.Property(e2 => e2.AssessmentPeriodId).HasMaxLength(100);
        e.Property(e2 => e2.Notes).HasMaxLength(2000);
        e.Property(e2 => e2.SubmittedByUserId).HasMaxLength(256).IsRequired();

        // Indexes
        e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
        e.HasIndex(e2 => e2.AcademicianUniveristyUserId);
        e.HasIndex(e2 => e2.EntryTypeSystemId);
        e.HasIndex(e2 => e2.AssessmentPeriodId);
        e.HasIndex(e2 => new { e2.AcademicianUniveristyUserId, e2.EntryTypeSystemId, e2.AssessmentPeriodId });
        e.HasIndex(e2 => e2.SubmittedByUserId);

        // Relationships
        e.HasOne(e2 => e2.EntryDefinition)
            .WithMany()
            .HasForeignKey(e2 => e2.EntryTypeSystemId)
            .HasPrincipalKey(ed => ed.EntryTypeSystemId)
            .OnDelete(DeleteBehavior.Restrict);
    });
}
```

## 4. Enhanced MongoDB Collection Design

### 4.1 MongoDB Index Strategy

**Dynamic Criteria Collection Indexes:**
```javascript
// Create indexes for DynamicCriterionTemplates collection
db.DynamicCriterionTemplates.createIndex({ "status": 1 });
db.DynamicCriterionTemplates.createIndex({ "name": "text", "description": "text" });
db.DynamicCriterionTemplates.createIndex({ "createdByUserId": 1 });
db.DynamicCriterionTemplates.createIndex({ "createdAt": -1 });
db.DynamicCriterionTemplates.createIndex({ "status": 1, "createdAt": -1 });

// Create indexes for SubmittedPerformanceData collection
db.SubmittedPerformanceData.createIndex({ "academicSubmissionId": 1, "formCriterionLinkId": 1 });
db.SubmittedPerformanceData.createIndex({ "academicianUniveristyUserId": 1 });
db.SubmittedPerformanceData.createIndex({ "academicSubmissionId": 1 });
db.SubmittedPerformanceData.createIndex({ "formCriterionLinkId": 1 });
db.SubmittedPerformanceData.createIndex({ "createdAt": -1 });
db.SubmittedPerformanceData.createIndex({ "updatedAt": -1 });
```

### 4.2 MongoDB Schema Validation

**Dynamic Criteria Template Validation:**
```javascript
db.createCollection("DynamicCriterionTemplates", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["name", "status", "inputFields", "createdAt", "createdByUserId"],
      properties: {
        name: { bsonType: "string", maxLength: 200 },
        description: { bsonType: "string", maxLength: 1000 },
        coefficient: { bsonType: ["double", "null"], minimum: 0 },
        maxLimit: { bsonType: ["int", "null"], minimum: 0 },
        status: { enum: ["Draft", "Active", "Inactive"] },
        inputFields: {
          bsonType: "array",
          items: {
            bsonType: "object",
            required: ["fieldId", "label", "inputType", "isMandatory"],
            properties: {
              fieldId: { bsonType: "string", maxLength: 100 },
              label: { bsonType: "string", maxLength: 100 },
              inputType: { enum: ["Text", "Number", "Date", "File", "Dropdown"] },
              isMandatory: { bsonType: "bool" },
              placeholderText: { bsonType: ["string", "null"] },
              minValue: { bsonType: ["double", "null"] },
              maxValue: { bsonType: ["double", "null"] },
              allowedFileTypes: { bsonType: ["string", "null"] },
              maxFileSizeMb: { bsonType: ["int", "null"] },
              dropdownOptions: { bsonType: "array", items: { bsonType: "string" } }
            }
          }
        },
        createdAt: { bsonType: "date" },
        createdByUserId: { bsonType: "string", maxLength: 256 },
        updatedAt: { bsonType: "date" },
        updatedByUserId: { bsonType: "string", maxLength: 256 }
      }
    }
  }
});
```

**Submitted Data Validation:**
```javascript
db.createCollection("SubmittedPerformanceData", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["academicSubmissionId", "formCriterionLinkId", "academicianUniveristyUserId", "data", "createdAt"],
      properties: {
        academicSubmissionId: { bsonType: "string", maxLength: 100 },
        formCriterionLinkId: { bsonType: "string", maxLength: 100 },
        academicianUniveristyUserId: { bsonType: "string", maxLength: 256 },
        data: { bsonType: "object" },
        createdAt: { bsonType: "date" },
        updatedAt: { bsonType: "date" }
      }
    }
  }
});
```

## 5. Comprehensive Indexing Strategy

### 5.1 PostgreSQL Performance Indexes

**Critical Query Pattern Indexes:**
```sql
-- User-centric queries
CREATE INDEX CONCURRENTLY idx_user_submissions ON AcademicSubmissionEntity (AcademicianUniveristyUserId, Status, SubmittedAt DESC);
CREATE INDEX CONCURRENTLY idx_user_roles ON UserApdysRoleMappingEntity (UniversityUserId, AssignedAt DESC);

-- Form and category queries
CREATE INDEX CONCURRENTLY idx_form_categories ON FormCategoryEntity (EvaluationFormAutoIncrementId, DisplayOrder);
CREATE INDEX CONCURRENTLY idx_category_criteria ON FormCriterionLinkEntity (FormCategoryAutoIncrementId, DisplayOrder);

-- Controller workflow queries
CREATE INDEX CONCURRENTLY idx_pending_submissions ON AcademicSubmissionEntity (Status, SubmittedAt DESC) WHERE Status = 'Submitted';
CREATE INDEX CONCURRENTLY idx_controller_approvals ON AcademicSubmissionEntity (ApprovedByControllerUserId, ApprovedAt DESC) WHERE ApprovedByControllerUserId IS NOT NULL;

-- Reporting and analytics queries
CREATE INDEX CONCURRENTLY idx_submission_period ON AcademicSubmissionEntity (EvaluationFormAutoIncrementId, SubmittedAt);
CREATE INDEX CONCURRENTLY idx_evidence_files ON EvidenceFileEntity (AcademicSubmissionAutoIncrementId, UploadedAt);

-- Specialized workflow indexes
CREATE INDEX CONCURRENTLY idx_strategic_performance ON DepartmentStrategicPerformanceDataEntity (DepartmentId, AssessmentPeriodId, SubmittedAt DESC);
CREATE INDEX CONCURRENTLY idx_competency_evaluations ON StaffCompetencyEvaluationEntity (AcademicianUniveristyUserId, EvaluationContextId, SubmittedAt DESC);
CREATE INDEX CONCURRENTLY idx_portfolio_verification ON PortfolioVerificationLogEntity (AcademicianUniveristyUserId, VerificationStatus, LastVerifiedAt DESC);
CREATE INDEX CONCURRENTLY idx_generic_data_entries ON GenericDataEntryRecordEntity (AcademicianUniveristyUserId, EntryTypeSystemId, AssessmentPeriodId);

-- Audit and monitoring indexes
CREATE INDEX CONCURRENTLY idx_created_by_date ON EvaluationFormEntity (CreatedByUserId, CreatedAt DESC);
CREATE INDEX CONCURRENTLY idx_updated_entities ON EvaluationFormEntity (UpdatedAt DESC) WHERE UpdatedAt > CreatedAt;
```

### 5.2 Materialized Views for Reporting

**User Dashboard View:**
```sql
CREATE MATERIALIZED VIEW mv_user_dashboard AS
SELECT
    s.AcademicianUniveristyUserId,
    f.Id as FormId,
    f.Name as FormName,
    f.EvaluationPeriodStartDate,
    f.EvaluationPeriodEndDate,
    s.Status as SubmissionStatus,
    s.Id as SubmissionId,
    s.SubmittedAt,
    s.UpdatedAt as LastModified,
    COUNT(ef.Id) as EvidenceFileCount
FROM AcademicSubmissionEntity s
INNER JOIN EvaluationFormEntity f ON s.EvaluationFormAutoIncrementId = f.AutoIncrementId
LEFT JOIN EvidenceFileEntity ef ON s.AutoIncrementId = ef.AcademicSubmissionAutoIncrementId
WHERE f.Deleted = false AND s.Deleted = false
GROUP BY s.AcademicianUniveristyUserId, f.Id, f.Name, f.EvaluationPeriodStartDate,
         f.EvaluationPeriodEndDate, s.Status, s.Id, s.SubmittedAt, s.UpdatedAt;

CREATE UNIQUE INDEX idx_mv_user_dashboard ON mv_user_dashboard (AcademicianUniveristyUserId, FormId);
CREATE INDEX idx_mv_dashboard_status ON mv_user_dashboard (SubmissionStatus, LastModified DESC);
```

**Controller Workload View:**
```sql
CREATE MATERIALIZED VIEW mv_controller_workload AS
SELECT
    f.Id as FormId,
    f.Name as FormName,
    COUNT(CASE WHEN s.Status = 'Submitted' THEN 1 END) as PendingCount,
    COUNT(CASE WHEN s.Status = 'Approved' THEN 1 END) as ApprovedCount,
    COUNT(CASE WHEN s.Status = 'Rejected' THEN 1 END) as RejectedCount,
    MIN(CASE WHEN s.Status = 'Submitted' THEN s.SubmittedAt END) as OldestPending,
    MAX(s.UpdatedAt) as LastActivity
FROM EvaluationFormEntity f
LEFT JOIN AcademicSubmissionEntity s ON f.AutoIncrementId = s.EvaluationFormAutoIncrementId
WHERE f.Status = 'Active' AND f.Deleted = false
GROUP BY f.Id, f.Name;

CREATE UNIQUE INDEX idx_mv_controller_workload ON mv_controller_workload (FormId);
CREATE INDEX idx_mv_workload_pending ON mv_controller_workload (PendingCount DESC, OldestPending);
```

## 6. Integration with OrganizationManagement

### 6.1 Shared Data Sources

- **User Information**: Retrieved from Person, PersonUser, PersonDetail entities
- **Organizational Structure**: Department data from Unit hierarchy
- **Academic Cadres**: From RlxEnumValue entities with specific enum types
- **Positions**: Academic positions from PersonPosition relationships

### 6.2 Data Synchronization

- Real-time integration via shared database access
- No data duplication - APDYS references OrganizationManagement entities
- Consistent user identification via UniversityUserId across systems

### 6.3 Shared Infrastructure

- Common Rlx.Shared base classes and patterns
- Shared entity change logging infrastructure
- Common enum and localization systems
- Consistent audit and soft delete patterns

## 7. Performance and Scalability Considerations

### 7.1 Query Optimization

- Materialized views for complex reporting queries
- Proper indexing strategy for both databases
- Connection pooling and query optimization

### 7.2 Data Archival Strategy

- Soft deletes for audit trail preservation
- Periodic archival of old evaluation periods
- Retention policies for evidence files

### 7.3 Backup and Recovery

- Coordinated backup strategy for both PostgreSQL and MongoDB
- Point-in-time recovery capabilities
- Cross-database consistency verification

## 8. Data Migration and Seeding Strategy

### 8.1 Comprehensive Data Seeding Strategy

**Complete RBAC Seeding:**
```sql
-- Insert Core APDYS Roles with proper GUID generation
INSERT INTO ApdysRoleEntity (Id, RoleName, Description, CreatedAt, Disabled, Deleted) VALUES
(gen_random_uuid()::text, 'APDYS_Admin', 'Full system administration access', NOW(), false, false),
(gen_random_uuid()::text, 'APDYS_Academician', 'Academic staff member role', NOW(), false, false),
(gen_random_uuid()::text, 'APDYS_Controller', 'Department controller for approvals', NOW(), false, false),
(gen_random_uuid()::text, 'APDYS_StrategicMgmt', 'Strategic management office staff', NOW(), false, false),
(gen_random_uuid()::text, 'APDYS_Manager', 'Academic manager for competency evaluations', NOW(), false, false),
(gen_random_uuid()::text, 'APDYS_Archivist', 'Portfolio verification staff', NOW(), false, false),
(gen_random_uuid()::text, 'APDYS_DataEntry_Library', 'Library data entry staff', NOW(), false, false),
(gen_random_uuid()::text, 'APDYS_DataEntry_TTO', 'Technology transfer office data entry', NOW(), false, false);

-- Insert Comprehensive Permissions
INSERT INTO ApdysPermissionEntity (Id, PermissionName, Description, CreatedAt) VALUES
(gen_random_uuid()::text, 'CanManageCriteria', 'Create and manage evaluation criteria', NOW()),
(gen_random_uuid()::text, 'CanManageForms', 'Create and manage evaluation forms', NOW()),
(gen_random_uuid()::text, 'CanSubmitPerformanceData', 'Submit academic performance data', NOW()),
(gen_random_uuid()::text, 'CanApproveSubmissions', 'Approve or reject submissions', NOW()),
(gen_random_uuid()::text, 'CanManageUsers', 'Manage user roles and permissions', NOW()),
(gen_random_uuid()::text, 'CanViewReports', 'Access system reports and analytics', NOW()),
(gen_random_uuid()::text, 'CanManageStrategicData', 'Manage department strategic performance data', NOW()),
(gen_random_uuid()::text, 'CanEvaluateCompetencies', 'Evaluate staff competencies', NOW()),
(gen_random_uuid()::text, 'CanVerifyPortfolios', 'Verify portfolio items in EBYS', NOW()),
(gen_random_uuid()::text, 'CanEnterLibraryData', 'Enter library-related data', NOW()),
(gen_random_uuid()::text, 'CanEnterTTOData', 'Enter technology transfer office data', NOW());

-- Create Role-Permission Mappings
WITH role_permissions AS (
  SELECT
    r.AutoIncrementId as role_id,
    p.AutoIncrementId as permission_id,
    r.RoleName,
    p.PermissionName
  FROM ApdysRoleEntity r
  CROSS JOIN ApdysPermissionEntity p
  WHERE
    -- Admin gets all permissions
    (r.RoleName = 'APDYS_Admin') OR
    -- Academician permissions
    (r.RoleName = 'APDYS_Academician' AND p.PermissionName IN ('CanSubmitPerformanceData', 'CanViewReports')) OR
    -- Controller permissions
    (r.RoleName = 'APDYS_Controller' AND p.PermissionName IN ('CanApproveSubmissions', 'CanViewReports')) OR
    -- Strategic Management permissions
    (r.RoleName = 'APDYS_StrategicMgmt' AND p.PermissionName IN ('CanManageStrategicData', 'CanViewReports')) OR
    -- Manager permissions
    (r.RoleName = 'APDYS_Manager' AND p.PermissionName IN ('CanEvaluateCompetencies', 'CanViewReports')) OR
    -- Archivist permissions
    (r.RoleName = 'APDYS_Archivist' AND p.PermissionName IN ('CanVerifyPortfolios', 'CanViewReports')) OR
    -- Library data entry permissions
    (r.RoleName = 'APDYS_DataEntry_Library' AND p.PermissionName IN ('CanEnterLibraryData', 'CanViewReports')) OR
    -- TTO data entry permissions
    (r.RoleName = 'APDYS_DataEntry_TTO' AND p.PermissionName IN ('CanEnterTTOData', 'CanViewReports'))
)
INSERT INTO ApdysRolePermissionEntity (ApdysRoleAutoIncrementId, ApdysPermissionAutoIncrementId, AssignedAt)
SELECT role_id, permission_id, NOW()
FROM role_permissions;
```

**Static Criteria Comprehensive Seeding:**
```sql
INSERT INTO StaticCriterionDefinitionEntity (
  StaticCriterionSystemId, Name, Description, IsActive, DataSourceHint
) VALUES
('YEARS_OF_SERVICE', 'Years of Service', 'Total years of service at the university', true, 'OrganizationManagement.PersonPosition'),
('ACADEMIC_DEGREE', 'Highest Academic Degree', 'Highest academic degree held', true, 'OrganizationManagement.Person.Title'),
('CURRENT_POSITION', 'Current Academic Position', 'Current academic position title', true, 'OrganizationManagement.PersonPosition.Position'),
('DEPARTMENT_AFFILIATION', 'Department Affiliation', 'Current department assignment', true, 'OrganizationManagement.Unit'),
('EMPLOYMENT_START_DATE', 'Employment Start Date', 'Date of first employment at university', true, 'OrganizationManagement.PersonPosition'),
('ACADEMIC_RANK', 'Academic Rank', 'Current academic rank (Professor, Associate Professor, etc.)', true, 'OrganizationManagement.PersonPosition'),
('TEACHING_LOAD', 'Teaching Load', 'Current semester teaching load', true, 'OrganizationManagement.PersonPosition'),
('ADMINISTRATIVE_ROLES', 'Administrative Roles', 'Current administrative responsibilities', true, 'OrganizationManagement.PersonPosition');
```

**Specialized Workflow Seeding:**
```sql
-- Department Strategic Indicators
INSERT INTO DepartmentStrategicIndicatorDefinitionEntity (
  IndicatorSystemId, Name, Description, DataType, IsHigherBetter, IsActive, CreatedAt
) VALUES
('STUDENT_SATISFACTION_RATE', 'Student Satisfaction Rate', 'Average student satisfaction score', 'Percentage', true, true, NOW()),
('RESEARCH_GRANT_COUNT', 'Research Grant Count', 'Number of active research grants', 'Number', true, true, NOW()),
('PUBLICATION_COUNT', 'Publication Count', 'Number of publications per year', 'Number', true, true, NOW()),
('GRADUATION_RATE', 'Graduation Rate', 'Percentage of students graduating on time', 'Percentage', true, true, NOW()),
('FACULTY_RETENTION_RATE', 'Faculty Retention Rate', 'Percentage of faculty retained year-over-year', 'Percentage', true, true, NOW());

-- Staff Competency Definitions
INSERT INTO StaffCompetencyDefinitionEntity (
  CompetencySystemId, Name, Description, RatingScaleJson, IsActive, CreatedAt
) VALUES
('LEADERSHIP_SKILLS', 'Leadership Skills', 'Ability to lead teams and initiatives', '["Below Expectations", "Meets Expectations", "Exceeds Expectations", "Outstanding"]', true, NOW()),
('RESEARCH_METHODOLOGY', 'Research Methodology', 'Proficiency in research methods and practices', '["Below Expectations", "Meets Expectations", "Exceeds Expectations", "Outstanding"]', true, NOW()),
('TEACHING_EFFECTIVENESS', 'Teaching Effectiveness', 'Quality of teaching and student engagement', '["Below Expectations", "Meets Expectations", "Exceeds Expectations", "Outstanding"]', true, NOW()),
('COMMUNICATION_SKILLS', 'Communication Skills', 'Verbal and written communication abilities', '["Below Expectations", "Meets Expectations", "Exceeds Expectations", "Outstanding"]', true, NOW()),
('COLLABORATION', 'Collaboration', 'Ability to work effectively with others', '["Below Expectations", "Meets Expectations", "Exceeds Expectations", "Outstanding"]', true, NOW());

-- Portfolio Checklist Items
INSERT INTO PortfolioChecklistItemDefinitionEntity (
  ItemSystemId, Name, Description, ApplicableCadreHint, ExpectedLocationInEbysHint, IsActive, CreatedAt
) VALUES
('COURSE_SYLLABUS_CURRENT', 'Current Course Syllabi', 'All current semester course syllabi', 'All Teaching Faculty', 'Academic Records/Course Materials', true, NOW()),
('THESIS_SUPERVISION_RECORDS', 'Thesis Supervision Records', 'Records of thesis and dissertation supervision', 'Graduate Faculty', 'Academic Records/Graduate Studies', true, NOW()),
('RESEARCH_ETHICS_APPROVAL', 'Research Ethics Approvals', 'Current research ethics committee approvals', 'Research Faculty', 'Research Office/Ethics Committee', true, NOW()),
('PUBLICATION_RECORDS', 'Publication Records', 'Complete list of academic publications', 'All Faculty', 'Academic Records/Publications', true, NOW()),
('CONFERENCE_PRESENTATIONS', 'Conference Presentations', 'Records of conference presentations', 'All Faculty', 'Academic Records/Conferences', true, NOW());

-- Generic Data Entry Definitions
INSERT INTO GenericDataEntryDefinitionEntity (
  EntryTypeSystemId, Name, Description, ApplicableRoleHint, DataFieldLabel, DataFieldType, IsActive, CreatedAt
) VALUES
('LIBRARY_FINES_OUTSTANDING', 'Outstanding Library Fines', 'Outstanding library fines for faculty', 'APDYS_DataEntry_Library', 'Outstanding Fine Amount', 'Currency', true, NOW()),
('LIBRARY_RESOURCE_USAGE', 'Library Resource Usage', 'Annual library resource usage statistics', 'APDYS_DataEntry_Library', 'Resources Used Count', 'Number', true, NOW()),
('TTO_PATENT_APPLICATIONS', 'Patent Applications', 'Number of patent applications filed', 'APDYS_DataEntry_TTO', 'Patent Applications Filed', 'Number', true, NOW()),
('TTO_TECHNOLOGY_TRANSFERS', 'Technology Transfers', 'Number of successful technology transfers', 'APDYS_DataEntry_TTO', 'Technology Transfers Count', 'Number', true, NOW()),
('TTO_LICENSING_REVENUE', 'Licensing Revenue', 'Revenue generated from licensing agreements', 'APDYS_DataEntry_TTO', 'Licensing Revenue Amount', 'Currency', true, NOW());
```

### 8.2 Migration from Legacy Systems

**Data Import Strategy:**
1. **User Migration**: Map existing users to OrganizationManagement entities
2. **Historical Data**: Import previous evaluation data as read-only records
3. **File Migration**: Transfer existing evidence files to new storage structure
4. **Validation**: Comprehensive data validation and integrity checks

### 8.3 Enhanced Development Environment Setup

**Complete Database Initialization Script:**
```bash
#!/bin/bash
# APDYS Database Setup Script

set -e  # Exit on any error

echo "Setting up APDYS Development Environment..."

# PostgreSQL setup
echo "Creating PostgreSQL database..."
createdb apdys_dev -E UTF8 -l en_US.UTF-8

echo "Running database migrations..."
psql -d apdys_dev -f scripts/01_create_extensions.sql
psql -d apdys_dev -f scripts/02_create_schema.sql
psql -d apdys_dev -f scripts/03_create_indexes.sql
psql -d apdys_dev -f scripts/04_create_materialized_views.sql
psql -d apdys_dev -f scripts/05_seed_data.sql

echo "Setting up database permissions..."
psql -d apdys_dev -c "
  CREATE USER apdys_app WITH PASSWORD 'apdys_dev_password';
  GRANT CONNECT ON DATABASE apdys_dev TO apdys_app;
  GRANT USAGE ON SCHEMA public TO apdys_app;
  GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO apdys_app;
  GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO apdys_app;
  ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO apdys_app;
  ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO apdys_app;
"

# MongoDB setup
echo "Setting up MongoDB..."
mongosh --eval "use apdys_dev"
mongosh apdys_dev scripts/mongo_collections.js
mongosh apdys_dev scripts/mongo_indexes.js
mongosh apdys_dev scripts/mongo_validation.js

echo "Creating MongoDB user..."
mongosh apdys_dev --eval "
  db.createUser({
    user: 'apdys_app',
    pwd: 'apdys_dev_password',
    roles: [
      { role: 'readWrite', db: 'apdys_dev' }
    ]
  })
"

echo "APDYS Development Environment setup complete!"
echo "PostgreSQL: apdys_dev database created"
echo "MongoDB: apdys_dev database created"
echo "Application user: apdys_app (password: apdys_dev_password)"
```

**Individual Setup Scripts:**

**scripts/01_create_extensions.sql:**
```sql
-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
```

**scripts/mongo_collections.js:**
```javascript
// Create MongoDB collections with validation
db.createCollection("DynamicCriterionTemplates", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["name", "status", "inputFields", "createdAt", "createdByUserId"],
      properties: {
        name: { bsonType: "string", maxLength: 200 },
        status: { enum: ["Draft", "Active", "Inactive"] },
        inputFields: { bsonType: "array" },
        createdAt: { bsonType: "date" },
        createdByUserId: { bsonType: "string", maxLength: 256 }
      }
    }
  }
});

db.createCollection("SubmittedPerformanceData", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["academicSubmissionId", "formCriterionLinkId", "academicianUniveristyUserId", "data", "createdAt"],
      properties: {
        academicSubmissionId: { bsonType: "string", maxLength: 100 },
        formCriterionLinkId: { bsonType: "string", maxLength: 100 },
        academicianUniveristyUserId: { bsonType: "string", maxLength: 256 },
        data: { bsonType: "object" },
        createdAt: { bsonType: "date" }
      }
    }
  }
});
```

**Docker Compose for Development:**
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:16
    environment:
      POSTGRES_DB: apdys_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts:/docker-entrypoint-initdb.d

  mongodb:
    image: mongo:7
    environment:
      MONGO_INITDB_DATABASE: apdys_dev
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo:/docker-entrypoint-initdb.d

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
```

## 9. Security and Access Control

### 9.1 Database Security

**PostgreSQL Security:**
- Row-level security (RLS) for multi-tenant data isolation
- Encrypted connections (SSL/TLS)
- Principle of least privilege for database users
- Regular security audits and vulnerability assessments

**MongoDB Security:**
- Authentication and authorization enabled
- Field-level encryption for sensitive data
- Network encryption for data in transit
- Regular backup encryption

### 9.2 Data Privacy Compliance

**Personal Data Protection:**
- Anonymization strategies for reporting
- Data retention policies aligned with regulations
- Audit trails for data access and modifications
- Right to be forgotten implementation

### 9.3 Access Patterns

**Read-Heavy Operations:**
- Dashboard queries optimized with materialized views
- Caching layer for frequently accessed reference data
- Read replicas for reporting workloads

**Write Operations:**
- Transactional consistency across related entities
- Optimistic concurrency control for form editing
- Batch processing for bulk data operations

## 10. Monitoring and Maintenance

### 10.1 Database Monitoring

**Performance Metrics:**
- Query execution times and frequency
- Index usage and effectiveness
- Connection pool utilization
- Storage growth patterns

**Health Checks:**
- Automated database connectivity tests
- Data integrity validation routines
- Cross-database reference validation
- Backup verification procedures

### 10.2 Maintenance Procedures

**Regular Maintenance:**
- Index rebuilding and statistics updates
- Database cleanup and archival processes
- Performance tuning based on usage patterns
- Capacity planning and scaling decisions

**Emergency Procedures:**
- Disaster recovery protocols
- Data corruption recovery procedures
- Cross-database consistency restoration
- Rollback strategies for failed deployments

## 11. Future Extensibility

### 11.1 Schema Evolution

**Versioning Strategy:**
- Database migration scripts with version control
- Backward compatibility considerations
- Feature flags for gradual rollouts
- A/B testing infrastructure support

### 11.2 Integration Readiness

**API Integration:**
- Database views optimized for API consumption
- Standardized data formats for external systems
- Event sourcing capabilities for real-time updates
- Webhook support for external notifications

### 11.3 Scalability Planning

**Horizontal Scaling:**
- Sharding strategies for large datasets
- Read replica configurations
- Microservice data boundaries
- Event-driven architecture support

**Vertical Scaling:**
- Resource optimization guidelines
- Performance bottleneck identification
- Caching strategies at multiple levels
- Query optimization best practices

This comprehensive database design ensures seamless integration with the existing university infrastructure while providing the flexibility, security, and scalability needed for the dynamic nature of academic performance evaluation criteria and data.
