
# Academic Performance Evaluation System (APDYS) Architecture Document

## 1. Technical Summary

The Academic Performance Evaluation System (APDYS) is a backend microservices application designed to digitize and streamline the academic performance evaluation processes at Arel University. Built on .NET 8 and hosted on-premise, APDYS provides a flexible and auditable platform for defining evaluation criteria, managing form structures, enabling academicians to submit performance data, and facilitating a review workflow by controllers. The system also supports specialized data entry workflows for various university roles.

Key architectural features include token-based authentication via Arel University's Identity Server, integration with the Organization Management module for user data, Role-Based Access Control (RBAC), and the use of PostgreSQL for relational data and MongoDB for dynamic data structures. Asynchronous email notifications are handled via RabbitMQ, and logging leverages the university's standard `Rlx.Shared` library. The architecture prioritizes clear service boundaries, adherence to defined coding standards, and robust API design for future extensibility and potential frontend integration.

## 2. High-Level System Overview

APDYS employs a microservices architecture, primarily consisting of a **Core APDYS Service** and a **Notification Service**. Users (Administrators, Academicians, Controllers, etc.) interact with the system's functionalities through RESTful APIs exposed by the Core APDYS Service. External university systems like the Identity Server, Organization Management Module, and SMTP Server are integral to its operation.

```mermaid
graph TD
    subgraph "External University Systems"
        direction LR
        Arel_IdentityServer["Arel Uni Identity Server (OIDC)"]
        Arel_OrgMgmt["Arel Uni Org. Mgmt. Module"]
        Arel_SMTPServer["Arel Uni SMTP Server"]
        RlxLogInfra["Central Logging Infrastructure (via Rlx.Shared/RabbitMQ)"]
        EBYS["EBYS (Manual Interaction by Archivist)"]
    end

    subgraph "APDYS On-Premise Infrastructure"
        direction LR
        subgraph "APDYS Application Services (.NET 8)"
            direction TB
            CAS[Core APDYS Service]
            NS[Notification Service]
        end

        subgraph "APDYS Data Stores"
            direction TB
            PostgreSQL_DB[("PostgreSQL Database (Relational Data, Queues)")]
            MongoDB_DB[("MongoDB Database (Dynamic Data)")]
            FileShare[("Network File Share (Evidence Files)")]
        end

        RabbitMQ_Broker[("RabbitMQ Message Broker")]
    end

    UserWeb[External API Consumers / Future Frontend] --> CAS
    ArchivistUser["Archivist User"] --> CAS
    ArchivistUser -.-> EBYS

    CAS --> Arel_IdentityServer
    CAS --> Arel_OrgMgmt
    CAS --> PostgreSQL_DB
    CAS --> MongoDB_DB
    CAS --> FileShare
    CAS --> RabbitMQ_Broker
    CAS --> RlxLogInfra

    RabbitMQ_Broker --> NS
    NS --> PostgreSQL_DB % For potential internal state if needed, or just consumes from RMQ
    NS --> Arel_SMTPServer
    NS --> RlxLogInfra

    classDef service fill:#D6EAF8,stroke:#3498DB,stroke-width:2px;
    classDef database fill:#D5F5E3,stroke:#2ECC71,stroke-width:2px;
    classDef external fill:#FCF3CF,stroke:#F1C40F,stroke-width:2px;
    classDef broker fill:#E8DAEF,stroke:#8E44AD,stroke-width:2px;
    classDef filestore fill:#EBDEF0,stroke:#8E44AD,stroke-width:2px;
    classDef user fill:#FDEDEC,stroke:#E74C3C,stroke-width:2px;

    class CAS,NS service;
    class PostgreSQL_DB,MongoDB_DB database;
    class Arel_IdentityServer,Arel_OrgMgmt,Arel_SMTPServer,RlxLogInfra,EBYS external;
    class RabbitMQ_Broker broker;
    class FileShare filestore;
    class UserWeb,ArchivistUser user;
````

## 3\. Component View (Services)

APDYS is composed of two primary backend services:

```mermaid
graph TD
    subgraph "APDYS System"
        direction LR

        subgraph "Core APDYS Service"
            id1(API Endpoints via HTTP/S)
            id2(Authentication & Authorization - OIDC, RBAC)
            id3(User & Org. Data Integration)
            id4(Criteria & Form Management Module)
            id5(Academician Submission Module)
            id6(Controller Verification Module)
            id7(Specialized Workflows Module)
            id8(Evidence File Handling)
            id9(Audit Logging - Rlx.Shared)
            id10(Notification Publishing - RabbitMQ)
            id11(Data Access Layer - PostgreSQL & MongoDB Stores)
        end

        subgraph "Notification Service"
            id20(Message Consumption - RabbitMQ)
            id21(Email Formatting & Dispatch - SMTP)
            id22(Audit Logging - Rlx.Shared)
        end

        id1 --> id2;
        id1 --> id4;
        id1 --> id5;
        id1 --> id6;
        id1 --> id7;
        id4 --> id11;
        id5 --> id11;
        id6 --> id11;
        id7 --> id11;
        id5 --> id8;
        id1 --> id9; % All major operations trigger audit
        id5 --> id10; % Example: Submission triggers notification
        id6 --> id10; % Example: Approval/Rejection triggers notification

        subgraph "External Dependencies"
            Arel_IdentityServer["Arel Uni Identity Server"]
            Arel_OrgMgmt["Arel Uni Org. Mgmt. Module"]
            PostgreSQL_DB[("PostgreSQL")]
            MongoDB_DB[("MongoDB")]
            FileShare[("Network File Share")]
            RabbitMQ_Broker[("RabbitMQ")]
            Arel_SMTPServer["Arel Uni SMTP Server"]
        end

        id2 --> Arel_IdentityServer;
        id3 --> Arel_OrgMgmt;
        id11 --> PostgreSQL_DB;
        id11 --> MongoDB_DB;
        id8 --> FileShare;
        id10 --> RabbitMQ_Broker; % Core publishes to RabbitMQ

        RabbitMQ_Broker --> id20; % Notification consumes from RabbitMQ
        id21 --> Arel_SMTPServer;

    end

    classDef serviceComp fill:#D6EAF8,stroke:#3498DB,stroke-width:1px,color:#000;
    classDef externalDep fill:#D5F5E3,stroke:#2ECC71,stroke-width:1px,color:#000;
    class id1,id2,id3,id4,id5,id6,id7,id8,id9,id10,id11 serviceComp;
    class id20,id21,id22 serviceComp;
    class Arel_IdentityServer,Arel_OrgMgmt,PostgreSQL_DB,MongoDB_DB,FileShare,RabbitMQ_Broker,Arel_SMTPServer externalDep;
```

  * **Core APDYS Service:**
      * **Responsibilities:** Handles all primary business logic, API exposure, user authentication/authorization, integration with university identity and organization modules, criteria and form management, academician data submissions, controller verification workflows, specialized data entry, evidence file management, and audit logging. It publishes notification events to RabbitMQ.
      * **Key Internal Modules (Logical):** Authentication/Authorization, User Integration, Criteria Management, Form Management, Submission Workflow, Controller Workflow, Specialized Workflows, Data Stores (Repositories for PostgreSQL & MongoDB), File Handling.
  * **Notification Service:**
      * **Responsibilities:** Consumes notification requests from RabbitMQ, formats email messages using templates, and dispatches emails via the Arel University SMTP server. It also performs its own audit logging.

## 4\. Key Architectural Decisions & Patterns

  * **Microservices Architecture:** Two main services (Core APDYS, Notification) for separation of concerns and independent scalability/deployment if needed.
  * **.NET 8 Platform:** Leverages the performance, LTS, and modern features of .NET 8 for backend development.
  * **Database Choices:**
      * **PostgreSQL:** For structured relational data (user/role mappings, form definitions, submission metadata, specialized workflow relational data).
      * **MongoDB:** For dynamic data with flexible schemas (dynamic criteria definitions, submitted academician performance data with varying fields).
  * **Authentication & Authorization:** Token-based (JWT) via Arel University's Identity Server (OIDC/OAuth2). Internal RBAC for APDYS-specific permissions.
  * **Asynchronous Notifications:** RabbitMQ used as a message broker between the Core APDYS Service (publisher) and the Notification Service (consumer) to ensure reliable and decoupled email sending.
  * **`Rlx.Shared` Library Utilization:** Common concerns like logging, base classes, some DTOs/models, messaging helpers (RabbitMQ), and potentially caching helpers (Redis) are leveraged from this Arel University standard shared library to promote consistency and reduce boilerplate.
  * **Repository Pattern:** Implemented via a `Stores` layer in each service to abstract data access logic from business logic (Managers).
  * **Manager Pattern:** Business logic is encapsulated within `Manager` classes.
  * **API Design:** RESTful APIs with versioning, documented via Swagger/OpenAPI. HTTP methods primarily used are `GET`, `POST`, `PUT`.
  * **On-Premise Deployment:** All services and infrastructure (databases, message broker) are hosted on Arel University's on-premise servers. Containerization with Docker will be used.

## 5\. Infrastructure and Deployment Overview

  * **Hosting:** On-premise Arel University servers (Physical or Virtual Machines).
  * **Containerization:** Docker will be used for containerizing the .NET 8 services (`Apdys.Core.Api`, `Apdys.Notification.Api`).
  * **Core Infrastructure Services (On-Premise):**
      * PostgreSQL Server
      * MongoDB Server
      * RabbitMQ Server
      * Network File Share (for evidence uploads)
      * Arel University SMTP Server (existing)
      * Arel University Identity Server (existing)
      * Arel University Organization Management Module (existing)
      * Central Logging Infrastructure (target for `Rlx.Shared` logging via RabbitMQ)
  * **Deployment Strategy:** To be defined in conjunction with Arel University IT, likely involving a CI/CD pipeline targeting Dev, Staging, and Production environments. Independent deployment of services is possible.
  * **Infrastructure as Code (IaC):** (To be determined with Arel IT) If IaC is used for managing on-premise VM configurations or service deployments, tools like Ansible, Chef, Puppet, or on-premise Kubernetes with YAML manifests might be considered.

## 6\. Key Reference Documents

This document provides a high-level overview. For detailed information, please refer to the following architectural sub-documents which would typically reside in a `docs/` folder alongside this `architecture.md` file:

  * `prd.md` (Product Requirements Document)
  * Epic Files (e.g., `epic1_core_setup.md`, `epic2_admin_criteria_form_management.md`, etc.)
  * `tech-stack.md` (Technology Stack Specification)
  * `project-structure.md` (Project Structure)
  * `coding-standards.md` (Coding Standards)
  * `api-reference.md` (API Reference Documentation)
  * `data-models.md` (Data Models)
  * `environment-vars.md` (Environment Variables)
  * `testing-strategy.md` (Testing Strategy)

*(Note: The actual file names for PRD and Epics might differ based on your project's repository structure. The links above assume they are converted to Markdown and placed in a `docs/` directory.)*

## Change Log

| Change        | Date       | Version | Description                                      | Author         |
| ------------- | ---------- | ------- | ------------------------------------------------ | -------------- |
| Initial draft | 2025-05-16 | 0.1     | Initial draft based on incremental design steps. | 1-mimar AI     |
