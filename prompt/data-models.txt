# APDYS Data Models

This document outlines the primary data structures used within the Academic Performance Evaluation System (APDYS). It includes definitions for API Data Transfer Objects (DTOs), Criteria/Command Objects (COs), PostgreSQL relational entities, and MongoDB document schemas.

These models will reside within the `Models/` directory of the Core APDYS Service, structured as `Models/Dtos/`, `Models/Cos/`, `Models/Entities/`, and `Models/MongoDocuments/`. Common DTOs or base entities might be inherited or consumed from `Rlx.Shared` where applicable. The dual ID pattern (string GUID `Id` and `int AutoIncrementId`) will be used for PostgreSQL entities.

## 1. API Data Transfer Objects (DTOs)

DTOs are used for request and response payloads in the API. Validation attributes (`[Required]`, `[StringLength]`, etc.) will be applied to request DTOs.

### 1.1 User & Authentication DTOs

* **`UserProfileDto` (Response)** (Story 1.5)
    * Purpose: To return basic profile information for the authenticated user.
    * Schema:
        ```csharp
        public class UserProfileDto
        {
            public string UserId { get; set; } // From Identity Server token
            public string FullName { get; set; } // From Org. Management
            public string Email { get; set; } // From Org. Management
            public List<string> ApdysRoles { get; set; } // APDYS-specific role names
            public string AcademicCadre { get; set; } // From Org. Management
            public string Department { get; set; } // From Org. Management
        }
        ```

### 1.2 Criteria Management DTOs (Admin - Epic 2)

* **`DynamicCriterionTemplateRequestDto` (Request - POST/PUT)** (Story 2.1)
    * Purpose: For creating or updating a dynamic criterion template.
    * Schema:
        ```csharp
        public class DynamicCriterionTemplateRequestDto
        {
            [Required]
            [StringLength(200)]
            public string Name { get; set; }

            [StringLength(1000)]
            public string Description { get; set; }

            [Range(0, double.MaxValue)]
            public double? Coefficient { get; set; } // Nullable if not always applicable

            [Range(0, int.MaxValue)]
            public int? MaxLimit { get; set; } // Max number of entries or points

            public List<InputFieldDefinitionDto> InputFields { get; set; } = new();
        }
        ```
* **`DynamicCriterionTemplateResponseDto` (Response)** (Story 2.1)
    * Purpose: To return details of a dynamic criterion template.
    * Schema:
        ```csharp
        public class DynamicCriterionTemplateResponseDto
        {
            public string Id { get; set; } // MongoDB ObjectId as string
            public string Name { get; set; }
            public string Description { get; set; }
            public double? Coefficient { get; set; }
            public int? MaxLimit { get; set; }
            public string Status { get; set; } // e.g., "Draft", "Active", "Inactive"
            public List<InputFieldDefinitionDto> InputFields { get; set; } = new();
            public DateTime CreatedAt { get; set; }
            public DateTime UpdatedAt { get; set; }
        }
        ```
* **`InputFieldDefinitionDto` (Shared within Criteria DTOs)** (Story 2.2)
    * Purpose: Defines an input field for a dynamic criterion.
    * Schema:
        ```csharp
        public class InputFieldDefinitionDto
        {
            [Required]
            public string FieldId { get; set; } // Unique ID within the criterion, e.g., "title", "publishDate"

            [Required]
            [StringLength(100)]
            public string Label { get; set; }

            [Required]
            public string InputType { get; set; } // e.g., "Text", "Number", "Date", "File", "Dropdown"

            public bool IsMandatory { get; set; }
            public string PlaceholderText { get; set; }

            // For Number type
            public double? MinValue { get; set; }
            public double? MaxValue { get; set; }

            // For Date type (future: date range constraints)

            // For File type
            public string AllowedFileTypes { get; set; } // e.g., "pdf,docx,jpg"
            public int? MaxFileSizeMb { get; set; }

            // For Dropdown type
            public List<string> DropdownOptions { get; set; } = new();
        }
        ```
* **`StaticCriterionResponseDto` (Response)** (Story 2.3)
    * Purpose: To return details of a predefined static criterion that can be activated/deactivated.
    * Schema:
        ```csharp
        public class StaticCriterionResponseDto
        {
            public string StaticCriterionSystemId { get; set; } // Predefined system unique key (e.g., "YEARS_OF_SERVICE")
            public string Name { get; set; }
            public string Description { get; set; }
            public bool IsActive { get; set; }
            public string DataSourceHint { get; set; } // Information about where its value comes from
        }
        ```
* **`ToggleActivationRequestDto` (Request - PUT)** (For Story 2.3 - Static Criteria activation)
    * Purpose: To activate or deactivate a static criterion.
    * Schema:
        ```csharp
        public class ToggleActivationRequestDto
        {
            [Required]
            public bool IsActive { get; set; }
        }
        ```

### 1.3 Form & Category Management DTOs (Admin - Epic 2)

* **`EvaluationFormRequestDto` (Request - POST/PUT)** (Story 2.4)
    * Purpose: For creating or updating an evaluation form.
    * Schema:
        ```csharp
        public class EvaluationFormRequestDto
        {
            [Required]
            [StringLength(250)]
            public string Name { get; set; }

            [Required]
            public List<string> ApplicableAcademicCadres { get; set; } = new(); // List of cadre identifiers

            [Required]
            public DateTime EvaluationPeriodStartDate { get; set; }

            [Required]
            public DateTime EvaluationPeriodEndDate { get; set; }
        }
        ```
* **`EvaluationFormResponseDto` (Response)** (Story 2.4)
    * Purpose: To return details of an evaluation form.
    * Schema:
        ```csharp
        public class EvaluationFormResponseDto
        {
            public string Id { get; set; } // Public GUID Id of the EvaluationFormEntity
            public string Name { get; set; }
            public List<string> ApplicableAcademicCadres { get; set; }
            public DateTime EvaluationPeriodStartDate { get; set; }
            public DateTime EvaluationPeriodEndDate { get; set; }
            public string Status { get; set; } // e.g., "Draft", "Active", "Archived"
            public DateTime CreatedAt { get; set; }
            public DateTime UpdatedAt { get; set; }
            public List<FormCategoryResponseDto> Categories { get; set; } = new();
        }
        ```
* **`FormCategoryRequestDto` (Request - POST/PUT)** (Story 2.5)
    * Purpose: For creating or updating a category within a form.
    * Schema:
        ```csharp
        public class FormCategoryRequestDto
        {
            [Required]
            [StringLength(150)]
            public string Name { get; set; }

            [Range(0, 100)] // Assuming percentage weight
            public double Weight { get; set; }
            public int DisplayOrder { get; set; }
        }
        ```
* **`FormCategoryResponseDto` (Response)** (Story 2.5)
    * Purpose: To return details of a form category.
    * Schema:
        ```csharp
        public class FormCategoryResponseDto
        {
            public string Id { get; set; } // Public GUID Id of the FormCategoryEntity
            public string EvaluationFormId { get; set; } // Public GUID Id of the parent EvaluationFormEntity
            public string Name { get; set; }
            public double Weight { get; set; }
            public int DisplayOrder { get; set; }
            public List<CriterionAssignmentResponseDto> AssignedCriteria { get; set; } = new();
        }
        ```
* **`CriterionAssignmentRequestDto` (Request - POST/PUT for Story 2.6)**
    * Purpose: For assigning a criterion to a form category.
    * Schema:
        ```csharp
        public class CriterionAssignmentRequestDto
        {
            [Required]
            public string CriterionId { get; set; } // Can be MongoDB ObjectId (string) for dynamic or StaticCriterionSystemId for static

            [Required]
            public string CriterionType { get; set; } // "Dynamic" or "Static"
            public int DisplayOrder { get; set; }
        }
        ```
* **`CriterionAssignmentResponseDto` (Response)** (Story 2.6)
    * Purpose: To return details of a criterion assigned to a category.
    * Schema:
        ```csharp
        public class CriterionAssignmentResponseDto
        {
            public string LinkId { get; set; } // Public GUID Id of the FormCriterionLinkEntity
            public string CriterionId { get; set; } // MongoDB ObjectId for dynamic, or StaticCriterionSystemId for static
            public string CriterionType { get; set; } // "Dynamic" or "Static"
            public string CriterionName { get; set; } // Fetched for display
            public string CriterionDescription { get; set; } // Fetched for display
            public double? CriterionCoefficient { get; set; } // For dynamic criteria
            public int DisplayOrder { get; set; }
            public List<InputFieldDefinitionDto> InputFields { get; set; } // For dynamic criteria, from its template
            public string DataSourceHint { get; set; } // For static criteria, from its definition
        }
        ```

### 1.4 Academician Submission DTOs (Epic 3)

* **`AcademicianDashboardFormDto` (Response)** (Story 3.1)
    * Purpose: Represents a form on the academician's dashboard.
    * Schema:
        ```csharp
        public class AcademicianDashboardFormDto
        {
            public string FormId { get; set; } // Public GUID Id of the EvaluationFormEntity
            public string FormName { get; set; }
            public DateTime EvaluationPeriodStartDate { get; set; }
            public DateTime EvaluationPeriodEndDate { get; set; }
            public string SubmissionStatus { get; set; } // e.g., "Not Started", "Draft", "Submitted", "Approved", "Rejected"
            public string SubmissionId { get; set; } // Public GUID Id of AcademicSubmissionEntity, if a submission exists
            public DateTime? SubmissionDeadline { get; set; } // Calculated or from form period end date
        }
        ```
* **`AcademicianSubmissionRequestDto` (Request - POST/PUT for Story 3.3, 3.5)**
    * Purpose: For saving draft or submitting final performance data (only for dynamic criteria).
    * Schema:
        ```csharp
        public class AcademicianSubmissionRequestDto
        {
            [Required]
            public string FormId { get; set; } // Public GUID Id of the EvaluationFormEntity
            public List<SubmittedDynamicCriterionEntryDto> DynamicCriterionEntries { get; set; } = new();
        }
        ```
* **`SubmittedDynamicCriterionEntryDto` (Shared for Story 3.3)**
    * Purpose: Represents data entered by an academician for a single instance of a *dynamic* criterion.
    * Schema:
        ```csharp
        public class SubmittedDynamicCriterionEntryDto
        {
            [Required]
            public string FormCriterionLinkId { get; set; } // Public GUID Id of the FormCriterionLinkEntity (must be of type "Dynamic")
            public Dictionary<string, object> Data { get; set; } = new(); // Key: InputFieldDefinition.FieldId
            public List<string> TempUploadedFileIds { get; set; } = new(); // Internal IDs for newly uploaded files
        }
        ```
* **`AcademicianSubmissionResponseDto` (Response)** (Story 3.1, 3.2)
    * Purpose: To return the full details of an academician's submission, including fetched static data.
    * Schema:
        ```csharp
        public class AcademicianSubmissionResponseDto
        {
            public string SubmissionId { get; set; } // Public GUID Id
            public string FormId { get; set; } // Public GUID Id
            public string FormName { get; set; }
            public string AcademicianUserId { get; set; }
            public string AcademicianName { get; set; }
            public string Status { get; set; }
            public DateTime SubmittedAt { get; set; }
            public DateTime? LastSavedAt { get; set; }
            public DateTime? ApprovedAt { get; set; }
            public string ApprovedByControllerUserId { get; set; }
            public string ApprovalComments { get; set; }
            public DateTime? RejectedAt { get; set; }
            public string RejectedByControllerUserId { get; set; }
            public string RejectionComments { get; set; }
            public List<AcademicianSubmissionCategoryViewDto> Categories { get; set; } = new();
        }

        public class AcademicianSubmissionCategoryViewDto
        {
            public string CategoryId { get; set; } // Public GUID Id
            public string CategoryName { get; set; }
            public double Weight { get; set; }
            public int DisplayOrder { get; set; }
            public List<AcademicianCriterionDataViewDto> CriteriaData { get; set; } = new();
        }

        public class AcademicianCriterionDataViewDto // Represents both dynamic and static criteria within a submission view
        {
            public string FormCriterionLinkId { get; set; } // Public GUID Id
            public string CriterionType { get; set; } // "Dynamic" or "Static"
            public string CriterionName { get; set; }
            public string CriterionDescription { get; set; }
            public double? CriterionCoefficient { get; set; } // From dynamic template

            // For Dynamic Criteria
            public List<InputFieldDefinitionDto> InputFields { get; set; } // Definition from template
            public List<SubmittedCriterionInstanceDataViewDto> SubmittedInstances {get; set;} = new(); // User-entered data

            // For Static Criteria
            public string StaticValue { get; set; } // The fetched/pre-calculated value for this academician
            public string DataSourceHint { get; set; } // From static criterion definition
        }

        public class SubmittedCriterionInstanceDataViewDto // Represents one entry for a dynamic criterion
        {
            public string InstanceId { get; set; } // ID for this specific entry (e.g., from MongoDB)
            public Dictionary<string, object> Data { get; set; }
            public List<EvidenceFileResponseDto> EvidenceFiles { get; set; } = new();
        }
        ```
* **`EvidenceFileResponseDto` (Response)** (Story 3.4)
    * Purpose: To provide details about an uploaded evidence file.
    * Schema:
        ```csharp
        public class EvidenceFileResponseDto
        {
            public string Id { get; set; } // Public GUID Id of the EvidenceFileEntity
            public string FileName { get; set; }
            public string ContentType { get; set; }
            public long SizeBytes { get; set; }
            public DateTime UploadedAt { get; set; }
            public string DownloadUrl { get; set; } // URL to download the file (to be constructed by API)
        }
        ```
* **`UploadEvidenceResponseDto` (Response)**
    * Purpose: Response after uploading an evidence file.
    * Schema:
        ```csharp
        public class UploadEvidenceResponseDto
        {
            public string TempUploadedFileId { get; set; } // Temporary ID to associate with submission entries before final save
            public string FileName { get; set; }
            public long SizeBytes { get; set; }
        }
        ```

### 1.5 Controller Workflow DTOs (Epic 4)

* **`ControllerSubmissionSummaryDto` (Response)** (Story 4.1)
    * Purpose: Summary of a submission for the controller's dashboard.
    * Schema:
        ```csharp
        public class ControllerSubmissionSummaryDto
        {
            public string SubmissionId { get; set; } // Public GUID Id
            public string FormName { get; set; }
            public string AcademicianName { get; set; }
            public string AcademicianDepartment { get; set; }
            public DateTime SubmittedAt { get; set; }
            public string Status { get; set; } // Should be "Submitted" or "Pending Approval"
        }
        ```
* **`SubmissionRejectionRequestDto` (Request - POST/PUT)** (Story 4.4)
    * Purpose: For a controller to reject a submission.
    * Schema:
        ```csharp
        public class SubmissionRejectionRequestDto
        {
            [Required]
            [StringLength(2000)]
            public string Comments { get; set; }
        }
        ```
* **`SubmissionApprovalRequestDto` (Request - POST/PUT)** (Story 4.3)
    * Purpose: For a controller to approve a submission.
    * Schema:
        ```csharp
        public class SubmissionApprovalRequestDto
        {
            [StringLength(2000)]
            public string Comments { get; set; } // Optional comments on approval
        }
        ```

### 1.6 RBAC DTOs (Admin - Epic 1)

* **`ApdysRoleResponseDto` (Response)**
    * Schema:
        ```csharp
        public class ApdysRoleResponseDto
        {
            public string Id { get; set; } // Public GUID Id
            public string RoleName { get; set; } // e.g., APDYS_Admin, APDYS_Academician
            public string Description { get; set; }
            public List<ApdysPermissionResponseDto> Permissions { get; set; } = new();
        }
        ```
* **`ApdysPermissionResponseDto` (Response)**
    * Schema:
        ```csharp
        public class ApdysPermissionResponseDto
        {
            public string Id { get; set; } // Public GUID Id
            public string PermissionName { get; set; } // e.g., CanManageCriteria, CanSubmitPerformanceData
            public string Description { get; set; }
        }
        ```
* **`ApdysRoleRequestDto` (Request - POST/PUT for creating/updating roles)**
    * Schema:
        ```csharp
        public class ApdysRoleRequestDto
        {
            [Required]
            [StringLength(100)]
            public string RoleName { get; set; }
            [StringLength(500)]
            public string Description { get; set; }
        }
        ```
* **`ApdysPermissionRequestDto` (Request - POST/PUT for creating/updating permissions - less likely to be CRUD, more likely seeded)**
    * Schema:
        ```csharp
        public class ApdysPermissionRequestDto
        {
            [Required]
            [StringLength(150)]
            public string PermissionName { get; set; }
            [StringLength(500)]
            public string Description { get; set; }
        }
        ```
* **`AssignPermissionToRoleRequestDto` (Request)**
    * Schema:
        ```csharp
        public class AssignPermissionToRoleRequestDto
        {
            [Required]
            public string PermissionId { get; set; } // Public GUID Id of the permission
        }
        ```
* **`AssignRoleToUserRequestDto` (Request - If APDYS manages this directly)**
    * Schema:
        ```csharp
        public class AssignRoleToUserRequestDto
        {
            [Required]
            public string UniversityUserId { get; set; }
            [Required]
            public string RoleId { get; set; } // Public GUID Id of the APDYS Role
        }
        ```

### 1.7 Department Strategic Performance DTOs (Sub-Epic 5.A)

* **`DepartmentStrategicIndicatorDefinitionRequestDto` (Request - Admin/Seed)** (Story 5.A.1)
    * Purpose: For defining a strategic performance indicator that departments will be evaluated against.
    * Schema:
        ```csharp
        public class DepartmentStrategicIndicatorDefinitionRequestDto
        {
            [Required]
            [StringLength(100)]
            public string IndicatorSystemId { get; set; } // e.g., "STUDENT_SATISFACTION_RATE", "RESEARCH_GRANT_COUNT"
            [Required]
            [StringLength(250)]
            public string Name { get; set; }
            [StringLength(1000)]
            public string Description { get; set; }
            [Required]
            public string DataType { get; set; } // e.g., "Percentage", "Number", "Currency", "Narrative"
            public bool IsHigherBetter { get; set; } = true;
        }
        ```
* **`DepartmentStrategicIndicatorDefinitionResponseDto` (Response)** (Story 5.A.1)
    * Schema:
        ```csharp
        public class DepartmentStrategicIndicatorDefinitionResponseDto
        {
            public string IndicatorSystemId { get; set; }
            public string Name { get; set; }
            public string Description { get; set; }
            public string DataType { get; set; }
            public bool IsHigherBetter { get; set; }
            public bool IsActive { get; set; } = true;
        }
        ```
* **`DepartmentStrategicPerformanceDataRequestDto` (Request - POST/PUT)** (Story 5.A.2 - Strategic Mgmt Office Staff)
    * Purpose: For Strategic Management Office staff to input performance data for a department against indicators.
    * Schema:
        ```csharp
        public class DepartmentStrategicPerformanceDataRequestDto
        {
            [Required]
            public string DepartmentId { get; set; } // Identifier for the department from Org. Management
            [Required]
            public string AssessmentPeriodId { get; set; } // e.g., "2025-AcademicYear", "2025-Q1"
            [Required]
            public List<IndicatorPerformanceEntryDto> PerformanceEntries { get; set; } = new();
        }

        public class IndicatorPerformanceEntryDto
        {
            [Required]
            public string IndicatorSystemId { get; set; } // FK to DepartmentStrategicIndicatorDefinitionEntity
            public string ActualValue { get; set; } // Stored as string, parsed based on Indicator's DataType
            public string TargetValue { get; set; } // Optional
            public string Notes { get; set; }
        }
        ```
* **`DepartmentStrategicPerformanceDataResponseDto` (Response)** (Story 5.A.2)
    * Schema:
        ```csharp
        public class DepartmentStrategicPerformanceDataResponseDto
        {
            public string Id { get; set; } // Public GUID Id of the main data entry record
            public string DepartmentId { get; set; }
            public string DepartmentName { get; set; } // Fetched for context
            public string AssessmentPeriodId { get; set; }
            public List<IndicatorPerformanceResponseDto> PerformanceEntries { get; set; } = new();
            public DateTime SubmittedAt { get; set; }
            public string SubmittedByUserId { get; set; }
            public DateTime LastUpdatedAt { get; set; }
        }

        public class IndicatorPerformanceResponseDto
        {
            public string IndicatorSystemId { get; set; }
            public string IndicatorName { get; set; } // Fetched for context
            public string ActualValue { get; set; }
            public string TargetValue { get; set; }
            public string Notes { get; set; }
            public string DataType { get; set; } // Fetched for context
        }
        ```

### 1.8 Academic Staff Competency DTOs (Sub-Epic 5.B)

* **`StaffCompetencyDefinitionRequestDto` (Request - Admin/Seed)** (Story 5.B.1)
    * Purpose: For defining a competency area.
    * Schema:
        ```csharp
        public class StaffCompetencyDefinitionRequestDto
        {
            [Required]
            [StringLength(100)]
            public string CompetencySystemId { get; set; } // e.g., "LEADERSHIP_SKILLS", "RESEARCH_METHODOLOGY"
            [Required]
            [StringLength(250)]
            public string Name { get; set; }
            [StringLength(1000)]
            public string Description { get; set; }
            public List<string> RatingScale { get; set; } = new(); // e.g., ["Below Expectations", "Meets Expectations", "Exceeds Expectations"]
        }
        ```
* **`StaffCompetencyDefinitionResponseDto` (Response)** (Story 5.B.1)
    * Schema:
        ```csharp
        public class StaffCompetencyDefinitionResponseDto
        {
            public string CompetencySystemId { get; set; }
            public string Name { get; set; }
            public string Description { get; set; }
            public List<string> RatingScale { get; set; }
            public bool IsActive { get; set; } = true;
        }
        ```
* **`StaffCompetencyEvaluationRequestDto` (Request - POST/PUT)** (Story 5.B.2 - Managers)
    * Purpose: For Managers to submit competency evaluations for their staff.
    * Schema:
        ```csharp
        public class StaffCompetencyEvaluationRequestDto
        {
            [Required]
            public string AcademicianUniveristyUserId { get; set; } // The staff member being evaluated
            [Required]
            public string EvaluationContextId { get; set; } // e.g., "AnnualReview-2025", "ProjectAlpha-Review"
            public List<CompetencyRatingEntryDto> CompetencyRatings { get; set; } = new();
            public string OverallComments { get; set; }
        }

        public class CompetencyRatingEntryDto
        {
            [Required]
            public string CompetencySystemId { get; set; } // FK to StaffCompetencyDefinitionEntity
            [Required]
            public string Rating { get; set; } // Must be one of the defined RatingScale values
            public string Comments { get; set; } // Specific comments for this competency
        }
        ```
* **`StaffCompetencyEvaluationResponseDto` (Response)** (Story 5.B.2)
    * Schema:
        ```csharp
        public class StaffCompetencyEvaluationResponseDto
        {
            public string Id { get; set; } // Public GUID Id of the evaluation record
            public string AcademicianUniveristyUserId { get; set; }
            public string AcademicianName { get; set; } // Fetched for context
            public string EvaluatingManagerUserId { get; set; } // From UserContextCo
            public string EvaluatingManagerName { get; set; } // Fetched for context
            public string EvaluationContextId { get; set; }
            public List<CompetencyRatingResponseDto> CompetencyRatings { get; set; } = new();
            public string OverallComments { get; set; }
            public DateTime SubmittedAt { get; set; }
        }

        public class CompetencyRatingResponseDto
        {
            public string CompetencySystemId { get; set; }
            public string CompetencyName { get; set; } // Fetched for context
            public string Rating { get; set; }
            public string Comments { get; set; }
        }
        ```

### 1.9 Portfolio Control DTOs (Sub-Epic 5.C)

* **`PortfolioChecklistItemDefinitionRequestDto` (Request - Admin/Seed)** (Story 5.C.1)
    * Purpose: Defining items that Archivists need to verify.
    * Schema:
        ```csharp
        public class PortfolioChecklistItemDefinitionRequestDto
        {
            [Required]
            [StringLength(100)]
            public string ItemSystemId { get; set; } // e.g., "COURSE_SYLLABUS_FALL2025_CS101", "THESIS_SUBMISSION_FORM_2025"
            [Required]
            [StringLength(250)]
            public string Name { get; set; }
            [StringLength(1000)]
            public string Description { get; set; }
            public string ApplicableCadreHint { get; set; } // e.g., "All", "PhD Candidates"
            public string ExpectedLocationInEbysHint { get; set; }
        }
        ```
* **`PortfolioChecklistItemDefinitionResponseDto` (Response)** (Story 5.C.1)
    * Schema:
        ```csharp
        public class PortfolioChecklistItemDefinitionResponseDto
        {
            public string ItemSystemId { get; set; }
            public string Name { get; set; }
            public string Description { get; set; }
            public string ApplicableCadreHint { get; set; }
            public string ExpectedLocationInEbysHint { get; set; }
            public bool IsActive { get; set; } = true;
        }
        ```
* **`PortfolioVerificationUpdateRequestDto` (Request - PUT)** (Story 5.C.2 - Archivists)
    * Purpose: For Archivists to update the verification status of a checklist item for an academician.
    * Schema:
        ```csharp
        public class PortfolioVerificationUpdateRequestDto
        {
            [Required]
            public string AcademicianUniveristyUserId { get; set; }
            [Required]
            public string ItemSystemId { get; set; } // FK to PortfolioChecklistItemDefinitionEntity
            [Required]
            public string VerificationStatus { get; set; } // e.g., "VerifiedInEbys", "Missing", "DiscrepancyFound", "NotApplicable"
            public string ArchivistComments { get; set; }
        }
        ```
* **`PortfolioVerificationResponseDto` (Response)** (Story 5.C.2)
    * Schema:
        ```csharp
        public class PortfolioVerificationResponseDto
        {
            public string Id { get; set; } // Public GUID Id of the verification record
            public string AcademicianUniveristyUserId { get; set; }
            public string AcademicianName { get; set; } // Fetched
            public string ItemSystemId { get; set; }
            public string ItemName { get; set; } // Fetched
            public string VerificationStatus { get; set; }
            public string ArchivistComments { get; set; }
            public DateTime LastVerifiedAt { get; set; }
            public string LastVerifiedByArchivistUserId { get; set; }
        }
        ```
* **`AcademicianPortfolioStatusResponseDto` (Response)** (For Archivist's dashboard)
    * Purpose: To show the overall portfolio verification status for an academician.
    * Schema:
        ```csharp
        public class AcademicianPortfolioStatusResponseDto
        {
            public string AcademicianUniveristyUserId { get; set; }
            public string AcademicianName { get; set; }
            public string Department { get; set; }
            public int TotalItems { get; set; }
            public int VerifiedItems { get; set; }
            public int MissingItems { get; set; }
            public string OverallPortfolioStatus { get; set; } // e.g., "PendingReview", "InReview", "CompletedWithIssues", "Completed"
            public List<PortfolioVerificationResponseDto> VerificationDetails { get; set; } = new();
        }
        ```

### 1.10 Generic Data Entry DTOs (Sub-Epic 5.D)

* **`GenericDataEntryDefinitionRequestDto` (Request - Admin/Seed)** (Story 5.D.1)
    * Purpose: For defining a generic data entry form/type for roles like Library, TTO.
    * Schema:
        ```csharp
        public class GenericDataEntryDefinitionRequestDto
        {
            [Required]
            [StringLength(100)]
            public string EntryTypeSystemId { get; set; } // e.g., "LIBRARY_FINES_OUTSTANDING", "TTO_PATENT_APPLICATIONS"
            [Required]
            [StringLength(250)]
            public string Name { get; set; }
            [StringLength(1000)]
            public string Description { get; set; }
            public string ApplicableRoleHint { get; set; } // e.g., "APDYS_DataEntry_Library", "APDYS_DataEntry_TTO"
            [Required]
            public string DataFieldLabel { get; set; } // e.g., "Outstanding Fine Amount", "Number of Patents Filed"
            [Required]
            public string DataFieldType { get; set; } // "Number", "Text", "Boolean", "Currency"
        }
        ```
* **`GenericDataEntryDefinitionResponseDto` (Response)** (Story 5.D.1)
    * Schema:
        ```csharp
        public class GenericDataEntryDefinitionResponseDto
        {
            public string EntryTypeSystemId { get; set; }
            public string Name { get; set; }
            public string Description { get; set; }
            public string ApplicableRoleHint { get; set; }
            public string DataFieldLabel { get; set; }
            public string DataFieldType { get; set; }
            public bool IsActive { get; set; } = true;
        }
        ```
* **`GenericDataEntryRequestDto` (Request - POST/PUT)** (Story 5.D.2 - Designated Data Entry Staff)
    * Purpose: For submitting a generic data entry.
    * Schema:
        ```csharp
        public class GenericDataEntryRequestDto
        {
            [Required]
            public string AcademicianUniveristyUserId { get; set; } // The academician this data pertains to
            [Required]
            public string EntryTypeSystemId { get; set; } // FK to GenericDataEntryDefinitionEntity
            [Required]
            public string Value { get; set; } // Stored as string, parsed based on DataFieldType
            public string AssessmentPeriodId { get; set; } // Optional, e.g., "2025"
            public string Notes { get; set; }
        }
        ```
* **`GenericDataEntryResponseDto` (Response)** (Story 5.D.2)
    * Schema:
        ```csharp
        public class GenericDataEntryResponseDto
        {
            public string Id { get; set; } // Public GUID Id of the entry
            public string AcademicianUniveristyUserId { get; set; }
            public string AcademicianName { get; set; } // Fetched
            public string EntryTypeSystemId { get; set; }
            public string EntryTypeName { get; set; } // Fetched
            public string Value { get; set; }
            public string AssessmentPeriodId { get; set; }
            public string Notes { get; set; }
            public DateTime SubmittedAt { get; set; }
            public string SubmittedByUserId { get; set; } // User from data entry role
        }
        ```

## 2. Criteria/Command Objects (COs)

COs are used as parameters for Manager/Service methods, often encapsulating more complex operations or query criteria.

* **`UserContextCo` (Internal)**
    * Purpose: To pass authenticated user information within the application layers.
    * Schema: (Likely provided or aligned with `Rlx.Shared` user context helpers)
        ```csharp
        public class UserContextCo
        {
            public string UserId { get; set; } // University User ID
            public List<string> ApdysRoleNames { get; set; } // APDYS specific role names
            public List<string> ApdysPermissions { get; set; } // APDYS specific permission names
            public string AcademicCadre { get; set; }
            public string Department { get; set; }
            // Other relevant Organization Management data
        }
        ```
* **`WorkspaceSubmissionsQueryCo` (Internal, for Stores/Managers)**
    * Purpose: Parameters for querying submissions (e.g., by academician, controller, status).
    * Schema:
        ```csharp
        public class FetchSubmissionsQueryCo
        {
            public string AcademicianUserId { get; set; }
            public string ControllerUserId { get; set; } // For controller's view
            public List<string> Statuses { get; set; }
            public string FormId { get; set; } // Public GUID Id
            public int PageNumber { get; set; } = 1;
            public int PageSize { get; set; } = 20;
            public string SortBy { get; set; } // e.g., "SubmittedAt", "AcademicianName"
            public string SortOrder { get; set; } // "asc" or "desc"
        }
        ```
* **`ProcessStaticCriterionValueCo` (Internal)**
    * Purpose: Parameters for a service method that resolves the value of a static criterion.
    * Schema:
        ```csharp
        public class ProcessStaticCriterionValueCo
        {
            public string AcademicianUserId { get; set; }
            public string StaticCriterionSystemId { get; set; }
            public string DataSourceHint { get; set; }
            public DateTime EvaluationDateContext { get; set; } // To fetch period-specific static data
        }
        ```

## 3. PostgreSQL Entities (`Models/Entities/`)

These entities map to tables in the PostgreSQL database. They all follow the dual ID pattern: `int AutoIncrementId` (DB PK) and `string Id` (public GUID).

### 3.1 RBAC Entities (Epic 1)

* **`ApdysRoleEntity`**
    * Schema:
        ```csharp
        public class ApdysRoleEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index
            [Required] [StringLength(100)] public string RoleName { get; set; } // Unique Index
            [StringLength(500)] public string Description { get; set; }
            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

            public virtual ICollection<ApdysRolePermissionEntity> RolePermissions { get; set; } = new List<ApdysRolePermissionEntity>();
            public virtual ICollection<UserApdysRoleMappingEntity> UserMappings { get; set; } = new List<UserApdysRoleMappingEntity>();
        }
        ```
* **`ApdysPermissionEntity`**
    * Schema:
        ```csharp
        public class ApdysPermissionEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index
            [Required] [StringLength(150)] public string PermissionName { get; set; } // Unique Index (e.g., "Forms.Create")
            [StringLength(500)] public string Description { get; set; }
            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

            public virtual ICollection<ApdysRolePermissionEntity> RolePermissions { get; set; } = new List<ApdysRolePermissionEntity>();
        }
        ```
* **`ApdysRolePermissionEntity`** (Join Table for Many-to-Many)
    * Schema:
        ```csharp
        public class ApdysRolePermissionEntity
        {
            public int ApdysRoleAutoIncrementId { get; set; } // Composite PK part 1, FK
            public int ApdysPermissionAutoIncrementId { get; set; } // Composite PK part 2, FK
            public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

            public virtual ApdysRoleEntity ApdysRole { get; set; }
            public virtual ApdysPermissionEntity ApdysPermission { get; set; }
        }
        ```
* **`UserApdysRoleMappingEntity`**
    * Purpose: Links a University User ID (from token) to APDYS-specific roles.
    * Schema:
        ```csharp
        public class UserApdysRoleMappingEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index
            [Required] public string UniversityUserId { get; set; } // Indexed
            public int ApdysRoleAutoIncrementId { get; set; } // FK
            public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

            public virtual ApdysRoleEntity ApdysRole { get; set; }
        }
        ```

### 3.2 Criteria & Form Management Entities (Epic 2)

* **`StaticCriterionDefinitionEntity`**
    * Schema:
        ```csharp
        public class StaticCriterionDefinitionEntity
        {
            [Key]
            [StringLength(100)]
            public string StaticCriterionSystemId { get; set; } // e.g., "YEARS_OF_SERVICE", "CUMULATIVE_COURSE_LOAD"
            [Required] [StringLength(200)] public string Name { get; set; }
            [StringLength(1000)] public string Description { get; set; }
            public bool IsActive { get; set; } = true;
            [StringLength(250)] public string DataSourceHint { get; set; }
        }
        ```
* **`EvaluationFormEntity`**
    * Schema:
        ```csharp
        public class EvaluationFormEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index

            [Required] [StringLength(250)] public string Name { get; set; }
            public string ApplicableAcademicCadresJson { get; set; } // Store as JSON string "[ \"Prof\", \"AssocProf\" ]"
            public DateTime EvaluationPeriodStartDate { get; set; }
            public DateTime EvaluationPeriodEndDate { get; set; }
            [Required] [StringLength(50)] public string Status { get; set; } // "Draft", "Active", "Archived"

            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public string CreatedByUserId { get; set; }
            public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
            public string UpdatedByUserId { get; set; }

            public virtual ICollection<FormCategoryEntity> Categories { get; set; } = new List<FormCategoryEntity>();
        }
        ```
* **`FormCategoryEntity`**
    * Schema:
        ```csharp
        public class FormCategoryEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index

            public int EvaluationFormAutoIncrementId { get; set; } // FK
            [Required] [StringLength(150)] public string Name { get; set; }
            public double Weight { get; set; } // e.g., 0.4 for 40%
            public int DisplayOrder { get; set; }

            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public string CreatedByUserId { get; set; }
            public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
            public string UpdatedByUserId { get; set; }

            public virtual EvaluationFormEntity EvaluationForm { get; set; }
            public virtual ICollection<FormCriterionLinkEntity> CriterionLinks { get; set; } = new List<FormCriterionLinkEntity>();
        }
        ```
* **`FormCriterionLinkEntity`** (Links a category to a dynamic or static criterion)
    * Schema:
        ```csharp
        public class FormCriterionLinkEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index

            public int FormCategoryAutoIncrementId { get; set; } // FK
            [Required] [StringLength(50)] public string CriterionType { get; set; } // "Dynamic" or "Static"

            [StringLength(100)] public string DynamicCriterionTemplateId { get; set; } // Nullable, MongoDB ObjectId string

            [StringLength(100)] public string StaticCriterionSystemId { get; set; } // Nullable, FK

            public int DisplayOrder { get; set; }

            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public string CreatedByUserId { get; set; }

            public virtual FormCategoryEntity FormCategory { get; set; }
            public virtual StaticCriterionDefinitionEntity StaticCriterionDefinition { get; set; }
        }
        ```

### 3.3 Academician Submission Entities (Epic 3 & 4)

* **`AcademicSubmissionEntity`** (Header/Metadata for a submission)
    * Schema:
        ```csharp
        public class AcademicSubmissionEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index

            public int EvaluationFormAutoIncrementId { get; set; } // FK
            [Required] public string AcademicianUniveristyUserId { get; set; }
            [Required] [StringLength(50)] public string Status { get; set; }

            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
            public DateTime? SubmittedAt { get; set; }
            public DateTime? ApprovedAt { get; set; }
            public string ApprovedByControllerUserId { get; set; }
            [StringLength(2000)] public string ApprovalComments { get; set; }
            public DateTime? RejectedAt { get; set; }
            public string RejectedByControllerUserId { get; set; }
            [StringLength(2000)] public string RejectionComments { get; set; }

            public virtual EvaluationFormEntity EvaluationForm { get; set; }
            public virtual ICollection<EvidenceFileEntity> EvidenceFiles { get; set; } = new List<EvidenceFileEntity>();
        }
        ```
* **`EvidenceFileEntity`** (Metadata for uploaded files)
    * Schema:
        ```csharp
        public class EvidenceFileEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index

            public int AcademicSubmissionAutoIncrementId { get; set; } // FK
            [Required] public string SubmittedDynamicDataInstanceId { get; set; } // FK (string): Links file to specific MongoDB dynamic data entry instance

            [Required] [StringLength(255)] public string FileName { get; set; }
            [Required] [StringLength(100)] public string ContentType { get; set; }
            public long SizeBytes { get; set; }
            [Required] public string StoredFilePath { get; set; }
            public DateTime UploadedAt { get; set; } = DateTime.UtcNow;
            [Required] public string UploadedByUniveristyUserId { get; set; }

            public virtual AcademicSubmissionEntity AcademicSubmission { get; set; }
        }
        ```

### 3.4 Department Strategic Performance Entities (Sub-Epic 5.A)

* **`DepartmentStrategicIndicatorDefinitionEntity`**
    * Schema:
        ```csharp
        public class DepartmentStrategicIndicatorDefinitionEntity
        {
            [Key]
            [StringLength(100)]
            public string IndicatorSystemId { get; set; }
            [Required] [StringLength(250)] public string Name { get; set; }
            [StringLength(1000)] public string Description { get; set; }
            [Required] [StringLength(50)] public string DataType { get; set; }
            public bool IsHigherBetter { get; set; } = true;
            public bool IsActive { get; set; } = true;
            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public string CreatedByUserId { get; set; }
        }
        ```
* **`DepartmentStrategicPerformanceDataEntity`**
    * Schema:
        ```csharp
        public class DepartmentStrategicPerformanceDataEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index

            [Required] [StringLength(100)] public string DepartmentId { get; set; }
            [Required] [StringLength(100)] public string AssessmentPeriodId { get; set; }

            [Required] [StringLength(100)] public string IndicatorSystemId { get; set; } // FK
            public string ActualValue { get; set; }
            public string TargetValue { get; set; }
            [StringLength(2000)] public string Notes { get; set; }

            public DateTime SubmittedAt { get; set; } = DateTime.UtcNow;
            [Required] public string SubmittedByUserId { get; set; }

            public virtual DepartmentStrategicIndicatorDefinitionEntity IndicatorDefinition { get; set; }
        }
        ```

### 3.5 Academic Staff Competency Entities (Sub-Epic 5.B)

* **`StaffCompetencyDefinitionEntity`**
    * Schema:
        ```csharp
        public class StaffCompetencyDefinitionEntity
        {
            [Key]
            [StringLength(100)]
            public string CompetencySystemId { get; set; }
            [Required] [StringLength(250)] public string Name { get; set; }
            [StringLength(1000)] public string Description { get; set; }
            public string RatingScaleJson { get; set; } // Store as JSON string
            public bool IsActive { get; set; } = true;
            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public string CreatedByUserId { get; set; }
        }
        ```
* **`StaffCompetencyEvaluationEntity`** (Header for one evaluation)
    * Schema:
        ```csharp
        public class StaffCompetencyEvaluationEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index

            [Required] public string AcademicianUniveristyUserId { get; set; }
            [Required] public string EvaluatingManagerUserId { get; set; }
            [Required] [StringLength(100)] public string EvaluationContextId { get; set; }
            [StringLength(4000)] public string OverallComments { get; set; }
            public DateTime SubmittedAt { get; set; } = DateTime.UtcNow;

            public virtual ICollection<CompetencyRatingEntity> CompetencyRatings { get; set; } = new List<CompetencyRatingEntity>();
        }
        ```
* **`CompetencyRatingEntity`** (Detail for each competency rated in an evaluation)
    * Schema:
        ```csharp
        public class CompetencyRatingEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index

            public int StaffCompetencyEvaluationAutoIncrementId { get; set; } // FK
            [Required] [StringLength(100)] public string CompetencySystemId { get; set; } // FK
            [Required] [StringLength(100)] public string Rating { get; set; }
            [StringLength(1000)] public string Comments { get; set; }

            public virtual StaffCompetencyEvaluationEntity Evaluation { get; set; }
            public virtual StaffCompetencyDefinitionEntity CompetencyDefinition { get; set; }
        }
        ```

### 3.6 Portfolio Control Entities (Sub-Epic 5.C)

* **`PortfolioChecklistItemDefinitionEntity`**
    * Schema:
        ```csharp
        public class PortfolioChecklistItemDefinitionEntity
        {
            [Key]
            [StringLength(100)]
            public string ItemSystemId { get; set; }
            [Required] [StringLength(250)] public string Name { get; set; }
            [StringLength(1000)] public string Description { get; set; }
            [StringLength(100)] public string ApplicableCadreHint { get; set; }
            [StringLength(250)] public string ExpectedLocationInEbysHint { get; set; }
            public bool IsActive { get; set; } = true;
            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public string CreatedByUserId { get; set; }
        }
        ```
* **`PortfolioVerificationLogEntity`**
    * Schema:
        ```csharp
        public class PortfolioVerificationLogEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index

            [Required] public string AcademicianUniveristyUserId { get; set; }
            [Required] [StringLength(100)] public string ItemSystemId { get; set; } // FK
            [Required] [StringLength(100)] public string VerificationStatus { get; set; }
            [StringLength(2000)] public string ArchivistComments { get; set; }
            public DateTime LastVerifiedAt { get; set; } = DateTime.UtcNow;
            [Required] public string LastVerifiedByArchivistUserId { get; set; }

            public virtual PortfolioChecklistItemDefinitionEntity ChecklistItemDefinition { get; set; }
        }
        ```

### 3.7 Generic Data Entry Entities (Sub-Epic 5.D)

* **`GenericDataEntryDefinitionEntity`**
    * Schema:
        ```csharp
        public class GenericDataEntryDefinitionEntity
        {
            [Key]
            [StringLength(100)]
            public string EntryTypeSystemId { get; set; }
            [Required] [StringLength(250)] public string Name { get; set; }
            [StringLength(1000)] public string Description { get; set; }
            [StringLength(100)] public string ApplicableRoleHint { get; set; }
            [Required] [StringLength(150)] public string DataFieldLabel { get; set; }
            [Required] [StringLength(50)] public string DataFieldType { get; set; }
            public bool IsActive { get; set; } = true;
            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public string CreatedByUserId { get; set; }
        }
        ```
* **`GenericDataEntryRecordEntity`**
    * Schema:
        ```csharp
        public class GenericDataEntryRecordEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index

            [Required] public string AcademicianUniveristyUserId { get; set; }
            [Required] [StringLength(100)] public string EntryTypeSystemId { get; set; } // FK
            [Required] public string Value { get; set; }
            [StringLength(100)] public string AssessmentPeriodId { get; set; }
            [StringLength(2000)] public string Notes { get; set; }
            public DateTime SubmittedAt { get; set; } = DateTime.UtcNow;
            [Required] public string SubmittedByUserId { get; set; }

            public virtual GenericDataEntryDefinitionEntity EntryDefinition { get; set; }
        }
        ```

## 4. MongoDB Documents (`Models/MongoDocuments/`)

These documents are stored in MongoDB. The `_id` will be an `ObjectId`, mapped to a `string Id` in C#.

### 4.1 Dynamic Criterion Template Document (Epic 2)

* **`DynamicCriterionTemplateDoc`**
    * Collection Name: `DynamicCriterionTemplates`
    * Schema:
        ```csharp
        public class DynamicCriterionTemplateDoc
        {
            [BsonId]
            [BsonRepresentation(BsonType.ObjectId)]
            public string Id { get; set; }

            [BsonRequired] public string Name { get; set; }
            public string Description { get; set; }
            public double? Coefficient { get; set; }
            public int? MaxLimit { get; set; }
            [BsonRequired] public string Status { get; set; } // "Draft", "Active", "Inactive"
            public List<InputFieldDefinitionDoc> InputFields { get; set; } = new();

            public DateTime CreatedAt { get; set; }
            public string CreatedByUserId { get; set; }
            public DateTime UpdatedAt { get; set; }
            public string UpdatedByUserId { get; set; }
        }

        public class InputFieldDefinitionDoc // Embedded document
        {
            [BsonRequired] public string FieldId { get; set; }
            [BsonRequired] public string Label { get; set; }
            [BsonRequired] public string InputType { get; set; }
            public bool IsMandatory { get; set; }
            public string PlaceholderText { get; set; }
            public double? MinValue { get; set; }
            public double? MaxValue { get; set; }
            public string AllowedFileTypes { get; set; }
            public int? MaxFileSizeMb { get; set; }
            public List<string> DropdownOptions { get; set; } = new();
        }
        ```

### 4.2 Submitted Dynamic Data Document (Epic 3)

* **`SubmittedDynamicDataDoc`**
    * Collection Name: `SubmittedPerformanceData`
    * Purpose: Stores each instance of data submitted by an academician against a *dynamic* criterion.
    * Schema:
        ```csharp
        public class SubmittedDynamicDataDoc
        {
            [BsonId]
            [BsonRepresentation(BsonType.ObjectId)]
            public string Id { get; set; } // Unique ID for this specific data entry instance

            [BsonRequired] public string AcademicSubmissionId { get; set; } // Public GUID Id of the parent AcademicSubmissionEntity
            [BsonRequired] public string FormCriterionLinkId { get; set; } // Public GUID Id of the FormCriterionLinkEntity (Dynamic type)
            [BsonRequired] public string AcademicianUniveristyUserId { get; set; }

            public Dictionary<string, object> Data { get; set; } = new(); // Key: InputFieldDefinitionDoc.FieldId

            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        }
        ```

## 5. Notification Queue & Audit Log Models (Epics 6 & 7)

### 5.1 Notification Queue DTO & Entity (Epic 6)

* **`NotificationMessageDto` (Message Payload for RabbitMQ)**
    * Purpose: Represents the data sent to RabbitMQ for the Notification Service to consume.
    * Schema:
        ```csharp
        public class NotificationMessageDto
        {
            [Required]
            public string NotificationType { get; set; } // e.g., "SubmissionReceived", "SubmissionApproved", "SubmissionRejected", "NewSubmissionForController"
            [Required]
            public string RecipientUserId { get; set; } // University User ID of the recipient
            public string RecipientEmail { get; set; } // Optional: Can be fetched by NotificationService if not provided
            public Dictionary<string, string> TemplateData { get; set; } = new(); // Key-value pairs for email template (e.g., "FormName", "AcademicianName", "ControllerComments")
            public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        }
        ```
* **`NotificationQueueLogEntity` (PostgreSQL - Optional: if logging to DB before sending to RabbitMQ or for retry mechanisms within Core Service)**
    * Purpose: To log notification requests before they are reliably sent to RabbitMQ, or to manage retries if RabbitMQ is temporarily unavailable. `Rlx.Shared` RabbitMQ helpers might handle this transparently. If `Rlx.Shared` guarantees delivery or has its own dead-lettering, this APDYS-specific table might not be needed. Assuming for now `Rlx.Shared` handles reliable publishing. If not, this entity would track status.
    * Schema (If Needed):
        ```csharp
        public class NotificationQueueLogEntity
        {
            public int AutoIncrementId { get; set; } // PK
            public string Id { get; private set; } = Guid.NewGuid().ToString(); // Unique Index
            public string NotificationType { get; set; }
            public string RecipientUserId { get; set; }
            public string PayloadJson { get; set; } // Serialized NotificationMessageDto
            public string Status { get; set; } // e.g., "PendingSend", "SentToBroker", "Failed"
            public int RetryCount { get; set; } = 0;
            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public DateTime? LastAttemptAt { get; set; }
            public string ErrorMessage { get; set; }
        }
        ```

### 5.2 Audit Log Model (Epic 7)

* **Structure:** The precise structure of audit log entries will be defined by the `rlx.log` NuGet package or the `RlxSystemLogHelper` from `Rlx.Shared`.
* **Key Information (as per Epic 7.1):**
    * Timestamp of the event.
    * User ID performing the action (University User ID).
    * Type of action/event (e.g., `LOGIN_SUCCESS`, `CRITERIA_CREATED`, `SUBMISSION_APPROVED`).
    * Entity ID related to the action (e.g., Public GUID of the form, submission, criterion).
    * Brief description or key details (e.g., changed fields, values).
    * Outcome (Success/Failure).
* **Storage:** `RlxSystemLogHelper` is expected to forward logs via RabbitMQ to a central log store. APDYS services will produce logs in the format required by this helper. No APDYS-specific database table is planned for storing audit logs directly, as this is a centralized concern handled by `Rlx.Shared` and the university's logging infrastructure. APDYS might have an internal API to *query* these logs if the central store exposes such a capability that `Rlx.Shared` can interface with.

## Change Log

| Change        | Date       | Version | Description                  | Author         |
| ------------- | ---------- | ------- | ---------------------------- | -------------- |
| Initial draft (Core, RBAC, Mongo) | 2025-05-16 | 0.1     | First pass of core DTOs, Entities, Docs. | 1-mimar AI     |
| Added Specialized Workflows | 2025-05-16 | 0.2     | Added DTOs and Entities for Epic 5. | 1-mimar AI     |
| Corrected & Consolidated DTOs | 2025-05-16 | 0.3     | Ensured all DTOs are fully defined. Added Notification & Audit Log sections. | 1-mimar AI     |