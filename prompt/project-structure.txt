# APDYS Project Structure

This document outlines the proposed project structure for the APDYS .NET 8 services (e.g., `Apdys.Core.Api` or `Apdys.Notification.Api`). The structure emphasizes a clean separation of concerns, aligning with the Manager and Repository patterns, and considers the use of the `Rlx.Shared` NuGet package.

## Solution Structure (Conceptual)

```

ApdysSolution/
├── Apdys.sln                                \# Master solution file
│
├── src/
│   ├── Apdys.Core.Api/                    \# Core APDYS Service (ASP.NET Core Web API)
│   │   ├── Apdys.Core.Api.csproj
│   │   └── (folders as detailed below)
│   │
│   ├── Apdys.Notification.Api/            \# Notification Service (ASP.NET Core Web API or Worker Service)
│   │   ├── Apdys.Notification.Api.csproj
│   │   └── (relevant folders like Program.cs, Workers, Services, Dtos)
│   └── (Potentially other future services...)
│
└── tests/
├── Apdys.Core.Api.UnitTests/
│   └── Apdys.Core.Api.UnitTests.csproj
├── Apdys.Core.Api.IntegrationTests/
│   └── Apdys.Core.Api.IntegrationTests.csproj
│
├── Apdys.Notification.Api.UnitTests/
│   └── Apdys.Notification.Api.UnitTests.csproj
└── Apdys.Notification.Api.IntegrationTests/
└── Apdys.Notification.Api.IntegrationTests.csproj

````

## Detailed Folder Structure for `Apdys.Core.Api` (Primary Service)


```plaintext
Apdys.Core.Api/
├── Apdys.Core.Api.csproj                # Project file for the Core API service
├── Apdys.Core.Api.sln                   # Solution file (could be a master solution including all services)
├── appsettings.json                     # Base configuration
├── appsettings.Development.json         # Development-specific configuration
├── appsettings.{Environment}.json       # Environment-specific configurations
├── Program.cs                           # Main application entry point, DI registration, middleware pipeline
├── Dockerfile                           # For containerizing the service
├── README.md                            # Service-specific overview and setup instructions
│
├── Properties/
│   └── launchSettings.json              # Development environment launch settings
│
├── Controllers/                         # API endpoint controllers (ASP.NET Core MVC Controllers)
│   ├── CriteriaController.cs
│   ├── FormsController.cs
│   ├── SubmissionsController.cs
│   ├── UserController.cs                # For /api/users/me (Story 1.5)
│   ├── DepartmentStrategicPerformanceController.cs
│   ├── StaffCompetencyController.cs
│   ├── PortfolioControlController.cs
│   └── GenericDataEntryController.cs
│
├── Models/                              # Contains DTOs, COs (Criteria/Command Objects), and DB Entities/Documents
│   ├── Dtos/                            # Data Transfer Objects for API requests/responses
│   │   ├── UserDtos.cs                  # e.g., UserProfileDto
│   │   ├── CriteriaDtos.cs              # e.g., DynamicCriterionTemplateRequestDto, InputFieldDefinitionDto
│   │   ├── FormDtos.cs                  # e.g., EvaluationFormRequestDto, FormCategoryRequestDto
│   │   ├── SubmissionDtos.cs            # e.g., AcademicianSubmissionRequestDto, EvidenceFileResponseDto
│   │   ├── ControllerDtos.cs            # e.g., SubmissionRejectionRequestDto
│   │   ├── RbacDtos.cs                  # e.g., ApdysRoleResponseDto
│   │   ├── DepartmentStrategicDtos.cs
│   │   ├── StaffCompetencyDtos.cs
│   │   ├── PortfolioControlDtos.cs
│   │   └── GenericDataEntryDtos.cs
│   │
│   ├── Cos/                             # Criteria Objects / Command Objects / Complex Operation parameters
│   │   ├── UserContextCo.cs
│   │   ├── FetchSubmissionsQueryCo.cs
│   │   └── ProcessStaticCriterionValueCo.cs
│   │
│   ├── Entities/                        # EF Core entity classes for APDYS-specific PostgreSQL tables
│   │   ├── ApdysRoleEntity.cs
│   │   ├── ApdysPermissionEntity.cs
│   │   ├── ApdysRolePermissionEntity.cs
│   │   ├── UserApdysRoleMappingEntity.cs
│   │   ├── StaticCriterionDefinitionEntity.cs
│   │   ├── EvaluationFormEntity.cs
│   │   ├── FormCategoryEntity.cs
│   │   ├── FormCriterionLinkEntity.cs
│   │   ├── AcademicSubmissionEntity.cs
│   │   ├── EvidenceFileEntity.cs
│   │   ├── DepartmentStrategicIndicatorDefinitionEntity.cs
│   │   ├── DepartmentStrategicPerformanceDataEntity.cs
│   │   ├── StaffCompetencyDefinitionEntity.cs
│   │   ├── StaffCompetencyEvaluationEntity.cs
│   │   ├── CompetencyRatingEntity.cs
│   │   ├── PortfolioChecklistItemDefinitionEntity.cs
│   │   ├── PortfolioVerificationLogEntity.cs
│   │   ├── GenericDataEntryDefinitionEntity.cs
│   │   └── GenericDataEntryRecordEntity.cs
│   │
│   └── MongoDocuments/                  # Document models for APDYS-specific MongoDB collections
│       ├── DynamicCriterionTemplateDoc.cs
│       └── SubmittedDynamicDataDoc.cs
│
├── Database/                            # Database infrastructure concerns
│   ├── DbContexts/                      # EF Core DbContext classes
│   │   └── ApdysCoreDbContext.cs        # For APDYS-specific PostgreSQL tables
│   │
│   └── Migrations/                      # EF Core migration files for ApdysCoreDbContext
│
├── Managers/                            # Business logic layer (Manager Pattern)
│   ├── ICriteriaManager.cs              # Interfaces for managers
│   ├── CriteriaManager.cs               # Implementations
│   ├── IFormManager.cs
│   ├── FormManager.cs
│   ├── ISubmissionManager.cs
│   ├── SubmissionManager.cs
│   ├── IRbacManager.cs
│   ├── RbacManager.cs
│   ├── IDepartmentStrategicPerformanceManager.cs
│   ├── DepartmentStrategicPerformanceManager.cs
│   ├── IStaffCompetencyManager.cs
│   ├── StaffCompetencyManager.cs
│   ├── IPortfolioControlManager.cs
│   ├── PortfolioControlManager.cs
│   ├── IGenericDataEntryManager.cs
│   └── GenericDataEntryManager.cs
│   └── (Other APDYS-specific managers for specialized workflows...)
│
├── Stores/                              # Data persistence layer (Repository Pattern)
│   ├── ICriteriaStore.cs                # Interfaces for stores/repositories
│   ├── CriteriaStore.cs                 # Implementations using EF Core for PostgreSQL or MongoDB Driver
│   ├── IFormStore.cs
│   ├── FormStore.cs
│   ├── ISubmissionStore.cs
│   ├── SubmissionStore.cs
│   ├── IRbacStore.cs
│   ├── RbacStore.cs
│   ├── IDepartmentStrategicPerformanceStore.cs
│   ├── DepartmentStrategicPerformanceStore.cs
│   ├── IStaffCompetencyStore.cs
│   ├── StaffCompetencyStore.cs
│   ├── IPortfolioControlStore.cs
│   ├── PortfolioControlStore.cs
│   ├── IGenericDataEntryStore.cs
│   └── GenericDataEntryStore.cs
│   └── (Other APDYS-specific stores...)
│
├── Services/                            # Specific business services or clients for external systems (if not in Rlx.Shared)
│   ├── IOrganizationManagementClient.cs # Interface for Arel Org. Mgmt. Module client
│   ├── OrganizationManagementClient.cs  # Implementation
│   └── IFileStorageService.cs           # Interface for handling evidence file uploads/downloads
│   └── FileStorageService.cs            # Implementation for Network File Share
│
├── Extensions/                          # Extension methods for DI, configuration, etc.
│   └── ServiceCollectionExtensions.cs   # For cleaner DI registration in Program.cs
│   └── WebApplicationExtensions.cs      # For middleware registration
│
├── Mappings/                            # Mapster mapping configurations
│   └── ApdysMappingProfile.cs
│
│
├── Constants/                           # Service-specific constants
│   └── ApdysConstants.cs
│
└── Tests/                               # Unit and Integration tests for this service
    ├── Apdys.Core.Api.UnitTests/
    │   ├── Apdys.Core.Api.UnitTests.csproj
    │   ├── Managers/
    │   │   └── CriteriaManagerTests.cs
    │   └── Controllers/
    │       └── CriteriaControllerTests.cs
    └── Apdys.Core.Api.IntegrationTests/
        ├── Apdys.Core.Api.IntegrationTests.csproj
        └── (Integration tests for API endpoints, database interactions, etc.)

---

## Folder Structure for `Apdys.Notification.Api` (Worker/Notification Service)

```plaintext
Apdys.Notification.Api/
├── Apdys.Notification.Api.csproj        # Project file
├── appsettings.json
├── appsettings.Development.json
├── Program.cs                           # DI registration, worker service hosting
├── Dockerfile
├── README.md
│
├── Properties/
│   └── launchSettings.json
│
├── Models/
│   └── Dtos/
│       └── NotificationMessageDto.cs    # DTO for messages consumed from RabbitMQ (defined in data-models.md)
│
├── Workers/                             # Background service(s)
│   └── EmailDispatchWorker.cs           # Consumes from RabbitMQ, uses EmailSender
│
├── Services/                            # Services used by the worker
│   ├── IEmailSender.cs
│   └── EmailSender.cs                   # Uses MailKit and SMTP settings
│   └── IOrgManagementClientMinimal.cs   # If needed for email lookup, might be a shared client or specific impl.
│   └── OrgManagementClientMinimal.cs
│
├── Templates/                           # Email templates (e.g., .txt or .cshtml files if using Razor for emails)
│   ├── SubmissionApprovedEmail.txt
│   └── SubmissionRejectedEmail.txt
│
├── Extensions/
│   └── ServiceCollectionExtensions.cs
│
└── Constants/
    └── NotificationConstants.cs
```