# Academic Performance Evaluation System (APDYS) Technology Stack

## Technology Choices

| Category             | Technology                  | Version / Details                      | Description / Purpose                                                                 | Justification                                                                                                |
| :------------------- | :-------------------------- | :------------------------------------- | :------------------------------------------------------------------------------------ | :----------------------------------------------------------------------------------------------------------- |
| **Languages** | C#                          | 12                                     | Primary backend development language for services.                                    | Mature, high-performance, excellent ecosystem, type-safe, specified by Arel University requirements.           |
| **Runtime** | .NET                        | 8.0 LTS                                | Cross-platform runtime for building and running .NET applications.                    | Long-Term Support (LTS), high performance, modern features, specified by Arel University requirements.       |
| **Frameworks** | ASP.NET Core Web API        | 8.0                                    | Framework for building RESTful APIs with .NET. Utilizes built-in Dependency Injection and model validation attributes. | Lightweight, modular, built-in Dependency Injection & validation, Kestrel web server, ideal for microservices. |
|                      | Entity Framework Core       | 8.0                                    | Object-Relational Mapper (ORM) for data access with PostgreSQL, used within the Repository Pattern. Patterns may be influenced by `Rlx.Shared`. | Simplifies database interactions, LINQ support, migrations, widely used with .NET.                         |
| **Shared Library** | **Rlx.Shared** | **1.0.3 (.NET 8.0)** | Central Arel University library for common microservice code: base classes, DB/Context management, infrastructure services (RabbitMQ, Redis), helpers, middleware, configs, constants. | Drives reusability, consistency, easier maintenance, faster development, and standardized security across services. |
| **Databases** | PostgreSQL                  | Latest Stable (e.g., 16.x at time of deployment) | Primary relational database for APDYS-specific structured data (forms, submissions metadata, APDYS roles, etc.). May leverage `Rlx.Shared` for context patterns. | Robust, open-source, ACID compliant, handles complex queries and relations well, .NET compatible.          |
|                      | MongoDB                     | Latest Stable (e.g., 7.x or 8.x at time of deployment) | NoSQL document database for dynamic criteria definitions and submitted performance data with varying fields. May leverage `Rlx.Shared` Mongo helpers. | Flexible schema, good for dynamic data structures, scalable, good performance for use cases like criteria. |
| **Authentication/Authorization** | OpenID Connect / OAuth 2.0 | Standard Protocols                   | Industry-standard protocols for authentication and authorization. APDYS will integrate with Arel Identity Server as an OIDC Provider. `Rlx.Shared` likely provides base configurations/helpers. | Secure, standardized, enables SSO, widely supported. Specified by Arel University's use of an Identity Server. |
| **Messaging** | RabbitMQ                    | Version compatible with `Rlx.Shared`   | Message broker for asynchronous inter-service communication (e.g., Core APDYS to Notification Service). Utilized via `Rlx.Shared` helpers. | Robust, scalable, reliable message queuing. Preferred due to `Rlx.Shared` support.                      |
| **Libraries (Core APDYS Service & Notification Service will reference Rlx.Shared)** |                         |                                        |                                                                                       |                                                                                                              |
|                      | `Microsoft.AspNetCore.Authentication.JwtBearer` | 8.0.x (Likely managed or configured via `Rlx.Shared` patterns) | Middleware for validating JWTs (access tokens) issued by Arel Identity Server (OIDC Provider). | Standard Microsoft library for JWT/OAuth2 bearer token validation in ASP.NET Core.                         |
|                      | Npgsql (EF Core Provider)   | 8.0.x                                  | Entity Framework Core provider for PostgreSQL.                                        | Official and performant way for EF Core to connect to PostgreSQL.                                            |
|                      | MongoDB.Driver              | Latest Stable (e.g., 2.25.x or newer, or version used by `Rlx.Shared`) | Official .NET driver for MongoDB.                                                     | Provides API for interacting with MongoDB from .NET applications.                                          |
|                      | Mapster                     | Version used by `Rlx.Shared`           | Object-to-object mapping library for DTOs, Entities, etc.                             | Fast, code-first. Standardized choice due to inclusion in `Rlx.Shared`.                                  |
|                      | `rlx.log` / `RlxSystemLogHelper` | University Provided Version / From `Rlx.Shared` | NuGet package/helper for standardized audit and system logging. `RlxSystemLogHelper` likely forwards logs via RabbitMQ. | Mandated by university for audit trails and consistent logging.                                            |
|                      | Swashbuckle.AspNetCore      | Latest Stable (e.g., 6.6.x)            | Library to generate Swagger/OpenAPI documentation for APIs.                           | Essential for API discoverability, testing, and client generation.                                         |
| **Libraries (Notification Service - Specific)** |                         |                                        |                                                                                       |                                                                                                              |
|                      | MailKit                     | Latest Stable (e.g., 4.5.x)            | Cross-platform .NET library for IMAP, POP3, and SMTP.                                 | Robust and feature-rich for sending emails via SMTP, recommended over SmtpClient (obsolete).             |
| **Containerization** | Docker                      | Latest Stable                          | Software platform for building, running, and managing applications in containers.     | Consistent environments, simplifies deployment and scaling, facilitates microservices.                   |
| **Infrastructure (On-Premise)** |                           |                                        |                                                                                       |                                                                                                              |
|                      | Arel Uni. Servers           | As provided                            | Physical or Virtual Machines for hosting services, databases, and RabbitMQ.           | Project requirement for on-premise deployment.                                                             |
|                      | Network File Share (NFS/SMB)| As provided                            | Storage for uploaded evidence files.                                                  | Standard way to handle file storage in on-premise environments.                                            |
|                      | Arel Uni. SMTP Server       | As provided                            | Service for sending outgoing emails.                                                  | Existing university infrastructure for email dispatch.                                                     |
|                      | Arel Uni. Identity Server   | Existing (OIDC Compliant)              | Centralized authentication service acting as the OpenID Connect Provider.             | Existing university infrastructure for identity and access management.                                     |
|                      | Arel Uni. Org. Mgmt. Module | Existing                               | Source for user and organizational data.                                              | Existing university system.                                                                                |
|                      | RabbitMQ Server             | Existing or to be set up on-premise    | Message broker infrastructure.                                                        | Required for inter-service communication as `Rlx.Shared` provides helpers.                               |
|                      | Redis Server (Optional for MVP) | Existing or to be set up on-premise    | In-memory data store for caching, if needed. `Rlx.Shared` provides helpers.       | Can improve performance for frequently accessed data.                                                    |
| **Development Tools**| Visual Studio 2022 / Rider  | Latest                                 | Integrated Development Environments (IDEs) for .NET development.                      | Provides rich features for C# and .NET development.                                                        |
|                      | .NET SDK                    | 8.0.x                                  | Software Development Kit for .NET.                                                    | Required for building, testing, and running .NET applications.                                           |
|                      | Git                         | Latest                                 | Distributed version control system.                                                   | Standard for source code management.                                                                       |
|                      | Postman / Insomnia          | Latest                                 | API testing and development tools.                                                    | Useful for manually testing API endpoints during development.                                            |

**Architectural Patterns Confirmed:**

* **Repository Pattern:** Implemented via a `Stores` layer for data abstraction, potentially using base classes/patterns from `Rlx.Shared`.
* **Manager Pattern:** Business logic will reside in `Manager` classes, utilizing `Stores`, DTOs from `Rlx.Shared` where applicable, and other services.
* **Dependency Injection (DI):** Leveraged throughout using ASP.NET Core's built-in DI container, with services/factories potentially provided by `Rlx.Shared`.
* **Model Validation:** Using built-in .NET data annotation attributes (e.g., `[Required]`, `[StringLength]`, `[Range]`) on DTOs (some DTOs might come from `Rlx.Shared`) and models, validated by ASP.NET Core framework.
* **Object Mapping:** Mapster will be used for object-to-object mapping, as it's a dependency of `Rlx.Shared`.
* **Asynchronous Messaging:** RabbitMQ will be used for inter-service communication, facilitated by helpers in `Rlx.Shared`.

**Notes:**

* **API Gateway:** Not included in MVP to simplify on-premise deployment. Can be added later if needed (e.g., Ocelot, or an Arel Uni standard if available).
* **CI/CD Tools:** To be defined in conjunction with Arel University IT.
* The `rlx.log` package mentioned in PRD and the `RlxSystemLogHelper` from `Rlx.Shared` are assumed to be complementary or the latter being the implementation standard.
* An on-premise RabbitMQ instance will be required. If Redis is to be used for caching later, an on-premise instance for that will also be needed.
* Specific versions for "Latest Stable" (PostgreSQL, MongoDB, etc.) will be pinned down at the start of development. Versions for libraries from `Rlx.Shared` (Mapster, EF Core, MongoDB.Driver, RabbitMQ.Client, StackExchange.Redis) will be dictated by `Rlx.Shared` version 1.0.3.