# APDYS Environment Variables

## Configuration Loading Mechanism

Configuration for APDYS services is loaded from multiple sources in a hierarchical manner, standard to ASP.NET Core applications:

1.  **`appsettings.json`**: Base configuration, included in the source code.
2.  **`appsettings.{Environment}.json`**: Environment-specific overrides (e.g., `appsettings.Development.json`, `appsettings.Production.json`). The `{Environment}` is determined by the `ASPNETCORE_ENVIRONMENT` variable.
3.  **User Secrets (Development only)**: For sensitive data during local development, managed via the .NET Secret Manager tool.
4.  **Environment Variables**: Values set in the operating environment override previous sources. This is the primary method for configuring deployed environments (Dev, Staging, Production on Arel University on-premise servers).
5.  **Command-line arguments**: Can also override settings.

The `Rlx.Shared` package may also provide standardized ways to load or access certain shared configurations.

## Required & Optional Variables

The following tables list the environment variables used by the APDYS services. Variables marked as "Sensitive: Yes" should be managed securely (e.g., injected by CI/CD, stored in a secure vault provided by Arel IT) and NOT hardcoded or committed to version control in plain text, except for local development using User Secrets or non-sensitive placeholders in `appsettings.Development.json`.

### Core APDYS Service (`Apdys.Core.Api`)

| Variable Name                      | Description                                                                 | Example / Default Value                                  | Required? | Sensitive? | Notes                                                                   |
| :--------------------------------- | :-------------------------------------------------------------------------- | :------------------------------------------------------- | :-------- | :--------- | :---------------------------------------------------------------------- |
| `ASPNETCORE_ENVIRONMENT`           | Runtime environment.                                                        | `Development`, `Staging`, `Production`                   | Yes       | No         | Controls which `appsettings.{Env}.json` is loaded, logging levels, etc. |
| `ASPNETCORE_URLS`                  | URLs the Kestrel web server will listen on.                                 | `http://+:5000`                                          | No        | No         | Typically set by hosting environment.                                   |
| **Connection Strings** |                                                                             |                                                          |           |            |                                                                         |
| `ConnectionStrings__ApdysPostgresDb` | Connection string for the APDYS PostgreSQL database.                      | `Host=localhost;Port=5432;Database=ApdysDb;Username=apdys_user;Password=xxxxxx` | Yes       | Yes        |                                                                         |
| `ConnectionStrings__ApdysMongoDb`    | Connection string for the APDYS MongoDB database.                           | `mongodb://localhost:27017/ApdysDynamicData`             | Yes       | Yes        |                                                                         |
| `ConnectionStrings__RabbitMq`        | Connection string for RabbitMQ.                                             | `amqp://guest:guest@localhost:5672/`                     | Yes       | Yes        | Used for publishing notification messages.                              |
| **Identity Server Integration** |                                                                             |                                                          |           |            | (OIDC/OAuth2 Configuration)                                             |
| `IdentityServer__Authority`        | URL of the Arel University Identity Server (Issuer URI).                    | `https://identity.arel.edu.tr`                           | Yes       | No         |                                                                         |
| `IdentityServer__Audience`         | Audience claim value APDYS API expects in JWTs.                             | `apdys_api` (or specific value from Identity Server config) | Yes       | No         |                                                                         |
| `IdentityServer__RequireHttpsMetadata` | Whether HTTPS is required for metadata endpoint (true for production).    | `true`                                                   | No        | No         | Default is true.                                                        |
| **Organization Management Module** |                                                                             |                                                          |           |            |                                                                         |
| `OrgManagement__ApiBaseUrl`        | Base URL for the Arel University Organization Management Module API.        | `https://orgmgmt.arel.edu.tr/api`                        | Yes       | No         | If accessed via API.                                                    |
| `OrgManagement__ApiKey`            | API Key for accessing the Org. Management Module, if required.              | `xxxxxx-xxxx-xxxx-xxxx-xxxxxxxx`                         | Maybe     | Yes        | Depends on Org. Mgmt. module's auth mechanism.                          |
| `OrgManagement__DbConnectionString`| Connection string if direct DB access is used (alternative to API).         | (PostgreSQL connection string)                           | Maybe     | Yes        | Use with caution, API preferred.                                        |
| **File Storage** |                                                                             |                                                          |           |            |                                                                         |
| `FileStorage__EvidencePath`        | Absolute path to the root directory on the network file share for evidence. | `/mnt/apdys_evidence_files`                              | Yes       | No         | Server must have read/write access.                                     |
| **Logging (`rlx.log` / `Rlx.Shared`)** |                                                                             |                                                          |           |            |                                                                         |
| `Logging__LogLevel__Default`       | Default minimum log level.                                                  | `Information` (`Debug` for Development)                | No        | No         |                                                                         |
| `RlxShared__Logging__RabbitMq__HostName` | Hostname for RabbitMQ if `RlxSystemLogHelper` uses it for log forwarding. | `rabbitmq.arel.internal`                                 | Maybe     | No         | If `Rlx.Shared` uses env vars for its internal RabbitMQ config.         |
| `RlxShared__Logging__RabbitMq__UserName` | Username for RabbitMQ log forwarding.                                     | `log_user`                                               | Maybe     | Yes        |                                                                         |
| `RlxShared__Logging__RabbitMq__Password` | Password for RabbitMQ log forwarding.                                     | `xxxxxx`                                                 | Maybe     | Yes        |                                                                         |
| **API Settings** |                                                                             |                                                          |           |            |                                                                         |
| `ApiSettings__CorsAllowedOrigins`  | Comma-separated list of allowed origins for CORS.                           | `https://apdys-frontend.arel.edu.tr,http://localhost:3000` | Maybe     | No         | Required if a separate frontend application will call the API.          |

### Notification Service (`Apdys.Notification.Api`)

| Variable Name                      | Description                                                                 | Example / Default Value                                  | Required? | Sensitive? | Notes                                                                   |
| :--------------------------------- | :-------------------------------------------------------------------------- | :------------------------------------------------------- | :-------- | :--------- | :---------------------------------------------------------------------- |
| `ASPNETCORE_ENVIRONMENT`           | Runtime environment.                                                        | `Development`, `Staging`, `Production`                   | Yes       | No         |                                                                         |
| `ASPNETCORE_URLS`                  | URLs the Kestrel web server will listen on (if it exposes any health/mgmt API). | `http://+:5001`                                          | No        | No         | Might not be needed if it's purely a background worker.                 |
| **Connection Strings** |                                                                             |                                                          |           |            |                                                                         |
| `ConnectionStrings__RabbitMq`        | Connection string for RabbitMQ.                                             | `amqp://guest:guest@localhost:5672/`                     | Yes       | Yes        | Used for consuming notification messages.                               |
| **SMTP Server Details** |                                                                             |                                                          |           |            |                                                                         |
| `Smtp__Host`                       | Hostname or IP address of the Arel University SMTP server.                  | `smtp.arel.edu.tr`                                       | Yes       | No         |                                                                         |
| `Smtp__Port`                       | Port for the SMTP server.                                                   | `587` (for TLS) or `25` (less common now)                | Yes       | No         |                                                                         |
| `Smtp__EnableSsl`                  | Whether to use SSL/TLS for SMTP connection.                                 | `true`                                                   | Yes       | No         | Strongly recommended.                                                   |
| `Smtp__Username`                   | Username for SMTP authentication.                                           | `<EMAIL>`                             | Yes       | Yes        |                                                                         |
| `Smtp__Password`                   | Password for SMTP authentication.                                           | `xxxxxx`                                                 | Yes       | Yes        |                                                                         |
| `Smtp__FromAddress`                | Default "From" email address for notifications.                             | `<EMAIL>`                              | Yes       | No         |                                                                         |
| `Smtp__FromName`                   | Default "From" display name for notifications.                              | `Arel APDYS Notifier`                                    | No        | No         |                                                                         |
| **Logging (`rlx.log` / `Rlx.Shared`)** |                                                                             |                                                          |           |            |                                                                         |
| `Logging__LogLevel__Default`       | Default minimum log level.                                                  | `Information` (`Debug` for Development)                | No        | No         |                                                                         |
| `RlxShared__Logging__RabbitMq__HostName` | Hostname for RabbitMQ if `RlxSystemLogHelper` uses it for log forwarding. | `rabbitmq.arel.internal`                                 | Maybe     | No         | If `Rlx.Shared` uses env vars for its internal RabbitMQ config.         |
| `RlxShared__Logging__RabbitMq__UserName` | Username for RabbitMQ log forwarding.                                     | `log_user`                                               | Maybe     | Yes        |                                                                         |
| `RlxShared__Logging__RabbitMq__Password` | Password for RabbitMQ log forwarding.                                     | `xxxxxx`                                                 | Maybe     | Yes        |                                                                         |

## Notes

* **Prefixing for Multiple Instances:** If multiple instances of APDYS services (e.g., for different faculties or a parallel staging environment on shared infra) were to be run, consider prefixing environment variables (e.g., `APDYS_CORE_ConnectionStrings__ApdysPostgresDb`) to avoid collisions, although for a single on-premise deployment this might be overkill.
* **`Rlx.Shared` Configuration:** The `Rlx.Shared` package may have its own conventions for environment variables or configuration sections (e.g., for its RabbitMQ helpers, Redis helpers, common logging). These should be documented by `Rlx.Shared` and will be used by APDYS services. The variables listed with `RlxShared__` prefix are assumptions if `Rlx.Shared` components are configured this way.
* **`.env.example` File:** An `.env.example` file (or equivalent in `appsettings.Development.json` with placeholder values) SHOULD be maintained in the repository for each service to guide developers in setting up their local environment. This file MUST NOT contain real sensitive credentials.
* **Configuration Validation:** At application startup, services SHOULD validate the presence and basic format of critical required environment variables/configurations to fail fast if the environment is misconfigured.

## Change Log

| Change        | Date       | Version | Description                  | Author         |
| ------------- | ---------- | ------- | ---------------------------- | -------------- |
| Initial draft | 2025-05-16 | 0.1     | Initial draft of environment variables. | 1-mimar AI     |