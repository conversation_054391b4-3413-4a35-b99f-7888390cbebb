# Epic 6: User Email Notification System

**Goal:** Implement a reliable and timely user email notification system to inform users about critical events, status changes, and actions required within their APDYS workflows, ensuring user engagement and smooth process flow. All notifications will be sent via email and are considered mandatory.

## Story List

### Story 6.1: Notification Service Core Implementation (Email Focused)

- **User Story / Goal:** As a Developer, I need a core notification service/module that can queue and process email notification requests, so that APDYS can reliably send email notifications for various system events.
- **Detailed Requirements:**
  - Design and implement a notification service that can be triggered by other services within APDYS.
  - This service will manage a queue of pending email notifications.
  - Define a clear interface or event structure for triggering email notifications.
  - Implement logging for email notification attempts, successes, and failures.
  - Requires configuration for SMTP server details (for email notifications) to be provided by Arel University IT.
- **Acceptance Criteria (ACs):**
  - **AC1:** A notification event can be successfully published by another service and received by the notification service for email dispatch.
  - **AC2:** The notification service can process an email notification request from a queue.
  - **AC3:** Logging is implemented for email dispatch attempts, successes, and failures.
  - **AC4:** The service is focused and optimized for email delivery.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Create NotificationService with INotificationManager and INotificationStore interfaces.
  - [ ] Implement NotificationManager and NotificationStore services.
  - [ ] Create Notification entity model in PostgreSQL for tracking notification status.
  - [ ] Implement event-based notification system using a message queue.
  - [ ] Create DTOs and Mapster configurations for notifications.
  - [ ] Configure logging for notification attempts and results.
  - [ ] Implement retry mechanism for failed notifications.

---

### Story 6.2: Email Notification Dispatcher

- **User Story / Goal:** As the System, I need to be able to send email notifications to users, so that they receive important updates.
- **Detailed Requirements:**
  - Implement functionality to connect to an SMTP server (details to be provided by Arel University IT).
  - Format and send emails based on predefined templates (content will vary by notification type).
  - User email addresses will be retrieved from the Organization Management module via their user profile.
  - Handle SMTP connection errors and email sending failures gracefully.
  - Ensure emails are professional, clearly indicate they are from Arel University APDYS, and state that they are mandatory system communications.
- **Acceptance Criteria (ACs):**
  - **AC1:** The system can successfully send a test email to a specified address via the configured SMTP server.
  - **AC2:** Email sending failures (e.g., invalid address, SMTP server issue) are logged.
  - **AC3:** User's correct email address is used for sending notifications.
  - **AC4:** Email content is templated and can be customized based on the notification type.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Integrate .NET email library (e.g., MailKit) in EmailService.
  - [ ] Implement SMTP client configuration and connection logic.
  - [ ] Create email templates using RazorLight or similar templating engine.
  - [ ] Implement IEmailManager and IEmailStore interfaces.
  - [ ] Create EmailTemplate entity model in PostgreSQL for storing email templates.
  - [ ] Add localization support for email templates.
  - [ ] Implement error handling and logging for email sending operations.
  - [ ] Create DTOs and Mapster configurations for email templates.

---

### Story 6.3: Notifications for Academician Submission Workflow (Email Only)

- **User Story / Goal:** As an Academician, I want to receive email notifications when my submission is successfully submitted, approved, or rejected, so that I am kept informed of its status.
- **Detailed Requirements:**
  - Trigger email notification to Academician upon:
    - Successful final submission of an evaluation form (confirmation).
    - Submission approval by a Controller.
    - Submission rejection by a Controller (including rejection comments in the email).
  - Email content should be clear, specify the form name, and the new status.
- **Acceptance Criteria (ACs):**
  - **AC1:** Academician receives an email notification when their evaluation form is successfully submitted.
  - **AC2:** Academician receives an email notification when their submission is approved by a Controller.
  - **AC3:** Academician receives an email notification, including controller comments, when their submission is rejected.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Define notification triggers within Epic 3 (Academician Workflow) logic.
  - [ ] Create email content templates for academician-specific notifications.
  - [ ] Implement integration between SubmissionManager and NotificationService.
  - [ ] Create NotificationTemplate entity models for different submission statuses.
  - [ ] Add localization support for academician notification templates.
  - [ ] Implement logic to retrieve user email from OrganizationManagement.
  - [ ] Add audit logging for notification events.

---

### Story 6.4: Notifications for Controller Workflow (Email Only)

- **User Story / Goal:** As a Controller, I want to receive email notifications when a new academician submission requires my review, so that I can act on it promptly.
- **Detailed Requirements:**
  - Trigger email notification to relevant Controller(s) when:
    - An Academician submits an evaluation form that falls under their review purview.
  - Email content should specify the Academician's name and the form name.
  - For MVP, a single email notification is sent per new submission relevant to the controller.
- **Acceptance Criteria (ACs):**
  - **AC1:** Relevant Controller(s) receive an email notification when a new submission matching their assignment criteria is made.
  - **AC2:** Email notification includes Academician name and form name.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Define notification triggers within Epic 4 (Controller Workflow) logic.
  - [ ] Create email content templates for controller-specific notifications.
  - [ ] Implement integration between ControllerManager and NotificationService.
  - [ ] Create NotificationTemplate entity models for new submission notifications.
  - [ ] Add localization support for controller notification templates.
  - [ ] Implement logic to determine which controllers should receive notifications.
  - [ ] Add audit logging for controller notification events.

---

### Story 6.5: Notifications for Specialized Workflows (Email Only)

- **User Story / Goal:** As a User involved in a specialized workflow (Strategic Management, Manager, Archivist, Other Data Entry), I want to receive email notifications relevant to tasks assigned to me or status changes in data I manage, so that I can stay on top of my responsibilities.
- **Detailed Requirements:**
  - Identify critical email notification points within each specialized workflow defined in Epic 5. Examples:
    - Strategic Management Office: Email notification if a department's strategic data is due or overdue.
    - Manager: Email notification when a staff competency evaluation is due or submitted by them.
    - Archivist: Email notification when new portfolios require EBYS verification or when a verification they performed is queried.
    - Other Data Entry Roles: Email notification for confirmations of their data submissions or if issues are found.
  - Implement triggers and content for these email notifications.
- **Acceptance Criteria (ACs):**
  - **AC1:** Users in specialized workflows receive defined critical email notifications.
  - **AC2:** Email notification content is tailored to the specific specialized workflow event.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Review Epic 5 workflows for critical email notification points.
  - [ ] Define and implement email notifications for specialized events.
  - [ ] Create email content templates for each specialized workflow.
  - [ ] Implement integration between specialized managers and NotificationService.
  - [ ] Create NotificationTemplate entity models for specialized workflow notifications.
  - [ ] Add localization support for specialized notification templates.
  - [ ] Implement role-based notification routing for specialized workflows.
  - [ ] Add audit logging for specialized workflow notification events.

## Change Log

| Change | Date | Version | Description | Author |
| ------ | ---- | ------- | ----------- | ------ |
| Initial Draft | 2025-05-15 | 0.1 | First draft of Epic 6 stories. | 1‑pm AI |
| Revised | 2025-05-15 | 0.2 | Focused on mandatory email notifications only. | 1‑pm AI |
