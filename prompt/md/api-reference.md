### 5\. `api-reference.md` (API Reference Documentation)

# APDYS API Reference

## 1. Introduction

This document provides an overview of the API design principles, conventions, authentication, error handling, and versioning strategy for the Academic Performance Evaluation System (APDYS). The APDYS system primarily exposes APIs through its **Core APDYS Service**.

Detailed endpoint specifications, request/response schemas, and interactive testing capabilities are available via the Swagger/OpenAPI documentation UI, automatically generated for each service (e.g., `/swagger` endpoint in development environments). All APIs are designed to be RESTful.

This document also outlines interactions with key external university systems that APDYS integrates with.

## 2. General API Design Principles & Conventions

* **Protocol:** All API communication MUST use HTTPS.
* **Base Path:** APIs exposed by the Core APDYS Service will be available under a versioned base path, e.g., `/api/v1/`.
* **Request/Response Format:** All request and response bodies MUST be in JSON (`application/json`).
* **HTTP Methods:** Standard HTTP methods (verbs) are used:
    * `GET`: Retrieve resources or collections of resources.
    * `POST`: Create new resources.
    * `PUT`: Update existing resources (e.g., full replacement or changing state like activation/deactivation).
* **Idempotency:** `GET` and `PUT` operations MUST be idempotent. `POST` operations are typically not idempotent.
* **Naming Conventions:**
    * Resource paths: Use `kebab-case` for multi-word path segments (e.g., `/evaluation-forms`). Plural nouns are preferred for resource collections.
    * JSON properties: Use `camelCase`.
* **Date/Time Format:** All dates and times in API requests and responses MUST be in ISO 8601 format and UTC (e.g., `YYYY-MM-DDTHH:mm:ss.sssZ`).
* **Pagination:** For collections that can grow large, pagination WILL be supported using query parameters (e.g., `pageNumber=1&pageSize=20`). Responses for paginated data SHOULD include pagination metadata (total items, total pages, current page, page size). (This will be implemented as needed per endpoint).
* **Sorting & Filtering:** Endpoints returning collections MAY support sorting (e.g., `sortBy=submissionDate&sortOrder=desc`) and filtering based on specific field values. These will be documented per endpoint in Swagger.

## 3. Authentication and Authorization

* **Authentication:**
    * All protected API endpoints require authentication. [cite: 16]
    * APDYS integrates with Arel University's Identity Server, which acts as an OpenID Connect (OIDC) / OAuth 2.0 Provider. [cite: 15, 442]
    * Clients (e.g., a frontend application) MUST obtain a JWT (JSON Web Token) access token from the Identity Server. [cite: 15]
    * This JWT access token MUST be included in the `Authorization` header of API requests using the `Bearer` scheme:
        ```
        Authorization: Bearer <jwt_access_token>
        ```
    * The Core APDYS Service validates the JWT using the `Microsoft.AspNetCore.Authentication.JwtBearer` middleware, checking its signature, issuer, audience, and expiry. User information (ID, potentially initial roles) will be extracted from the validated token. [cite: 15, 17]
* **Authorization:**
    * Once authenticated, authorization is performed based on the user's roles and permissions defined within APDYS (RBAC). [cite: 40]
    * User roles and detailed attributes (like academic cadre, department) are fetched from the Arel University Organization Management module after initial token validation. [cite: 24, 26]
    * APIs will enforce permissions based on these roles (e.g., only an `APDYS_Admin` can manage criteria).
    * If authorization fails, the API will respond with a `403 Forbidden` status code. [cite: 398]
    * Specific permissions required for each endpoint will be detailed in the Swagger documentation.
* **`Rlx.Shared` Package:** Authentication and authorization mechanisms will leverage helpers and base configurations provided by the `Rlx.Shared` package for consistency with Arel University's microservice ecosystem.

## 4. Standard Error Response Format

As per Epic 7 (Story 7.2)[cite: 397], all API error responses will follow a standardized JSON structure. `Rlx.Shared` may provide a base error model or middleware for this.

**Example Error Response (`4xx` or `5xx` status codes):**

```json
{
  "type": "[https://tools.ietf.org/html/rfc7231#section-6.5.1](https://tools.ietf.org/html/rfc7231#section-6.5.1)", // Link to HTTP status code spec, or custom error type URI
  "title": "One or more validation errors occurred.", // General error message
  "status": 400, // HTTP status code
  "traceId": "00-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx-yyyyyyyyyyyyyyyy-00", // Correlation ID for logging
  "errors": { // Optional: Detailed error messages, especially for validation
    "propertyName1": [
      "The propertyName1 field is required.",
      "Another error for propertyName1."
    ],
    "propertyName2": [
      "Error message for propertyName2."
    ],
    "general": [ // For errors not specific to a property
        "A general error message."
    ]
  }
}
````

  * For `500 Internal Server Error`, the `errors` object might be omitted or contain a generic message to avoid exposing sensitive details. The `traceId` is crucial for diagnostics. [cite: 400]
  * .NET validation attributes on DTOs will automatically populate the `errors` field for `400 Bad Request` responses when model validation fails. [cite: 400, 405]

## 5\. API Versioning Strategy

  * **URL-based versioning** will be used. [cite: 483]
  * The version will be included in the API base path: e.g., `/api/v1/resource`.
  * The initial version for the MVP will be `v1`.

## 6\. Core APDYS Service - Main API Resources Overview

The Core APDYS Service will expose several groups of endpoints related to the main entities and workflows. The Swagger documentation will provide full details. High-level resources include:

  * **Authentication & User Context:**
      * Endpoints related to token validation (handled by middleware).
      * `/api/v1/users/me` (`GET`): Retrieves profile information for the currently authenticated user. [cite: 48]
  * **Criteria Management (Admin):**
      * `/api/v1/criteria-templates` (`GET`, `POST`, `PUT`): Dynamic Criteria CRUD. [cite: 61]
      * `/api/v1/static-criteria` (`GET`, `PUT` to activate/deactivate). [cite: 88, 90]
  * **Form Management (Admin):**
      * `/api/v1/evaluation-forms` (`GET`, `POST`, `PUT`): CRUD for forms. [cite: 98]
      * `/api/v1/evaluation-forms/{formId}/categories` (`GET`, `POST`, `PUT`): CRUD for categories within a form. [cite: 108, 111]
      * `/api/v1/evaluation-forms/{formId}/categories/{categoryId}/criteria` (`POST`, `PUT`): Assign/manage criteria within categories. [cite: 121, 123]
  * **Academician Data Submission:**
      * `/api/v1/academician/forms` (`GET`): View assigned forms. [cite: 137]
      * `/api/v1/academician/submissions` (`GET`, `POST`, `PUT`): Create, update (drafts), submit, view submissions. [cite: 158, 184, 195]
      * `/api/v1/academician/submissions/{submissionId}/evidence` (`POST`): Upload evidence files. [cite: 172]
  * **Controller Data Verification:**
      * `/api/v1/controller/submissions` (`GET`): View pending submissions, get submission details. [cite: 208, 219]
      * `/api/v1/controller/submissions/{submissionId}/approve` (`POST` or `PUT`): Approve a submission. [cite: 230]
      * `/api/v1/controller/submissions/{submissionId}/reject` (`POST` or `PUT`): Reject a submission. [cite: 238]
  * **Specialized Evaluation Workflows (Data Entry):**
      * `/api/v1/strategic-performance-data` (`GET`, `POST`, `PUT`). [cite: 264]
      * `/api/v1/staff-competency-evaluations` (`GET`, `POST`, `PUT`). [cite: 283]
      * `/api/v1/portfolio-control-verifications` (`GET`, `POST`, `PUT`). [cite: 300]
      * `/api/v1/generic-data-entries/{entryType}` (`GET`, `POST`, `PUT`). [cite: 317]
  * **RBAC Management (Admin - if exposed via API, otherwise internal or script-based for MVP):**
      * Potentially endpoints for managing APDYS-specific roles and permissions. [cite: 36, 38]
  * **System Essentials:**
      * `/health` (`GET`): Health check endpoint. [cite: 409]

## 7\. External APIs & Integrations

APDYS interacts with the following existing Arel University systems. These are primarily for authentication, data retrieval, or system services rather than consuming business-logic APIs from third parties.

### 7.1 Arel University Identity Server

  * **Purpose:** Authenticates users and issues JWT access tokens (OIDC Provider). [cite: 14, 442]
  * **Interaction:** APDYS Core Service (specifically the `JwtBearer` authentication middleware) validates tokens issued by this server. Configuration will include issuer URI, audience, and public keys (or discovery endpoint). This is a standard OIDC/OAuth2 interaction. [cite: 15, 18]
  * **Protocols:** OpenID Connect, OAuth 2.0.

### 7.2 Arel University Organization Management Module

  * **Purpose:** Provides user details (name, email, Arel roles, academic cadre, department) based on user identifiers from the JWT. [cite: 24, 26]
  * **Interaction:** The Core APDYS Service will have a client component (e.g., `OrganizationManagementClient`) to query this module (likely via an internal API or direct database query if permitted and designed that way by Arel IT). [cite: 25]
  * **API Details:** (To be provided by Arel University IT - endpoint, authentication method for APDYS to call this module). If this module doesn't have an API, APDYS might need direct read access to its database, which should be clarified.

### 7.3 Arel University SMTP Server

  * **Purpose:** Used by the APDYS Notification Service to send emails. [cite: 334, 343]
  * **Interaction:** The Notification Service will connect to this SMTP server using provided credentials and send emails. [cite: 343]
  * **Configuration:** Host, port, username, password (to be stored securely).

### 7.4 `rlx.log` / `RlxSystemLogHelper` Infrastructure

  * **Purpose:** Centralized logging and audit trail. [cite: 384]
  * **Interaction:** APDYS services use `RlxSystemLogHelper` (from `Rlx.Shared`), which is assumed to handle log formatting and forwarding (e.g., via RabbitMQ to a central log store) as per `Rlx.Shared` design. Audit events will be logged using this mechanism. [cite: 385]

## 8\. Swagger / OpenAPI Documentation

  * Each service (Core APDYS, Notification Service if it exposes any management API) will expose an OpenAPI specification at `/swagger/v1/swagger.json`. [cite: 414]
  * An interactive UI will be available at `/swagger` in development environments. [cite: 414]
  * XML documentation comments in the C\# code (Controllers, DTOs) will be used to enrich the generated OpenAPI specification with descriptions, summaries, and examples. [cite: 414]

## Change Log

| Change        | Date       | Version | Description                  | Author         |
| ------------- | ---------- | ------- | ---------------------------- | -------------- |
| Initial draft | 2025-05-16 | 0.1     | Initial draft of API Reference conventions. | 1-mimar AI     |
| Revised HTTP Methods | 2025-05-16 | 0.2     | Simplified HTTP methods to GET, POST, PUT as per user feedback. | 1-mimar AI     |
