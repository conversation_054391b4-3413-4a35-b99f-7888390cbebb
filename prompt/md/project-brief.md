# Project Brief: Academic Performance Evaluation System (APDYS)

## Introduction / Problem Statement

Arel University requires a comprehensive and dynamic system to measure the performance of its academic staff. The **Academic Performance Evaluation System (APDYS)** will address this need by:

- Providing a platform for defining performance criteria.
- Allowing academicians and other designated personnel to submit relevant data and evidence.
- Enabling a review & approval workflow.
- Facilitating performance reporting.

The system must be flexible enough to adapt to varying academic roles and evolving evaluation metrics while integrating with existing university systems for identity management and organizational data to streamline processes and ensure data consistency.

## Vision & Goals

- **Vision:** Establish a transparent, efficient, and adaptable digital system for the continuous evaluation of academic performance at Arel University, fostering professional growth and supporting institutional excellence.

- **Primary Goals (MVP):**
  - **Core Admin Functionality:** Admins can create, configure, and manage dynamic / static performance criteria, criteria forms (tailored to academic cadres), and weighted categories.
  - **Academician Data Entry:** Academicians can log in, view role‑specific criteria, and submit performance data with supporting evidence.
  - **Controller Review & Approval:** Controllers can review, approve, or reject submissions with comments.
  - **Essential Integrations:** Integrate seamlessly with the Identity Server for login and the Organization Management module for user details & roles.
  - **Specialized Evaluation Workflows:** Provide basic data entry & processing for Department Strategic Performance, Academic Staff Competency, and Portfolio Control.

- **Success Metrics (Initial Ideas):**
  - Percentage of academic staff actively submitting data within evaluation periods.
  - Average turnaround time from submission to controller review.
  - Admin satisfaction with criteria / form management tools.
  - Reliability of data exchange with integrated systems.
  - Accuracy of aggregated scores once reporting is enabled.

## Target Audience / Users

| Role | Responsibility |
| --- | --- |
| **Admin** | Configure criteria, forms, categories, and dynamic properties |
| **Academician** | Enter performance data and upload evidence |
| **Controller User** | Validate and approve / reject submissions |
| **Strategic Management Office Staff** | Enter Department Strategic Performance data |
| **Academician’s Manager** (Dean / HoD) | Evaluate staff for Academic Staff Competency |
| **Archivist** | Verify portfolio submissions for Portfolio Control |
| **Other Data Entry Roles** | Contribute data for library, TTO, Erasmus, etc. |

## Key Features / Scope (High‑Level Ideas for MVP)

### Module 1 – Core System & Authentication
- Token‑based authentication via external Identity Server.
- Role & user data retrieval from Organization Management.
- Internal RBAC & user‑context handling.

### Module 2 – Criteria Management (Admin)
- Create / edit dynamic criteria templates (name, coefficient, limits, permissions, etc.).
- Define custom typed inputs (text, number, date, file).
- Activate / deactivate static criteria.

### Module 3 – Form & Category Management (Admin)
- Build criteria forms linked to academic cadres.
- Manage weighted categories within forms.
- Assign criteria to categories with drag‑and‑drop ordering.

### Module 4 – Academician Data Submission
- Personalized dashboard with assigned form & categories.
- Input interfaces + evidence upload.
- Draft save & final submission.

### Module 5 – Data Verification (Controller)
- Dashboard of pending submissions.
- Review interface with evidence preview.
- Approve / reject with comments.

### Module 6 – Specialized Workflows
- **Department Strategic Performance** (Strategic Management Office).
- **Academic Staff Competency** (Managers).
- **Portfolio Control** (Archivists with EBYS check tracking).

### Module 7 – Reporting & Aggregation (Future)
- Data structures prepared for future score calculation and reporting.

## Known Technical Constraints or Preferences

- **Development Platform:** .NET 8 (backend).
- **Architecture:** Microservices; APDYS is a module in a larger ecosystem.
- **Authentication:** Must use existing Identity Server (token‑based).
- **User Data Source:** Must integrate with Organization Management software.
- **EBYS Interaction:** Archivists record verification status manually; no EBYS API connection for MVP.
- **Data Storage:** Relational DB supporting dynamic inputs & relationships.

## Risks

- **Integration Complexity** with Identity Server & Organization Management.
- **Dynamic Configuration UI/UX**—balancing power vs. usability for admins.
- **Workflow Management** for distinct data‑entry & approval processes.
- **Data Integrity & Versioning** when criteria/forms evolve.
- **Scalability & Performance** during peak evaluation periods.

## Relevant Research (Optional)

N/A — requirements are internal to Arel University.

## PM Prompt

# PM Agent Handoff Prompt: Academic Performance Evaluation System (APDYS)

## Summary of Key Insights

- **Project Scope**: Develop a **.NET 8** microservices–based backend system that delivers a dynamic, flexible, and integrated platform for measuring Arel University academic staff performance.  
- **Primary Market Need**: Replace manual or fragmented evaluation processes with a structured, digital, and auditable solution.  
- **Target Users**:  
  - **Admins** – configure criteria, forms, categories, dynamic inputs.  
  - **Academicians** – submit performance data & evidence.  
  - **Controllers** – review and approve/reject submissions.  
  - **Specialized Roles** – Strategic Management Office staff, Managers (Deans/HoDs), Archivists, and other staff (Library, TTO, etc.).  
- **Key Value Proposition**: High configurability via dynamic criteria creation, tight integration with existing university systems, and tailored workflows for diverse roles.  
- **Core Problem Solved**: Ensure complex evaluations are **consistent, transparent, auditable, and efficient**, while remaining adaptable to evolving requirements.

## Areas Requiring Special Attention

1. **Dynamic Criteria & Input Configuration**  
   - Robust admin UI/logic for creating criteria, defining input types (text, number, date, file) + constraints, and managing their lifecycle.

2. **System Integrations**  
   - **Identity Server** – seamless, secure token validation.  
   - **Organization Management** – reliable retrieval of user details, roles, cadres, departments.  
   - **EBYS (Portfolio Control)** – manual verification workflow; APDYS must accurately display required course docs and record Archivist status.

3. **Role‑Based Access Control (RBAC)**  
   - Granular permissions ensuring each role sees only relevant data and actions.

4. **Specialized Evaluation Workflows**  
   - Department Strategic Performance, Academic Staff Competency, Portfolio Control—all with distinct UIs and processes.

5. **Data Model for Dynamic Inputs**  
   - Flexible persistence supporting efficient querying and future reporting.

6. **“Onay Bekleyen Kriter” Workflow**  
   - Robust routing and state transitions: **Submitted → Pending Approval → Approved/Rejected**.

## Development Context

The brief stems from extensive requirement analysis. APDYS is envisioned as a **backend‑focused .NET 8** application within Arel University’s IT ecosystem. Deliverables are primarily APIs and backend services to be consumed by a separate frontend effort (unless later expanded).

## Guidance on PRD Detail

### High Detail Required for

- **API Specifications** – endpoints, payloads, auth, validation, error models.  
- **Admin Module User Stories** – all dynamic/static criteria operations.  
- **Academician Submission Workflow** – data entry, file uploads, draft, submit.  
- **Controller Verification Workflow** – review, evidence preview, approve/reject (mandatory comment on rejection).  
- **Specialized Evaluation Modules** – workflow diagrams, user stories, mock‑ups for each specialty module.  
- **RBAC Matrix** – detailed mapping of roles to permissions (CRUD, Approve, etc.).  
- **Data Models** – ERDs and schema for dynamic inputs and submitted data tables.

### Sufficient Detail for MVP

- Core error‑handling & validation message patterns.  
- Basic audit logging (who, what, when).

### Out of Scope for MVP (Future)

- Full reporting aggregation service.  
- Direct EBYS API integration.  
- User notification system (email alerts) unless critical.

## User Preferences

| Category | Preference |
| --- | --- |
| **Backend Tech** | .NET 8 |
| **Architecture** | Microservices |
| **Mandatory Integrations** | Identity Server, Organization Management |
| **System Philosophy** | Highly dynamic & configurable—admins can tailor evaluation logic without redeployments |
| **UX Expectation** | APIs should enable intuitive front‑end experiences; admin UIs must balance power & usability |

