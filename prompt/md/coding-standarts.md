# APDYS Coding Standards

## 1. Language & Runtime

* **Primary Language:** C# 12
* **Primary Runtime:** .NET 8.0 LTS
* **Project SDK Style:** All projects MUST use the SDK-style format (`<Project Sdk="Microsoft.NET.Sdk.Web">` or `<Project Sdk="Microsoft.NET.Sdk">`).

## 2. Code Style & Formatting

* **Style Guide:** Adhere to the official [Microsoft C# Coding Conventions](https://docs.microsoft.com/en-us/dotnet/csharp/fundamentals/coding-style/coding-conventions).
* **EditorConfig:** An `.editorconfig` file MUST be present at the root of the solution to enforce common formatting rules (indentation, spacing, newlines, etc.). This file should be configured to align with the Microsoft C# Coding Conventions.
    * Indentation: 4 spaces (no tabs).
    * `using` directives: Placed outside the namespace. System `using` directives should be placed before application/custom `using` directives, sorted alphabetically.
    * Braces: Opening braces for types and methods on a new line (Allman style).
* **Implicit Typing:** Use `var` for local variable declarations when the type is obvious from the right-hand side of the assignment. Use explicit types when the type is not immediately apparent or to improve readability.
* **Expression-bodied members:** Use for simple, single-line methods, properties, and constructors where it enhances readability.
* **File Scoped Namespaces:** Preferred for C# 10+ (`namespace MyNamespace;`).

## 3. Naming Conventions

* **General:** Use `PascalCase` for classes, interfaces, enums, methods, properties, and events.
    * Examples: `CriteriaManager`, `ICriteriaStore`, `SubmissionStatus`, `GetCriteriaAsync`, `IsEnabled`.
* **Local Variables & Method Parameters:** Use `camelCase`.
    * Examples: `academicianId`, `evaluationForm`.
* **Interfaces:** Prefix with `I`.
    * Example: `ISubmissionService`.
* **Constants:** Use `PascalCase` for `public const` and `static readonly` fields if they are part of a public API. For internal or private constants, `UPPER_SNAKE_CASE` can be used if it enhances readability for a specific domain (e.g., `DEFAULT_PAGE_SIZE`), but `PascalCase` is generally preferred for consistency in C#.
* **Private Fields:** Prefix with an underscore `_` and use `camelCase` (e.g., `_criteriaStore`).
* **Acronyms:** Treat acronyms as words (e.g., `HtmlHelper`, `parseUrl`) unless they are two letters long (e.g., `DBContext`, `IOStream`).
* **File Names:** Match the primary public type name within the file (e.g., `CriteriaManager.cs` contains `CriteriaManager` class).

## 4. Asynchronous Programming

* **`async/await`:** MANDATORY for all I/O-bound operations (database calls, HTTP requests, file system access).
* **Method Suffix:** Asynchronous methods MUST have an `Async` suffix (e.g., `GetCriteriaAsync`, `SaveSubmissionAsync`).
* **Return Types:**
    * Use `Task` for async methods that do not return a value.
    * Use `Task<TResult>` for async methods that return a value.
    * Avoid `async void` except for event handlers.
* **`ConfigureAwait(false)`:** Use `ConfigureAwait(false)` in general-purpose library code (like `Rlx.Shared` would, and potentially in APDYS `Stores` or utility `Services`) to avoid deadlocks by not attempting to resume on the original synchronization context. For ASP.NET Core application code (Controllers, Managers), this is less critical as there isn't a UI thread synchronization context, but it's a good habit if the code might be reused.

## 5. Type Safety & Null Handling

* **Nullable Reference Types (NRTs):** Enabled by default in .NET 8 projects (`<Nullable>enable</Nullable>` in `.csproj`). Code MUST be NRT-aware and aim to eliminate null reference warnings/errors.
* **Explicit Null Checks:** Use `ArgumentNullException.ThrowIfNull()` for public API parameter validation. For other null checks, use `is null` or `is not null`.
* **Return Values:** Clearly define whether methods/properties can return null and annotate accordingly.

## 6. Comments & Documentation

* **XML Documentation Comments:** MANDATORY for all public types and members (classes, interfaces, methods, properties, enums). Use `///` comments.
    * Include `<summary>`, `<param>`, `<returns>`, and `<exception>` tags where appropriate.
    * These will be used for generating Swagger/OpenAPI documentation.
* **Inline Comments:** Use `//` for clarifying complex, non-obvious logic within method bodies. Avoid comments that merely restate what the code does. Focus on *why* something is done a certain way if it's not apparent.
* **`TODO` / `FIXME` Comments:** Use standard `// TODO:` or `// FIXME:` comments to mark areas needing future work or attention. Include a brief explanation.
* **Module/File Headers:** Not strictly required if XML docs and clear naming are used, but can be added if they provide significant context.

## 7. Dependency Management & Injection

* **Dependency Injection (DI):** Heavily utilize ASP.NET Core's built-in DI container.
    * Register services in `Program.cs` or dedicated extension methods in the `Extensions/` folder.
    * Favor constructor injection for required dependencies.
    * Define service lifetimes appropriately (`Singleton`, `Scoped`, `Transient`).
* **NuGet Packages:**
    * All third-party packages MUST be approved.
    * Use specific, locked versions in `.csproj` files (avoid floating versions for production dependencies).
    * Regularly review and update dependencies, especially for security patches.
    * Prefer official Microsoft packages or well-known, well-maintained community packages.
    * Reference `Rlx.Shared` (version 1.0.3) as specified.

## 8. Error Handling & Logging

* **Exceptions:**
    * Use custom exceptions for domain-specific error conditions where appropriate, inheriting from `System.Exception` or more specific base exceptions.
    * Throw exceptions rather than returning error codes for exceptional situations.
    * Catch specific exceptions rather than `System.Exception` where possible.
    * Do not use exceptions for control flow.
* **Global Error Handling:** Implement global error handling middleware (ASP.NET Core) to catch unhandled exceptions, log them, and return standardized API error responses (as defined in Epic 7, Story 7.2). `Rlx.Shared` may provide a base for this.
* **Logging:**
    * Utilize `RlxSystemLogHelper` from `Rlx.Shared` for all application logging to ensure consistency and centralized aggregation (via RabbitMQ as indicated by `Rlx.Shared` description).
    * Log meaningful information, including relevant context (e.g., request ID, user ID, entity IDs).
    * Use appropriate log levels (`Information`, `Warning`, `Error`, `Critical`, `Debug`).
    * Avoid logging sensitive information (passwords, raw API keys, PII) unless explicitly required, masked, and secured. Audit logging via `rlx.log` (or `RlxSystemLogHelper` implementing audit requirements) will handle specific PII for audit purposes as defined.


## 9. API Design

* **RESTful Principles:** Adhere to RESTful design principles for HTTP APIs. HTTP methods `GET`, `POST`, `PUT` are primarily used.
* **HTTP Methods:** Use appropriate HTTP verbs.
* **Status Codes:** Return standard HTTP status codes (200, 201, 204, 400, 401, 403, 404, 500, etc.).
* **Versioning:** URL-based versioning (`/api/v1/...`).
* **Request/Response Payloads:** Use JSON. DTOs should be clearly defined and validated.
* **Idempotency:** `GET` and `PUT` operations MUST be idempotent.
* **Validation:** Input validation MUST be performed on API request DTOs using .NET validation attributes (`[Required]`, `[StringLength]`, etc.) as per Epic 7, Story 7.3.



## 10. Testing

* **Unit Tests:** Classes (especially Managers, Stores, and complex services) should have corresponding unit tests. Aim for high coverage of business logic. Use mocking frameworks like Moq or NSubstitute if not provided by `Rlx.Shared` testing patterns.
* **Integration Tests:** Test interactions between components, especially API endpoints and their dependencies (e.g., database interactions within a transaction).
* **Test Naming:** Test methods should be descriptive, clearly stating what is being tested and the expected outcome (e.g., `MethodName_WhenCondition_ShouldExpectedBehavior`).

## 11. Security Best Practices (Leveraging Rlx.Shared where possible)

* **Input Validation:** All external input (API requests, messages) MUST be validated.
* **Authentication & Authorization:** Enforce authentication and authorization rigorously at API endpoints and service method boundaries. Utilize OpenID Connect/OAuth2 integration with Arel Identity Server. `Rlx.Shared` may provide helpers or middleware.
* **Secrets Management:** Connection strings, API keys, and other secrets MUST NOT be hardcoded. Load from configuration (e.g., `appsettings.json` for local dev, environment variables, or a secure secrets store for deployed environments, as guided by Arel IT and potentially `Rlx.Shared` patterns).
* **Least Privilege:** Services and users should operate with the minimum permissions necessary.
* **Data Protection:** Be mindful of sensitive data. Use HTTPS for all communication.
* **Dependency Security:** Regularly scan NuGet packages for known vulnerabilities (e.g., using `dotnet list package --vulnerable`).

## 12. General Principles

* **KISS (Keep It Simple, Stupid):** Prefer simple, clear solutions over complex ones.
* **DRY (Don't Repeat Yourself):** Avoid code duplication by using utility methods, base classes (potentially from `Rlx.Shared`), or services.
* **SOLID Principles:** Strive to follow SOLID principles where practical to create maintainable and flexible code.
* **Code Readability:** Write code that is easy to read and understand. Use meaningful variable and method names.
* **Modularity:** Design components with clear responsibilities and well-defined interfaces.

## Change Log

| Change        | Date       | Version | Description                  | Author         |
| ------------- | ---------- | ------- | ---------------------------- | -------------- |
| Initial draft | 2025-05-16 | 0.1     | Initial draft based on project requirements and .NET 8 best practices. | 1-mimar AI     |