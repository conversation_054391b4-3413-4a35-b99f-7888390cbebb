# Epic 7: Audit Logging & System Essentials

**Goal:** Implement foundational system capabilities including basic audit logging for key actions using the `rlx.log` package, standardized error handling and validation message patterns for APIs utilizing .NET's built-in validation, and essential operational endpoints, ensuring the system is robust, maintainable, and auditable from the MVP stage.

## Story List

### Story 7.1: Basic Audit Logging Implementation with rlx.log

- **User Story / Goal:** As an Administrator/System Operator, I need key system actions to be logged (who, what, when) using the university's standard `rlx.log` NuGet package, so that there is an audit trail for security, troubleshooting, and compliance purposes.
- **Detailed Requirements:**
  - Integrate and utilize the `rlx.log` NuGet package for all audit logging.
  - Implement a service or mechanism to log critical events.
  - Information to log:
    - Timestamp of the event.
    - User ID performing the action.
    - Type of action/event (e.g., `LOGIN_SUCCESS`, `CRITERIA_CREATED`).
    - Entity ID related to the action.
    - Brief description or key details.
    - Outcome (Success/Failure).
  - Key events to audit for <PERSON> include login attempts, admin changes, submissions, approvals/rejections, and specialized workflow actions.
  - Audit logs stored as directed by `rlx.log` configuration.
  - Provide a basic internal API for authorized personnel to query these logs.
- **Acceptance Criteria (ACs):**
  1. Successful and failed login attempts are logged via `rlx.log`.
  2. Creation/modification of criteria, forms, categories are logged.
  3. Submission lifecycle events are logged.
  4. Audit logs include who, what, and when.
  5. Logs persist via `rlx.log` storage configuration.
- **Tasks:**
  - [ ] Integrate `rlx.log` NuGet package.
  - [ ] Configure `rlx.log` for the AcademicPerformance project.
  - [ ] Create IAuditLogHelper interface and AuditLogHelper implementation.
  - [ ] Implement audit logging for user actions, following OrganizationManagement patterns.
  - [ ] Add `rlx.log` calls to all manager services.
  - [ ] Define comprehensive list of events to audit.
  - [ ] Implement consistent audit log format across all services.
  - [ ] Add audit logging middleware for API requests.

---

### Story 7.2: Standardized API Error Handling and Response Format

- **User Story / Goal:** As an API Consumer, I need consistent and predictable error responses from all APDYS APIs, so that I can reliably handle errors.
- **Detailed Requirements:**
  - Define a standard JSON error response structure (status, code, message, details).
  - Implement global exception middleware to transform unhandled exceptions.
  - Use appropriate HTTP status codes (400, 401, 403, 404, 500).
- **Acceptance Criteria (ACs):**
  1. All errors follow the standard JSON structure.
  2. Unhandled exceptions return a 500 with standard format.
  3. Validation errors return 400 with detailed field messages.
  4. 404 for missing resources, 401 for unauthenticated access.
- **Tasks:**
  - [ ] Define standardized JSON error response schema.
  - [ ] Create ErrorResponse and ValidationErrorResponse DTOs.
  - [ ] Implement global exception handling middleware.
  - [ ] Create custom exception types for different error scenarios.
  - [ ] Implement consistent HTTP status code mapping.
  - [ ] Add localization support for error messages.
  - [ ] Ensure all controllers use the standardized error format.
  - [ ] Add comprehensive error logging.

---

### Story 7.3: Consistent API Validation Message Patterns using .NET Attributes

- **User Story / Goal:** As an API Consumer, when I submit invalid data, I need clear validation error messages based on .NET attributes.
- **Detailed Requirements:**
  - Decorate DTOs with built‑in attributes (`[Required]`, `[Range]`, etc.).
  - Model-state validation errors feed into standard JSON error response.
- **Acceptance Criteria (ACs):**
  1. Missing required fields return detailed 400 errors.
  2. Format/range violations return field‑specific messages.
- **Tasks:**
  - [ ] Ensure all DTOs use appropriate validation attributes.
  - [ ] Create custom validation attributes for specific business rules.
  - [ ] Implement validation filter to standardize validation responses.
  - [ ] Add localization support for validation error messages.
  - [ ] Integrate validation errors with the standardized error response format.
  - [ ] Implement client-side validation rules that match server-side validation.
  - [ ] Add comprehensive validation error logging.

---

### Story 7.4: Basic Health Check Endpoint

- **User Story / Goal:** As a System Operator, I need a `/health` endpoint per microservice.
- **Detailed Requirements:**
  - Endpoint returns 200 and `{"status":"Healthy"}` if service is up.
  - Optionally checks DB connectivity.
- **Acceptance Criteria:**
  1. `/health` returns 200 when healthy.
  2. Returns non‑200 if critical dependency down.
- **Tasks:**
  - [ ] Create HealthCheckController with `/health` endpoint.
  - [ ] Implement health check for database connectivity.
  - [ ] Add health checks for other critical dependencies (Redis, etc.).
  - [ ] Implement detailed health status reporting.
  - [ ] Configure health check middleware.
  - [ ] Add health check logging.
  - [ ] Implement health check response caching to prevent overload.

---

### Story 7.5: API Documentation Generation (Swagger/OpenAPI)

- **User Story / Goal:** As a Developer, I need autogenerated Swagger/OpenAPI docs.
- **Detailed Requirements:**
  - Add Swashbuckle to generate `swagger.json` and UI.
  - Include XML comments for richer docs.
- **Acceptance Criteria:**
  1. `swagger.json` reflects all endpoints, DTOs, and validation.
  2. Swagger UI accessible in dev environments.
- **Tasks:**
  - [ ] Add Swashbuckle.AspNetCore package.
  - [ ] Configure Swagger generation in Program.cs.
  - [ ] Enable XML documentation comments in project file.
  - [ ] Add comprehensive XML comments to all controllers and DTOs.
  - [ ] Configure Swagger UI with proper authentication.
  - [ ] Implement custom Swagger document filters.
  - [ ] Add operation filters for consistent parameter descriptions.
  - [ ] Configure Swagger to show enum descriptions.

## Change Log

| Change | Date | Version | Description | Author |
| ------ | ---- | ------- | ----------- | ------ |
| Initial Draft | 2025‑05‑15 | 0.1 | First draft of Epic 7 stories. | 1‑pm AI |
| Revised | 2025‑05‑15 | 0.2 | Updated to use `rlx.log` & .NET validation attributes. | 1‑pm AI |
