# Epic 1: Core Setup, Authentication & User Management Integration

**Goal:** Establish foundational services for APDYS, integrate with Arel University's existing Identity Server for secure token-based authentication, connect to the Organization Management module for user data retrieval, and implement the core Role-Based Access Control (RBAC) structure. This epic ensures that users can securely access the system and that APDYS has the necessary user context to enforce permissions.

## Story List

### Story 1.1: Project Scaffolding and Microservice Boilerplate

- **User Story / Goal:** As a Developer, I need the initial .NET 8 microservice project(s) set up with appropriate boilerplate, configuration management, and logging framework, so that development can begin on a consistent and structured codebase.
- **Detailed Requirements:**
  - Set up solution and project structure for APDYS backend services (.NET 8).
  - Implement basic configuration management (e.g., for database connection strings, Identity Server URL).
  - Integrate a standard logging library (e.g., Serilog) for application logging.
  - Define initial Dockerfile(s) for containerization (for on-premise deployment).
  - Establish base API project structure if a gateway or multiple services are anticipated early.
- **Acceptance Criteria (ACs):**
  - **AC1:** A new .NET 8 solution/project for APDYS backend can be built and run.
  - **AC2:** Application configuration can be loaded from environment variables or configuration files.
  - **AC3:** Basic application startup and error events are logged.
  - **AC4:** A Docker image can be built from the project.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Initialize .NET solution and project(s).
  - [ ] Add logging and configuration packages.
  - [ ] Create initial Dockerfile.

---

### Story 1.2: Identity Server Integration for Authentication

- **User Story / Goal:** As a User, I want the system to use Arel University's Identity Server for authentication, so that I can log in with my existing university credentials securely.
- **Detailed Requirements:**
  - Integrate with OpenIddict for token validation from Arel University's Identity Server.
  - Configure the Rlx.Shared package components for authentication and role-based claims transformation.
  - Secure API endpoints to require a valid token for access.
  - Handle token validation errors gracefully (e.g., expired, invalid).
  - Extract user identifiers and roles from the validated token.
  - Configuration for Identity Server endpoint, issuer.
- **Acceptance Criteria (ACs):**
  - **AC1:** API endpoints are protected and return a 401 Unauthorized error if no token or an invalid token is provided.
  - **AC2:** A valid token from the Identity Server grants access to protected API endpoints.
  - **AC3:** User identifier (e.g., user ID, username) can be successfully extracted from the token.
  - **AC4:** System correctly handles expired or malformed tokens by denying access.
  - **AC5:** User roles and claims are properly transformed and available for authorization.
- **Tasks (Optional Initial Breakdown):**
  - [x] Configure OpenIddict validation for authentication.
  - [x] Register Rlx.Shared services (RlxIdentitySharedManager, RlxIdentitySharedStore, RoleClaimsTransformation).
  - [x] Configure authorization policies.
  - [x] Secure sample endpoints with authorization attributes.

---

### Story 1.3: Organization Management Module Integration for User Data

- **User Story / Goal:** As the System, I need to retrieve user details (like name, email, roles, academic cadre, department) from the Organization Management module after authentication, so that APDYS has the necessary context for user operations and RBAC.
- **Detailed Requirements:**
  - Configure direct database access to the OrganizationManagement database.
  - Implement services to fetch user details based on the identifier obtained from the authenticated token.
  - Map relevant fields from the Organization Management module to APDYS internal user representation (e.g., User ID, Name, Email, Arel Roles, Academic Cadre, Department).
  - Handle potential errors during data retrieval (e.g., user not found, database unavailability).
  - Utilize Redis cache service to minimize database load, with a clear cache invalidation strategy.
- **Acceptance Criteria (ACs):**
  - **AC1:** Given a user identifier from a valid token, the system can retrieve the user's full name and email from the Organization Management database.
  - **AC2:** System can retrieve the user's assigned roles, academic cadre, and department from the Organization Management database.
  - **AC3:** Errors during data retrieval from Organization Management are logged, and the system handles them gracefully (e.g., denying access or using cached data if permissible).
  - **AC4:** Retrieved user data is correctly mapped to the internal APDYS user model.
  - **AC5:** User data is properly cached using Redis to improve performance.
- **Tasks (Optional Initial Breakdown):**
  - [x] Configure OrganizationManagementDbContext for database access.
  - [x] Implement user data retrieval services.
  - [x] Configure Redis caching for user data.
  - [x] Implement user data mapping using Mapster.

---

### Story 1.4: Initial RBAC Data Model and Permission Structure

- **User Story / Goal:** As an Admin, I need a foundational Role-Based Access Control (RBAC) system defined with core roles and permissions, so that system functionalities can be secured based on user roles.
- **Detailed Requirements:**
  - Define core APDYS roles (e.g., `APDYS_Admin`, `APDYS_Academician`, `APDYS_Controller`, `APDYS_StrategicMgmt`, `APDYS_Manager`, `APDYS_Archivist`, `APDYS_DataEntry_Library`, etc.) within the Identity Server.
  - Define an initial set of permissions as claims (e.g., `permission:page:apdys`, `permission:action:manageCriteria`, `permission:action:submitPerformanceData`).
  - Leverage the existing RlxIdentityShared infrastructure to retrieve and validate role-based claims.
  - Configure policy-based authorization that maps claims to permissions.
  - Implement authorization policies that check if a user has the required claims for specific actions.
- **Acceptance Criteria (ACs):**
  - **AC1:** APDYS roles and permissions (as claims) are defined in the Identity Server.
  - **AC2:** The system can retrieve and transform claims from the Identity Server token.
  - **AC3:** Authorization policies are configured to check for specific claims.
  - **AC4:** A basic authorization check can successfully grant or deny access to endpoints based on the authenticated user's claims.
  - **AC5:** Policy configuration follows the same pattern as the OrganizationManagement project.
- **Tasks (Optional Initial Breakdown):**
  - [x] Configure RoleClaimsTransformation to process claims from tokens.
  - [x] Define authorization policies for APDYS-specific permissions.
  - [x] Implement PolicyConfig to configure policies.
  - [x] Create authorization attributes for controllers and actions.
  - [x] Document the required roles and claims in the Identity Server.

---

### Story 1.5: Basic User Profile Service

- **User Story / Goal:** As an Authenticated User, I want to be able to retrieve my basic profile information relevant to APDYS (e.g., name, email, APDYS roles, academic cadre), so that I know how the system identifies me.
- **Detailed Requirements:**
  - Create a UserController with an API endpoint (e.g., `/api/users/me`) that returns basic profile information for the currently authenticated user.
  - The information should be sourced from the data retrieved via the Identity Server token and the Organization Management database.
  - Implement a UserContextHelper to extract user information from the current HttpContext.
  - Ensure only the authenticated user can access their own profile information using authorization policies.
- **Acceptance Criteria (ACs):**
  - **AC1:** Authenticated user can successfully call the `/api/users/me` endpoint.
  - **AC2:** The endpoint returns the user's correct name, email, assigned APDYS roles, and academic cadre.
  - **AC3:** An unauthenticated request to the endpoint results in a 401 error.
  - **AC4:** A user cannot request profile information for another user via this endpoint.
  - **AC5:** The profile information includes data from both the Identity Server and Organization Management.
- **Tasks (Optional Initial Breakdown):**
  - [x] Create UserController with `/api/users/me` endpoint.
  - [x] Implement UserProfileDto for response data.
  - [x] Implement service to aggregate user data from multiple sources.
  - [x] Configure authorization policy for the endpoint.
  - [x] Add Mapster configuration for user profile mapping.

## Change Log

| Change | Date | Version | Description | Author |
| ------ | ---- | ------- | ----------- | ------ |
| Initial Draft | 2025-05-15 | 0.1 | First draft of Epic 1 stories. | 1-pm AI |