# APDYS Testing Strategy

## 1. Overall Philosophy & Goals

The testing strategy for APDYS aims to ensure the delivery of a high-quality, reliable, and maintainable backend system. We will follow the principles of the Testing Pyramid, emphasizing a strong foundation of unit tests, complemented by integration tests for service interactions and API contract validation. While manual testing by university staff is a requirement, automated tests are crucial for enabling CI/CD, preventing regressions, and facilitating confident refactoring.

**Key Goals:**

* **Correctness:** Ensure all business logic within the APDYS Core Service and Notification Service functions as per the requirements defined in the PRD and Epics.
* **Reliability:** Verify the stability of services, data integrity, and error handling.
* **Maintainability:** Create tests that are easy to understand, run, and maintain alongside the application code.
* **Confidence:** Provide a safety net that allows developers to make changes and add features with a high degree of confidence that existing functionality is not broken.
* **API Testability:** Ensure APIs are easily testable, supporting manual testing by university staff using tools like Postman, and automated contract testing.

## 2. Testing Levels

### 2.1 Unit Tests

* **Scope:** Test individual classes, methods, or components in isolation. This includes testing business logic within Manager classes, data transformation in DTOs/Mappings, and specific logic within Store (Repository) implementations (with data access mocked). Helper utilities and specific algorithms will also be unit tested.
* **Tools:**
    * **Test Framework:** xUnit (a common and flexible .NET testing framework). NUnit or MSTest are also acceptable if Arel University has a strong preference or if `Rlx.Shared` provides test utilities based on one of them.
    * **Mocking/Stubbing:** Moq or NSubstitute for creating test doubles (mocks, stubs, fakes) for dependencies.
* **Location:** In separate test projects named `{ServiceProjectName}.UnitTests` (e.g., `Apdys.Core.Api.UnitTests`, `Apdys.Notification.Api.UnitTests`). Tests will be organized in folders mirroring the structure of the code being tested.
* **Expectations:**
    * Cover all significant logic paths, boundary conditions, and equivalence classes.
    * Tests should be fast, independent, and repeatable.
    * Aim for high code coverage for critical business logic (Managers, complex Stores).
    * Focus on testing a single unit of behavior per test method.
* **Example Test Areas:**
    * Validation logic in DTOs/COs (if custom beyond attributes).
    * Business rule enforcement in Manager classes.
    * Mapping logic (e.g., Entity to DTO using Mapster).
    * Complex query construction logic in Stores (mocking DbContext/DbSet).

### 2.2 Integration Tests

* **Scope:** Verify the interaction and collaboration between multiple internal components of a single service, or between a service and its direct infrastructure dependencies (like a test database instance or a test message queue). This level focuses on ensuring that different parts of a service work together correctly.
* **Tools:**
    * **Test Framework:** xUnit (or chosen .NET framework).
    * **ASP.NET Core TestHost (`Microsoft.AspNetCore.Mvc.Testing`):** For in-memory testing of API controllers, request/response pipeline, authentication, and authorization. This allows testing the full request flow within a service without needing to deploy it.
    * **Testcontainers (optional, but recommended for on-premise):** To spin up ephemeral instances of PostgreSQL, MongoDB, and RabbitMQ in Docker containers for testing against real database/broker technologies. This provides higher fidelity than in-memory alternatives for data stores.
    * **EF Core InMemory Database Provider (for simpler DB tests):** Can be used for faster integration tests that don't require the full fidelity of a real PostgreSQL instance, but with caution as it doesn't behave exactly like a relational DB.
* **Location:** In separate test projects named `{ServiceProjectName}.IntegrationTests`.
* **Expectations:**
    * Focus on API contracts, database interactions (CRUD operations via Stores), message publishing/consumption, and interactions with `Rlx.Shared` components within the service boundary.
    * Slower than unit tests but provide more confidence in component collaboration.
    * Ensure proper data seeding and teardown for test environments.
* **Example Test Areas:**
    * API endpoint tests: Send HTTP requests to controllers and verify responses, status codes, and side effects (e.g., database changes, messages published).
    * Store (Repository) tests against a test database instance (PostgreSQL, MongoDB).
    * Authentication and Authorization middleware tests for API endpoints.
    * Message publishing to RabbitMQ by the Core APDYS Service and consumption by the Notification Service (using Testcontainers for RabbitMQ).
    * Interaction with `OrganizationManagementClient` (potentially against a mocked HTTP client or a test stub of the Org. Mgmt. API if available).

### 2.3 End-to-End (E2E) / Acceptance Tests (Primarily Manual for MVP)

* **Scope:** Test the entire system flow involving interactions between the APDYS Core API and potentially external systems (from an API consumer's perspective). Given the PRD's emphasis on manual testing by university staff and easily testable APIs, automated E2E tests might be limited for MVP, focusing instead on providing excellent API documentation and Postman collections.
* **Tools (for enabling manual testing):**
    * **Swagger/OpenAPI UI:** Auto-generated for each service, allowing interactive API exploration and testing.
    * **Postman Collections:** Pre-defined Postman collections with sample requests for all key API workflows will be provided to facilitate manual testing by Arel University staff and for developer testing.
* **Environment:** Manual E2E tests will be conducted against deployed environments (Dev, Staging).
* **Expectations:**
    * Validate complete user journeys and business processes as defined in the Epics.
    * Cover critical workflows like full form definition, submission, and approval.
    * Ensure integrations with Identity Server and Organization Management Module behave as expected in a deployed environment.
* **Future Automated E2E (Post-MVP):** If desired, lightweight automated API E2E tests could be developed using tools like RestSharp, RestAssured (if Java-based testing is an option), or even basic .NET HttpClient tests against deployed environments.

### 2.4 Manual / Exploratory Testing

* **Scope:** As specified in the PRD, Arel University staff will perform manual testing. This will likely include:
    * Exploratory testing to find edge cases or usability issues from the perspective of an API consumer (or a future frontend).
    * User Acceptance Testing (UAT) of the API functionalities based on the defined Epics and stories.
    * Verification of complex scenarios that are hard to automate.
* **Process:** Arel University will define its UAT process. The development team will support this by providing stable deployed environments, API documentation (Swagger UI), and Postman collections. Feedback will be tracked in an issue management system.

## 3. Specialized Testing Types

### 3.1 Security Testing

* **Scope & Goals:**
    * Ensure authentication and authorization mechanisms are robust.
    * Protect against common API vulnerabilities (OWASP Top 10 for APIs).
    * Validate input sanitization.
    * Leverage security features provided by `Rlx.Shared`.
* **Tools & Approaches:**
    * **Manual Code Reviews:** Focus on security-sensitive areas (auth, input handling).
    * **Dependency Scanning:** Use tools like `dotnet list package --vulnerable` or integrated GitHub Dependabot alerts to check for vulnerabilities in NuGet packages.
    * **Static Application Security Testing (SAST):** Consider basic SAST tools or linters if available.
    * **Penetration Testing (Post-MVP or by Arel Security Team):** Recommended before going live with sensitive data.

### 3.2 Performance Testing (Post-MVP Focus)

* **Scope & Goals:** While not a primary MVP focus, the architecture should support future performance testing. Key areas would include API response times under load for common operations (form loading, submission, dashboard views).
* **Tools:** K6, Apache JMeter, or similar tools could be used against deployed environments.

## 4. Test Data Management

* **Unit Tests:** Use mocked data or small, controlled in-memory datasets.
* **Integration Tests:**
    * Use predefined data seeding scripts to populate test databases (PostgreSQL, MongoDB) before test runs.
    * Ensure tests clean up their data or run in transactions that are rolled back to maintain test independence.
    * For `TestHost` API tests, data can be seeded directly into the test database instance.
* **Manual/E2E Tests:** Dev and Staging environments should have representative (anonymized if PII is involved) sample data. Procedures for resetting these environments to a known state may be needed.

## 5. CI/CD Integration

* **Automated Execution:** Unit and integration tests MUST be executed automatically as part of the Continuous Integration (CI) pipeline for every code commit/merge to the main branches.
* **Pipeline Failure:** A CI build WILL fail if any automated tests (unit or integration) fail. This prevents regressions from being merged.
* **Postman Collections:** While not "run" in CI in the same way as unit/integration tests, the Postman collections should be versioned with the code and can be used for automated API contract/smoke tests in a CD pipeline against deployed environments if tools like Newman are incorporated.
* **On-Premise CI/CD:** The specifics of the CI/CD pipeline will be defined with Arel University IT, leveraging their existing on-premise tools (e.g., Jenkins, Azure DevOps Server, GitLab CI on-premise).

## Change Log

| Change        | Date       | Version | Description                  | Author         |
| ------------- | ---------- | ------- | ---------------------------- | -------------- |
| Initial draft | 2025-05-16 | 0.1     | Initial draft of the testing strategy. | 1-mimar AI     |