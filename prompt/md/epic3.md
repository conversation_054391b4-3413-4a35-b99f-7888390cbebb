# Epic 3: Academician - Data Submission Workflow

**Goal:** Enable Academicians to easily access their assigned evaluation forms, understand the criteria they need to address, input their performance data accurately, upload necessary supporting evidence, and manage the submission process (drafts and final submission) for their performance evaluations.

## Story List

### Story 3.1: Academician Dashboard - View Assigned Evaluation Forms & Submissions

- **User Story / Goal:** As an Academician, I want to see a personalized dashboard listing my active evaluation forms and the status of my submissions (e.g., Not Started, Draft, Submitted, Approved, Rejected), so that I can easily track my tasks and progress.
- **Detailed Requirements:**
  - Upon login, an Academician should be presented with a list of evaluation forms assigned to their cadre and currently active for the relevant evaluation period.
  - For each form, display its name, evaluation period, and the status of their submission for that form.
  - If a submission exists, provide a way to view/edit (if in draft) or view (if submitted/approved/rejected).
  - The dashboard should clearly indicate any upcoming submission deadlines.
- **Acceptance Criteria (ACs):**
  - **AC1:** Academician can view a list of all evaluation forms currently assigned to them based on their cadre and active evaluation periods.
  - **AC2:** Each listed form shows its title, evaluation period, and the current status of the Academician's submission for that form.
  - **AC3:** Links/actions are available to start a new submission or open an existing one.
  - **AC4:** Submission deadlines are clearly visible for active forms.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API for fetching assigned forms and submission statuses for an academician.
  - [ ] Implement logic to determine active forms based on cadre and period.

---

### Story 3.2: View Form Details and Criteria Structure

- **User Story / Goal:** As an Academician, when I select an evaluation form, I want to view its detailed structure, including all categories and the specific criteria (dynamic and static) within each category, along with their descriptions and any defined coefficients or limits, so that I understand what information I need to provide.
- **Detailed Requirements:**
  - When an Academician opens a specific form for submission, display all categories within that form in their defined order.
  - Under each category, list all assigned criteria (dynamic and static) in their defined order.
  - For each criterion, display its name, description, coefficient/weight, and any limits (e.g., max occurrences).
  - Display the input fields defined for each dynamic criterion (as set up by the Admin in Epic 2).
- **Acceptance Criteria (ACs):**
  - **AC1:** Academician can select an assigned form and view its categories in the correct order.
  - **AC2:** Within each category, all assigned criteria are displayed in order, showing name, description, and coefficient/limits.
  - **AC3:** For each dynamic criterion, the required input fields (text, number, date, file placeholder, dropdown) are displayed as defined by the admin.
  - **AC4:** Static criteria are displayed with their names and descriptions.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Design API to fetch the detailed structure of a specific form (categories, criteria, input definitions) for an Academician.
  - [ ] Implement logic to retrieve and present form structure based on Admin configurations from Epic 2.

---

### Story 3.3: Input Performance Data for Criteria

- **User Story / Goal:** As an Academician, I want to input my performance data for each criterion according to the defined input types (text, number, date, dropdown selections), so that I can accurately record my achievements.
- **Detailed Requirements:**
  - For each criterion requiring input, the Academician can enter data into the specified fields (text boxes, number inputs, date pickers, dropdowns).
  - Input validation should occur based on constraints defined by the Admin (e.g., number ranges, date formats, mandatory fields).
  - Data should be temporarily stored (e.g., client-side or frequent auto-save to draft) before a formal "Save Draft" or "Submit".
  - The system should handle multiple entries for a single criterion if allowed (e.g., "Published Journal Article" – submit details for several articles).
  - Submitted data will be stored in PostgreSQL, linked to the user, form, and criterion.
  - Use RlxEnum for dropdown option values to support localization.
- **Acceptance Criteria (ACs):**
  - **AC1:** Academician can successfully enter valid text, number, and date data for relevant criteria.
  - **AC2:** Academician can select options from predefined dropdowns for relevant criteria.
  - **AC3:** Backend validation prevents submission of data that violates defined constraints.
  - **AC4:** Mandatory fields must be filled before proceeding with saving certain states (e.g., final submission).
  - **AC5:** System allows for multiple data entries for a single criterion where applicable.
  - **AC6:** Data is properly stored in PostgreSQL with appropriate relationships.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Create SubmissionData entity model in PostgreSQL.
  - [ ] Implement relationships between SubmissionData, Criteria, and User entities.
  - [ ] Create ISubmissionManager and ISubmissionStore interfaces.
  - [ ] Implement SubmissionManager and SubmissionStore services.
  - [ ] Implement input validation logic based on criteria definitions.
  - [ ] Create DTOs and Mapster configurations for submission data.

---

### Story 3.4: Upload Supporting Evidence for Criteria

- **User Story / Goal:** As an Academician, I want to upload supporting evidence files (e.g., PDFs, images) for relevant criteria, so that I can substantiate my performance claims.
- **Detailed Requirements:**
  - For criteria that require or allow file uploads, provide a mechanism to upload files.
  - Define supported file types (e.g., PDF, DOCX, JPG, PNG) and maximum file size limits.
  - Allow multiple files to be uploaded per criterion entry if applicable.
  - Uploaded files should be stored securely in a file storage system with metadata in PostgreSQL.
  - Files should be linked to the specific criterion entry and submission through database relationships.
  - Provide feedback on upload progress, success, or failure.
  - Use RlxEnum for file types to support validation and localization.
- **Acceptance Criteria (ACs):**
  - **AC1:** Academician can successfully upload a file of a permitted type and size.
  - **AC2:** System rejects files that exceed the maximum size limit or are of an unsupported type.
  - **AC3:** Uploaded files are correctly associated with the specific criterion entry in the database.
  - **AC4:** Academician can see a list of files they've uploaded for a specific criterion entry.
  - **AC5:** Academician can remove an uploaded file (before final submission).
  - **AC6:** File metadata is properly stored in PostgreSQL with appropriate relationships.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Create SubmissionFile entity model in PostgreSQL for file metadata.
  - [ ] Implement relationships between SubmissionFile, SubmissionData, and User entities.
  - [ ] Configure file storage system (e.g., local file system, cloud storage).
  - [ ] Extend ISubmissionManager and ISubmissionStore interfaces for file operations.
  - [ ] Implement file upload and download functionality.
  - [ ] Implement validation for file types and size.
  - [ ] Create DTOs and Mapster configurations for file metadata.

---

### Story 3.5: Save Submission as Draft

- **User Story / Goal:** As an Academician, I want to save my performance data submission as a draft at any point, so that I can complete it over multiple sessions without losing my work.
- **Detailed Requirements:**
  - Provide a "Save Draft" option through the API.
  - When a draft is saved, all entered data and uploaded file references are persisted in PostgreSQL.
  - Use RlxEnum for submission status values (e.g., "Draft", "Submitted", "Approved", "Rejected").
  - The submission status on the dashboard is updated to "Draft".
  - The Academician can later reopen the draft and continue editing.
  - Drafts do not trigger notifications to Controllers.
  - Implement proper transaction handling to ensure data consistency.
- **Acceptance Criteria (ACs):**
  - **AC1:** Academician can save a draft, and all data is persisted in PostgreSQL.
  - **AC2:** The submission status is reflected as "Draft" on the dashboard.
  - **AC3:** Academician can resume editing the saved draft with data intact.
  - **AC4:** No notifications are sent to Controllers when a draft is saved.
  - **AC5:** Draft submissions are properly linked to the user and form in the database.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Create Submission entity model in PostgreSQL with status field.
  - [ ] Define RlxEnum values for submission statuses.
  - [ ] Implement relationships between Submission, SubmissionData, and User entities.
  - [ ] Extend ISubmissionManager and ISubmissionStore interfaces for draft operations.
  - [ ] Implement transaction handling for saving drafts.
  - [ ] Create DTOs and Mapster configurations for submission status.

---

### Story 3.6: Final Submit of Evaluation Form

- **User Story / Goal:** As an Academician, I want to make a final submission of my completed evaluation form, so that it can be reviewed by the designated Controller.
- **Detailed Requirements:**
  - Provide a "Submit" option through the API.
  - Before final submission, run all validations based on criteria requirements.
  - Once submitted, the data becomes read-only for the Academician through permission checks.
  - The submission status is updated to "Submitted" using the RlxEnum value.
  - A notification should be triggered to the relevant Controller(s) through the notification system.
  - Record the submission timestamp and user information for audit purposes.
- **Acceptance Criteria (ACs):**
  - **AC1:** Academician can submit a completed form through the API.
  - **AC2:** All validations are performed; submission is prevented if validations fail.
  - **AC3:** Upon successful submission, the data is marked as final and becomes read‑only.
  - **AC4:** The submission status is updated to "Submitted" in the database.
  - **AC5:** The system triggers a notification to the appropriate Controller(s).
  - **AC6:** Submission timestamp and user information are recorded for auditing.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Implement final validation checks in SubmissionManager.
  - [ ] Update Submission entity status to "Submitted" on successful validation.
  - [ ] Implement permission checks to make submitted data read-only.
  - [ ] Integrate with notification system to alert Controllers.
  - [ ] Add audit logging for submission events.
  - [ ] Create API endpoint for final submission in SubmissionsController.

## Change Log

| Change | Date | Version | Description | Author |
| ------ | ---- | ------- | ----------- | ------ |
| Initial Draft | 2025-05-15 | 0.1 | First draft of Epic 3 stories. | 1-pm AI |
