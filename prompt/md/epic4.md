# Epic 4: Controller - Data Verification Workflow

**Goal:** Enable Controllers to efficiently review performance data submissions from Academicians, verify the provided information and evidence, and make a decision to approve or reject the submission with appropriate feedback, ensuring the integrity of the evaluation process.

## Story List

### Story 4.1: Controller Dashboard - View Pending Submissions

- **User Story / Goal:** As a Controller, I want to see a dashboard listing all academician submissions that are pending my review, so that I can manage my workload and address submissions in a timely manner.
- **Detailed Requirements:**
  - Upon login, a Controller should see a list of submissions assigned to them or to a pool they are part of, which are in a "Submitted" status (using RlxEnum value).
  - The list should display key information for each submission, such as Academician Name, Form Name, Submission Date, and Department/Cadre.
  - Controllers should be able to sort or filter this list (e.g., by submission date, academician name).
  - The dashboard should clearly indicate the source of the submission (e.g., which academician from which department).
  - Data should be retrieved from PostgreSQL with proper relationships between Submission, User, and Department entities.
- **Acceptance Criteria (ACs):**
  - **AC1:** Controller can view a list of all submissions awaiting their review through the API.
  - **AC2:** Each item in the list shows Academician Name, Form Name, Submission Date, and Department.
  - **AC3:** Controller can sort and filter the list of pending submissions by various criteria.
  - **AC4:** The system correctly identifies and displays submissions that fall under the Controller's purview based on permissions.
  - **AC5:** Performance is optimized for potentially large lists of submissions.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Create SubmissionAssignment entity model in PostgreSQL to track controller assignments.
  - [ ] Implement relationships between SubmissionAssignment, Submission, and User entities.
  - [ ] Create IControllerManager and IControllerStore interfaces for submission review operations.
  - [ ] Implement ControllerManager and ControllerStore services.
  - [ ] Create API endpoints in a dedicated ControllerController.
  - [ ] Implement sorting and filtering capabilities with pagination.
  - [ ] Add permission checks to ensure controllers only see relevant submissions.

---

### Story 4.2: Review Submitted Data and Evidence

- **User Story / Goal:** As a Controller, when I select a submission for review, I want to view all the data entered by the Academician, structured by categories and criteria, and be able to easily access/preview any uploaded supporting evidence, so that I can thoroughly assess the submission.
- **Detailed Requirements:**
  - When a Controller opens a submission, they should see the data in the same form structure (categories, criteria, input fields) that the Academician used.
  - All data entered by the Academician for each criterion should be clearly displayed.
  - For each criterion entry with uploaded evidence, the Controller must be able to view a list of associated files.
  - Controllers should be able to download evidence files with proper security checks.
  - The system should retrieve all submission data from PostgreSQL with proper relationships.
  - The interface should clearly distinguish between Academician-provided data and system-defined criteria information (like coefficients).
- **Acceptance Criteria (ACs):**
  - **AC1:** Controller can select a submission and view all submitted data, organized by categories and criteria.
  - **AC2:** Controller can view the names and metadata of all evidence files uploaded for each criterion entry.
  - **AC3:** Controller can download any evidence file with proper authorization.
  - **AC4:** The system efficiently retrieves all submission data with optimized database queries.
  - **AC5:** File access is properly secured to prevent unauthorized access.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Create API endpoint in ControllerController to fetch a specific submission's full details.
  - [ ] Extend IControllerManager and IControllerStore interfaces for detailed submission retrieval.
  - [ ] Implement optimized database queries to retrieve submission data with related entities.
  - [ ] Create secure file download functionality with proper authorization checks.
  - [ ] Create DTOs and Mapster configurations for detailed submission view.
  - [ ] Implement permission checks to ensure controllers only access authorized submissions.

---

### Story 4.3: Approve Submission

- **User Story / Goal:** As a Controller, I want to approve a submission once I have verified its accuracy and completeness, so that the evaluation process can move forward for that Academician.
- **Detailed Requirements:**
  - Provide an "Approve" action through the API for the Controller.
  - Upon approval, the submission status is updated to "Approved" using the RlxEnum value.
  - A notification should be triggered to the Academician through the notification system (Epic 6).
  - Controller may add an optional comment upon approval, stored in PostgreSQL.
  - Record the approval timestamp and controller information for audit purposes.
  - Implement proper transaction handling to ensure data consistency.
- **Acceptance Criteria (ACs):**
  - **AC1:** Controller can approve a submission through the API.
  - **AC2:** Submission status is updated to "Approved" in the database.
  - **AC3:** Approved submission becomes read-only through permission checks.
  - **AC4:** Notification to Academician is triggered through the notification system.
  - **AC5:** Controller can add an optional comment that is stored in the database.
  - **AC6:** Approval timestamp and controller information are recorded for auditing.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Create SubmissionReview entity model in PostgreSQL to store approval information.
  - [ ] Implement relationships between SubmissionReview, Submission, and User entities.
  - [ ] Extend IControllerManager and IControllerStore interfaces for approval operations.
  - [ ] Create API endpoint in ControllerController for approving submissions.
  - [ ] Implement transaction handling for the approval process.
  - [ ] Integrate with notification system to alert the Academician.
  - [ ] Add audit logging for approval events.

---

### Story 4.4: Reject Submission with Mandatory Comments

- **User Story / Goal:** As a Controller, I want to reject a submission if it is inaccurate or incomplete, providing mandatory comments explaining the reasons for rejection, so that the Academician can make necessary corrections and resubmit.
- **Detailed Requirements:**
  - Provide a "Reject" action through the API.
  - Controller must enter comments explaining rejection; field is mandatory.
  - Submission status is updated to "Rejected" using the RlxEnum value.
  - Academician must be able to edit and resubmit based on feedback.
  - Notification with comments is sent to the Academician through the notification system (Epic 6).
  - Record the rejection timestamp and controller information for audit purposes.
- **Acceptance Criteria (ACs):**
  - **AC1:** Controller can reject a submission through the API.
  - **AC2:** Comment field is required; rejection cannot proceed without it.
  - **AC3:** Submission status is updated to "Rejected" in the database.
  - **AC4:** Rejection comments are stored and visible to the Academician.
  - **AC5:** Academician can edit/resubmit after rejection.
  - **AC6:** Notification with comments is sent to the Academician.
  - **AC7:** Rejection timestamp and controller information are recorded for auditing.
- **Tasks (Optional Initial Breakdown):**
  - [ ] Extend SubmissionReview entity model to include rejection comments.
  - [ ] Extend IControllerManager and IControllerStore interfaces for rejection operations.
  - [ ] Create API endpoint in ControllerController for rejecting submissions.
  - [ ] Implement validation to ensure comment requirement is enforced.
  - [ ] Implement transaction handling for the rejection process.
  - [ ] Integrate with notification system to alert the Academician.
  - [ ] Add audit logging for rejection events.

## Change Log

| Change | Date | Version | Description | Author |
| ------ | ---- | ------- | ----------- | ------ |
| Initial Draft | 2025-05-15 | 0.1 | First draft of Epic 4 stories. | 1-pm AI |
