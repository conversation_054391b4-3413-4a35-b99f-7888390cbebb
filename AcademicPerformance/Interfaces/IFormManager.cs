using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Interfaces
{
    public interface IFormManager
    {
        // Evaluation Form Management
        Task<List<EvaluationFormDto>> GetEvaluationFormsAsync();
        Task<EvaluationFormDto?> GetEvaluationFormByIdAsync(string id);
        Task<List<EvaluationFormDto>> GetEvaluationFormsByStatusAsync(string status);
        Task<EvaluationFormDto> CreateEvaluationFormAsync(EvaluationFormCreateDto dto, string createdByUserId);
        Task<bool> UpdateEvaluationFormAsync(EvaluationFormUpdateDto dto, string updatedByUserId);
        Task<bool> UpdateEvaluationFormStatusAsync(EvaluationFormStatusUpdateDto dto, string updatedByUserId);
        Task<bool> DeleteEvaluationFormAsync(string id);

        // Evaluation Form Management - Pagination
        Task<PagedListDto<EvaluationFormDto>> GetEvaluationFormsAsync(PagedListCo<GetEvaluationFormsCo> co);
        Task<PagedListDto<EvaluationFormDto>> GetEvaluationFormsByStatusAsync(PagedListCo<GetEvaluationFormsCo> co, string status);
        Task<PagedListDto<EvaluationFormDto>> GetEvaluationFormsByStatusAsync(string status, PagedListCo<GetStatusFilterCo> co);

        // Form Category Management
        Task<List<FormCategoryDto>> GetFormCategoriesByFormIdAsync(string evaluationFormId);
        Task<FormCategoryDto?> GetFormCategoryByIdAsync(string id);
        Task<FormCategoryDto> CreateFormCategoryAsync(FormCategoryCreateDto dto, string createdByUserId);
        Task<bool> UpdateFormCategoryAsync(FormCategoryUpdateDto dto, string updatedByUserId);
        Task<bool> DeleteFormCategoryAsync(string id);
        Task<bool> ValidateFormCategoryWeightsAsync(FormCategoryWeightValidationDto dto);

        // Form Category Management - Pagination
        Task<PagedListDto<FormCategoryDto>> GetFormCategoriesByFormIdAsync(string evaluationFormId, PagedListCo<GetStatusFilterCo> co);
        Task<PagedListDto<FormCategoryDto>?> GetFormCategoriesForAcademicianAsync(string universityUserId, string formId, PagedListCo<GetStatusFilterCo> co);

        // Form Criterion Link Management
        Task<List<FormCriterionLinkDto>> GetFormCriterionLinksByCategoryIdAsync(string formCategoryId);
        Task<FormCriterionLinkDto?> GetFormCriterionLinkByIdAsync(string id);
        Task<FormCriterionLinkDto> CreateFormCriterionLinkAsync(FormCriterionLinkCreateDto dto, string createdByUserId);
        Task<bool> UpdateFormCriterionLinkAsync(FormCriterionLinkUpdateDto dto);
        Task<bool> DeleteFormCriterionLinkAsync(string id);
        Task<List<FormCriterionLinkDto>> AssignCriteriaToFormCategoryAsync(CriterionAssignmentDto dto, string createdByUserId);

        // Form Criterion Link Management - Pagination
        Task<PagedListDto<FormCriterionLinkDto>> GetFormCriterionLinksByCategoryIdAsync(string formCategoryId, PagedListCo<GetStatusFilterCo> co);
        Task<PagedListDto<FormCriterionLinkDto>?> GetFormCriterionLinksForAcademicianAsync(string universityUserId, string categoryId, PagedListCo<GetStatusFilterCo> co);

        // Validation
        Task<bool> CanUpdateEvaluationFormAsync(string id);
        Task<bool> CanDeleteEvaluationFormAsync(string id);
        Task<bool> CanDeleteFormCategoryAsync(string id);
        Task<bool> CanDeleteFormCriterionLinkAsync(string id);
        Task<bool> ValidateCriterionAssignmentAsync(FormCriterionLinkCreateDto dto);

        // Academician-specific methods
        Task<EvaluationFormDto?> GetEvaluationFormForAcademicianAsync(string universityUserId, string formId);
        Task<List<FormCategoryDto>?> GetFormCategoriesForAcademicianAsync(string universityUserId, string formId);
        Task<List<FormCriterionLinkDto>?> GetFormCriterionLinksForAcademicianAsync(string universityUserId, string categoryId);
    }
}
