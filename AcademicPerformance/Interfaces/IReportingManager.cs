using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Reporting iş mantığı interface'i
    /// </summary>
    public interface IReportingManager
    {
        #region Performance Reports

        /// <summary>
        /// Akademisyen performans raporu oluştur
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Performans raporu</returns>
        Task<PerformanceReportDto> GenerateAcademicianPerformanceReportAsync(string academicianUserId, PerformanceReportFilterCo filterCo);

        /// <summary>
        /// Çoklu akademisyen performans raporu oluştur (sayfalanmış)
        /// </summary>
        /// <param name="co"><PERSON><PERSON><PERSON>a ve filtreleme kriterleri</param>
        /// <returns>Sayfalanmış performans raporları</returns>
        Task<PagedListDto<PerformanceReportDto>> GenerateMultiplePerformanceReportsAsync(PagedListCo<PerformanceReportFilterCo> co);

        /// <summary>
        /// Detaylı akademisyen raporu oluştur
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Detaylı akademisyen raporu</returns>
        Task<AcademicianReportDto> GenerateDetailedAcademicianReportAsync(string academicianUserId, PerformanceReportFilterCo filterCo);

        #endregion

        #region Department Reports

        /// <summary>
        /// Bölüm performans raporu oluştur
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Bölüm raporu</returns>
        Task<DepartmentReportDto> GenerateDepartmentReportAsync(string departmentId, DepartmentReportFilterCo filterCo);

        /// <summary>
        /// Çoklu bölüm raporu oluştur (sayfalanmış)
        /// </summary>
        /// <param name="co">Sayfalama ve filtreleme kriterleri</param>
        /// <returns>Sayfalanmış bölüm raporları</returns>
        Task<PagedListDto<DepartmentReportDto>> GenerateMultipleDepartmentReportsAsync(PagedListCo<DepartmentReportFilterCo> co);

        /// <summary>
        /// Fakülte bazında bölüm karşılaştırma raporu
        /// </summary>
        /// <param name="facultyId">Fakülte ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Bölüm karşılaştırma raporu</returns>
        Task<List<DepartmentReportDto>> GenerateFacultyDepartmentComparisonAsync(string facultyId, DepartmentReportFilterCo filterCo);

        #endregion

        #region Criterion Analysis

        /// <summary>
        /// Kriter analiz raporu oluştur
        /// </summary>
        /// <param name="criterionId">Kriter ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Kriter analiz raporu</returns>
        Task<CriterionAnalysisReportDto> GenerateCriterionAnalysisReportAsync(string criterionId, CriterionAnalysisFilterCo filterCo);

        /// <summary>
        /// Çoklu kriter analiz raporu oluştur
        /// </summary>
        /// <param name="co">Sayfalama ve filtreleme kriterleri</param>
        /// <returns>Sayfalanmış kriter analiz raporları</returns>
        Task<PagedListDto<CriterionAnalysisReportDto>> GenerateMultipleCriterionAnalysisAsync(PagedListCo<CriterionAnalysisFilterCo> co);

        /// <summary>
        /// Kategori bazında kriter performans analizi
        /// </summary>
        /// <param name="categoryId">Kategori ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Kategori kriter analizi</returns>
        Task<List<CriterionAnalysisReportDto>> GenerateCategoryAnalysisAsync(string categoryId, CriterionAnalysisFilterCo filterCo);

        #endregion

        #region Trend Analysis

        /// <summary>
        /// Trend analizi raporu oluştur
        /// </summary>
        /// <param name="filterCo">Trend analizi kriterleri</param>
        /// <returns>Trend analizi raporu</returns>
        Task<TrendAnalysisReportDto> GenerateTrendAnalysisReportAsync(TrendAnalysisFilterCo filterCo);

        /// <summary>
        /// Akademisyen performans trendi
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="filterCo">Trend analizi kriterleri</param>
        /// <returns>Akademisyen trend raporu</returns>
        Task<TrendAnalysisReportDto> GenerateAcademicianTrendAnalysisAsync(string academicianUserId, TrendAnalysisFilterCo filterCo);

        /// <summary>
        /// Bölüm performans trendi
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="filterCo">Trend analizi kriterleri</param>
        /// <returns>Bölüm trend raporu</returns>
        Task<TrendAnalysisReportDto> GenerateDepartmentTrendAnalysisAsync(string departmentId, TrendAnalysisFilterCo filterCo);

        #endregion

        #region Performance Calculations

        /// <summary>
        /// Akademisyen genel performans skoru hesapla
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Performans skoru (0-100)</returns>
        Task<double> CalculateAcademicianOverallScoreAsync(string academicianUserId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Bölüm genel performans skoru hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Bölüm performans skoru</returns>
        Task<double> CalculateDepartmentOverallScoreAsync(string departmentId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Kategori bazında performans skoru hesapla
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="categoryId">Kategori ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Kategori performans skoru</returns>
        Task<double> CalculateCategoryScoreAsync(string academicianUserId, string categoryId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Performans seviyesi belirle (Excellent, Good, Average, Poor)
        /// </summary>
        /// <param name="score">Performans skoru</param>
        /// <returns>Performans seviyesi</returns>
        string DeterminePerformanceLevel(double score);

        #endregion

        #region Statistics and Analytics

        /// <summary>
        /// Akademisyen istatistikleri hesapla
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Akademisyen istatistikleri</returns>
        Task<FeedbackStatisticsDto> CalculateAcademicianStatisticsAsync(string academicianUserId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Bölüm istatistikleri hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Bölüm istatistikleri</returns>
        Task<DepartmentFeedbackStatsDto> CalculateDepartmentStatisticsAsync(string departmentId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Performans dağılımı hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Performans dağılımı</returns>
        Task<PerformanceDistributionDto> CalculatePerformanceDistributionAsync(string departmentId, DateTime startDate, DateTime endDate);

        #endregion

        #region Export and Generation

        /// <summary>
        /// Rapor export et
        /// </summary>
        /// <param name="exportCo">Export kriterleri</param>
        /// <returns>Export sonucu</returns>
        Task<ReportExportDto> ExportReportAsync(ReportExportCo exportCo);

        /// <summary>
        /// Rapor oluştur ve kaydet
        /// </summary>
        /// <param name="generateCo">Rapor oluşturma kriterleri</param>
        /// <returns>Oluşturulan rapor ID'si</returns>
        Task<string> GenerateAndSaveReportAsync(GenerateReportCo generateCo);

        /// <summary>
        /// Kaydedilmiş raporu getir
        /// </summary>
        /// <param name="reportId">Rapor ID'si</param>
        /// <returns>Rapor verisi</returns>
        Task<object> GetSavedReportAsync(string reportId);

        /// <summary>
        /// Kaydedilmiş raporları listele
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="co">Sayfalama kriterleri</param>
        /// <returns>Sayfalanmış rapor listesi</returns>
        Task<PagedListDto<object>> GetSavedReportsAsync(string userId, PagedListCo<object> co);

        #endregion

        #region Ranking and Comparison

        /// <summary>
        /// Akademisyen sıralaması oluştur
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si (opsiyonel)</param>
        /// <param name="facultyId">Fakülte ID'si (opsiyonel)</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Sıralanmış akademisyen listesi</returns>
        Task<List<AcademicianPerformanceSummaryDto>> GetAcademicianRankingAsync(string? departmentId, string? facultyId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Bölüm sıralaması oluştur
        /// </summary>
        /// <param name="facultyId">Fakülte ID'si (opsiyonel)</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Sıralanmış bölüm listesi</returns>
        Task<List<DepartmentSummaryDto>> GetDepartmentRankingAsync(string? facultyId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Karşılaştırmalı analiz yap
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Karşılaştırmalı analiz</returns>
        Task<ComparativeAnalysisDto> GenerateComparativeAnalysisAsync(string academicianUserId, DateTime startDate, DateTime endDate);

        #endregion

        #region Advanced Reporting

        /// <summary>
        /// Gelişmiş filtreleme ile bölüm performans raporu oluştur
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="filterCo">Gelişmiş filtreleme kriterleri</param>
        /// <returns>Gelişmiş bölüm raporu</returns>
        Task<AdvancedDepartmentReportDto> GenerateAdvancedDepartmentReportAsync(string departmentId, AdvancedReportFilterCo filterCo);

        /// <summary>
        /// Çoklu akademisyen karşılaştırmalı analiz
        /// </summary>
        /// <param name="comparisonCo">Karşılaştırma kriterleri</param>
        /// <returns>Çoklu akademisyen karşılaştırma</returns>
        Task<MultiAcademicianComparisonDto> GenerateMultiAcademicianComparisonAsync(MultiAcademicianComparisonCo comparisonCo);

        /// <summary>
        /// Performans trend analizi
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="trendCo">Trend analiz kriterleri</param>
        /// <returns>Trend analizi</returns>
        Task<PerformanceTrendAnalysisDto> GeneratePerformanceTrendAnalysisAsync(string academicianUserId, TrendAnalysisCo trendCo);

        #endregion

        #region Analytics Functionality

        /// <summary>
        /// İstatistiksel analiz yap
        /// </summary>
        /// <param name="analysisCo">Analiz kriterleri</param>
        /// <returns>İstatistiksel analiz sonuçları</returns>
        Task<StatisticalAnalysisResultDto> PerformStatisticalAnalysisAsync(StatisticalAnalysisCo analysisCo);

        /// <summary>
        /// Dashboard veri toplama
        /// </summary>
        /// <param name="dashboardCo">Dashboard kriterleri</param>
        /// <returns>Dashboard verileri</returns>
        Task<DashboardDataDto> AggregateDashboardDataAsync(DashboardDataCo dashboardCo);

        /// <summary>
        /// Performans metrikleri hesaplama
        /// </summary>
        /// <param name="metricsCo">Metrik hesaplama kriterleri</param>
        /// <returns>Hesaplanmış performans metrikleri</returns>
        Task<PerformanceMetricsResultDto> CalculatePerformanceMetricsAsync(PerformanceMetricsCo metricsCo);

        /// <summary>
        /// Gerçek zamanlı analiz
        /// </summary>
        /// <param name="realtimeCo">Gerçek zamanlı analiz kriterleri</param>
        /// <returns>Gerçek zamanlı analiz sonuçları</returns>
        Task<RealtimeAnalysisResultDto> PerformRealtimeAnalysisAsync(RealtimeAnalysisCo realtimeCo);

        /// <summary>
        /// Batch analiz işlemi
        /// </summary>
        /// <param name="batchCo">Batch analiz kriterleri</param>
        /// <returns>Batch analiz sonuçları</returns>
        Task<BatchAnalysisResultDto> PerformBatchAnalysisAsync(BatchAnalysisCo batchCo);

        #endregion

        #region Export Capabilities

        /// <summary>
        /// PDF rapor oluştur
        /// </summary>
        /// <param name="pdfExportCo">PDF export kriterleri</param>
        /// <returns>PDF dosya bilgileri</returns>
        Task<ExportFileResultDto> ExportToPdfAsync(PdfExportCo pdfExportCo);

        /// <summary>
        /// Excel rapor oluştur
        /// </summary>
        /// <param name="excelExportCo">Excel export kriterleri</param>
        /// <returns>Excel dosya bilgileri</returns>
        Task<ExportFileResultDto> ExportToExcelAsync(ExcelExportCo excelExportCo);

        /// <summary>
        /// CSV veri export et
        /// </summary>
        /// <param name="csvExportCo">CSV export kriterleri</param>
        /// <returns>CSV dosya bilgileri</returns>
        Task<ExportFileResultDto> ExportToCsvAsync(CsvExportCo csvExportCo);

        /// <summary>
        /// Custom template ile rapor oluştur
        /// </summary>
        /// <param name="templateExportCo">Template export kriterleri</param>
        /// <returns>Template rapor bilgileri</returns>
        Task<ExportFileResultDto> ExportWithCustomTemplateAsync(TemplateExportCo templateExportCo);

        /// <summary>
        /// Bulk export işlemi
        /// </summary>
        /// <param name="bulkExportCo">Bulk export kriterleri</param>
        /// <returns>Bulk export sonuçları</returns>
        Task<BulkExportResultDto> BulkExportAsync(BulkExportCo bulkExportCo);

        /// <summary>
        /// Export durumunu kontrol et
        /// </summary>
        /// <param name="exportId">Export ID'si</param>
        /// <returns>Export durumu</returns>
        Task<ExportStatusDto> GetExportStatusAsync(string exportId);

        /// <summary>
        /// Export dosyasını indir
        /// </summary>
        /// <param name="fileId">Dosya ID'si</param>
        /// <returns>Dosya stream bilgileri</returns>
        Task<FileDownloadResultDto?> DownloadExportFileAsync(string fileId);

        #endregion
    }
}
