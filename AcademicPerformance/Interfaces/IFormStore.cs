using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Interfaces
{
    public interface IFormStore
    {
        // Evaluation Forms
        Task<List<EvaluationFormEntity>> GetEvaluationFormsAsync();
        Task<EvaluationFormEntity?> GetEvaluationFormByIdAsync(string id);
        Task<EvaluationFormEntity?> GetEvaluationFormForUpdateAsync(string id);
        Task<List<EvaluationFormEntity>> GetEvaluationFormsByStatusAsync(string status);
        Task<EvaluationFormEntity> CreateEvaluationFormAsync(EvaluationFormEntity entity, bool saveChanges = true);
        Task<bool> UpdateEvaluationFormAsync(EvaluationFormEntity entity, bool saveChanges = true);
        Task<bool> UpdateEvaluationFormStatusAsync(string id, string status);
        Task<bool> DeleteEvaluationFormAsync(string id);

        // Evaluation Forms - Pagination
        Task<PagedListDto<EvaluationFormEntity>> GetEvaluationFormsAsync(PagedListCo<GetEvaluationFormsCo> co);
        Task<PagedListDto<EvaluationFormEntity>> GetEvaluationFormsByStatusAsync(PagedListCo<GetEvaluationFormsCo> co, string status);

        // Form Categories
        Task<List<FormCategoryEntity>> GetFormCategoriesByFormIdAsync(string evaluationFormId);
        Task<FormCategoryEntity?> GetFormCategoryByIdAsync(string id);
        Task<FormCategoryEntity?> GetFormCategoryForUpdateAsync(string id);
        Task<FormCategoryEntity> CreateFormCategoryAsync(FormCategoryEntity entity, bool saveChanges = true);
        Task<bool> UpdateFormCategoryAsync(FormCategoryEntity entity, bool saveChanges = true);
        Task<bool> DeleteFormCategoryAsync(string id);
        Task<bool> ValidateCategoryWeightsAsync(string evaluationFormId, List<(string categoryId, double weight)> categoryWeights);

        // Form Criterion Links
        Task<List<FormCriterionLinkEntity>> GetFormCriterionLinksByCategoryIdAsync(string formCategoryId);
        Task<FormCriterionLinkEntity?> GetFormCriterionLinkByIdAsync(string id);
        Task<FormCriterionLinkEntity?> GetFormCriterionLinkForUpdateAsync(string id);
        Task<FormCriterionLinkEntity> CreateFormCriterionLinkAsync(FormCriterionLinkEntity entity, bool saveChanges = true);
        Task<bool> UpdateFormCriterionLinkAsync(FormCriterionLinkEntity entity, bool saveChanges = true);
        Task<bool> DeleteFormCriterionLinkAsync(string id);
        Task<List<FormCriterionLinkEntity>> CreateFormCriterionLinksAsync(List<FormCriterionLinkEntity> entities, bool saveChanges = true);

        // Helper methods
        Task<IDictionary<string, int>> IdConvertForEvaluationForm(IEnumerable<string> formIds);
        Task<IDictionary<string, int>> IdConvertForFormCategory(IEnumerable<string> categoryIds);
        Task<bool> EvaluationFormExistsAsync(string id);
        Task<bool> FormCategoryExistsAsync(string id);
        Task<bool> FormCriterionLinkExistsAsync(string id);

        // Academician-specific helper methods
        Task<EvaluationFormEntity?> GetEvaluationFormByAutoIncrementIdAsync(int autoIncrementId);
    }
}
