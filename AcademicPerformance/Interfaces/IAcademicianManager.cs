using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Interfaces
{
    public interface IAcademicianManager
    {
        // Dashboard Operations
        Task<AcademicianDashboardDto> GetAcademicianDashboardAsync(string universityUserId);
        Task<List<AssignedFormDto>> GetAssignedFormsAsync(string universityUserId);
        Task<DashboardStatisticsDto> GetDashboardStatisticsAsync(string universityUserId);

        // Profile Management
        Task<AcademicianProfileDto?> GetAcademicianProfileAsync(string universityUserId);
        Task<List<AcademicianProfileDto>> GetAcademicianProfilesAsync();
        Task<List<AcademicianProfileDto>> GetAcademicianProfilesByDepartmentAsync(string department);
        Task<List<AcademicianProfileDto>> GetAcademicianProfilesByAcademicCadreAsync(string academicCadre);
        Task<List<AcademicianProfileDto>> SearchAcademicianProfilesAsync(string searchTerm);

        // Profile Sync Operations
        Task<AcademicianProfileSyncResultDto> SyncAcademicianProfileAsync(string universityUserId, bool forceSync = false);
        Task<AcademicianProfileSyncResultDto> SyncAcademicianProfileAsync(AcademicianProfileSyncDto syncDto);
        Task<BulkProfileSyncResultDto> SyncAcademicianProfilesBulkAsync(BulkProfileSyncRequestDto requestDto);
        Task<BulkProfileSyncResultDto> SyncAllAcademicianProfilesAsync(bool forceSync = false);
        Task<List<AcademicianProfileDto>> GetProfilesRequiringSyncAsync(int maxHoursOld = 24);

        // Form Assignment Logic
        Task<List<AssignedFormDto>> GetActiveFormsForAcademicCadreAsync(string academicCadre);
        Task<bool> IsFormAssignedToAcademicianAsync(string universityUserId, string formId);
        Task<List<string>> GetEligibleAcademicCadresForFormAsync(string formId);

        // Submission Status Operations
        Task<SubmissionStatusDto?> GetSubmissionStatusAsync(string universityUserId, string formId);
        Task<List<SubmissionStatusDto>> GetAllSubmissionStatusesAsync(string universityUserId);
        Task<bool> HasPendingSubmissionsAsync(string universityUserId);
        Task<int> GetCompletedSubmissionCountAsync(string universityUserId);

        // Quick Actions
        Task<List<QuickActionDto>> GetAvailableQuickActionsAsync(string universityUserId);
        Task<QuickActionDto?> GetQuickActionForFormAsync(string universityUserId, string formId);

        // Quick Actions - Pagination
        Task<PagedListDto<QuickActionDto>> GetAvailableQuickActionsAsync(string universityUserId, PagedListCo<GetStatusFilterCo> co);

        // Statistics and Analytics
        Task<Dictionary<string, int>> GetSubmissionStatisticsByStatusAsync(string universityUserId);
        Task<double> GetOverallCompletionPercentageAsync(string universityUserId);
        Task<List<AssignedFormDto>> GetFormsWithApproachingDeadlinesAsync(string universityUserId, int daysThreshold = 7);

        // Pagination Methods
        Task<PagedListDto<AssignedFormDto>> GetAssignedFormsAsync(string universityUserId, PagedListCo<GetAcademicianFormsCo> co);
        Task<PagedListDto<SubmissionDto>> GetSubmissionsAsync(string universityUserId, PagedListCo<GetAcademicianSubmissionsCo> co);
        Task<PagedListDto<SubmissionStatusDto>> GetAllSubmissionStatusesAsync(string universityUserId, PagedListCo<GetStatusFilterCo> co);
        Task<PagedListDto<AssignedFormDto>> GetFormsWithApproachingDeadlinesAsync(string universityUserId, int daysThreshold, PagedListCo<GetStatusFilterCo> co);

        // Validation and Business Rules
        Task<bool> CanAcademicianAccessFormAsync(string universityUserId, string formId);
        Task<bool> CanAcademicianEditSubmissionAsync(string universityUserId, string formId);
        Task<bool> CanAcademicianSubmitFormAsync(string universityUserId, string formId);
        Task<bool> IsAcademicianProfileActiveAsync(string universityUserId);

        // Cache Management
        Task<bool> RefreshAcademicianCacheAsync(string universityUserId);
        Task<bool> ClearAcademicianCacheAsync(string universityUserId);
        Task<bool> WarmupAcademicianCacheAsync(string universityUserId);

        // Administrative Operations
        Task<bool> ActivateAcademicianProfileAsync(string universityUserId, string activatedByUserId);
        Task<bool> DeactivateAcademicianProfileAsync(string universityUserId, string deactivatedByUserId);
        Task<bool> UpdateAcademicianProfileNotesAsync(string universityUserId, string notes, string updatedByUserId);
    }
}
