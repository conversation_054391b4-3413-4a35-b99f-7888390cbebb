using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Bölüm performans yönetimi interface'i
    /// </summary>
    public interface IDepartmentPerformanceManager
    {
        #region CRUD Operations

        /// <summary>
        /// Bölüm performans kaydı oluştur
        /// </summary>
        /// <param name="dto">Oluşturma DTO'su</param>
        /// <param name="createdByUserId">Oluşturan kullanıcı ID'si</param>
        /// <returns>Oluşturulan bölüm performans DTO'su</returns>
        Task<DepartmentPerformanceDto> CreateDepartmentPerformanceAsync(
            DepartmentPerformanceCreateDto dto,
            string createdByUserId);

        /// <summary>
        /// Bölüm performans kaydını güncelle
        /// </summary>
        /// <param name="dto">Güncelleme DTO'su</param>
        /// <param name="updatedByUserId">Güncelleyen kullanıcı ID'si</param>
        /// <returns>Güncelleme başarılı mı</returns>
        Task<bool> UpdateDepartmentPerformanceAsync(
            DepartmentPerformanceUpdateDto dto,
            string updatedByUserId);

        /// <summary>
        /// Bölüm performans kaydını sil
        /// </summary>
        /// <param name="id">Kayıt ID'si</param>
        /// <param name="deletedByUserId">Silen kullanıcı ID'si</param>
        /// <returns>Silme başarılı mı</returns>
        Task<bool> DeleteDepartmentPerformanceAsync(string id, string deletedByUserId);

        /// <summary>
        /// Bölüm performans kaydını getir
        /// </summary>
        /// <param name="id">Kayıt ID'si</param>
        /// <returns>Bölüm performans DTO'su</returns>
        Task<DepartmentPerformanceDto?> GetDepartmentPerformanceAsync(string id);

        /// <summary>
        /// Bölüm performans kayıtlarını filtreli listele
        /// </summary>
        /// <param name="co">Filtreleme ve sayfalama parametreleri</param>
        /// <returns>Sayfalanmış bölüm performans listesi</returns>
        Task<PagedListDto<DepartmentPerformanceDto>> GetDepartmentPerformancesAsync(
            PagedListCo<DepartmentPerformanceFilterCo> co);

        #endregion

        #region Dashboard Operations

        /// <summary>
        /// Bölüm dashboard'ını getir
        /// </summary>
        /// <param name="co">Dashboard parametreleri</param>
        /// <returns>Bölüm dashboard DTO'su</returns>
        Task<DepartmentDashboardDto> GetDepartmentDashboardAsync(DepartmentDashboardCo co);

        /// <summary>
        /// Bölüm özet bilgilerini getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem (opsiyonel)</param>
        /// <returns>Bölüm performans özet DTO'su</returns>
        Task<DepartmentPerformanceSummaryDto> GetDepartmentSummaryAsync(
            string departmentId,
            string? period = null);

        /// <summary>
        /// Bölüm istatistiklerini getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="periodCount">Dönem sayısı</param>
        /// <returns>Bölüm istatistikleri DTO'su</returns>
        Task<DepartmentStatisticsDto> GetDepartmentStatisticsAsync(
            string departmentId,
            int periodCount = 12);

        #endregion

        #region Comparison & Analysis

        /// <summary>
        /// Bölüm karşılaştırması yap
        /// </summary>
        /// <param name="co">Karşılaştırma parametreleri</param>
        /// <returns>Bölüm karşılaştırma DTO'su</returns>
        Task<DepartmentComparisonDto> CompareDepartmentsAsync(DepartmentComparisonCo co);

        /// <summary>
        /// Bölüm trend analizi yap
        /// </summary>
        /// <param name="co">Trend analizi parametreleri</param>
        /// <returns>Bölüm trend analizi DTO'su</returns>
        Task<DepartmentTrendAnalysisDto> AnalyzeDepartmentTrendAsync(DepartmentTrendAnalysisCo co);

        /// <summary>
        /// Bölüm benchmark'ını hesapla
        /// </summary>
        /// <param name="co">Benchmark parametreleri</param>
        /// <returns>Bölüm benchmark DTO'su</returns>
        Task<DepartmentBenchmarkDto> CalculateDepartmentBenchmarkAsync(DepartmentBenchmarkCo co);

        #endregion

        #region Ranking Operations

        /// <summary>
        /// Bölüm sıralamasını getir
        /// </summary>
        /// <param name="co">Sıralama parametreleri</param>
        /// <returns>Sıralanmış bölüm listesi</returns>
        Task<List<DepartmentPerformanceSummaryDto>> GetDepartmentRankingAsync(DepartmentRankingCo co);

        /// <summary>
        /// Belirli bir bölümün sıralama bilgilerini getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Bölüm sıralama DTO'su</returns>
        Task<DepartmentRankingDto> GetDepartmentRankingInfoAsync(string departmentId, string period);

        #endregion

        #region Alert & Notification Operations

        /// <summary>
        /// Bölüm uyarılarını getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="includeRead">Okunmuş uyarıları dahil et</param>
        /// <returns>Bölüm uyarı listesi</returns>
        Task<List<DepartmentAlertDto>> GetDepartmentAlertsAsync(
            string departmentId,
            bool includeRead = false);

        /// <summary>
        /// Bölüm uyarısını okundu olarak işaretle
        /// </summary>
        /// <param name="alertId">Uyarı ID'si</param>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <returns>İşaretleme başarılı mı</returns>
        Task<bool> MarkAlertAsReadAsync(string alertId, string userId);

        /// <summary>
        /// Bölüm için otomatik uyarılar oluştur
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Oluşturulan uyarı sayısı</returns>
        Task<int> GenerateAutomaticAlertsAsync(string departmentId, string period);

        #endregion

        #region Report Operations

        /// <summary>
        /// Bölüm performans raporu oluştur
        /// </summary>
        /// <param name="co">Rapor parametreleri</param>
        /// <returns>Rapor verisi</returns>
        Task<object> GenerateDepartmentReportAsync(DepartmentPerformanceReportCo co);

        /// <summary>
        /// Bölüm performans raporunu export et
        /// </summary>
        /// <param name="co">Rapor parametreleri</param>
        /// <returns>Export edilen dosya bilgileri</returns>
        Task<FileExportResultDto> ExportDepartmentReportAsync(DepartmentPerformanceReportCo co);

        #endregion

        #region Calculation Operations

        /// <summary>
        /// Bölüm genel skorunu hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Hesaplanan genel skor</returns>
        Task<double> CalculateDepartmentOverallScoreAsync(string departmentId, string period);

        /// <summary>
        /// Bölüm tamamlanma oranını hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Tamamlanma oranı (0-100)</returns>
        Task<double> CalculateDepartmentCompletionRateAsync(string departmentId, string period);

        /// <summary>
        /// Bölüm büyüme oranını hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="currentPeriod">Mevcut dönem</param>
        /// <param name="previousPeriod">Önceki dönem</param>
        /// <returns>Büyüme oranı (%)</returns>
        Task<double> CalculateDepartmentGrowthRateAsync(
            string departmentId,
            string currentPeriod,
            string previousPeriod);

        #endregion

        #region Validation Operations

        /// <summary>
        /// Bölüm performans verilerini doğrula
        /// </summary>
        /// <param name="dto">Doğrulanacak DTO</param>
        /// <returns>Doğrulama sonucu</returns>
        Task<ValidationResultDto> ValidateDepartmentPerformanceDataAsync(
            DepartmentPerformanceCreateDto dto);

        /// <summary>
        /// Bölümün performans verisi girme yetkisini kontrol et
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <returns>Yetki var mı</returns>
        Task<bool> CanUserManageDepartmentPerformanceAsync(string userId, string departmentId);

        #endregion

        #region Utility Operations

        /// <summary>
        /// Bölüm performans verilerini senkronize et
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Senkronizasyon başarılı mı</returns>
        Task<bool> SynchronizeDepartmentDataAsync(string departmentId, string period);

        /// <summary>
        /// Bölüm performans cache'ini temizle
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <returns>Temizleme başarılı mı</returns>
        Task<bool> ClearDepartmentPerformanceCacheAsync(string departmentId);

        #endregion
    }


}
