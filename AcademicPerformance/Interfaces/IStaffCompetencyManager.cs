using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Personel yetkinlik değerlendirme yönetimi interface'i
    /// </summary>
    public interface IStaffCompetencyManager
    {
        #region CRUD Operations

        /// <summary>
        /// Personel yetkinlik değerlendirmesi oluştur
        /// </summary>
        /// <param name="dto">Oluşturma DTO'su</param>
        /// <param name="createdByUserId">Oluşturan kullanıcı ID'si</param>
        /// <returns>Oluşturulan personel yetkinlik DTO'su</returns>
        Task<StaffCompetencyDto> CreateStaffCompetencyAsync(
            StaffCompetencyCreateDto dto, 
            string createdByUserId);

        /// <summary>
        /// Personel yetkinlik değerlendirmesini güncelle
        /// </summary>
        /// <param name="dto">Güncelleme DTO'su</param>
        /// <param name="updatedByUserId">Güncelleyen kullanıcı ID'si</param>
        /// <returns>Güncelleme başarılı mı</returns>
        Task<bool> UpdateStaffCompetencyAsync(
            StaffCompetencyUpdateDto dto, 
            string updatedByUserId);

        /// <summary>
        /// Personel yetkinlik değerlendirmesini sil
        /// </summary>
        /// <param name="id">Kayıt ID'si</param>
        /// <param name="deletedByUserId">Silen kullanıcı ID'si</param>
        /// <returns>Silme başarılı mı</returns>
        Task<bool> DeleteStaffCompetencyAsync(string id, string deletedByUserId);

        /// <summary>
        /// Personel yetkinlik değerlendirmesini getir
        /// </summary>
        /// <param name="id">Kayıt ID'si</param>
        /// <returns>Personel yetkinlik DTO'su</returns>
        Task<StaffCompetencyDto?> GetStaffCompetencyAsync(string id);

        /// <summary>
        /// Personel yetkinlik değerlendirmelerini filtreli listele
        /// </summary>
        /// <param name="co">Filtreleme ve sayfalama parametreleri</param>
        /// <returns>Sayfalanmış personel yetkinlik listesi</returns>
        Task<PagedListDto<StaffCompetencyDto>> GetStaffCompetenciesAsync(
            PagedListCo<StaffCompetencyFilterDto> co);

        #endregion

        #region Dashboard Operations

        /// <summary>
        /// Personel yetkinlik dashboard'ını getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Personel yetkinlik dashboard DTO'su</returns>
        Task<StaffCompetencyDashboardDto> GetStaffCompetencyDashboardAsync(
            string departmentId, 
            string period);

        /// <summary>
        /// Personel yetkinlik istatistiklerini getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Yetkinlik istatistikleri</returns>
        Task<List<CompetencyAreaSummaryDto>> GetCompetencyStatisticsAsync(
            string departmentId, 
            string period);

        /// <summary>
        /// Personel yetkinlik trend analizini getir
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="periodCount">Dönem sayısı</param>
        /// <returns>Trend analizi verileri</returns>
        Task<List<CompetencyTrendDataDto>> GetCompetencyTrendAnalysisAsync(
            string staffId, 
            int periodCount = 12);

        #endregion

        #region Comparison & Analysis

        /// <summary>
        /// Personel yetkinlik karşılaştırması yap
        /// </summary>
        /// <param name="staffIds">Karşılaştırılacak personel ID'leri</param>
        /// <param name="period">Dönem</param>
        /// <returns>Karşılaştırma sonuçları</returns>
        Task<StaffCompetencyComparisonDto> CompareStaffCompetenciesAsync(
            List<string> staffIds, 
            string period);

        /// <summary>
        /// Personel yetkinlik analizi yap
        /// </summary>
        /// <param name="analysisType">Analiz türü (Individual, Department, Faculty)</param>
        /// <param name="targetId">Hedef ID'si</param>
        /// <param name="period">Dönem</param>
        /// <param name="analyzedByUserId">Analiz yapan kullanıcı ID'si</param>
        /// <returns>Analiz sonuçları</returns>
        Task<StaffCompetencyAnalysisDto> AnalyzeStaffCompetencyAsync(
            string analysisType,
            string targetId,
            string period,
            string analyzedByUserId);

        /// <summary>
        /// Yetkinlik benchmark'ını hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Benchmark sonuçları</returns>
        Task<CompetencyBenchmarkDto> CalculateCompetencyBenchmarkAsync(
            string departmentId, 
            string period);

        #endregion

        #region Evaluation Form Operations

        /// <summary>
        /// Yetkinlik değerlendirme formu oluştur
        /// </summary>
        /// <param name="dto">Form oluşturma DTO'su</param>
        /// <param name="createdByUserId">Oluşturan kullanıcı ID'si</param>
        /// <returns>Oluşturulan form DTO'su</returns>
        Task<CompetencyEvaluationFormDto> CreateEvaluationFormAsync(
            CompetencyEvaluationFormCreateDto dto,
            string createdByUserId);

        /// <summary>
        /// Yetkinlik değerlendirme formunu getir
        /// </summary>
        /// <param name="formId">Form ID'si</param>
        /// <returns>Form DTO'su</returns>
        Task<CompetencyEvaluationFormDto?> GetEvaluationFormAsync(string formId);

        /// <summary>
        /// Personel için değerlendirme formlarını getir
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Form listesi</returns>
        Task<List<CompetencyEvaluationFormDto>> GetEvaluationFormsForStaffAsync(
            string staffId, 
            string period);

        /// <summary>
        /// Değerlendirme formunu onayla
        /// </summary>
        /// <param name="formId">Form ID'si</param>
        /// <param name="approvedByUserId">Onaylayan kullanıcı ID'si</param>
        /// <returns>Onaylama başarılı mı</returns>
        Task<bool> ApproveEvaluationFormAsync(string formId, string approvedByUserId);

        #endregion

        #region Report Operations

        /// <summary>
        /// Personel yetkinlik raporu oluştur
        /// </summary>
        /// <param name="reportType">Rapor türü</param>
        /// <param name="scopeId">Kapsam ID'si</param>
        /// <param name="period">Dönem</param>
        /// <param name="generatedByUserId">Raporu oluşturan kullanıcı ID'si</param>
        /// <returns>Rapor DTO'su</returns>
        Task<StaffCompetencyReportDto> GenerateCompetencyReportAsync(
            string reportType,
            string scopeId,
            string period,
            string generatedByUserId);

        /// <summary>
        /// Personel yetkinlik raporunu export et
        /// </summary>
        /// <param name="reportId">Rapor ID'si</param>
        /// <param name="format">Export formatı (PDF, Excel, HTML)</param>
        /// <returns>Export edilen dosya bilgileri</returns>
        Task<FileExportResultDto> ExportCompetencyReportAsync(string reportId, string format);

        #endregion

        #region Calculation Operations

        /// <summary>
        /// Personel genel yetkinlik skorunu hesapla
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Genel yetkinlik skoru</returns>
        Task<double> CalculateOverallCompetencyScoreAsync(string staffId, string period);

        /// <summary>
        /// Yetkinlik alanı skorunu hesapla
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="competencyArea">Yetkinlik alanı</param>
        /// <param name="period">Dönem</param>
        /// <returns>Yetkinlik alanı skoru</returns>
        Task<double> CalculateCompetencyAreaScoreAsync(
            string staffId, 
            string competencyArea, 
            string period);

        /// <summary>
        /// Yetkinlik büyüme oranını hesapla
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="currentPeriod">Mevcut dönem</param>
        /// <param name="previousPeriod">Önceki dönem</param>
        /// <returns>Büyüme oranı (%)</returns>
        Task<double> CalculateCompetencyGrowthRateAsync(
            string staffId, 
            string currentPeriod, 
            string previousPeriod);

        #endregion

        #region Validation Operations

        /// <summary>
        /// Personel yetkinlik verilerini doğrula
        /// </summary>
        /// <param name="dto">Doğrulanacak DTO</param>
        /// <returns>Doğrulama sonucu</returns>
        Task<ValidationResultDto> ValidateStaffCompetencyDataAsync(StaffCompetencyCreateDto dto);

        /// <summary>
        /// Değerlendirme formunu doğrula
        /// </summary>
        /// <param name="dto">Doğrulanacak form DTO'su</param>
        /// <returns>Doğrulama sonucu</returns>
        Task<ValidationResultDto> ValidateEvaluationFormAsync(CompetencyEvaluationFormCreateDto dto);

        #endregion

        #region Utility Operations

        /// <summary>
        /// Personel yetkinlik verilerini senkronize et
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Senkronizasyon başarılı mı</returns>
        Task<bool> SynchronizeStaffCompetencyDataAsync(string staffId, string period);

        /// <summary>
        /// Personel yetkinlik cache'ini temizle
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <returns>Temizleme başarılı mı</returns>
        Task<bool> ClearStaffCompetencyCacheAsync(string staffId);

        #endregion
    }
}
