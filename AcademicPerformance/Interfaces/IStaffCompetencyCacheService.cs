using AcademicPerformance.Models.Dtos;

namespace AcademicPerformance.Interfaces;

/// <summary>
/// Personel yetkinlik cache servisi interface'i
/// </summary>
public interface IStaffCompetencyCacheService
{
    #region Staff Competency Cache

    /// <summary>
    /// Personel yetkinlik değerlendirmelerini cache'den getir
    /// </summary>
    Task<List<CompetencyEvaluationFormDto>?> GetStaffCompetencyEvaluationsAsync(string staffId, string period);

    /// <summary>
    /// Personel yetkinlik değerlendirmelerini cache'e kaydet
    /// </summary>
    Task SetStaffCompetencyEvaluationsAsync(string staffId, string period, List<CompetencyEvaluationFormDto> evaluations);

    #endregion

    #region Statistics Cache

    /// <summary>
    /// Yetkinlik istatistiklerini cache'den getir
    /// </summary>
    Task<Dictionary<string, CompetencyAreaSummaryDto>?> GetCompetencyStatisticsAsync(string departmentId, string period);

    /// <summary>
    /// Yetkinlik istatistiklerini cache'e kaydet
    /// </summary>
    Task SetCompetencyStatisticsAsync(string departmentId, string period, Dictionary<string, CompetencyAreaSummaryDto> statistics);

    #endregion

    #region Report Cache

    /// <summary>
    /// Yetkinlik raporunu cache'den getir
    /// </summary>
    Task<StaffCompetencyReportDto?> GetCompetencyReportAsync(string reportType, string scopeId, string period);

    /// <summary>
    /// Yetkinlik raporunu cache'e kaydet
    /// </summary>
    Task SetCompetencyReportAsync(string reportType, string scopeId, string period, StaffCompetencyReportDto report);

    #endregion

    #region Cache Invalidation

    /// <summary>
    /// Personel bazında cache'i temizle
    /// </summary>
    Task InvalidateStaffCacheAsync(string staffId);

    /// <summary>
    /// Bölüm bazında cache'i temizle
    /// </summary>
    Task InvalidateDepartmentCacheAsync(string departmentId);

    /// <summary>
    /// Tüm yetkinlik cache'ini temizle
    /// </summary>
    Task InvalidateAllCompetencyCacheAsync();

    #endregion
}
