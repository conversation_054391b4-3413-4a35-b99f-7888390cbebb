using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Generic Data Entry Store interface
    /// Epic 5 - Specialized Workflows için generic veri girişi veri katmanı
    /// </summary>
    public interface IGenericDataEntryStore
    {
        #region Definition CRUD Operations

        /// <summary>
        /// Generic data entry definition oluştur
        /// </summary>
        /// <param name="entity">Definition entity</param>
        /// <returns>Oluşturulan entity</returns>
        Task<GenericDataEntryDefinitionEntity> CreateDefinitionAsync(GenericDataEntryDefinitionEntity entity);

        /// <summary>
        /// Generic data entry definition güncelle
        /// </summary>
        /// <param name="entity">Definition entity</param>
        /// <returns>Güncelleme başarılı mı?</returns>
        Task<bool> UpdateDefinitionAsync(GenericDataEntryDefinitionEntity entity);

        /// <summary>
        /// Generic data entry definition sil (soft delete)
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <returns>Silme başarılı mı?</returns>
        Task<bool> DeleteDefinitionAsync(string definitionId);

        /// <summary>
        /// Generic data entry definition getir
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <returns>Definition entity</returns>
        Task<GenericDataEntryDefinitionEntity?> GetDefinitionByIdAsync(string definitionId);

        /// <summary>
        /// Generic data entry definition'ları listele
        /// </summary>
        /// <param name="co">Pagination ve filtreleme</param>
        /// <returns>Sayfalanmış definition listesi</returns>
        Task<PagedListDto<GenericDataEntryDefinitionEntity>> GetDefinitionsAsync(PagedListCo<GenericDataEntryFilterCo> co);

        /// <summary>
        /// Definition name ile getir
        /// </summary>
        /// <param name="name">Definition adı</param>
        /// <returns>Definition entity</returns>
        Task<GenericDataEntryDefinitionEntity?> GetDefinitionByNameAsync(string name);

        #endregion

        #region Record CRUD Operations

        /// <summary>
        /// Generic data entry record oluştur
        /// </summary>
        /// <param name="entity">Record entity</param>
        /// <returns>Oluşturulan entity</returns>
        Task<GenericDataEntryRecordEntity> CreateRecordAsync(GenericDataEntryRecordEntity entity);

        /// <summary>
        /// Generic data entry record güncelle
        /// </summary>
        /// <param name="entity">Record entity</param>
        /// <returns>Güncelleme başarılı mı?</returns>
        Task<bool> UpdateRecordAsync(GenericDataEntryRecordEntity entity);

        /// <summary>
        /// Generic data entry record sil (soft delete)
        /// </summary>
        /// <param name="recordId">Record ID</param>
        /// <returns>Silme başarılı mı?</returns>
        Task<bool> DeleteRecordAsync(string recordId);

        /// <summary>
        /// Generic data entry record getir
        /// </summary>
        /// <param name="recordId">Record ID</param>
        /// <returns>Record entity</returns>
        Task<GenericDataEntryRecordEntity?> GetRecordByIdAsync(string recordId);

        /// <summary>
        /// Generic data entry record'ları listele
        /// </summary>
        /// <param name="co">Pagination ve filtreleme</param>
        /// <returns>Sayfalanmış record listesi</returns>
        Task<PagedListDto<GenericDataEntryRecordEntity>> GetRecordsAsync(PagedListCo<GenericDataEntryFilterCo> co);

        /// <summary>
        /// Belirli definition için record'ları getir
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <param name="co">Pagination ve filtreleme</param>
        /// <returns>Sayfalanmış record listesi</returns>
        Task<PagedListDto<GenericDataEntryRecordEntity>> GetRecordsByDefinitionAsync(string definitionId, PagedListCo<GenericDataEntryFilterCo> co);

        #endregion

        #region Statistics and Analytics

        /// <summary>
        /// Definition için record sayısı
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <returns>Record sayısı</returns>
        Task<int> GetRecordCountByDefinitionAsync(string definitionId);

        /// <summary>
        /// Toplam definition sayısı
        /// </summary>
        /// <returns>Definition sayısı</returns>
        Task<int> GetTotalDefinitionCountAsync();

        /// <summary>
        /// Toplam record sayısı
        /// </summary>
        /// <returns>Record sayısı</returns>
        Task<int> GetTotalRecordCountAsync();

        /// <summary>
        /// Son eklenen record'lar
        /// </summary>
        /// <param name="count">Kaç adet</param>
        /// <returns>Son record'lar</returns>
        Task<List<GenericDataEntryRecordEntity>> GetRecentRecordsAsync(int count = 10);

        #endregion

        #region Validation Helpers

        /// <summary>
        /// Definition name benzersiz mi kontrol et
        /// </summary>
        /// <param name="name">Definition adı</param>
        /// <param name="excludeId">Hariç tutulacak ID (güncelleme için)</param>
        /// <returns>Benzersiz mi?</returns>
        Task<bool> IsDefinitionNameUniqueAsync(string name, string? excludeId = null);

        /// <summary>
        /// Definition kullanımda mı kontrol et
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <returns>Kullanımda mı?</returns>
        Task<bool> IsDefinitionInUseAsync(string definitionId);

        #endregion
    }
}
