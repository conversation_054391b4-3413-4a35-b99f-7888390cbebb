using AcademicPerformance.Services.Interfaces;
using System.Diagnostics;

namespace AcademicPerformance.Middlewares;

/// <summary>
/// Middleware for monitoring API endpoint performance
/// </summary>
public class PerformanceMonitoringMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PerformanceMonitoringMiddleware> _logger;

    public PerformanceMonitoringMiddleware(RequestDelegate next, ILogger<PerformanceMonitoringMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, IPerformanceMonitoringService performanceService)
    {
        var stopwatch = Stopwatch.StartNew();
        var originalBodyStream = context.Response.Body;
        long responseSize = 0;

        try
        {
            // Capture response size
            using var responseBody = new MemoryStream();
            context.Response.Body = responseBody;

            await _next(context);

            responseSize = responseBody.Length;
            responseBody.Seek(0, SeekOrigin.Begin);
            await responseBody.CopyToAsync(originalBodyStream);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in performance monitoring middleware");
            throw;
        }
        finally
        {
            stopwatch.Stop();
            context.Response.Body = originalBodyStream;

            // Record performance metrics (fire and forget)
            _ = Task.Run(async () =>
            {
                try
                {
                    await performanceService.RecordApiEndpointAsync(
                        context.Request.Path.Value ?? "unknown",
                        context.Request.Method,
                        stopwatch.Elapsed,
                        context.Response.StatusCode,
                        responseSize);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error recording API endpoint performance");
                }
            });
        }
    }
}
