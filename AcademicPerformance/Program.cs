using System.Reflection;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Rlx.Shared.Configs;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Factories;
using Rlx.Shared.Handlers;
using Rlx.Shared.Helpers;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Managers;
using Rlx.Shared.Middlewares;
using Rlx.Shared.Services;
using Rlx.Shared.Stores;
using StackExchange.Redis;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Services.Implementations;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Configurations;
using AcademicPerformance.Seeds;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Models.Configurations;
using System.Security.Cryptography.X509Certificates;
using Minio;
var builder = WebApplication.CreateBuilder(args);
MapsterConfig.RegisterMappings();
RlxLocalizationMapsterConfig.RegisterMappings();
RlxEnumMapsterConfig.RegisterMappings();
builder.Services.AddLocalization();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Academic Performance API",
        Version = "v1",
        Description = "Academic Performance Management System API"
    });

    // Bearer token authentication için Swagger konfigürasyonu
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer",
        BearerFormat = "JWT"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // XML dokümantasyon dosyasını dahil et (opsiyonel)
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});
builder.Services.AddHttpContextAccessor();
builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
});
builder.Services.AddDbContext<RlxIdentitySharedDbContext>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("RlxIdentityShared"));
});
// Configure performance settings
builder.Services.Configure<DatabasePerformanceConfiguration>(
    builder.Configuration.GetSection("DatabasePerformance"));
builder.Services.Configure<PaginationConfiguration>(
    builder.Configuration.GetSection("Pagination"));
builder.Services.Configure<RedisCacheConfiguration>(
    builder.Configuration.GetSection("RedisCache"));

builder.Services.AddDbContext<AcademicPerformanceDbContext>((serviceProvider, options) =>
{
    var performanceConfig = serviceProvider.GetService<IConfiguration>()!
        .GetSection("DatabasePerformance").Get<DatabasePerformanceConfiguration>() ?? new DatabasePerformanceConfiguration();

    var connectionString = builder.Configuration.GetConnectionString("AcademicPerformance");
    options.UseNpgsql(connectionString, npgsqlOptions =>
    {
        npgsqlOptions.CommandTimeout(performanceConfig.QueryTimeout);
        npgsqlOptions.EnableRetryOnFailure(
            maxRetryCount: performanceConfig.MaxRetryCount,
            maxRetryDelay: performanceConfig.MaxRetryDelay,
            errorCodesToAdd: null);
    });

    // Performance optimizations
    if (performanceConfig.EnableQueryLogging)
    {
        options.LogTo(Console.WriteLine, LogLevel.Information);
    }

    if (performanceConfig.EnableSensitiveDataLogging)
    {
        options.EnableSensitiveDataLogging();
    }

    if (performanceConfig.EnableServiceProviderCaching)
    {
        options.EnableServiceProviderCaching();
    }

    // Query splitting is configured at query level when needed

    // Set default tracking behavior
    var trackingBehavior = performanceConfig.TrackingBehavior switch
    {
        "NoTracking" => QueryTrackingBehavior.NoTracking,
        "NoTrackingWithIdentityResolution" => QueryTrackingBehavior.NoTrackingWithIdentityResolution,
        _ => QueryTrackingBehavior.TrackAll
    };
    options.UseQueryTrackingBehavior(trackingBehavior);
});
builder.Services.AddDbContext<AcademicPerformanceLocalizationDbContext>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("AcademicPerformance"));
});
var encryptionCert = new X509Certificate2("Certs/encryption.pfx", "YxDnlZdvPimyg5");
// var signingCert = new X509Certificate2("Certs/signing.pfx", "37evwu1vlX5iPl");

builder.Services.AddAuthentication(OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme);
builder.Services.AddOpenIddict()
    .AddValidation(options =>
    {
        options.SetIssuer(builder.Configuration["OpenIddict:Issuer"]!);
        options.UseSystemNetHttp();
        options.UseAspNetCore();
        options.AddEncryptionCertificate(encryptionCert);
        // options.AddSigningCertificate(signingCert);
    });
builder.Services.AddAuthorization(options =>
{
    options.ConfigurePolicies();
    PolicyConfig.ConfigurePolicies(options);
});
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigin", b =>
    {
        b.WithOrigins(builder.Configuration.GetSection("CorsOrigins").Get<string[]>()!)
               .AllowCredentials()
               .AllowAnyHeader()
               .AllowAnyMethod();
    });
});
// AcademicPerformance özel RoleClaimsTransformation (super admin bypass ile)
builder.Services.AddTransient<IClaimsTransformation, AcademicPerformance.Handlers.APRoleClaimsTransformation>();
builder.Services.AddScoped<IRlxIdentitySharedManager, RlxIdentitySharedManager>();
builder.Services.AddScoped<IRlxIdentitySharedStore, RlxIdentitySharedStore>();
builder.Services.AddScoped<IEntityChangeLogHelper, EntityChangeLogHelper>();
builder.Services.AddScoped<IRlxLocalizationManager<AcademicPerformanceLocalizationDbContext>, RlxLocalizationManager<AcademicPerformanceLocalizationDbContext>>();
builder.Services.AddScoped<IRlxLocalizationStore<AcademicPerformanceLocalizationDbContext>, RlxLocalizationStore<AcademicPerformanceLocalizationDbContext>>();
builder.Services.AddScoped<IUserContextHelper, UserContextHelper>();
builder.Services.AddScoped<IConnectionMultiplexer>(sp => ConnectionMultiplexer.Connect(builder.Configuration["RedisCache:ConnectionString"]!));
builder.Services.AddScoped<IRlxCacheService, Rlx.Shared.Services.RedisCacheService>();
builder.Services.AddScoped(typeof(IRlxSystemLogHelper<>), typeof(RlxSystemLogHelper<>));
builder.Services.AddSingleton(typeof(RlxQueueServiceFactory));
builder.Services.AddScoped<ExceptionHandler>();
builder.Services.AddSingleton(typeof(MongoDbServiceFactory));
builder.Services.AddSingleton(typeof(MongoCollectionServiceFactory<>));
builder.Services.AddHttpClient("OrganizationManagementApi", client =>
{
    client.BaseAddress = new Uri(builder.Configuration["ExternalApis:OrganizationManagement:BaseUrl"]!);
    client.DefaultRequestHeaders.Add("Accept", "application/json");
});
builder.Services.AddScoped<ICacheService, AcademicPerformance.Services.Implementations.RedisCacheService>();
builder.Services.AddScoped<IStaffCompetencyCacheService, StaffCompetencyCacheService>();
builder.Services.AddScoped<UserDataService>();
builder.Services.AddScoped<IUserDataService, CachedUserDataService>();
builder.Services.AddScoped<IOrganizationManagementApiService, OrganizationManagementApiService>();
builder.Services.AddScoped<IMongoDbService, AcademicPerformance.Services.Implementations.MongoDbService>();
// Store ve Manager kayıtları gevşek bağımlılık için
builder.Services.AddScoped<AcademicPerformance.Interfaces.ICriteriaStore, AcademicPerformance.Stores.CriteriaStore>();
builder.Services.AddScoped<AcademicPerformance.Interfaces.IFormStore, AcademicPerformance.Stores.FormStore>();
builder.Services.AddScoped<AcademicPerformance.Interfaces.IAcademicianStore, AcademicPerformance.Stores.AcademicianStore>();
builder.Services.AddScoped<AcademicPerformance.Interfaces.ISubmissionStore, AcademicPerformance.Stores.SubmissionStore>();
builder.Services.AddScoped<AcademicPerformance.Interfaces.ICriteriaManager, AcademicPerformance.Managers.CriteriaManager>();
builder.Services.AddScoped<AcademicPerformance.Interfaces.IFormManager, AcademicPerformance.Managers.FormManager>();
builder.Services.AddScoped<AcademicPerformance.Interfaces.IAcademicianManager, AcademicPerformance.Managers.AcademicianManager>();
builder.Services.AddScoped<AcademicPerformance.Interfaces.ISubmissionManager, AcademicPerformance.Managers.SubmissionManager>();
builder.Services.AddScoped<AcademicPerformance.Interfaces.IControllerManager, AcademicPerformance.Managers.ControllerManager>();
builder.Services.AddScoped<AcademicPerformance.Services.Interfaces.ISubmissionSecurityService, AcademicPerformance.Services.SubmissionSecurityService>();
builder.Services.AddScoped<AcademicPerformance.Services.Interfaces.ISubmissionAuditService, AcademicPerformance.Services.SubmissionAuditService>();

// Epic 1 Feedback Sistemi - Task 1.6
builder.Services.AddScoped<AcademicPerformance.Interfaces.IFeedbackStore, AcademicPerformance.Stores.FeedbackStore>();
builder.Services.AddScoped<AcademicPerformance.Interfaces.IFeedbackManager, AcademicPerformance.Managers.FeedbackManager>();

// Epic 2 Reporting Sistemi - Task 4 & 5
// builder.Services.AddScoped<AcademicPerformance.Interfaces.IReportingStore, AcademicPerformance.Stores.ReportingStore>();
// builder.Services.AddScoped<AcademicPerformance.Interfaces.IReportingManager, AcademicPerformance.Managers.ReportingManager>();

// Epic 4 Department Performance Module - Task 6
builder.Services.AddScoped<AcademicPerformance.Interfaces.IDepartmentPerformanceStore, AcademicPerformance.Stores.DepartmentPerformanceStore>();
builder.Services.AddScoped<AcademicPerformance.Interfaces.IDepartmentPerformanceManager, AcademicPerformance.Managers.DepartmentPerformanceManager>();

// Epic 3 Database Performance - Task 3.3
builder.Services.AddSingleton<AcademicPerformance.Services.Interfaces.IPerformanceMonitoringService, AcademicPerformance.Services.Implementations.PerformanceMonitoringService>();
builder.Services.AddHostedService<AcademicPerformance.Services.BackgroundServices.PerformanceCleanupService>();

// Epic 6 Email Notification System
builder.Services.AddScoped<AcademicPerformance.Services.Interfaces.INotificationService, AcademicPerformance.Services.Implementations.NotificationService>();
builder.Services.AddScoped<AcademicPerformance.Services.Interfaces.IEmailTemplateService, AcademicPerformance.Services.Implementations.EmailTemplateService>();

// Epic 5 Generic Data Entry Module
builder.Services.AddScoped<AcademicPerformance.Interfaces.IGenericDataEntryManager, AcademicPerformance.Managers.GenericDataEntryManager>();
builder.Services.AddScoped<AcademicPerformance.Interfaces.IGenericDataEntryStore, AcademicPerformance.Stores.GenericDataEntryStore>();

// Portfolio Control Module services
builder.Services.AddScoped<AcademicPerformance.Stores.Interfaces.IPortfolioControlStore, AcademicPerformance.Stores.PortfolioControlStore>();
builder.Services.AddScoped<AcademicPerformance.Managers.Interfaces.IPortfolioControlManager, AcademicPerformance.Managers.PortfolioControlManager>();
builder.Services.AddScoped<AcademicPerformance.Services.Interfaces.IArelBridgeApiService, AcademicPerformance.Services.Implementations.ArelBridgeApiService>();

// HttpClient for ArelBridge API
builder.Services.AddHttpClient<AcademicPerformance.Services.Interfaces.IArelBridgeApiService, AcademicPerformance.Services.Implementations.ArelBridgeApiService>(client =>
{
    var arelBridgeBaseUrl = builder.Configuration["ExternalApis:ArelBridge:BaseUrl"];
    if (!string.IsNullOrEmpty(arelBridgeBaseUrl))
    {
        client.BaseAddress = new Uri(arelBridgeBaseUrl);
    }
    client.Timeout = TimeSpan.FromSeconds(30);
});

// MinIO Configuration
builder.Services.Configure<MinIOConfiguration>(
    builder.Configuration.GetSection("MinIO"));

// MinIO Client Registration
builder.Services.AddSingleton<IMinioClient>(serviceProvider =>
{
    var config = serviceProvider.GetRequiredService<IOptions<MinIOConfiguration>>().Value;
    return new MinioClient()
        .WithEndpoint(config.Endpoint)
        .WithCredentials(config.AccessKey, config.SecretKey)
        .WithSSL(config.UseSSL)
        .Build();
});

// MinIO File Service Registration
builder.Services.AddScoped<AcademicPerformance.Services.Interfaces.IMinIOFileService,
                          AcademicPerformance.Services.Implementations.MinIOFileService>();
MapsterConfig.Configure();
var app = builder.Build();
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    // ForwardedHeaders = ForwardedHeaders.XForwardedProto | ForwardedHeaders.XForwardedFor,
    // KnownNetworks =
    // {
    //     new Microsoft.AspNetCore.HttpOverrides.IPNetwork(IPAddress.Parse("***********"), 16) // Docker ağınızı buraya yazın
    // }
    ForwardedHeaders = ForwardedHeaders.All, // Tüm header'ları kabul et
    ForwardLimit = null, // Limit kaldır (birden fazla proxy varsa)
    KnownProxies = { }, // Tüm proxy'lere güven (TEHLİKELİ, production'da kullanmayın!)
    KnownNetworks = { } // Tüm ağlara güven
});
using (var scope = app.Services.CreateScope())
{
    var dbContext = scope.ServiceProvider.GetRequiredService<AcademicPerformanceDbContext>();
    dbContext.Database.Migrate();
    var localizationDbContext = scope.ServiceProvider.GetRequiredService<AcademicPerformanceLocalizationDbContext>();
    localizationDbContext.Database.Migrate();
}
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    await DataSeed.Initialize(services);
}
app.UseMiddleware<RequestLoggingMiddleware>();
app.UseMiddleware<AcademicPerformance.Middlewares.PerformanceMonitoringMiddleware>();
app.UseLocalization();
app.UseExceptionHandler(
    errorApp =>
{
    errorApp.Run(async context =>
    {
        var exceptionHandler = app.Services.GetRequiredService<ExceptionHandler>();
        var exceptionFeature = context.Features.Get<IExceptionHandlerPathFeature>();
        if (exceptionFeature?.Error != null)
        {
            await exceptionHandler.TryHandleAsync(context, exceptionFeature.Error, context.RequestAborted);
        }
    });
});
app.Use(async (context, next) =>
{
    var logger = app.Services.GetRequiredService<ILogger<Program>>();
    logger.LogInformation($"SCHEME: {context.Request.Scheme}");
    logger.LogInformation($"IS HTTPS: {context.Request.IsHttps}");
    await next();
});
app.UseSwagger();
app.UseSwaggerUI();
app.UseCors("AllowSpecificOrigin");
app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.Run();
