{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"OrganizationManagement": "Host=********;Database=organizationmanagement;Username=********;Password=********;Pooling=true;MinPoolSize=5;MaxPoolSize=100;ConnectionLifetime=300;CommandTimeout=60", "RlxIdentityShared": "Host=********;Database=rlxidentity;Username=********;Password=********;Pooling=true;MinPoolSize=2;MaxPoolSize=20;ConnectionLifetime=300;CommandTimeout=30", "AcademicPerformance": "Host=********;Database=academicperformance;Username=********;Password=********;Pooling=true;MinPoolSize=10;MaxPoolSize=200;ConnectionLifetime=300;CommandTimeout=120;ConnectionTimeout=30;ApplicationName=AcademicPerformance-Docker", "Redis": "redis:6379"}, "ApdysMongoDb": {"ConnectionString": "************************************************************************", "DatabaseName": "ApdysDynamicData"}, "RedisCache": {"ConnectionString": "redis:6379", "DefaultExpiration": "02:00:00", "SlidingExpiration": "01:00:00", "AbsoluteExpiration": "24:00:00"}, "DatabasePerformance": {"EnableQueryLogging": false, "EnableSensitiveDataLogging": false, "QueryTimeout": 180, "BatchSize": 2000, "MaxRetryCount": 5, "MaxRetryDelay": "00:01:00", "EnableServiceProviderCaching": true, "EnableQuerySplitting": true, "TrackingBehavior": "NoTracking"}, "Pagination": {"DefaultPageSize": 25, "MaxPageSize": 200, "EnableTotalCountOptimization": true, "CachePagedResults": true, "CacheExpiration": "00:30:00"}, "MinIO": {"Endpoint": "minio:9000", "UseSSL": false, "DefaultBucket": "apdys-evidence-files", "MaxFileSize": 10485760, "AllowedExtensions": [".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png"], "AllowedMimeTypes": ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "image/jpeg", "image/png"]}, "OpenIddict": {"Issuer": "https://local-rlxidentity-api.arel.edu.tr:6010"}, "CorsOrigins": ["http://localhost:3000", "https://apdys.arel.edu.tr"], "EntityLog": {"Enabled": "0"}}