using AcademicPerformance.Services.Interfaces;

namespace AcademicPerformance.Services.BackgroundServices;

/// <summary>
/// Background service for cleaning up old performance data
/// </summary>
public class PerformanceCleanupService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<PerformanceCleanupService> _logger;
    private readonly TimeSpan _cleanupInterval = TimeSpan.FromHours(6); // Run every 6 hours
    private readonly TimeSpan _dataRetentionPeriod = TimeSpan.FromDays(7); // Keep data for 7 days

    public PerformanceCleanupService(IServiceProvider serviceProvider, ILogger<PerformanceCleanupService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Performance cleanup service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CleanupOldDataAsync(stoppingToken);
                await Task.Delay(_cleanupInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in performance cleanup service");
                // Wait a bit before retrying
                await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken);
            }
        }

        _logger.LogInformation("Performance cleanup service stopped");
    }

    private async Task CleanupOldDataAsync(CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var performanceService = scope.ServiceProvider.GetService<IPerformanceMonitoringService>();

            if (performanceService != null)
            {
                var cutoffDate = DateTime.UtcNow - _dataRetentionPeriod;
                await performanceService.CleanupOldDataAsync(cutoffDate);
                _logger.LogInformation("Performance data cleanup completed. Removed data older than {CutoffDate}", cutoffDate);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during performance data cleanup");
        }
    }
}
