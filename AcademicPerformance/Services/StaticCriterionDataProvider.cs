using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Services.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Rlx.Shared.Interfaces;
using System.Text.Json;

namespace AcademicPerformance.Services
{
    /// <summary>
    /// Statik kriterler için veri sağlayıcı servisi implementasyonu
    /// ArelBridge'den statik kriter verilerini çekmek ve AcademicPerformance formatına dönüştürmek için kullanılır
    /// </summary>
    public class StaticCriterionDataProvider : IStaticCriterionDataProvider
    {
        private readonly IArelBridgeHttpClient _arelBridgeClient;
        private readonly IRlxCacheService _cacheService;
        private readonly ILogger<StaticCriterionDataProvider> _logger;
        private readonly StaticCriterionDataProviderOptions _options;
        private readonly StaticCriterionDataProviderMetricsDto _metrics;

        private const string CACHE_KEY_PREFIX = "static_criterion_data";
        private const string MAPPINGS_CACHE_KEY = "arelbridge_criterion_mappings";

        public StaticCriterionDataProvider(
            IArelBridgeHttpClient arelBridgeClient,
            IRlxCacheService cacheService,
            ILogger<StaticCriterionDataProvider> logger,
            IOptions<StaticCriterionDataProviderOptions> options)
        {
            _arelBridgeClient = arelBridgeClient;
            _cacheService = cacheService;
            _logger = logger;
            _options = options.Value;
            _metrics = new StaticCriterionDataProviderMetricsDto();
        }

        /// <summary>
        /// Belirli bir akademisyen için belirli statik kriterlerin verilerini getirir
        /// </summary>
        public async Task<List<StaticCriterionDataDto>> GetStaticCriterionDataAsync(string academicianId, List<string> staticCriterionIds)
        {
            var startTime = DateTime.UtcNow;
            _metrics.TotalRequests++;

            try
            {
                _logger.LogInformation("Getting static criterion data for academician: {AcademicianId}, Criteria: {CriterionIds}",
                    academicianId, string.Join(", ", staticCriterionIds));

                // Cache'den kontrol et
                var cachedData = await GetCachedStaticCriterionDataAsync(academicianId, staticCriterionIds);
                if (cachedData.Any())
                {
                    _metrics.CacheHits++;
                    _logger.LogInformation("Found {Count} cached static criterion data for academician: {AcademicianId}",
                        cachedData.Count, academicianId);

                    // Cache'de olmayan kriterler için ArelBridge'den çek
                    var missingCriterionIds = staticCriterionIds.Except(cachedData.Select(d => d.StaticCriterionSystemId)).ToList();
                    if (missingCriterionIds.Any())
                    {
                        var freshData = await FetchStaticCriterionDataFromArelBridgeAsync(academicianId, missingCriterionIds);
                        cachedData.AddRange(freshData);
                    }

                    return cachedData;
                }

                _metrics.CacheMisses++;

                // ArelBridge'den veri çek
                var data = await FetchStaticCriterionDataFromArelBridgeAsync(academicianId, staticCriterionIds);

                // Cache'e kaydet
                await CacheStaticCriterionDataAsync(academicianId, data);

                _metrics.SuccessfulRequests++;
                var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
                _metrics.AverageResponseTime = (_metrics.AverageResponseTime + responseTime) / 2;

                _logger.LogInformation("Successfully retrieved {Count} static criterion data for academician: {AcademicianId}",
                    data.Count, academicianId);

                return data;
            }
            catch (Exception ex)
            {
                _metrics.FailedRequests++;
                _logger.LogError(ex, "Error getting static criterion data for academician: {AcademicianId}", academicianId);

                // Fallback değerleri döndür
                return await GetFallbackStaticCriterionDataAsync(academicianId, staticCriterionIds);
            }
        }

        /// <summary>
        /// Belirli bir akademisyen için tüm ArelBridge statik kriterlerinin verilerini getirir
        /// </summary>
        public async Task<List<StaticCriterionDataDto>> GetAllArelBridgeStaticCriterionDataAsync(string academicianId)
        {
            try
            {
                _logger.LogInformation("Getting all ArelBridge static criterion data for academician: {AcademicianId}", academicianId);

                // Kriter eşlemelerini al
                var mappings = await GetCriterionMappingsAsync();
                var staticCriterionIds = mappings.Select(m => m.AcademicPerformanceStaticCriterionId).ToList();

                return await GetStaticCriterionDataAsync(academicianId, staticCriterionIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all ArelBridge static criterion data for academician: {AcademicianId}", academicianId);
                return new List<StaticCriterionDataDto>();
            }
        }

        /// <summary>
        /// Belirli bir akademisyen için belirli bir statik kriterin verisini getirir
        /// </summary>
        public async Task<StaticCriterionDataDto?> GetSingleStaticCriterionDataAsync(string academicianId, string staticCriterionId)
        {
            try
            {
                var data = await GetStaticCriterionDataAsync(academicianId, new List<string> { staticCriterionId });
                return data.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting single static criterion data for academician: {AcademicianId}, Criterion: {CriterionId}",
                    academicianId, staticCriterionId);
                return null;
            }
        }

        /// <summary>
        /// Birden fazla akademisyen için belirli statik kriterlerin verilerini toplu olarak getirir
        /// </summary>
        public async Task<Dictionary<string, List<StaticCriterionDataDto>>> GetBatchStaticCriterionDataAsync(List<string> academicianIds, List<string> staticCriterionIds)
        {
            var results = new Dictionary<string, List<StaticCriterionDataDto>>();

            try
            {
                _logger.LogInformation("Getting batch static criterion data for {AcademicianCount} academicians, {CriterionCount} criteria",
                    academicianIds.Count, staticCriterionIds.Count);

                // Batch işlem boyutuna göre böl
                var batchSize = _options.BatchSize;
                for (int i = 0; i < academicianIds.Count; i += batchSize)
                {
                    var batch = academicianIds.Skip(i).Take(batchSize);
                    var batchTasks = batch.Select(async academicianId =>
                    {
                        var data = await GetStaticCriterionDataAsync(academicianId, staticCriterionIds);
                        return new { AcademicianId = academicianId, Data = data };
                    });

                    var batchResults = await Task.WhenAll(batchTasks);

                    foreach (var result in batchResults)
                    {
                        results[result.AcademicianId] = result.Data;
                    }

                    // Batch'ler arası bekleme
                    if (i + batchSize < academicianIds.Count)
                    {
                        await Task.Delay(_options.BatchDelayMs);
                    }
                }

                _logger.LogInformation("Successfully retrieved batch static criterion data for {Count} academicians", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting batch static criterion data");
                return results;
            }
        }

        /// <summary>
        /// ArelBridge kriter eşleme konfigürasyonlarını getirir ve günceller
        /// </summary>
        public async Task<List<ArelBridgeCriterionMappingDto>> RefreshCriterionMappingsAsync()
        {
            try
            {
                _logger.LogInformation("Refreshing criterion mappings from ArelBridge");

                var mappings = await _arelBridgeClient.GetCriterionMappingsAsync();
                if (mappings != null)
                {
                    // Cache'e kaydet
                    var json = JsonSerializer.Serialize(mappings);
                    await _cacheService.SetAsync(MAPPINGS_CACHE_KEY, json, TimeSpan.FromHours(_options.MappingsCacheTtlHours));

                    _logger.LogInformation("Successfully refreshed {Count} criterion mappings", mappings.Count);
                    return mappings;
                }

                _logger.LogWarning("Failed to refresh criterion mappings from ArelBridge");
                return new List<ArelBridgeCriterionMappingDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing criterion mappings");
                return new List<ArelBridgeCriterionMappingDto>();
            }
        }

        /// <summary>
        /// Belirli bir statik kriter için veri kaynağının mevcut olup olmadığını kontrol eder
        /// </summary>
        public async Task<bool> IsDataSourceAvailableAsync(string staticCriterionId)
        {
            try
            {
                var mappings = await GetCriterionMappingsAsync();
                var mapping = mappings.FirstOrDefault(m => m.AcademicPerformanceStaticCriterionId == staticCriterionId);

                if (mapping == null || !mapping.IsActive)
                {
                    return false;
                }

                return await _arelBridgeClient.IsHealthyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking data source availability for criterion: {CriterionId}", staticCriterionId);
                return false;
            }
        }

        /// <summary>
        /// ArelBridge servisinin sağlık durumunu kontrol eder
        /// </summary>
        public async Task<bool> IsServiceHealthyAsync()
        {
            try
            {
                return await _arelBridgeClient.IsHealthyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking ArelBridge service health");
                return false;
            }
        }

        /// <summary>
        /// Statik kriter verilerini doğrular
        /// </summary>
        public async Task<(bool IsValid, List<string> ValidationErrors)> ValidateStaticCriterionDataAsync(StaticCriterionDataDto data)
        {
            var errors = new List<string>();

            try
            {
                // Temel doğrulamalar
                if (string.IsNullOrEmpty(data.AcademicianId))
                    errors.Add("Akademisyen ID'si boş olamaz");

                if (string.IsNullOrEmpty(data.StaticCriterionSystemId))
                    errors.Add("Statik kriter sistem ID'si boş olamaz");

                if (string.IsNullOrEmpty(data.DataType))
                    errors.Add("Veri tipi boş olamaz");

                // Veri tipi doğrulaması
                if (!string.IsNullOrEmpty(data.DataType))
                {
                    switch (data.DataType.ToLower())
                    {
                        case "integer":
                            if (data.RawValue != null && !int.TryParse(data.RawValue.ToString(), out _))
                                errors.Add("Integer veri tipi için geçersiz değer");
                            break;
                        case "decimal":
                            if (data.RawValue != null && !decimal.TryParse(data.RawValue.ToString(), out _))
                                errors.Add("Decimal veri tipi için geçersiz değer");
                            break;
                        case "boolean":
                            if (data.RawValue != null && !bool.TryParse(data.RawValue.ToString(), out _))
                                errors.Add("Boolean veri tipi için geçersiz değer");
                            break;
                    }
                }

                // Puan doğrulaması
                if (data.CalculatedScore.HasValue && data.CalculatedScore < 0)
                    errors.Add("Hesaplanmış puan negatif olamaz");

                if (data.MaximumLimit.HasValue && data.CalculatedScore.HasValue && data.CalculatedScore > data.MaximumLimit)
                    errors.Add("Hesaplanmış puan maksimum limiti aşamaz");

                return await Task.FromResult((errors.Count == 0, errors));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating static criterion data");
                errors.Add($"Doğrulama hatası: {ex.Message}");
                return (false, errors);
            }
        }

        /// <summary>
        /// Statik kriter verilerini AcademicPerformance formatına dönüştürür
        /// </summary>
        public async Task<List<StaticCriterionDataDto>> ConvertArelBridgeDataToStaticCriterionDataAsync(
            ArelBridgeAcademicEducationActivityResponseDto arelBridgeData,
            List<ArelBridgeCriterionMappingDto> mappings)
        {
            var results = new List<StaticCriterionDataDto>();

            try
            {
                _logger.LogInformation("Converting ArelBridge data to static criterion data for academician: {FullName}", arelBridgeData.FullName);

                foreach (var mapping in mappings.Where(m => m.IsActive))
                {
                    var criterionData = await CreateStaticCriterionDataFromMapping(arelBridgeData, mapping);
                    if (criterionData != null)
                    {
                        results.Add(criterionData);
                    }
                }

                _logger.LogInformation("Successfully converted {Count} static criterion data", results.Count);
                return await Task.FromResult(results);
            }
            catch (Exception ex)
            {
                _metrics.DataConversionErrors++;
                _logger.LogError(ex, "Error converting ArelBridge data to static criterion data");
                return results;
            }
        }

        /// <summary>
        /// Statik kriter veri sağlayıcı performans metriklerini getirir
        /// </summary>
        public async Task<StaticCriterionDataProviderMetricsDto> GetPerformanceMetricsAsync()
        {
            try
            {
                _metrics.LastUpdated = DateTime.UtcNow;
                return await Task.FromResult(_metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting performance metrics");
                return new StaticCriterionDataProviderMetricsDto();
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// ArelBridge'den statik kriter verilerini çeker
        /// </summary>
        private async Task<List<StaticCriterionDataDto>> FetchStaticCriterionDataFromArelBridgeAsync(string academicianId, List<string> staticCriterionIds)
        {
            try
            {
                _metrics.ArelBridgeApiCalls++;

                // Kriter eşlemelerini al
                var mappings = await GetCriterionMappingsAsync();
                var relevantMappings = mappings.Where(m => staticCriterionIds.Contains(m.AcademicPerformanceStaticCriterionId)).ToList();

                if (!relevantMappings.Any())
                {
                    _logger.LogWarning("No relevant mappings found for static criterion IDs: {CriterionIds}", string.Join(", ", staticCriterionIds));
                    return new List<StaticCriterionDataDto>();
                }

                // ArelBridge'den veri çek
                var arelBridgeData = await _arelBridgeClient.GetAcademicEducationActivityAsync(academicianId);
                if (arelBridgeData == null)
                {
                    _metrics.ArelBridgeApiErrors++;
                    _logger.LogWarning("No data received from ArelBridge for academician: {AcademicianId}", academicianId);
                    return new List<StaticCriterionDataDto>();
                }

                // Veriyi dönüştür
                var convertedData = await ConvertArelBridgeDataToStaticCriterionDataAsync(arelBridgeData, relevantMappings);

                // Sadece istenen kriterleri filtrele
                return convertedData.Where(d => staticCriterionIds.Contains(d.StaticCriterionSystemId)).ToList();
            }
            catch (Exception ex)
            {
                _metrics.ArelBridgeApiErrors++;
                _logger.LogError(ex, "Error fetching static criterion data from ArelBridge for academician: {AcademicianId}", academicianId);
                return new List<StaticCriterionDataDto>();
            }
        }

        /// <summary>
        /// Cache'den statik kriter verilerini getirir
        /// </summary>
        private async Task<List<StaticCriterionDataDto>> GetCachedStaticCriterionDataAsync(string academicianId, List<string> staticCriterionIds)
        {
            var results = new List<StaticCriterionDataDto>();

            try
            {
                foreach (var criterionId in staticCriterionIds)
                {
                    var cacheKey = $"{CACHE_KEY_PREFIX}:{academicianId}:{criterionId}";
                    var cachedJson = await _cacheService.GetAsync(cacheKey);

                    if (!string.IsNullOrEmpty(cachedJson))
                    {
                        var cachedData = JsonSerializer.Deserialize<StaticCriterionDataDto>(cachedJson);
                        if (cachedData != null)
                        {
                            cachedData.IsFromCache = true;
                            results.Add(cachedData);
                        }
                    }
                }

                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cached static criterion data for academician: {AcademicianId}", academicianId);
                return new List<StaticCriterionDataDto>();
            }
        }

        /// <summary>
        /// Statik kriter verilerini cache'e kaydeder
        /// </summary>
        private async Task CacheStaticCriterionDataAsync(string academicianId, List<StaticCriterionDataDto> data)
        {
            try
            {
                foreach (var item in data)
                {
                    var cacheKey = $"{CACHE_KEY_PREFIX}:{academicianId}:{item.StaticCriterionSystemId}";
                    var json = JsonSerializer.Serialize(item);
                    await _cacheService.SetAsync(cacheKey, json, TimeSpan.FromMinutes(_options.CacheTtlMinutes));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error caching static criterion data for academician: {AcademicianId}", academicianId);
            }
        }

        /// <summary>
        /// Kriter eşlemelerini getirir (cache'den veya ArelBridge'den)
        /// </summary>
        private async Task<List<ArelBridgeCriterionMappingDto>> GetCriterionMappingsAsync()
        {
            try
            {
                // Cache'den kontrol et
                var cachedJson = await _cacheService.GetAsync(MAPPINGS_CACHE_KEY);
                if (!string.IsNullOrEmpty(cachedJson))
                {
                    var cachedMappings = JsonSerializer.Deserialize<List<ArelBridgeCriterionMappingDto>>(cachedJson);
                    if (cachedMappings != null)
                    {
                        return cachedMappings;
                    }
                }

                // Cache'de yoksa ArelBridge'den çek
                return await RefreshCriterionMappingsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting criterion mappings");
                return new List<ArelBridgeCriterionMappingDto>();
            }
        }

        /// <summary>
        /// Fallback statik kriter verilerini döndürür
        /// </summary>
        private async Task<List<StaticCriterionDataDto>> GetFallbackStaticCriterionDataAsync(string academicianId, List<string> staticCriterionIds)
        {
            var results = new List<StaticCriterionDataDto>();

            try
            {
                foreach (var criterionId in staticCriterionIds)
                {
                    results.Add(new StaticCriterionDataDto
                    {
                        AcademicianId = academicianId,
                        StaticCriterionSystemId = criterionId,
                        CriterionName = "Fallback Criterion",
                        DataType = "Integer",
                        RawValue = _options.FallbackValue,
                        CalculatedScore = 0,
                        Status = "Fallback",
                        ErrorMessage = "ArelBridge servisinden veri alınamadı, fallback değer kullanıldı",
                        CalculationDate = DateTime.UtcNow
                    });
                }

                return await Task.FromResult(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating fallback static criterion data");
                return results;
            }
        }

        /// <summary>
        /// Eşleme konfigürasyonundan statik kriter verisi oluşturur
        /// Sadece ham veri ve katsayı döndürür, puan hesaplaması raporlama sisteminde yapılacak
        /// </summary>
        private async Task<StaticCriterionDataDto?> CreateStaticCriterionDataFromMapping(
            ArelBridgeAcademicEducationActivityResponseDto arelBridgeData,
            ArelBridgeCriterionMappingDto mapping)
        {
            try
            {
                var criterionData = new StaticCriterionDataDto
                {
                    AcademicianId = arelBridgeData.FullName ?? "Unknown", // TC yerine isim kullanıyoruz
                    StaticCriterionSystemId = mapping.AcademicPerformanceStaticCriterionId,
                    CriterionName = mapping.CriterionName,
                    DataType = mapping.DataType,
                    DataSource = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationDate = arelBridgeData.CalculationDate,
                    Status = arelBridgeData.Status
                };

                // ArelBridge kriter ID'sine göre sadece sayı değerini ata
                switch (mapping.ArelBridgeCriterionId)
                {
                    case "A1":
                        criterionData.RawValue = arelBridgeData.Criteria!.A1_Count;
                        break;
                    case "A2_1":
                        criterionData.RawValue = arelBridgeData.Criteria!.A2_1_Count;
                        break;
                    case "A3_1":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_1_Count;
                        break;
                    case "A3_2":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_2_Count;
                        break;
                    case "A3_3":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_3_Count;
                        break;
                    case "A3_4":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_4_Count;
                        break;
                    case "A3_5":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_5_Count;
                        break;
                    case "A3_6":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_6_Count;
                        break;
                    case "A3_7":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_7_Count;
                        break;
                    case "A3_8":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_8_Count;
                        break;
                    case "A3_9":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_9_Count;
                        break;
                    case "A3_10":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_10_Count;
                        break;
                    case "A3_11":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_11_Count;
                        break;
                    case "A3_12":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_12_Count;
                        break;
                    case "A3_13":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_13_Count;
                        break;
                    case "A3_14":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_14_Count;
                        break;
                    case "A3_15":
                        criterionData.RawValue = arelBridgeData.Criteria!.A3_15_Count;
                        break;
                    case "A4":
                        criterionData.RawValue = arelBridgeData.Criteria!.A4_Count;
                        break;
                    case "A5":
                        criterionData.RawValue = arelBridgeData.Criteria!.A5_Count;
                        break;
                    default:
                        _logger.LogWarning("Unknown ArelBridge criterion ID: {CriterionId}", mapping.ArelBridgeCriterionId);
                        return null;
                }

                // Veritabanından katsayıyı al (UI'dan değiştirilebilir)
                var coefficient = await GetCoefficientFromDatabase(mapping.AcademicPerformanceStaticCriterionId);
                criterionData.Coefficient = coefficient;

                // Puan hesaplaması yapılmaz, raporlama sisteminde yapılacak
                criterionData.CalculatedScore = null;

                return criterionData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating static criterion data from mapping for criterion: {CriterionId}", mapping.ArelBridgeCriterionId);
                return null;
            }
        }

        /// <summary>
        /// Veritabanından katsayı değerini getirir
        /// </summary>
        private async Task<decimal?> GetCoefficientFromDatabase(string staticCriterionSystemId)
        {
            try
            {
                // Bu metod StaticCriterionCoefficientService ile implement edilecek
                // Şimdilik varsayılan değer döndürüyoruz
                var defaultCoefficients = new Dictionary<string, decimal>
                {
                    { "ARELBRIDGE_A1_COURSE_LOAD", 0.5m },
                    { "ARELBRIDGE_A2_1_STUDENT_EVALUATION", 50m },
                    { "ARELBRIDGE_A3_1_ASSIGNED_STUDENTS", 2m },
                    { "ARELBRIDGE_A3_2_STUDENT_COMMUNICATION", 1m },
                    { "ARELBRIDGE_A3_3_STUDENT_SATISFACTION", 10m },
                    { "ARELBRIDGE_A3_4_ADVISOR_RETENTION_SUCCESS", 5m },
                    { "ARELBRIDGE_A3_5_STUDENT_CLUB_ADVISORY", 10m },
                    { "ARELBRIDGE_A3_6_STUDENT_GPA_AVERAGE", 20m },
                    { "ARELBRIDGE_A3_7_HONOR_STUDENTS", 15m },
                    { "ARELBRIDGE_A3_8_ABSENTEEISM_RATE", 10m },
                    { "ARELBRIDGE_A3_9_ERASMUS_PARTICIPATION", 5m },
                    { "ARELBRIDGE_A3_10_SOCIAL_CLUB_MEMBERSHIP", 3m },
                    { "ARELBRIDGE_A3_11_SPORTS_CLUB_MEMBERSHIP", 3m },
                    { "ARELBRIDGE_A3_12_DOUBLE_MAJOR_MINOR", 5m },
                    { "ARELBRIDGE_A3_13_UNIVERSITY_TEAMS", 5m },
                    { "ARELBRIDGE_A3_14_TECHNOPARK_TTO_PROJECTS", 10m },
                    { "ARELBRIDGE_A3_15_TUBITAK_2209_APPLICATIONS", 5m },
                    { "ARELBRIDGE_A4_MASTERS_SUPERVISION", 30m },
                    { "ARELBRIDGE_A5_PHD_SUPERVISION", 50m }
                };

                return await Task.FromResult(defaultCoefficients.GetValueOrDefault(staticCriterionSystemId, 1m));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting coefficient from database for criterion: {CriterionId}", staticCriterionSystemId);
                return 1m; // Varsayılan katsayı
            }
        }

        #endregion
    }

    /// <summary>
    /// Statik kriter veri sağlayıcı konfigürasyon seçenekleri
    /// </summary>
    public class StaticCriterionDataProviderOptions
    {
        public const string SectionName = "StaticCriterionDataProvider";

        /// <summary>
        /// Cache TTL (dakika)
        /// </summary>
        public int CacheTtlMinutes { get; set; } = 60;

        /// <summary>
        /// Mappings cache TTL (saat)
        /// </summary>
        public int MappingsCacheTtlHours { get; set; } = 24;

        /// <summary>
        /// Batch işlem boyutu
        /// </summary>
        public int BatchSize { get; set; } = 10;

        /// <summary>
        /// Batch'ler arası bekleme süresi (ms)
        /// </summary>
        public int BatchDelayMs { get; set; } = 1000;

        /// <summary>
        /// Fallback değeri
        /// </summary>
        public object? FallbackValue { get; set; } = 0;

        /// <summary>
        /// Retry sayısı
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// Retry bekleme süresi (ms)
        /// </summary>
        public int RetryDelayMs { get; set; } = 2000;
    }
}
