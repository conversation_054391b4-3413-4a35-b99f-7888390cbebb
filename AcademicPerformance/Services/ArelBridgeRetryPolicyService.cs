using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.Extensions.Http;
using System.Net;

namespace AcademicPerformance.Services
{
    /// <summary>
    /// ArelBridge servis bağlantı hataları için retry mekanizması ve fallback stratejileri
    /// Polly kütüphanesi kull<PERSON> resilient HTTP istekleri sağlar
    /// </summary>
    public class ArelBridgeRetryPolicyService
    {
        private readonly ILogger<ArelBridgeRetryPolicyService> _logger;
        private readonly ArelBridgeRetryPolicyOptions _options;

        public ArelBridgeRetryPolicyService(
            ILogger<ArelBridgeRetryPolicyService> logger,
            IOptions<ArelBridgeRetryPolicyOptions> options)
        {
            _logger = logger;
            _options = options.Value;
        }

        /// <summary>
        /// HTTP istekleri için retry policy'si oluşturur
        /// </summary>
        public IAsyncPolicy<HttpResponseMessage> CreateHttpRetryPolicy()
        {
            return Policy
                .Handle<HttpRequestException>()
                .Or<TaskCanceledException>()
                .OrResult<HttpResponseMessage>(r => !r.IsSuccessStatusCode && ShouldRetry(r.StatusCode))
                .WaitAndRetryAsync(
                    retryCount: _options.RetryCount,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromMilliseconds(
                        _options.BaseDelayMs * Math.Pow(_options.BackoffMultiplier, retryAttempt - 1)),
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        var exception = outcome.Exception;
                        var result = outcome.Result;

                        if (exception != null)
                        {
                            _logger.LogWarning("ArelBridge HTTP request failed (attempt {RetryCount}/{MaxRetries}). " +
                                "Retrying in {Delay}ms. Exception: {Exception}",
                                retryCount, _options.RetryCount, timespan.TotalMilliseconds, exception.Message);
                        }
                        else if (result != null)
                        {
                            _logger.LogWarning("ArelBridge HTTP request returned {StatusCode} (attempt {RetryCount}/{MaxRetries}). " +
                                "Retrying in {Delay}ms.",
                                result.StatusCode, retryCount, _options.RetryCount, timespan.TotalMilliseconds);
                        }
                    });
        }

        /// <summary>
        /// Circuit breaker policy'si oluşturur
        /// </summary>
        public IAsyncPolicy<HttpResponseMessage> CreateCircuitBreakerPolicy()
        {
            return Policy
                .Handle<HttpRequestException>()
                .Or<TaskCanceledException>()
                .OrResult<HttpResponseMessage>(r => !r.IsSuccessStatusCode && ShouldBreakCircuit(r.StatusCode))
                .CircuitBreakerAsync(
                    handledEventsAllowedBeforeBreaking: _options.CircuitBreakerFailureThreshold,
                    durationOfBreak: TimeSpan.FromSeconds(_options.CircuitBreakerDurationSeconds),
                    onBreak: (exception, duration) =>
                    {
                        _logger.LogError("ArelBridge circuit breaker opened for {Duration}s. Exception: {Exception}",
                            duration.TotalSeconds, exception?.Exception?.Message ?? "HTTP error");
                    },
                    onReset: () =>
                    {
                        _logger.LogInformation("ArelBridge circuit breaker reset - service is healthy again");
                    },
                    onHalfOpen: () =>
                    {
                        _logger.LogInformation("ArelBridge circuit breaker half-open - testing service health");
                    });
        }

        /// <summary>
        /// Timeout policy'si oluşturur
        /// </summary>
        public IAsyncPolicy<HttpResponseMessage> CreateTimeoutPolicy()
        {
            return Policy.TimeoutAsync<HttpResponseMessage>(
                timeout: TimeSpan.FromSeconds(_options.TimeoutSeconds));
        }

        /// <summary>
        /// Fallback policy'si oluşturur
        /// </summary>
        public IAsyncPolicy<T> CreateFallbackPolicy<T>(T fallbackValue, string operationName)
        {
            return Policy<T>
                .Handle<Exception>()
                .FallbackAsync(fallbackValue);
        }

        /// <summary>
        /// Kombinasyon policy'si oluşturur (retry + circuit breaker + timeout)
        /// </summary>
        public IAsyncPolicy<HttpResponseMessage> CreateCombinedPolicy()
        {
            var retryPolicy = CreateHttpRetryPolicy();
            var circuitBreakerPolicy = CreateCircuitBreakerPolicy();
            var timeoutPolicy = CreateTimeoutPolicy();

            // Policy sırası: Timeout -> Retry -> Circuit Breaker
            return Policy.WrapAsync(timeoutPolicy, retryPolicy, circuitBreakerPolicy);
        }

        /// <summary>
        /// Veri işleme için retry policy'si oluşturur
        /// </summary>
        public IAsyncPolicy CreateDataProcessingRetryPolicy()
        {
            return Policy
                .Handle<Exception>(ex => !(ex is ArgumentNullException || ex is ArgumentException))
                .WaitAndRetryAsync(
                    retryCount: _options.DataProcessingRetryCount,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromMilliseconds(_options.BaseDelayMs),
                    onRetry: (exception, timespan, retryCount, context) =>
                    {
                        _logger.LogWarning("Data processing failed (attempt {RetryCount}/{MaxRetries}). " +
                            "Retrying in {Delay}ms. Exception: {Exception}",
                            retryCount, _options.DataProcessingRetryCount, timespan.TotalMilliseconds, exception.Message);
                    });
        }

        /// <summary>
        /// Cache işlemleri için retry policy'si oluşturur
        /// </summary>
        public IAsyncPolicy CreateCacheRetryPolicy()
        {
            return Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(
                    retryCount: _options.CacheRetryCount,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromMilliseconds(_options.BaseDelayMs / 2),
                    onRetry: (exception, timespan, retryCount, context) =>
                    {
                        _logger.LogWarning("Cache operation failed (attempt {RetryCount}/{MaxRetries}). " +
                            "Retrying in {Delay}ms. Exception: {Exception}",
                            retryCount, _options.CacheRetryCount, timespan.TotalMilliseconds, exception.Message);
                    });
        }

        /// <summary>
        /// Bulk operations için retry policy'si oluşturur
        /// </summary>
        public IAsyncPolicy CreateBulkOperationRetryPolicy()
        {
            return Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(
                    retryCount: _options.BulkOperationRetryCount,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromMilliseconds(
                        _options.BaseDelayMs * retryAttempt), // Linear backoff for bulk operations
                    onRetry: (exception, timespan, retryCount, context) =>
                    {
                        _logger.LogWarning("Bulk operation failed (attempt {RetryCount}/{MaxRetries}). " +
                            "Retrying in {Delay}ms. Exception: {Exception}",
                            retryCount, _options.BulkOperationRetryCount, timespan.TotalMilliseconds, exception.Message);
                    });
        }

        /// <summary>
        /// Health check için basit retry policy'si oluşturur
        /// </summary>
        public IAsyncPolicy<bool> CreateHealthCheckRetryPolicy()
        {
            return Policy<bool>
                .Handle<Exception>()
                .WaitAndRetryAsync(
                    retryCount: 2, // Health check için az retry
                    sleepDurationProvider: retryAttempt => TimeSpan.FromMilliseconds(500),
                    onRetry: (exception, timespan, retryCount, context) =>
                    {
                        _logger.LogDebug("Health check failed (attempt {RetryCount}/2). " +
                            "Retrying in {Delay}ms. Exception: {Exception}",
                            retryCount, timespan.TotalMilliseconds, exception.Exception?.Message ?? "Unknown");
                    });
        }

        #region Private Helper Methods

        /// <summary>
        /// HTTP status code'una göre retry yapılıp yapılmayacağını belirler
        /// </summary>
        private bool ShouldRetry(HttpStatusCode statusCode)
        {
            return statusCode switch
            {
                HttpStatusCode.RequestTimeout => true,
                HttpStatusCode.InternalServerError => true,
                HttpStatusCode.BadGateway => true,
                HttpStatusCode.ServiceUnavailable => true,
                HttpStatusCode.GatewayTimeout => true,
                HttpStatusCode.TooManyRequests => true,
                HttpStatusCode.Unauthorized => false, // Auth hatası için retry yapma
                HttpStatusCode.Forbidden => false,    // Permission hatası için retry yapma
                HttpStatusCode.NotFound => false,     // Not found için retry yapma
                HttpStatusCode.BadRequest => false,   // Bad request için retry yapma
                _ => false
            };
        }

        /// <summary>
        /// HTTP status code'una göre circuit breaker açılıp açılmayacağını belirler
        /// </summary>
        private bool ShouldBreakCircuit(HttpStatusCode statusCode)
        {
            return statusCode switch
            {
                HttpStatusCode.InternalServerError => true,
                HttpStatusCode.BadGateway => true,
                HttpStatusCode.ServiceUnavailable => true,
                HttpStatusCode.GatewayTimeout => true,
                _ => false
            };
        }

        #endregion
    }

    /// <summary>
    /// ArelBridge retry policy konfigürasyon seçenekleri
    /// </summary>
    public class ArelBridgeRetryPolicyOptions
    {
        public const string SectionName = "ArelBridgeRetryPolicy";

        /// <summary>
        /// HTTP istekleri için retry sayısı
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// Temel bekleme süresi (ms)
        /// </summary>
        public int BaseDelayMs { get; set; } = 1000;

        /// <summary>
        /// Exponential backoff çarpanı
        /// </summary>
        public double BackoffMultiplier { get; set; } = 2.0;

        /// <summary>
        /// HTTP timeout süresi (saniye)
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Circuit breaker hata eşiği
        /// </summary>
        public int CircuitBreakerFailureThreshold { get; set; } = 5;

        /// <summary>
        /// Circuit breaker açık kalma süresi (saniye)
        /// </summary>
        public int CircuitBreakerDurationSeconds { get; set; } = 60;

        /// <summary>
        /// Veri işleme retry sayısı
        /// </summary>
        public int DataProcessingRetryCount { get; set; } = 2;

        /// <summary>
        /// Cache işlemleri retry sayısı
        /// </summary>
        public int CacheRetryCount { get; set; } = 2;

        /// <summary>
        /// Bulk operations retry sayısı
        /// </summary>
        public int BulkOperationRetryCount { get; set; } = 3;

        /// <summary>
        /// Jitter kullan (rastgele gecikme)
        /// </summary>
        public bool UseJitter { get; set; } = true;

        /// <summary>
        /// Maximum jitter süresi (ms)
        /// </summary>
        public int MaxJitterMs { get; set; } = 500;
    }

    /// <summary>
    /// Retry policy extension methods
    /// </summary>
    public static class RetryPolicyExtensions
    {
        /// <summary>
        /// HttpClient için retry policy'si ekler
        /// </summary>
        public static IServiceCollection AddArelBridgeRetryPolicies(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<ArelBridgeRetryPolicyOptions>(
                configuration.GetSection(ArelBridgeRetryPolicyOptions.SectionName));

            services.AddSingleton<ArelBridgeRetryPolicyService>();

            return services;
        }

        /// <summary>
        /// HttpClient'a retry policy'si uygular
        /// </summary>
        public static IHttpClientBuilder AddArelBridgeRetryPolicy(this IHttpClientBuilder builder)
        {
            return builder.AddPolicyHandler((serviceProvider, request) =>
            {
                var retryService = serviceProvider.GetRequiredService<ArelBridgeRetryPolicyService>();
                return retryService.CreateCombinedPolicy();
            });
        }
    }
}
