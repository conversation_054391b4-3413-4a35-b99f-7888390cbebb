using AcademicPerformance.DbContexts;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace AcademicPerformance.Services
{
    /// <summary>
    /// Submission audit service - Epic 4 Task 3.1
    /// Submission işlemlerinin audit trail'inin tutulması
    /// </summary>
    public class SubmissionAuditService : ISubmissionAuditService
    {
        private readonly AcademicPerformanceDbContext _dbContext;
        private readonly ILogger<SubmissionAuditService> _logger;

        public SubmissionAuditService(
            AcademicPerformanceDbContext dbContext,
            ILogger<SubmissionAuditService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// Audit kaydı oluştur
        /// </summary>
        /// <param name="submissionId">Submission AutoIncrement ID'si</param>
        /// <param name="action">Ya<PERSON>ılan action</param>
        /// <param name="performedByUserId">Action'ı gerçekleştiren kullanıcı ID'si</param>
        /// <param name="comments">Yorumlar (opsiyonel)</param>
        /// <param name="oldValue">Eski değer (opsiyonel)</param>
        /// <param name="newValue">Yeni değer (opsiyonel)</param>
        /// <param name="entityType">Entity tipi (opsiyonel)</param>
        /// <param name="entityId">Entity ID'si (opsiyonel)</param>
        /// <param name="category">Kategori (opsiyonel)</param>
        /// <param name="metadata">Ek metadata (opsiyonel)</param>
        /// <returns>Audit kaydı başarılı mı?</returns>
        public async Task<bool> LogAsync(
            int submissionId,
            string action,
            string performedByUserId,
            string? comments = null,
            string? oldValue = null,
            string? newValue = null,
            string? entityType = null,
            string? entityId = null,
            string? category = null,
            object? metadata = null)
        {
            try
            {
                var auditEntry = new SubmissionAuditEntity
                {
                    Id = Guid.NewGuid().ToString(),
                    AcademicSubmissionAutoIncrementId = submissionId,
                    Action = action,
                    PerformedByUserId = performedByUserId,
                    Comments = comments,
                    PerformedAt = DateTime.UtcNow,
                    OldValue = oldValue,
                    NewValue = newValue,
                    EntityType = entityType ?? AuditEntityTypes.Submission,
                    EntityId = entityId,
                    Category = category ?? GetCategoryFromAction(action),
                    IsSuccessful = true,
                    Metadata = metadata != null ? JsonSerializer.Serialize(metadata) : null
                };

                _dbContext.SubmissionAudits.Add(auditEntry);
                await _dbContext.SaveChangesAsync();

                _logger.LogInformation($"Audit kaydı oluşturuldu: Submission: {submissionId}, Action: {action}, User: {performedByUserId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Audit kaydı oluşturma hatası: Submission: {submissionId}, Action: {action}, User: {performedByUserId}");
                return false;
            }
        }

        /// <summary>
        /// Status change audit kaydı oluştur
        /// </summary>
        /// <param name="submissionId">Submission AutoIncrement ID'si</param>
        /// <param name="oldStatus">Eski status</param>
        /// <param name="newStatus">Yeni status</param>
        /// <param name="performedByUserId">Action'ı gerçekleştiren kullanıcı ID'si</param>
        /// <param name="comments">Yorumlar (opsiyonel)</param>
        /// <returns>Audit kaydı başarılı mı?</returns>
        public async Task<bool> LogStatusChangeAsync(
            int submissionId,
            string oldStatus,
            string newStatus,
            string performedByUserId,
            string? comments = null)
        {
            var action = GetStatusChangeAction(newStatus);
            return await LogAsync(
                submissionId,
                action,
                performedByUserId,
                comments,
                oldStatus,
                newStatus,
                AuditEntityTypes.Submission,
                submissionId.ToString(),
                AuditCategories.StatusChange);
        }

        /// <summary>
        /// File operation audit kaydı oluştur
        /// </summary>
        /// <param name="submissionId">Submission AutoIncrement ID'si</param>
        /// <param name="fileId">File ID'si</param>
        /// <param name="fileName">Dosya adı</param>
        /// <param name="operation">File operation (Upload, Delete, Download)</param>
        /// <param name="performedByUserId">Action'ı gerçekleştiren kullanıcı ID'si</param>
        /// <param name="fileSize">Dosya boyutu (opsiyonel)</param>
        /// <returns>Audit kaydı başarılı mı?</returns>
        public async Task<bool> LogFileOperationAsync(
            int submissionId,
            string fileId,
            string fileName,
            string operation,
            string performedByUserId,
            long? fileSize = null)
        {
            var action = $"File{operation}"; // FileUploaded, FileDeleted, FileDownloaded
            var metadata = new
            {
                fileName = fileName,
                fileSize = fileSize,
                operation = operation
            };

            return await LogAsync(
                submissionId,
                action,
                performedByUserId,
                $"File {operation.ToLower()}: {fileName}",
                null,
                fileName,
                AuditEntityTypes.EvidenceFile,
                fileId,
                AuditCategories.FileOperation,
                metadata);
        }

        /// <summary>
        /// Criterion data operation audit kaydı oluştur
        /// </summary>
        /// <param name="submissionId">Submission AutoIncrement ID'si</param>
        /// <param name="criterionLinkId">Criterion link ID'si</param>
        /// <param name="operation">Data operation (Input, Update, Delete)</param>
        /// <param name="performedByUserId">Action'ı gerçekleştiren kullanıcı ID'si</param>
        /// <param name="oldData">Eski data (opsiyonel)</param>
        /// <param name="newData">Yeni data (opsiyonel)</param>
        /// <returns>Audit kaydı başarılı mı?</returns>
        public async Task<bool> LogCriterionDataOperationAsync(
            int submissionId,
            string criterionLinkId,
            string operation,
            string performedByUserId,
            object? oldData = null,
            object? newData = null)
        {
            var action = $"CriterionData{operation}"; // CriterionDataInputted, CriterionDataUpdated, CriterionDataDeleted
            var metadata = new
            {
                criterionLinkId = criterionLinkId,
                operation = operation,
                oldData = oldData,
                newData = newData
            };

            return await LogAsync(
                submissionId,
                action,
                performedByUserId,
                $"Criterion data {operation.ToLower()}: {criterionLinkId}",
                oldData?.ToString(),
                newData?.ToString(),
                AuditEntityTypes.CriterionData,
                criterionLinkId,
                AuditCategories.DataInput,
                metadata);
        }

        /// <summary>
        /// Submission'a ait audit kayıtlarını getir
        /// </summary>
        /// <param name="submissionId">Submission AutoIncrement ID'si</param>
        /// <param name="pageSize">Sayfa boyutu (default: 50)</param>
        /// <param name="pageNumber">Sayfa numarası (default: 1)</param>
        /// <returns>Audit kayıtları</returns>
        public async Task<List<SubmissionAuditEntity>> GetAuditTrailAsync(
            int submissionId,
            int pageSize = 50,
            int pageNumber = 1)
        {
            try
            {
                return await _dbContext.SubmissionAudits
                    .Where(a => a.AcademicSubmissionAutoIncrementId == submissionId)
                    .OrderByDescending(a => a.PerformedAt)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Audit trail getirme hatası: Submission: {submissionId}");
                return new List<SubmissionAuditEntity>();
            }
        }

        /// <summary>
        /// Kullanıcının audit kayıtlarını getir
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="pageSize">Sayfa boyutu (default: 50)</param>
        /// <param name="pageNumber">Sayfa numarası (default: 1)</param>
        /// <returns>Audit kayıtları</returns>
        public async Task<List<SubmissionAuditEntity>> GetUserAuditTrailAsync(
            string userId,
            int pageSize = 50,
            int pageNumber = 1)
        {
            try
            {
                return await _dbContext.SubmissionAudits
                    .Where(a => a.PerformedByUserId == userId)
                    .OrderByDescending(a => a.PerformedAt)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"User audit trail getirme hatası: User: {userId}");
                return new List<SubmissionAuditEntity>();
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Action'dan kategori belirle
        /// </summary>
        private string GetCategoryFromAction(string action)
        {
            return action switch
            {
                var a when a.Contains("Status") || a.Contains("Approved") || a.Contains("Rejected") || a.Contains("Submitted") => AuditCategories.StatusChange,
                var a when a.Contains("File") => AuditCategories.FileOperation,
                var a when a.Contains("Data") || a.Contains("Criterion") => AuditCategories.DataInput,
                var a when a.Contains("View") || a.Contains("Access") => AuditCategories.AccessControl,
                var a when a.Contains("System") || a.Contains("Config") => AuditCategories.SystemOperation,
                _ => AuditCategories.UserAction
            };
        }

        /// <summary>
        /// Status'tan action belirle
        /// </summary>
        private string GetStatusChangeAction(string newStatus)
        {
            return newStatus switch
            {
                "Submitted" => AuditActions.SubmissionSubmitted,
                "Approved" => AuditActions.SubmissionApproved,
                "Rejected" => AuditActions.SubmissionRejected,
                "UnderReview" => AuditActions.ReviewStarted,
                "Draft" => AuditActions.SubmissionReturned,
                _ => "StatusChanged"
            };
        }

        #endregion
    }
}
