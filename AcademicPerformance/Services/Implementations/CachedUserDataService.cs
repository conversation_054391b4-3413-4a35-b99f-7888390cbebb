using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Services.Interfaces;

namespace AcademicPerformance.Services.Implementations
{
    public class CachedUserDataService : IUserDataService
    {
        private readonly IUserDataService _userDataService;
        private readonly ICacheService _cacheService;
        private readonly ILogger<CachedUserDataService> _logger;
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromHours(1);

        public CachedUserDataService(
            UserDataService userDataService,
            ICacheService cacheService,
            ILogger<CachedUserDataService> logger)
        {
            _userDataService = userDataService;
            _cacheService = cacheService;
            _logger = logger;
        }

        public async Task<UserProfileDto> GetUserProfileAsync(string userId)
        {
            var cacheKey = $"user:profile:{userId}";
            
            var cachedProfile = await _cacheService.GetAsync<UserProfileDto>(cacheKey);
            if (cachedProfile != null)
            {
                _logger.LogDebug("User profile for {UserId} retrieved from cache", userId);
                return cachedProfile;
            }

            var profile = await _userDataService.GetUserProfileAsync(userId);
            
            await _cacheService.SetAsync(cacheKey, profile, _cacheExpiry);
            _logger.LogDebug("User profile for {UserId} cached", userId);
            
            return profile;
        }

        public async Task<UserContextCo> GetUserContextAsync(string userId)
        {
            var cacheKey = $"user:context:{userId}";
            
            var cachedContext = await _cacheService.GetAsync<UserContextCo>(cacheKey);
            if (cachedContext != null)
            {
                _logger.LogDebug("User context for {UserId} retrieved from cache", userId);
                return cachedContext;
            }

            var context = await _userDataService.GetUserContextAsync(userId);
            
            await _cacheService.SetAsync(cacheKey, context, _cacheExpiry);
            _logger.LogDebug("User context for {UserId} cached", userId);
            
            return context;
        }

        public async Task<string> GetUserDepartmentAsync(string userId)
        {
            var cacheKey = $"user:department:{userId}";
            
            var cachedDepartment = await _cacheService.GetAsync<string>(cacheKey);
            if (!string.IsNullOrEmpty(cachedDepartment))
            {
                _logger.LogDebug("User department for {UserId} retrieved from cache", userId);
                return cachedDepartment;
            }

            var department = await _userDataService.GetUserDepartmentAsync(userId);
            
            await _cacheService.SetAsync(cacheKey, department, _cacheExpiry);
            _logger.LogDebug("User department for {UserId} cached", userId);
            
            return department;
        }

        public async Task<string> GetUserAcademicCadreAsync(string userId)
        {
            var cacheKey = $"user:academicCadre:{userId}";
            
            var cachedCadre = await _cacheService.GetAsync<string>(cacheKey);
            if (!string.IsNullOrEmpty(cachedCadre))
            {
                _logger.LogDebug("User academic cadre for {UserId} retrieved from cache", userId);
                return cachedCadre;
            }

            var cadre = await _userDataService.GetUserAcademicCadreAsync(userId);
            
            await _cacheService.SetAsync(cacheKey, cadre, _cacheExpiry);
            _logger.LogDebug("User academic cadre for {UserId} cached", userId);
            
            return cadre;
        }
    }
}
