using MongoDB.Driver;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using AcademicPerformance.Models.MongoDocuments;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Services.Interfaces;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Services.Implementations;

public class MongoDbService : IMongoDbService
{
    private readonly IMongoDatabase _database;
    private readonly IMongoCollection<DynamicCriterionTemplate> _dynamicCriterionTemplates;
    private readonly IMongoCollection<SubmittedDynamicDataDoc> _submittedPerformanceData;
    private readonly ILogger<MongoDbService> _logger;

    // MongoDB document size limit (16MB = 16 * 1024 * 1024 bytes)
    private const long MONGODB_MAX_DOCUMENT_SIZE = 16 * 1024 * 1024;
    private const long DEFAULT_WARNING_SIZE = 10 * 1024 * 1024; // 10MB warning threshold

    public MongoDbService(IConfiguration configuration, ILogger<MongoDbService> logger)
    {
        _logger = logger;

        var connectionString = configuration["ApdysMongoDb:ConnectionString"];
        var databaseName = configuration["ApdysMongoDb:DatabaseName"];

        var client = new MongoClient(connectionString);
        _database = client.GetDatabase(databaseName);

        _dynamicCriterionTemplates = _database.GetCollection<DynamicCriterionTemplate>("DynamicCriterionTemplates");
        _submittedPerformanceData = _database.GetCollection<SubmittedDynamicDataDoc>("SubmittedDynamicDataDoc");
    }

    #region Genel MongoDB İşlemleri

    public async Task<T?> GetDocumentAsync<T>(FilterDefinition<T> filter)
    {
        try
        {
            var collection = _database.GetCollection<T>(typeof(T).Name);
            return await collection.Find(filter).FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document of type {Type}", typeof(T).Name);
            throw;
        }
    }

    public async Task<List<T>> GetDocumentsAsync<T>(FilterDefinition<T> filter, SortDefinition<T>? sort = null)
    {
        try
        {
            var collection = _database.GetCollection<T>(typeof(T).Name);
            var query = collection.Find(filter);

            if (sort != null)
                query = query.Sort(sort);

            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting documents of type {Type}", typeof(T).Name);
            throw;
        }
    }

    public async Task InsertDocumentAsync<T>(T document)
    {
        try
        {
            var collection = _database.GetCollection<T>(typeof(T).Name);
            await collection.InsertOneAsync(document);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error inserting document of type {Type}", typeof(T).Name);
            throw;
        }
    }

    public async Task<ReplaceOneResult> ReplaceDocumentAsync<T>(FilterDefinition<T> filter, T document)
    {
        try
        {
            var collection = _database.GetCollection<T>(typeof(T).Name);
            return await collection.ReplaceOneAsync(filter, document);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error replacing document of type {Type}", typeof(T).Name);
            throw;
        }
    }

    public async Task<UpdateResult> UpdateDocumentAsync<T>(FilterDefinition<T> filter, UpdateDefinition<T> update, UpdateOptions? options = null)
    {
        try
        {
            var collection = _database.GetCollection<T>(typeof(T).Name);
            return await collection.UpdateOneAsync(filter, update, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating document of type {Type}", typeof(T).Name);
            throw;
        }
    }

    public async Task<DeleteResult> DeleteDocumentAsync<T>(FilterDefinition<T> filter)
    {
        try
        {
            var collection = _database.GetCollection<T>(typeof(T).Name);
            return await collection.DeleteOneAsync(filter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document of type {Type}", typeof(T).Name);
            throw;
        }
    }

    public async Task<long> CountDocumentsAsync<T>(FilterDefinition<T> filter)
    {
        try
        {
            var collection = _database.GetCollection<T>(typeof(T).Name);
            return await collection.CountDocumentsAsync(filter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error counting documents of type {Type}", typeof(T).Name);
            throw;
        }
    }

    #endregion

    #region Dinamik Kriter Şablonları

    public async Task<List<DynamicCriterionTemplate>> GetDynamicCriterionTemplatesAsync()
    {
        try
        {
            return await _dynamicCriterionTemplates
                .Find(template => true)
                .SortByDescending(t => t.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving dynamic criterion templates");
            throw;
        }
    }

    public async Task<DynamicCriterionTemplate?> GetDynamicCriterionTemplateByIdAsync(string id)
    {
        try
        {
            return await _dynamicCriterionTemplates
                .Find(template => template.Id == id)
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving dynamic criterion template by id: {Id}", id);
            throw;
        }
    }

    public async Task<List<DynamicCriterionTemplate>> GetDynamicCriterionTemplatesByStatusAsync(string status)
    {
        try
        {
            return await _dynamicCriterionTemplates
                .Find(template => template.Status == status)
                .SortByDescending(t => t.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving dynamic criterion templates by status: {Status}", status);
            throw;
        }
    }

    public async Task<DynamicCriterionTemplate> CreateDynamicCriterionTemplateAsync(DynamicCriterionTemplate template)
    {
        try
        {
            template.CreatedAt = DateTime.UtcNow;

            // Document size validation
            await InsertDocumentWithSizeValidationAsync(template);
            return template;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating dynamic criterion template");
            throw;
        }
    }

    public async Task<bool> UpdateDynamicCriterionTemplateAsync(string id, DynamicCriterionTemplate template)
    {
        try
        {
            template.UpdatedAt = DateTime.UtcNow;
            template.Version++;

            // Document size validation ile replace
            var filter = Builders<DynamicCriterionTemplate>.Filter.Eq(t => t.Id, id);
            var result = await ReplaceDocumentWithSizeValidationAsync(filter, template);

            return result.ModifiedCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating dynamic criterion template: {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteDynamicCriterionTemplateAsync(string id)
    {
        try
        {
            var result = await _dynamicCriterionTemplates
                .DeleteOneAsync(template => template.Id == id);

            return result.DeletedCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting dynamic criterion template: {Id}", id);
            throw;
        }
    }

    // Pagination metodu
    public async Task<PagedListDto<DynamicCriterionTemplate>> GetDynamicCriterionTemplatesAsync(PagedListCo<GetDynamicCriterionTemplatesCo> co)
    {
        try
        {
            var pl = new PagedListDto<DynamicCriterionTemplate>
            {
                Page = co.Pager.Page,
                Size = co.Pager.Size
            };

            // Filter oluştur
            var filterBuilder = Builders<DynamicCriterionTemplate>.Filter;
            var filter = filterBuilder.Empty;

            if (co.Criteria != null)
            {
                if (!string.IsNullOrEmpty(co.Criteria!.NameContains))
                {
                    filter &= filterBuilder.Regex(x => x.Name, new MongoDB.Bson.BsonRegularExpression(co.Criteria!.NameContains, "i"));
                }

                if (!string.IsNullOrEmpty(co.Criteria!.Status))
                {
                    filter &= filterBuilder.Eq(x => x.Status, co.Criteria!.Status);
                }

                if (!string.IsNullOrEmpty(co.Criteria!.CreatedByUserId))
                {
                    filter &= filterBuilder.Eq(x => x.CreatedByUserId, co.Criteria!.CreatedByUserId);
                }

                if (!string.IsNullOrEmpty(co.Criteria!.UpdatedByUserId))
                {
                    filter &= filterBuilder.Eq(x => x.UpdatedByUserId, co.Criteria!.UpdatedByUserId);
                }

                if (co.Criteria!.CreatedAfter.HasValue)
                {
                    filter &= filterBuilder.Gte(x => x.CreatedAt, co.Criteria!.CreatedAfter.Value);
                }

                if (co.Criteria!.CreatedBefore.HasValue)
                {
                    filter &= filterBuilder.Lte(x => x.CreatedAt, co.Criteria!.CreatedBefore.Value);
                }

                if (co.Criteria!.UpdatedAfter.HasValue)
                {
                    filter &= filterBuilder.Gte(x => x.UpdatedAt, co.Criteria!.UpdatedAfter.Value);
                }

                if (co.Criteria!.UpdatedBefore.HasValue)
                {
                    filter &= filterBuilder.Lte(x => x.UpdatedAt, co.Criteria!.UpdatedBefore.Value);
                }

                if (co.Criteria!.OnlyActive == true)
                {
                    filter &= filterBuilder.Eq(x => x.Status, "Active");
                }

                if (co.Criteria!.OnlyDrafts == true)
                {
                    filter &= filterBuilder.Eq(x => x.Status, "Draft");
                }

                if (co.Criteria!.OnlyInactive == true)
                {
                    filter &= filterBuilder.Eq(x => x.Status, "Inactive");
                }

                if (!string.IsNullOrEmpty(co.Criteria!.TagContains))
                {
                    filter &= filterBuilder.AnyEq(x => x.Tags, co.Criteria!.TagContains);
                }

                if (co.Criteria!.Tags != null && co.Criteria!.Tags.Any())
                {
                    filter &= filterBuilder.AnyIn(x => x.Tags, co.Criteria!.Tags);
                }

                if (co.Criteria!.MinCoefficient.HasValue)
                {
                    filter &= filterBuilder.Gte(x => x.Coefficient, co.Criteria!.MinCoefficient.Value);
                }

                if (co.Criteria!.MaxCoefficient.HasValue)
                {
                    filter &= filterBuilder.Lte(x => x.Coefficient, co.Criteria!.MaxCoefficient.Value);
                }

                if (co.Criteria!.MinLimit.HasValue)
                {
                    filter &= filterBuilder.Gte(x => x.MaxLimit, co.Criteria!.MinLimit.Value);
                }

                if (co.Criteria!.MaxLimit.HasValue)
                {
                    filter &= filterBuilder.Lte(x => x.MaxLimit, co.Criteria!.MaxLimit.Value);
                }

                if (co.Criteria!.Version.HasValue)
                {
                    filter &= filterBuilder.Eq(x => x.Version, co.Criteria!.Version.Value);
                }
            }

            // Count
            pl.Count = (int)await _dynamicCriterionTemplates.CountDocumentsAsync(filter);

            if (pl.Count != 0)
            {
                // Sorting
                var sortBuilder = Builders<DynamicCriterionTemplate>.Sort;
                SortDefinition<DynamicCriterionTemplate> sort;

                if (!string.IsNullOrEmpty(co.Sort))
                {
                    switch (co.Sort.ToLowerInvariant())
                    {
                        case "name":
                            sort = sortBuilder.Ascending(x => x.Name);
                            break;
                        case "name_desc":
                            sort = sortBuilder.Descending(x => x.Name);
                            break;
                        case "status":
                            sort = sortBuilder.Ascending(x => x.Status);
                            break;
                        case "status_desc":
                            sort = sortBuilder.Descending(x => x.Status);
                            break;
                        case "created":
                            sort = sortBuilder.Ascending(x => x.CreatedAt);
                            break;
                        case "created_desc":
                        default:
                            sort = sortBuilder.Descending(x => x.CreatedAt);
                            break;
                    }
                }
                else
                {
                    // Varsayılan sıralama: En yeni oluşturulanlar önce
                    sort = sortBuilder.Descending(x => x.CreatedAt);
                }

                // Pagination
                pl.Data = await _dynamicCriterionTemplates
                    .Find(filter)
                    .Sort(sort)
                    .Skip(co.Pager.Skip)
                    .Limit(co.Pager.Size)
                    .ToListAsync();
            }

            return pl;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving paginated dynamic criterion templates");
            throw;
        }
    }

    #endregion

    #region Submitted Performance Data

    public async Task<List<SubmittedDynamicDataDoc>> GetSubmittedPerformanceDataAsync()
    {
        try
        {
            return await _submittedPerformanceData
                .Find(FilterDefinition<SubmittedDynamicDataDoc>.Empty)
                .SortByDescending(d => d.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving submitted performance data");
            throw;
        }
    }

    public async Task<SubmittedDynamicDataDoc?> GetSubmittedPerformanceDataByIdAsync(string id)
    {
        try
        {
            return await _submittedPerformanceData
                .Find(data => data.Id == id)
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving submitted performance data by id: {Id}", id);
            throw;
        }
    }

    public async Task<List<SubmittedDynamicDataDoc>> GetSubmittedPerformanceDataBySubmissionIdAsync(string academicSubmissionId)
    {
        try
        {
            return await _submittedPerformanceData
                .Find(data => data.AcademicSubmissionId == academicSubmissionId)
                .SortBy(d => d.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving submitted performance data by submission id: {SubmissionId}", academicSubmissionId);
            throw;
        }
    }

    public async Task<List<SubmittedDynamicDataDoc>> GetSubmittedPerformanceDataByUserIdAsync(string academicianUserId)
    {
        try
        {
            return await _submittedPerformanceData
                .Find(data => data.AcademicianUniveristyUserId == academicianUserId)
                .SortByDescending(d => d.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving submitted performance data by user id: {UserId}", academicianUserId);
            throw;
        }
    }

    public async Task<SubmittedDynamicDataDoc> CreateSubmittedPerformanceDataAsync(SubmittedDynamicDataDoc data)
    {
        try
        {
            data.CreatedAt = DateTime.UtcNow;

            // Document size validation
            await InsertDocumentWithSizeValidationAsync(data);
            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating submitted performance data");
            throw;
        }
    }

    public async Task<bool> UpdateSubmittedPerformanceDataAsync(string id, SubmittedDynamicDataDoc data)
    {
        try
        {
            data.UpdatedAt = DateTime.UtcNow;

            // Document size validation ile replace
            var filter = Builders<SubmittedDynamicDataDoc>.Filter.Eq(d => d.Id, id);
            var result = await ReplaceDocumentWithSizeValidationAsync(filter, data);

            return result.ModifiedCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating submitted performance data: {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteSubmittedPerformanceDataAsync(string id)
    {
        try
        {
            var result = await _submittedPerformanceData
                .DeleteOneAsync(d => d.Id == id);

            return result.DeletedCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting submitted performance data: {Id}", id);
            throw;
        }
    }

    #endregion





    #region Document Size Validation

    /// <summary>
    /// Document'ın BSON serialize edilmiş boyutunu hesaplar
    /// </summary>
    public Task<long> GetDocumentSizeAsync<T>(T document)
    {
        try
        {
            var bsonDocument = document.ToBsonDocument();
            var bsonBytes = bsonDocument.ToBson();
            return Task.FromResult((long)bsonBytes.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating document size for type {Type}", typeof(T).Name);
            throw;
        }
    }

    /// <summary>
    /// Document boyutunu MongoDB limit'ine göre validate eder (16MB)
    /// </summary>
    public async Task<bool> ValidateDocumentSizeAsync<T>(T document)
    {
        return await ValidateDocumentSizeAsync(document, MONGODB_MAX_DOCUMENT_SIZE);
    }

    /// <summary>
    /// Document boyutunu belirtilen limit'e göre validate eder
    /// </summary>
    public async Task<bool> ValidateDocumentSizeAsync<T>(T document, long maxSizeBytes)
    {
        try
        {
            var documentSize = await GetDocumentSizeAsync(document);

            if (documentSize > maxSizeBytes)
            {
                _logger.LogError("Document size validation failed. Size: {Size} bytes, Limit: {Limit} bytes, Type: {Type}",
                    documentSize, maxSizeBytes, typeof(T).Name);
                return false;
            }

            // Warning threshold kontrolü
            if (documentSize > DEFAULT_WARNING_SIZE)
            {
                _logger.LogWarning("Document size approaching limit. Size: {Size} bytes ({SizeMB} MB), Type: {Type}",
                    documentSize, documentSize / (1024.0 * 1024.0), typeof(T).Name);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating document size for type {Type}", typeof(T).Name);
            throw;
        }
    }

    /// <summary>
    /// Document boyut kontrolü ile insert işlemi
    /// </summary>
    public async Task InsertDocumentWithSizeValidationAsync<T>(T document)
    {
        try
        {
            // Boyut kontrolü
            var isValidSize = await ValidateDocumentSizeAsync(document);
            if (!isValidSize)
            {
                var documentSize = await GetDocumentSizeAsync(document);
                throw new InvalidOperationException(
                    $"Document size ({documentSize} bytes) exceeds MongoDB limit ({MONGODB_MAX_DOCUMENT_SIZE} bytes). " +
                    $"Document type: {typeof(T).Name}");
            }

            // Normal insert işlemi
            await InsertDocumentAsync(document);

            _logger.LogInformation("Document inserted successfully with size validation. Type: {Type}", typeof(T).Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error inserting document with size validation. Type: {Type}", typeof(T).Name);
            throw;
        }
    }

    /// <summary>
    /// Document boyut kontrolü ile replace işlemi
    /// </summary>
    public async Task<ReplaceOneResult> ReplaceDocumentWithSizeValidationAsync<T>(FilterDefinition<T> filter, T document)
    {
        try
        {
            // Boyut kontrolü
            var isValidSize = await ValidateDocumentSizeAsync(document);
            if (!isValidSize)
            {
                var documentSize = await GetDocumentSizeAsync(document);
                throw new InvalidOperationException(
                    $"Document size ({documentSize} bytes) exceeds MongoDB limit ({MONGODB_MAX_DOCUMENT_SIZE} bytes). " +
                    $"Document type: {typeof(T).Name}");
            }

            // Normal replace işlemi
            var result = await ReplaceDocumentAsync(filter, document);

            _logger.LogInformation("Document replaced successfully with size validation. Type: {Type}", typeof(T).Name);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error replacing document with size validation. Type: {Type}", typeof(T).Name);
            throw;
        }
    }

    #endregion
}
