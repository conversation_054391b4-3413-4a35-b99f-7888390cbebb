using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Models.Dtos;
using Microsoft.Extensions.Localization;
using Rlx.Shared.Resources;
using System.Text;

namespace AcademicPerformance.Services.Implementations
{
    /// <summary>
    /// Email template service implementation
    /// Basit string replacement ile template rendering
    /// </summary>
    public class EmailTemplateService : IEmailTemplateService
    {
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly ILogger<EmailTemplateService> _logger;

        // Template definitions
        private readonly Dictionary<string, (string Subject, string Body)> _templates;

        public EmailTemplateService(
            IStringLocalizer<SharedResource> localizer,
            ILogger<EmailTemplateService> logger)
        {
            _localizer = localizer;
            _logger = logger;
            _templates = InitializeTemplates();
        }

        public async Task<(string Subject, string Body)> RenderTemplateAsync(string templateName, object templateData)
        {
            try
            {
                if (!_templates.ContainsKey(templateName))
                {
                    throw new ArgumentException($"Template '{templateName}' bulunamadı");
                }

                var template = _templates[templateName];
                var renderedSubject = await RenderStringAsync(template.Subject, templateData);
                var renderedBody = await RenderStringAsync(template.Body, templateData);

                return (renderedSubject, renderedBody);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Template render hatası: {TemplateName}", templateName);
                throw;
            }
        }

        public bool TemplateExists(string templateName)
        {
            return _templates.ContainsKey(templateName);
        }

        public List<string> GetAvailableTemplates()
        {
            return _templates.Keys.ToList();
        }

        private async Task<string> RenderStringAsync(string template, object data)
        {
            var result = template;
            var properties = data.GetType().GetProperties();

            foreach (var prop in properties)
            {
                var value = prop.GetValue(data)?.ToString() ?? "";
                result = result.Replace($"{{{{{prop.Name}}}}}", value);
            }

            return result;
        }

        private Dictionary<string, (string Subject, string Body)> InitializeTemplates()
        {
            return new Dictionary<string, (string Subject, string Body)>
            {
                ["SubmissionApproved"] = (
                    Subject: "✅ Başvurunuz Onaylandı - {{FormName}}",
                    Body: @"Sayın {{AcademicianName}},

{{FormName}} formu için yaptığınız başvuru (ID: {{SubmissionId}}) {{ControllerName}} tarafından onaylanmıştır.

📅 Onay Tarihi: {{ApprovalDate}}
👤 Onaylayan: {{ControllerName}}

{{#if ApprovalNotes}}
📝 Onay Notları: {{ApprovalNotes}}
{{/if}}

Tebrikler! Başvurunuz başarıyla tamamlanmıştır.

Saygılarımızla,
Akademik Performans Değerlendirme Sistemi"
                ),

                ["SubmissionRejected"] = (
                    Subject: "❌ Başvurunuz Reddedildi - {{FormName}}",
                    Body: @"Sayın {{AcademicianName}},

{{FormName}} formu için yaptığınız başvuru (ID: {{SubmissionId}}) {{ControllerName}} tarafından reddedilmiştir.

📅 Red Tarihi: {{RejectionDate}}
👤 Reddeden: {{ControllerName}}

❗ Red Nedeni: {{RejectionReason}}

{{#if RevisionInstructions}}
📋 Revizyon Talimatları: {{RevisionInstructions}}
{{/if}}

Lütfen gerekli düzeltmeleri yaparak başvurunuzu yeniden gönderin.

Saygılarımızla,
Akademik Performans Değerlendirme Sistemi"
                ),

                ["NewSubmission"] = (
                    Subject: "🔔 Yeni Başvuru İncelemenizi Bekliyor - {{FormName}}",
                    Body: @"Sayın {{ControllerName}},

{{AcademicianName}} tarafından {{FormName}} formu için yeni bir başvuru yapılmıştır.

📋 Başvuru ID: {{SubmissionId}}
👤 Başvuran: {{AcademicianName}}
🏢 Bölüm: {{Department}}
📅 Başvuru Tarihi: {{SubmissionDate}}

Lütfen başvuruyu inceleyerek onay/red işlemini gerçekleştirin.

Sisteme giriş yapmak için: [APDYS Portal]

Saygılarımızla,
Akademik Performans Değerlendirme Sistemi"
                ),

                ["RevisionRequest"] = (
                    Subject: "📝 Revizyon Talebi - {{FormName}}",
                    Body: @"Sayın {{AcademicianName}},

{{FormName}} formu için yaptığınız başvuru (ID: {{SubmissionId}}) için revizyon talep edilmiştir.

👤 Talep Eden: {{ControllerName}}
⏰ Son Tarih: {{Deadline}}

📝 Revizyon Notları:
{{RevisionNotes}}

Lütfen belirtilen düzeltmeleri yaparak başvurunuzu son tarihe kadar yeniden gönderin.

Saygılarımızla,
Akademik Performans Değerlendirme Sistemi"
                ),

                ["DeadlineReminder"] = (
                    Subject: "⏰ Son Tarih Hatırlatması - {{FormName}}",
                    Body: @"Sayın {{AcademicianName}},

{{FormName}} formu için başvurunuzun (ID: {{SubmissionId}}) son teslim tarihine {{DaysRemaining}} gün kalmıştır.

⏰ Son Tarih: {{Deadline}}

Lütfen başvurunuzu zamanında tamamlayın.

Saygılarımızla,
Akademik Performans Değerlendirme Sistemi"
                ),

                ["PortfolioVerification"] = (
                    Subject: "📁 Portfolio Doğrulama Talebi - {{CourseName}}",
                    Body: @"Sayın {{ArchivistName}},

{{AcademicianName}} tarafından {{CourseName}} dersi için portfolio doğrulama talebi yapılmıştır.

📚 Ders: {{CourseName}} ({{CourseId}})
👤 Akademisyen: {{AcademicianName}}
📅 Dönem: {{Period}}
📅 Talep Tarihi: {{VerificationDate}}

Lütfen EBYS sisteminde gerekli kontrolleri yaparak doğrulama işlemini tamamlayın.

Saygılarımızla,
Akademik Performans Değerlendirme Sistemi"
                ),

                ["StaffCompetencyEvaluation"] = (
                    Subject: "👥 Personel Yetkinlik Değerlendirmesi - {{StaffName}}",
                    Body: @"Sayın {{ManagerName}},

{{StaffName}} için personel yetkinlik değerlendirmesi yapılması gerekmektedir.

👤 Personel: {{StaffName}}
🏢 Bölüm: {{Department}}
📅 Değerlendirme Dönemi: {{EvaluationPeriod}}
🆔 Değerlendirme ID: {{EvaluationId}}

Lütfen sisteme giriş yaparak değerlendirme işlemini tamamlayın.

Saygılarımızla,
Akademik Performans Değerlendirme Sistemi"
                ),

                ["DepartmentPerformance"] = (
                    Subject: "📊 Bölüm Performans Verisi Girişi - {{Period}}",
                    Body: @"Sayın {{StrategicOfficeName}},

{{DepartmentName}} bölümü için {{Period}} dönemi performans verisi girişi yapılması gerekmektedir.

🏢 Bölüm: {{DepartmentName}}
📅 Dönem: {{Period}}
📅 Veri Giriş Tarihi: {{DataEntryDate}}

{{#if Notes}}
📝 Notlar: {{Notes}}
{{/if}}

Lütfen sisteme giriş yaparak gerekli verileri girin.

Saygılarımızla,
Akademik Performans Değerlendirme Sistemi"
                ),

                ["TestEmail"] = (
                    Subject: "✅ APDYS Test Email",
                    Body: @"Bu bir test emailidir.

APDYS Email Notification sistemi başarıyla çalışmaktadır.

📅 Test Tarihi: {{TestDate}}
🔧 Sistem: Akademik Performans Değerlendirme Sistemi

Bu email otomatik olarak gönderilmiştir."
                )
            };
        }
    }
}
