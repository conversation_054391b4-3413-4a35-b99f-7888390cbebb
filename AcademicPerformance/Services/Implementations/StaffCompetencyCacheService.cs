using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Services.Interfaces;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AcademicPerformance.Services.Implementations;

/// <summary>
/// Personel yetkinlik verileri için cache servisi
/// </summary>
public class StaffCompetencyCacheService : IStaffCompetencyCacheService
{
    private readonly ICacheService _cacheService;
    private readonly ILogger<StaffCompetencyCacheService> _logger;

    // Cache key prefix'leri
    private const string STAFF_COMPETENCY_PREFIX = "staff_competency";
    private const string COMPETENCY_STATS_PREFIX = "competency_stats";
    private const string COMPETENCY_REPORT_PREFIX = "competency_report";
    private const string COMPETENCY_TREND_PREFIX = "competency_trend";

    // Cache expiration süreleri (dakika)
    private const int DEFAULT_CACHE_MINUTES = 30;
    private const int STATS_CACHE_MINUTES = 60;
    private const int REPORT_CACHE_MINUTES = 120;

    public StaffCompetencyCacheService(ICacheService cacheService, ILogger<StaffCompetencyCacheService> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    #region Staff Competency Cache

    /// <summary>
    /// Personel yetkinlik değerlendirmelerini cache'den getir
    /// </summary>
    public async Task<List<CompetencyEvaluationFormDto>?> GetStaffCompetencyEvaluationsAsync(string staffId, string period)
    {
        try
        {
            var cacheKey = $"{STAFF_COMPETENCY_PREFIX}:evaluations:{staffId}:{period}";
            var cachedData = await _cacheService.GetAsync<string>(cacheKey);

            if (!string.IsNullOrEmpty(cachedData))
            {
                _logger.LogInformation("Personel yetkinlik değerlendirmeleri cache'den getirildi - Staff: {StaffId}", staffId);
                return JsonSerializer.Deserialize<List<CompetencyEvaluationFormDto>>(cachedData);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Personel yetkinlik değerlendirmeleri cache getirme hatası - Staff: {StaffId}", staffId);
            return null;
        }
    }

    /// <summary>
    /// Personel yetkinlik değerlendirmelerini cache'e kaydet
    /// </summary>
    public async Task SetStaffCompetencyEvaluationsAsync(string staffId, string period, List<CompetencyEvaluationFormDto> evaluations)
    {
        try
        {
            var cacheKey = $"{STAFF_COMPETENCY_PREFIX}:evaluations:{staffId}:{period}";
            var serializedData = JsonSerializer.Serialize(evaluations);

            await _cacheService.SetAsync(cacheKey, serializedData, TimeSpan.FromMinutes(DEFAULT_CACHE_MINUTES));

            _logger.LogInformation("Personel yetkinlik değerlendirmeleri cache'e kaydedildi - Staff: {StaffId}, Count: {Count}",
                staffId, evaluations.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Personel yetkinlik değerlendirmeleri cache kaydetme hatası - Staff: {StaffId}", staffId);
        }
    }

    #endregion

    #region Statistics Cache

    /// <summary>
    /// Yetkinlik istatistiklerini cache'den getir
    /// </summary>
    public async Task<Dictionary<string, CompetencyAreaSummaryDto>?> GetCompetencyStatisticsAsync(string departmentId, string period)
    {
        try
        {
            var cacheKey = $"{COMPETENCY_STATS_PREFIX}:{departmentId}:{period}";
            var cachedData = await _cacheService.GetAsync<string>(cacheKey);

            if (!string.IsNullOrEmpty(cachedData))
            {
                _logger.LogInformation("Yetkinlik istatistikleri cache'den getirildi - Department: {DepartmentId}", departmentId);
                return JsonSerializer.Deserialize<Dictionary<string, CompetencyAreaSummaryDto>>(cachedData);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Yetkinlik istatistikleri cache getirme hatası - Department: {DepartmentId}", departmentId);
            return null;
        }
    }

    /// <summary>
    /// Yetkinlik istatistiklerini cache'e kaydet
    /// </summary>
    public async Task SetCompetencyStatisticsAsync(string departmentId, string period, Dictionary<string, CompetencyAreaSummaryDto> statistics)
    {
        try
        {
            var cacheKey = $"{COMPETENCY_STATS_PREFIX}:{departmentId}:{period}";
            var serializedData = JsonSerializer.Serialize(statistics);

            await _cacheService.SetAsync(cacheKey, serializedData, TimeSpan.FromMinutes(STATS_CACHE_MINUTES));

            _logger.LogInformation("Yetkinlik istatistikleri cache'e kaydedildi - Department: {DepartmentId}, Count: {Count}",
                departmentId, statistics.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Yetkinlik istatistikleri cache kaydetme hatası - Department: {DepartmentId}", departmentId);
        }
    }

    #endregion

    #region Report Cache

    /// <summary>
    /// Yetkinlik raporunu cache'den getir
    /// </summary>
    public async Task<StaffCompetencyReportDto?> GetCompetencyReportAsync(string reportType, string scopeId, string period)
    {
        try
        {
            var cacheKey = $"{COMPETENCY_REPORT_PREFIX}:{reportType}:{scopeId}:{period}";
            var cachedData = await _cacheService.GetAsync<string>(cacheKey);

            if (!string.IsNullOrEmpty(cachedData))
            {
                _logger.LogInformation("Yetkinlik raporu cache'den getirildi - Type: {ReportType}, Scope: {ScopeId}",
                    reportType, scopeId);
                return JsonSerializer.Deserialize<StaffCompetencyReportDto>(cachedData);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Yetkinlik raporu cache getirme hatası - Type: {ReportType}, Scope: {ScopeId}",
                reportType, scopeId);
            return null;
        }
    }

    /// <summary>
    /// Yetkinlik raporunu cache'e kaydet
    /// </summary>
    public async Task SetCompetencyReportAsync(string reportType, string scopeId, string period, StaffCompetencyReportDto report)
    {
        try
        {
            var cacheKey = $"{COMPETENCY_REPORT_PREFIX}:{reportType}:{scopeId}:{period}";
            var serializedData = JsonSerializer.Serialize(report);

            await _cacheService.SetAsync(cacheKey, serializedData, TimeSpan.FromMinutes(REPORT_CACHE_MINUTES));

            _logger.LogInformation("Yetkinlik raporu cache'e kaydedildi - Type: {ReportType}, Scope: {ScopeId}",
                reportType, scopeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Yetkinlik raporu cache kaydetme hatası - Type: {ReportType}, Scope: {ScopeId}",
                reportType, scopeId);
        }
    }

    #endregion

    #region Cache Invalidation

    /// <summary>
    /// Personel bazında cache'i temizle
    /// </summary>
    public async Task InvalidateStaffCacheAsync(string staffId)
    {
        try
        {
            // Basit implementasyon - belirli key'leri temizle
            var keysToRemove = new[]
            {
                $"{STAFF_COMPETENCY_PREFIX}:evaluations:{staffId}:*"
            };

            // Pattern matching olmadığı için şimdilik log'la
            _logger.LogInformation("Personel cache invalidation işaretlendi - Staff: {StaffId}", staffId);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Personel cache temizleme hatası - Staff: {StaffId}", staffId);
        }
    }

    /// <summary>
    /// Bölüm bazında cache'i temizle
    /// </summary>
    public async Task InvalidateDepartmentCacheAsync(string departmentId)
    {
        try
        {
            // Basit implementasyon - log'la
            _logger.LogInformation("Bölüm cache invalidation işaretlendi - Department: {DepartmentId}", departmentId);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Bölüm cache temizleme hatası - Department: {DepartmentId}", departmentId);
        }
    }

    /// <summary>
    /// Tüm yetkinlik cache'ini temizle
    /// </summary>
    public async Task InvalidateAllCompetencyCacheAsync()
    {
        try
        {
            // Basit implementasyon - log'la
            _logger.LogInformation("Tüm yetkinlik cache invalidation işaretlendi");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Tüm yetkinlik cache temizleme hatası");
        }
    }

    #endregion
}
