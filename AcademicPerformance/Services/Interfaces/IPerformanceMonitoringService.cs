namespace AcademicPerformance.Services.Interfaces;

/// <summary>
/// Performance monitoring service interface
/// </summary>
public interface IPerformanceMonitoringService
{
    /// <summary>
    /// Start performance measurement
    /// </summary>
    IDisposable StartMeasurement(string operationName, Dictionary<string, object>? properties = null);

    /// <summary>
    /// Record query execution time
    /// </summary>
    Task RecordQueryExecutionAsync(string queryName, TimeSpan duration, bool isSuccessful, string? errorMessage = null);

    /// <summary>
    /// Record cache hit/miss
    /// </summary>
    Task RecordCacheOperationAsync(string cacheKey, bool isHit, TimeSpan duration);

    /// <summary>
    /// Record API endpoint performance
    /// </summary>
    Task RecordApiEndpointAsync(string endpoint, string method, TimeSpan duration, int statusCode, long? responseSize = null);

    /// <summary>
    /// Get performance metrics
    /// </summary>
    Task<PerformanceMetrics> GetMetricsAsync(DateTime? from = null, DateTime? to = null);

    /// <summary>
    /// Get slow queries report
    /// </summary>
    Task<List<SlowQueryReport>> GetSlowQueriesAsync(TimeSpan threshold, int limit = 100);

    /// <summary>
    /// Get cache performance report
    /// </summary>
    Task<CachePerformanceReport> GetCachePerformanceAsync();

    /// <summary>
    /// Clear performance data older than specified date
    /// </summary>
    Task CleanupOldDataAsync(DateTime olderThan);
}

/// <summary>
/// Performance metrics model
/// </summary>
public class PerformanceMetrics
{
    public DateTime From { get; set; }
    public DateTime To { get; set; }
    public int TotalRequests { get; set; }
    public double AverageResponseTime { get; set; }
    public double MedianResponseTime { get; set; }
    public double P95ResponseTime { get; set; }
    public double P99ResponseTime { get; set; }
    public int ErrorCount { get; set; }
    public double ErrorRate => TotalRequests > 0 ? (double)ErrorCount / TotalRequests : 0;
    public Dictionary<string, int> StatusCodeDistribution { get; set; } = new();
    public Dictionary<string, double> EndpointPerformance { get; set; } = new();
    public List<string> SlowestEndpoints { get; set; } = new();
}

/// <summary>
/// Slow query report model
/// </summary>
public class SlowQueryReport
{
    public string QueryName { get; set; } = string.Empty;
    public TimeSpan AverageDuration { get; set; }
    public TimeSpan MaxDuration { get; set; }
    public int ExecutionCount { get; set; }
    public int ErrorCount { get; set; }
    public DateTime LastExecuted { get; set; }
    public string? LastError { get; set; }
}

/// <summary>
/// Cache performance report model
/// </summary>
public class CachePerformanceReport
{
    public long TotalRequests { get; set; }
    public long HitCount { get; set; }
    public long MissCount { get; set; }
    public double HitRatio => TotalRequests > 0 ? (double)HitCount / TotalRequests : 0;
    public double AverageHitTime { get; set; }
    public double AverageMissTime { get; set; }
    public Dictionary<string, long> KeyPatternStats { get; set; } = new();
    public List<string> MostAccessedKeys { get; set; } = new();
    public List<string> SlowestKeys { get; set; } = new();
}

/// <summary>
/// Performance measurement disposable
/// </summary>
public class PerformanceMeasurement : IDisposable
{
    private readonly string _operationName;
    private readonly Dictionary<string, object>? _properties;
    private readonly IPerformanceMonitoringService _service;
    private readonly DateTime _startTime;
    private bool _disposed = false;

    public PerformanceMeasurement(string operationName, Dictionary<string, object>? properties, IPerformanceMonitoringService service)
    {
        _operationName = operationName;
        _properties = properties;
        _service = service;
        _startTime = DateTime.UtcNow;
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            var duration = DateTime.UtcNow - _startTime;
            // Record the measurement (fire and forget)
            _ = Task.Run(async () =>
            {
                try
                {
                    await _service.RecordQueryExecutionAsync(_operationName, duration, true);
                }
                catch
                {
                    // Ignore errors in performance monitoring
                }
            });
            _disposed = true;
        }
    }
}
