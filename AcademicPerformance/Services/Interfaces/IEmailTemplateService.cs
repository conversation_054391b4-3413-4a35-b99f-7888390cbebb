namespace AcademicPerformance.Services.Interfaces
{
    /// <summary>
    /// Email template service interface
    /// Email template'lerini yönetir ve render eder
    /// </summary>
    public interface IEmailTemplateService
    {
        /// <summary>
        /// Template render et
        /// </summary>
        /// <param name="templateName">Template adı</param>
        /// <param name="templateData">Template verileri</param>
        /// <returns>Render edilmiş template (subject, body)</returns>
        Task<(string Subject, string Body)> RenderTemplateAsync(string templateName, object templateData);

        /// <summary>
        /// Template var mı kontrol et
        /// </summary>
        /// <param name="templateName">Template adı</param>
        /// <returns>Template var mı?</returns>
        bool TemplateExists(string templateName);

        /// <summary>
        /// Mevcut template'leri listele
        /// </summary>
        /// <returns>Template adları listesi</returns>
        List<string> GetAvailableTemplates();
    }
}
