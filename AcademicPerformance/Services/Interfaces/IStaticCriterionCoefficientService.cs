using AcademicPerformance.Models.AcademicPerformanceDbContextModels;

namespace AcademicPerformance.Services.Interfaces
{
    /// <summary>
    /// Statik kriter katsayıları yönetim servisi interface'i
    /// UI'dan katsayı değerlerini değiştirmek için kullanılır
    /// </summary>
    public interface IStaticCriterionCoefficientService
    {
        /// <summary>
        /// Tüm statik kriter katsayılarını getirir
        /// </summary>
        /// <param name="includeInactive">Pasif kriterleri dahil et</param>
        /// <returns>Statik kriter katsayıları listesi</returns>
        Task<List<StaticCriterionCoefficientDto>> GetAllCoefficientsAsync(bool includeInactive = false);

        /// <summary>
        /// Belirli bir kategorideki statik kriter katsayılarını getirir
        /// </summary>
        /// <param name="category">Krite<PERSON> kategorisi (A, B, C, vb.)</param>
        /// <param name="includeInactive">Pasif kriterleri dahil et</param>
        /// <returns>Kategoriye ait statik kriter katsayıları listesi</returns>
        Task<List<StaticCriterionCoefficientDto>> GetCoefficientsByCategoryAsync(string category, bool includeInactive = false);

        /// <summary>
        /// Belirli bir statik kriter katsayısını getirir
        /// </summary>
        /// <param name="staticCriterionSystemId">Statik kriter sistem ID'si</param>
        /// <returns>Statik kriter katsayısı</returns>
        Task<StaticCriterionCoefficientDto?> GetCoefficientAsync(string staticCriterionSystemId);

        /// <summary>
        /// Belirli bir statik kriter katsayısını ID ile getirir
        /// </summary>
        /// <param name="id">Katsayı ID'si</param>
        /// <returns>Statik kriter katsayısı</returns>
        Task<StaticCriterionCoefficientDto?> GetCoefficientByIdAsync(Guid id);

        /// <summary>
        /// Statik kriter katsayısını günceller
        /// </summary>
        /// <param name="updateDto">Güncelleme DTO'su</param>
        /// <param name="updatedBy">Güncelleyen kullanıcı</param>
        /// <returns>Güncelleme başarılı mı?</returns>
        Task<bool> UpdateCoefficientAsync(UpdateStaticCriterionCoefficientDto updateDto, string updatedBy);

        /// <summary>
        /// Birden fazla statik kriter katsayısını toplu olarak günceller
        /// </summary>
        /// <param name="bulkUpdateDto">Toplu güncelleme DTO'su</param>
        /// <param name="updatedBy">Güncelleyen kullanıcı</param>
        /// <returns>Güncelleme başarılı mı?</returns>
        Task<bool> BulkUpdateCoefficientsAsync(BulkUpdateStaticCriterionCoefficientsDto bulkUpdateDto, string updatedBy);

        /// <summary>
        /// Statik kriter katsayısını onaylar
        /// </summary>
        /// <param name="id">Katsayı ID'si</param>
        /// <param name="approvedBy">Onaylayan kullanıcı</param>
        /// <returns>Onaylama başarılı mı?</returns>
        Task<bool> ApproveCoefficientAsync(Guid id, string approvedBy);

        /// <summary>
        /// Birden fazla statik kriter katsayısını toplu olarak onaylar
        /// </summary>
        /// <param name="ids">Katsayı ID'leri</param>
        /// <param name="approvedBy">Onaylayan kullanıcı</param>
        /// <returns>Onaylama başarılı mı?</returns>
        Task<bool> BulkApproveCoefficientsAsync(List<Guid> ids, string approvedBy);

        /// <summary>
        /// Statik kriter katsayısının geçmişini getirir
        /// </summary>
        /// <param name="staticCriterionSystemId">Statik kriter sistem ID'si</param>
        /// <returns>Katsayı geçmişi</returns>
        Task<List<CoefficientHistoryDto>> GetCoefficientHistoryAsync(string staticCriterionSystemId);

        /// <summary>
        /// Statik kriter katsayısını varsayılan değere sıfırlar
        /// </summary>
        /// <param name="staticCriterionSystemId">Statik kriter sistem ID'si</param>
        /// <param name="resetBy">Sıfırlayan kullanıcı</param>
        /// <returns>Sıfırlama başarılı mı?</returns>
        Task<bool> ResetCoefficientToDefaultAsync(string staticCriterionSystemId, string resetBy);

        /// <summary>
        /// Statik kriter katsayısını aktif/pasif yapar
        /// </summary>
        /// <param name="staticCriterionSystemId">Statik kriter sistem ID'si</param>
        /// <param name="isActive">Aktif durumu</param>
        /// <param name="updatedBy">Güncelleyen kullanıcı</param>
        /// <returns>Güncelleme başarılı mı?</returns>
        Task<bool> SetCoefficientActiveStatusAsync(string staticCriterionSystemId, bool isActive, string updatedBy);

        /// <summary>
        /// Onay bekleyen statik kriter katsayılarını getirir
        /// </summary>
        /// <returns>Onay bekleyen katsayılar listesi</returns>
        Task<List<StaticCriterionCoefficientDto>> GetPendingApprovalsAsync();

        /// <summary>
        /// Statik kriter katsayılarını Excel'e aktarır
        /// </summary>
        /// <param name="category">Kriter kategorisi (opsiyonel)</param>
        /// <returns>Excel dosyası byte array'i</returns>
        Task<byte[]> ExportCoefficientsToExcelAsync(string? category = null);

        /// <summary>
        /// Excel'den statik kriter katsayılarını içe aktarır
        /// </summary>
        /// <param name="excelData">Excel dosyası byte array'i</param>
        /// <param name="importedBy">İçe aktaran kullanıcı</param>
        /// <returns>İçe aktarma sonucu</returns>
        Task<(bool Success, List<string> Errors, int ImportedCount)> ImportCoefficientsFromExcelAsync(byte[] excelData, string importedBy);

        /// <summary>
        /// Statik kriter katsayılarını doğrular
        /// </summary>
        /// <param name="coefficients">Doğrulanacak katsayılar</param>
        /// <returns>Doğrulama sonucu</returns>
        Task<(bool IsValid, List<string> ValidationErrors)> ValidateCoefficientsAsync(List<UpdateStaticCriterionCoefficientDto> coefficients);

        /// <summary>
        /// Varsayılan katsayı değerlerini getirir
        /// </summary>
        /// <returns>Varsayılan katsayılar</returns>
        Task<Dictionary<string, decimal>> GetDefaultCoefficientsAsync();

        /// <summary>
        /// Katsayı değişiklik önerisi oluşturur
        /// </summary>
        /// <param name="staticCriterionSystemId">Statik kriter sistem ID'si</param>
        /// <param name="proposedCoefficient">Önerilen katsayı</param>
        /// <param name="proposedBy">Öneren kullanıcı</param>
        /// <param name="reason">Öneri nedeni</param>
        /// <returns>Öneri oluşturma başarılı mı?</returns>
        Task<bool> CreateCoefficientProposalAsync(string staticCriterionSystemId, decimal proposedCoefficient, string proposedBy, string reason);

        /// <summary>
        /// Katsayı değişiklik önerilerini getirir
        /// </summary>
        /// <returns>Öneriler listesi</returns>
        Task<List<CoefficientProposalDto>> GetCoefficientProposalsAsync();

        /// <summary>
        /// Katsayı değişiklik önerisini onaylar veya reddeder
        /// </summary>
        /// <param name="proposalId">Öneri ID'si</param>
        /// <param name="isApproved">Onaylandı mı?</param>
        /// <param name="reviewedBy">İnceleyen kullanıcı</param>
        /// <param name="reviewNotes">İnceleme notları</param>
        /// <returns>İnceleme başarılı mı?</returns>
        Task<bool> ReviewCoefficientProposalAsync(Guid proposalId, bool isApproved, string reviewedBy, string? reviewNotes = null);
    }

    /// <summary>
    /// Katsayı değişiklik önerisi DTO'su
    /// </summary>
    public class CoefficientProposalDto
    {
        /// <summary>
        /// Öneri ID'si
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Statik kriter sistem ID'si
        /// </summary>
        public required string StaticCriterionSystemId { get; set; }

        /// <summary>
        /// Kriter adı
        /// </summary>
        public required string CriterionName { get; set; }

        /// <summary>
        /// Mevcut katsayı
        /// </summary>
        public decimal CurrentCoefficient { get; set; }

        /// <summary>
        /// Önerilen katsayı
        /// </summary>
        public decimal ProposedCoefficient { get; set; }

        /// <summary>
        /// Öneri nedeni
        /// </summary>
        public required string Reason { get; set; }

        /// <summary>
        /// Öneren kullanıcı
        /// </summary>
        public required string ProposedBy { get; set; }

        /// <summary>
        /// Öneri tarihi
        /// </summary>
        public DateTime ProposedAt { get; set; }

        /// <summary>
        /// Durum (Pending, Approved, Rejected)
        /// </summary>
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// İnceleyen kullanıcı
        /// </summary>
        public string? ReviewedBy { get; set; }

        /// <summary>
        /// İnceleme tarihi
        /// </summary>
        public DateTime? ReviewedAt { get; set; }

        /// <summary>
        /// İnceleme notları
        /// </summary>
        public string? ReviewNotes { get; set; }
    }
}
