using AcademicPerformance.Models.Dtos;

namespace AcademicPerformance.Services.Interfaces
{
    public interface IArelBridgeApiService
    {
        Task<IEnumerable<CourseInformationDto>?> GetCoursesByAcademicianTcAsync(string academicianTc);
        Task<IEnumerable<CourseInformationDto>?> GetCoursesByPeriodAsync(string period);
        Task<IEnumerable<CourseInformationDto>?> GetCoursesByCourseCodeAsync(string courseCode, string? period = null);
        Task<IEnumerable<string>?> GetActivePeriodsAsync();
        Task<(bool IsConnected, string Message)> TestConnectionAsync();
        Task<bool> IsHealthyAsync();
    }
}
