using Mapster;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;
using System.Text.Json;

namespace AcademicPerformance.Managers
{
    public class FormManager : IFormManager
    {
        private readonly IFormStore _formStore;
        private readonly ICriteriaStore _criteriaStore;
        private readonly ILogger<FormManager> _logger;

        public FormManager(IFormStore formStore, ICriteriaStore criteriaStore, ILogger<FormManager> logger)
        {
            _formStore = formStore;
            _criteriaStore = criteriaStore;
            _logger = logger;
        }

        #region Evaluation Form Management

        public async Task<List<EvaluationFormDto>> GetEvaluationFormsAsync()
        {
            var forms = await _formStore.GetEvaluationFormsAsync();
            var formDtos = forms.Adapt<List<EvaluationFormDto>>();

            // Parse JSON fields
            foreach (var formDto in formDtos)
            {
                var form = forms.First(f => f.Id == formDto.Id);
                if (!string.IsNullOrEmpty(form.ApplicableAcademicCadresJson))
                {
                    try
                    {
                        formDto.ApplicableAcademicCadres = JsonSerializer.Deserialize<List<string>>(form.ApplicableAcademicCadresJson);
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogWarning(ex, "Failed to parse ApplicableAcademicCadresJson for form {FormId}", form.Id);
                        formDto.ApplicableAcademicCadres = new List<string>();
                    }
                }
            }

            return formDtos;
        }

        public async Task<EvaluationFormDto?> GetEvaluationFormByIdAsync(string id)
        {
            var form = await _formStore.GetEvaluationFormByIdAsync(id);
            if (form == null)
                return null;

            var formDto = form.Adapt<EvaluationFormDto>();

            // Parse JSON fields
            if (!string.IsNullOrEmpty(form.ApplicableAcademicCadresJson))
            {
                try
                {
                    formDto.ApplicableAcademicCadres = JsonSerializer.Deserialize<List<string>>(form.ApplicableAcademicCadresJson);
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "Failed to parse ApplicableAcademicCadresJson for form {FormId}", form.Id);
                    formDto.ApplicableAcademicCadres = new List<string>();
                }
            }

            return formDto;
        }

        public async Task<List<EvaluationFormDto>> GetEvaluationFormsByStatusAsync(string status)
        {
            var forms = await _formStore.GetEvaluationFormsByStatusAsync(status);
            return forms.Adapt<List<EvaluationFormDto>>();
        }

        // Pagination metodları
        public async Task<PagedListDto<EvaluationFormDto>> GetEvaluationFormsAsync(PagedListCo<GetEvaluationFormsCo> co)
        {
            var pagedForms = await _formStore.GetEvaluationFormsAsync(co);

            // Entity'leri DTO'ya çevir
            var formDtos = pagedForms.Data?.ToList().Adapt<List<EvaluationFormDto>>() ?? new List<EvaluationFormDto>();

            // JSON alanları parse et
            await ParseJsonFieldsAsync(formDtos, pagedForms.Data?.ToList() ?? new List<EvaluationFormEntity>());

            return new PagedListDto<EvaluationFormDto>
            {
                Count = pagedForms.Count,
                Page = pagedForms.Page,
                Size = pagedForms.Size,
                Data = formDtos
            };
        }

        public async Task<PagedListDto<EvaluationFormDto>> GetEvaluationFormsByStatusAsync(PagedListCo<GetEvaluationFormsCo> co, string status)
        {
            var pagedForms = await _formStore.GetEvaluationFormsByStatusAsync(co, status);

            // Entity'leri DTO'ya çevir
            var formDtos = pagedForms.Data?.ToList().Adapt<List<EvaluationFormDto>>() ?? new List<EvaluationFormDto>();

            // JSON alanları parse et
            await ParseJsonFieldsAsync(formDtos, pagedForms.Data?.ToList() ?? new List<EvaluationFormEntity>());

            return new PagedListDto<EvaluationFormDto>
            {
                Count = pagedForms.Count,
                Page = pagedForms.Page,
                Size = pagedForms.Size,
                Data = formDtos
            };
        }

        // Status-based Pagination Method
        public async Task<PagedListDto<EvaluationFormDto>> GetEvaluationFormsByStatusAsync(string status, PagedListCo<GetStatusFilterCo> co)
        {
            // Status kriterini GetEvaluationFormsCo'ya çevir
            var formCo = new PagedListCo<GetEvaluationFormsCo>
            {
                Pager = co.Pager,
                Sort = co.Sort,
                Criteria = new GetEvaluationFormsCo
                {
                    Status = status,
                    NameContains = co.Criteria?.NameContains,
                    CreatedAfter = co.Criteria?.CreatedAfter,
                    CreatedBefore = co.Criteria?.CreatedBefore
                }
            };

            return await GetEvaluationFormsAsync(formCo);
        }

        private async Task ParseJsonFieldsAsync(List<EvaluationFormDto> formDtos, List<EvaluationFormEntity> forms)
        {
            await Task.Run(() =>
            {
                foreach (var formDto in formDtos)
                {
                    var form = forms.FirstOrDefault(f => f.Id == formDto.Id);
                    if (form != null && !string.IsNullOrEmpty(form.ApplicableAcademicCadresJson))
                    {
                        try
                        {
                            formDto.ApplicableAcademicCadres = JsonSerializer.Deserialize<List<string>>(form.ApplicableAcademicCadresJson);
                        }
                        catch (JsonException ex)
                        {
                            _logger.LogWarning(ex, "Failed to parse ApplicableAcademicCadresJson for form {FormId}", form.Id);
                            formDto.ApplicableAcademicCadres = new List<string>();
                        }
                    }
                }
            });
        }

        public async Task<EvaluationFormDto> CreateEvaluationFormAsync(EvaluationFormCreateDto dto, string createdByUserId)
        {
            var entity = dto.Adapt<EvaluationFormEntity>();
            entity.CreatedByUserId = createdByUserId;

            // Serialize JSON fields
            if (dto.ApplicableAcademicCadres != null && dto.ApplicableAcademicCadres.Any())
            {
                entity.ApplicableAcademicCadresJson = JsonSerializer.Serialize(dto.ApplicableAcademicCadres);
            }

            var createdEntity = await _formStore.CreateEvaluationFormAsync(entity);
            return createdEntity.Adapt<EvaluationFormDto>();
        }

        public async Task<bool> UpdateEvaluationFormAsync(EvaluationFormUpdateDto dto, string updatedByUserId)
        {
            if (!await CanUpdateEvaluationFormAsync(dto.Id))
            {
                throw new InvalidOperationException("Cannot update this evaluation form");
            }

            var existingEntity = await _formStore.GetEvaluationFormForUpdateAsync(dto.Id);
            if (existingEntity == null)
                return false;

            // Map updates
            dto.Adapt(existingEntity);
            existingEntity.UpdatedByUserId = updatedByUserId;

            // Serialize JSON fields
            if (dto.ApplicableAcademicCadres != null && dto.ApplicableAcademicCadres.Any())
            {
                existingEntity.ApplicableAcademicCadresJson = JsonSerializer.Serialize(dto.ApplicableAcademicCadres);
            }

            return await _formStore.UpdateEvaluationFormAsync(existingEntity);
        }

        public async Task<bool> UpdateEvaluationFormStatusAsync(EvaluationFormStatusUpdateDto dto, string updatedByUserId)
        {
            var validStatuses = new[] { "Draft", "Active", "Archived" };
            if (!validStatuses.Contains(dto.Status))
            {
                throw new ArgumentException("Invalid status value");
            }

            var existingEntity = await _formStore.GetEvaluationFormForUpdateAsync(dto.Id);
            if (existingEntity == null)
                return false;

            existingEntity.UpdatedByUserId = updatedByUserId;
            return await _formStore.UpdateEvaluationFormStatusAsync(dto.Id, dto.Status);
        }

        public async Task<bool> DeleteEvaluationFormAsync(string id)
        {
            if (!await CanDeleteEvaluationFormAsync(id))
            {
                throw new InvalidOperationException("Cannot delete this evaluation form");
            }

            return await _formStore.DeleteEvaluationFormAsync(id);
        }

        #endregion

        #region Form Category Management

        public async Task<List<FormCategoryDto>> GetFormCategoriesByFormIdAsync(string evaluationFormId)
        {
            var categories = await _formStore.GetFormCategoriesByFormIdAsync(evaluationFormId);
            return categories.Adapt<List<FormCategoryDto>>();
        }

        public async Task<FormCategoryDto?> GetFormCategoryByIdAsync(string id)
        {
            var category = await _formStore.GetFormCategoryByIdAsync(id);
            return category?.Adapt<FormCategoryDto>();
        }

        public async Task<FormCategoryDto> CreateFormCategoryAsync(FormCategoryCreateDto dto, string createdByUserId)
        {
            // Get form AutoIncrementId
            var formIdMap = await _formStore.IdConvertForEvaluationForm(new[] { dto.EvaluationFormId });
            if (!formIdMap.TryGetValue(dto.EvaluationFormId, out var formAutoIncrementId))
            {
                throw new ArgumentException("Invalid evaluation form ID");
            }

            var entity = dto.Adapt<FormCategoryEntity>();
            entity.EvaluationFormAutoIncrementId = formAutoIncrementId;
            entity.CreatedByUserId = createdByUserId;

            var createdEntity = await _formStore.CreateFormCategoryAsync(entity);
            return createdEntity.Adapt<FormCategoryDto>();
        }

        public async Task<bool> UpdateFormCategoryAsync(FormCategoryUpdateDto dto, string updatedByUserId)
        {
            var existingEntity = await _formStore.GetFormCategoryForUpdateAsync(dto.Id);
            if (existingEntity == null)
                return false;

            dto.Adapt(existingEntity);
            existingEntity.UpdatedByUserId = updatedByUserId;

            return await _formStore.UpdateFormCategoryAsync(existingEntity);
        }

        public async Task<bool> DeleteFormCategoryAsync(string id)
        {
            if (!await CanDeleteFormCategoryAsync(id))
            {
                throw new InvalidOperationException("Cannot delete this form category");
            }

            return await _formStore.DeleteFormCategoryAsync(id);
        }

        public async Task<bool> ValidateFormCategoryWeightsAsync(FormCategoryWeightValidationDto dto)
        {
            var categoryWeights = dto.CategoryWeights.Select(cw => (cw.CategoryId, cw.Weight)).ToList();
            return await _formStore.ValidateCategoryWeightsAsync(dto.EvaluationFormId, categoryWeights);
        }

        #endregion

        #region Form Criterion Link Management

        public async Task<List<FormCriterionLinkDto>> GetFormCriterionLinksByCategoryIdAsync(string formCategoryId)
        {
            var links = await _formStore.GetFormCriterionLinksByCategoryIdAsync(formCategoryId);
            return links.Adapt<List<FormCriterionLinkDto>>();
        }

        public async Task<FormCriterionLinkDto?> GetFormCriterionLinkByIdAsync(string id)
        {
            var link = await _formStore.GetFormCriterionLinkByIdAsync(id);
            return link?.Adapt<FormCriterionLinkDto>();
        }

        public async Task<FormCriterionLinkDto> CreateFormCriterionLinkAsync(FormCriterionLinkCreateDto dto, string createdByUserId)
        {
            if (!await ValidateCriterionAssignmentAsync(dto))
            {
                throw new ArgumentException("Invalid criterion assignment");
            }

            // Get category AutoIncrementId
            var categoryIdMap = await _formStore.IdConvertForFormCategory(new[] { dto.FormCategoryId });
            if (!categoryIdMap.TryGetValue(dto.FormCategoryId, out var categoryAutoIncrementId))
            {
                throw new ArgumentException("Invalid form category ID");
            }

            var entity = dto.Adapt<FormCriterionLinkEntity>();
            entity.FormCategoryAutoIncrementId = categoryAutoIncrementId;
            entity.CreatedByUserId = createdByUserId;

            var createdEntity = await _formStore.CreateFormCriterionLinkAsync(entity);
            return createdEntity.Adapt<FormCriterionLinkDto>();
        }

        public async Task<bool> UpdateFormCriterionLinkAsync(FormCriterionLinkUpdateDto dto)
        {
            var existingEntity = await _formStore.GetFormCriterionLinkForUpdateAsync(dto.Id);
            if (existingEntity == null)
                return false;

            dto.Adapt(existingEntity);
            return await _formStore.UpdateFormCriterionLinkAsync(existingEntity);
        }

        public async Task<bool> DeleteFormCriterionLinkAsync(string id)
        {
            if (!await CanDeleteFormCriterionLinkAsync(id))
            {
                throw new InvalidOperationException("Cannot delete this form criterion link");
            }

            return await _formStore.DeleteFormCriterionLinkAsync(id);
        }

        public async Task<List<FormCriterionLinkDto>> AssignCriteriaToFormCategoryAsync(CriterionAssignmentDto dto, string createdByUserId)
        {
            var createdLinks = new List<FormCriterionLinkDto>();

            foreach (var linkDto in dto.CriterionLinks)
            {
                linkDto.FormCategoryId = dto.FormCategoryId;
                var createdLink = await CreateFormCriterionLinkAsync(linkDto, createdByUserId);
                createdLinks.Add(createdLink);
            }

            return createdLinks;
        }

        #endregion

        #region Validation

        public async Task<bool> CanUpdateEvaluationFormAsync(string id)
        {
            var form = await _formStore.GetEvaluationFormForUpdateAsync(id);
            if (form == null)
                return false;

            // Can only update Draft forms
            return form.Status == "Draft";
        }

        public async Task<bool> CanDeleteEvaluationFormAsync(string id)
        {
            var form = await _formStore.GetEvaluationFormForUpdateAsync(id);
            if (form == null)
                return false;

            // Can only delete Draft forms
            return form.Status == "Draft";
        }

        public async Task<bool> CanDeleteFormCategoryAsync(string id)
        {
            // Check if category has any criterion links
            var links = await _formStore.GetFormCriterionLinksByCategoryIdAsync(id);
            return !links.Any();
        }

        public async Task<bool> CanDeleteFormCriterionLinkAsync(string id)
        {
            // For now, allow deletion if link exists
            return await _formStore.FormCriterionLinkExistsAsync(id);
        }

        public async Task<bool> ValidateCriterionAssignmentAsync(FormCriterionLinkCreateDto dto)
        {
            // Validate criterion type and IDs
            if (dto.CriterionType == "Static")
            {
                if (string.IsNullOrEmpty(dto.StaticCriterionSystemId))
                    return false;

                var staticCriterion = await _criteriaStore.GetStaticCriterionDefinitionByIdAsync(dto.StaticCriterionSystemId);
                return staticCriterion != null && staticCriterion.IsActive;
            }
            else if (dto.CriterionType == "Dynamic")
            {
                if (string.IsNullOrEmpty(dto.DynamicCriterionTemplateId))
                    return false;

                var dynamicCriterion = await _criteriaStore.GetDynamicCriterionTemplateByIdAsync(dto.DynamicCriterionTemplateId);
                return dynamicCriterion != null && dynamicCriterion.Status == "Active";
            }

            return false;
        }

        #endregion

        #region Academician-specific Methods

        public async Task<EvaluationFormDto?> GetEvaluationFormForAcademicianAsync(string universityUserId, string formId)
        {
            // First check if academician can access this form
            var canAccess = await CanAcademicianAccessFormAsync(universityUserId, formId);
            if (!canAccess)
                return null;

            // Get form details (same as admin but with access control)
            return await GetEvaluationFormByIdAsync(formId);
        }

        public async Task<List<FormCategoryDto>?> GetFormCategoriesForAcademicianAsync(string universityUserId, string formId)
        {
            // First check if academician can access this form
            var canAccess = await CanAcademicianAccessFormAsync(universityUserId, formId);
            if (!canAccess)
                return null;

            // Get categories (same as admin but with access control)
            return await GetFormCategoriesByFormIdAsync(formId);
        }

        // Pagination Methods for Form Categories
        public async Task<PagedListDto<FormCategoryDto>> GetFormCategoriesByFormIdAsync(string evaluationFormId, PagedListCo<GetStatusFilterCo> co)
        {
            var allCategories = await GetFormCategoriesByFormIdAsync(evaluationFormId);

            // Filtreleme uygula
            var filteredCategories = allCategories.AsQueryable();

            if (!string.IsNullOrEmpty(co.Criteria?.NameContains))
            {
                filteredCategories = filteredCategories.Where(c => c.Name != null && c.Name.Contains(co.Criteria!.NameContains, StringComparison.OrdinalIgnoreCase));
            }

            if (co.Criteria?.OnlyActive == true)
            {
                filteredCategories = filteredCategories.Where(c => c.IsActive);
            }

            if (co.Criteria?.CreatedAfter.HasValue == true)
            {
                filteredCategories = filteredCategories.Where(c => c.CreatedAt >= co.Criteria!.CreatedAfter.Value);
            }

            if (co.Criteria?.CreatedBefore.HasValue == true)
            {
                filteredCategories = filteredCategories.Where(c => c.CreatedAt <= co.Criteria!.CreatedBefore.Value);
            }

            // Sıralama uygula
            if (!string.IsNullOrEmpty(co.Sort))
            {
                filteredCategories = co.Sort.ToLower() switch
                {
                    "name" => filteredCategories.OrderBy(c => c.Name),
                    "name_desc" => filteredCategories.OrderByDescending(c => c.Name),
                    "weight" => filteredCategories.OrderBy(c => c.Weight),
                    "weight_desc" => filteredCategories.OrderByDescending(c => c.Weight),
                    "created" => filteredCategories.OrderBy(c => c.CreatedAt),
                    "created_desc" => filteredCategories.OrderByDescending(c => c.CreatedAt),
                    _ => filteredCategories.OrderBy(c => c.DisplayOrder)
                };
            }
            else
            {
                filteredCategories = filteredCategories.OrderBy(c => c.DisplayOrder);
            }

            // Pagination uygula
            var totalCount = filteredCategories.Count();
            var pagedData = filteredCategories
                .Skip(co.Pager.Skip)
                .Take(co.Pager.Size)
                .ToList();

            return new PagedListDto<FormCategoryDto>
            {
                Count = totalCount,
                Page = co.Pager.Page,
                Size = co.Pager.Size,
                Data = pagedData
            };
        }

        public async Task<PagedListDto<FormCategoryDto>?> GetFormCategoriesForAcademicianAsync(string universityUserId, string formId, PagedListCo<GetStatusFilterCo> co)
        {
            // First check if academician can access this form
            var canAccess = await CanAcademicianAccessFormAsync(universityUserId, formId);
            if (!canAccess)
                return null;

            // Get paginated categories (same as admin but with access control)
            return await GetFormCategoriesByFormIdAsync(formId, co);
        }

        public async Task<List<FormCriterionLinkDto>?> GetFormCriterionLinksForAcademicianAsync(string universityUserId, string categoryId)
        {
            // First check if academician can access this category
            var canAccess = await CanAcademicianAccessCategoryAsync(universityUserId, categoryId);
            if (!canAccess)
                return null;

            // Get criterion links (same as admin but with access control)
            return await GetFormCriterionLinksByCategoryIdAsync(categoryId);
        }

        // Pagination Methods for Form Criterion Links
        public async Task<PagedListDto<FormCriterionLinkDto>> GetFormCriterionLinksByCategoryIdAsync(string formCategoryId, PagedListCo<GetStatusFilterCo> co)
        {
            var allLinks = await GetFormCriterionLinksByCategoryIdAsync(formCategoryId);

            // Filtreleme uygula
            var filteredLinks = allLinks.AsQueryable();

            if (!string.IsNullOrEmpty(co.Criteria?.NameContains))
            {
                filteredLinks = filteredLinks.Where(l =>
                    (l.CriterionName != null && l.CriterionName.Contains(co.Criteria!.NameContains, StringComparison.OrdinalIgnoreCase)) ||
                    (l.CriterionType != null && l.CriterionType.Contains(co.Criteria!.NameContains, StringComparison.OrdinalIgnoreCase)));
            }

            if (co.Criteria?.OnlyActive == true)
            {
                filteredLinks = filteredLinks.Where(l => l.IsActive);
            }

            if (co.Criteria?.CreatedAfter.HasValue == true)
            {
                filteredLinks = filteredLinks.Where(l => l.CreatedAt >= co.Criteria!.CreatedAfter.Value);
            }

            if (co.Criteria?.CreatedBefore.HasValue == true)
            {
                filteredLinks = filteredLinks.Where(l => l.CreatedAt <= co.Criteria!.CreatedBefore.Value);
            }

            // Sıralama uygula
            if (!string.IsNullOrEmpty(co.Sort))
            {
                filteredLinks = co.Sort.ToLower() switch
                {
                    "name" => filteredLinks.OrderBy(l => l.CriterionName),
                    "name_desc" => filteredLinks.OrderByDescending(l => l.CriterionName),
                    "type" => filteredLinks.OrderBy(l => l.CriterionType),
                    "type_desc" => filteredLinks.OrderByDescending(l => l.CriterionType),
                    "weight" => filteredLinks.OrderBy(l => l.Weight),
                    "weight_desc" => filteredLinks.OrderByDescending(l => l.Weight),
                    "created" => filteredLinks.OrderBy(l => l.CreatedAt),
                    "created_desc" => filteredLinks.OrderByDescending(l => l.CreatedAt),
                    _ => filteredLinks.OrderBy(l => l.DisplayOrder)
                };
            }
            else
            {
                filteredLinks = filteredLinks.OrderBy(l => l.DisplayOrder);
            }

            // Pagination uygula
            var totalCount = filteredLinks.Count();
            var pagedData = filteredLinks
                .Skip(co.Pager.Skip)
                .Take(co.Pager.Size)
                .ToList();

            return new PagedListDto<FormCriterionLinkDto>
            {
                Count = totalCount,
                Page = co.Pager.Page,
                Size = co.Pager.Size,
                Data = pagedData
            };
        }

        public async Task<PagedListDto<FormCriterionLinkDto>?> GetFormCriterionLinksForAcademicianAsync(string universityUserId, string categoryId, PagedListCo<GetStatusFilterCo> co)
        {
            // First check if academician can access this category
            var canAccess = await CanAcademicianAccessCategoryAsync(universityUserId, categoryId);
            if (!canAccess)
                return null;

            // Get paginated criterion links (same as admin but with access control)
            return await GetFormCriterionLinksByCategoryIdAsync(categoryId, co);
        }

        private async Task<bool> CanAcademicianAccessFormAsync(string universityUserId, string formId)
        {
            // Get form details
            var form = await _formStore.GetEvaluationFormByIdAsync(formId);
            if (form == null || form.Status != "Active")
                return false;

            // Check if form is within evaluation period
            var now = DateTime.UtcNow;
            if (now < form.EvaluationPeriodStartDate || now > form.EvaluationPeriodEndDate)
                return false;

            // Get academician profile to check academic cadre
            var academicianProfile = await GetAcademicianProfileAsync(universityUserId);
            if (academicianProfile == null)
                return false;

            // Check if form is applicable to academician's cadre
            if (!string.IsNullOrEmpty(form.ApplicableAcademicCadresJson))
            {
                try
                {
                    var applicableCadres = JsonSerializer.Deserialize<List<string>>(form.ApplicableAcademicCadresJson);
                    if (applicableCadres != null && applicableCadres.Any())
                    {
                        return applicableCadres.Contains(academicianProfile.AcademicCadre);
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "Failed to parse ApplicableAcademicCadresJson for form {FormId}", formId);
                    return false;
                }
            }

            return true;
        }

        private async Task<bool> CanAcademicianAccessCategoryAsync(string universityUserId, string categoryId)
        {
            // Get category details
            var category = await _formStore.GetFormCategoryByIdAsync(categoryId);
            if (category == null)
                return false;

            // Get form ID from category
            var form = await _formStore.GetEvaluationFormByAutoIncrementIdAsync(category.EvaluationFormAutoIncrementId);
            if (form == null)
                return false;

            // Check form access
            return await CanAcademicianAccessFormAsync(universityUserId, form.Id);
        }

        private Task<dynamic?> GetAcademicianProfileAsync(string universityUserId)
        {
            // This should be implemented to get academician profile
            // For now, return a mock object
            // In real implementation, this would call IAcademicianStore or similar
            return Task.FromResult<dynamic?>(new { AcademicCadre = "Professor" }); // Mock data
        }

        #endregion
    }
}
