using Mapster;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.MongoDocuments;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Helpers;

namespace AcademicPerformance.Managers
{
    public class CriteriaManager : ICriteriaManager
    {
        private readonly ICriteriaStore _criteriaStore;
        private readonly ILogger<CriteriaManager> _logger;
        private readonly IRlxLocalizationManager<AcademicPerformance.DbContexts.AcademicPerformanceLocalizationDbContext> _localizationManager;

        public CriteriaManager(ICriteriaStore criteriaStore,
            ILogger<CriteriaManager> logger,
            IRlxLocalizationManager<AcademicPerformance.DbContexts.AcademicPerformanceLocalizationDbContext> localizationManager)
        {
            _criteriaStore = criteriaStore;
            _logger = logger;
            _localizationManager = localizationManager;
        }

        #region Dynamic Criteria Management

        public async Task<List<DynamicCriterionTemplateDto>> GetDynamicCriterionTemplatesAsync()
        {
            var templates = await _criteriaStore.GetDynamicCriterionTemplatesAsync();
            return templates.Adapt<List<DynamicCriterionTemplateDto>>();
        }

        public async Task<DynamicCriterionTemplateDto?> GetDynamicCriterionTemplateByIdAsync(string id)
        {
            var template = await _criteriaStore.GetDynamicCriterionTemplateByIdAsync(id);
            return template?.Adapt<DynamicCriterionTemplateDto>();
        }

        public async Task<List<DynamicCriterionTemplateDto>> GetDynamicCriterionTemplatesByStatusAsync(string status)
        {
            var templates = await _criteriaStore.GetDynamicCriterionTemplatesByStatusAsync(status);
            return templates.Adapt<List<DynamicCriterionTemplateDto>>();
        }

        public async Task<List<DynamicCriterionTemplateDto>> GetActiveDynamicCriterionTemplatesAsync()
        {
            return await GetDynamicCriterionTemplatesByStatusAsync("Active");
        }

        public async Task<DynamicCriterionTemplateDto> CreateDynamicCriterionTemplateAsync(DynamicCriterionTemplateCreateDto dto, string createdByUserId)
        {
            if (!await ValidateDynamicCriterionTemplateAsync(dto))
            {
                throw new ArgumentException("Invalid dynamic criterion template data");
            }

            var template = dto.Adapt<DynamicCriterionTemplate>();
            template.Status = "Draft";
            template.CreatedByUserId = createdByUserId;

            var createdTemplate = await _criteriaStore.CreateDynamicCriterionTemplateAsync(template);
            return createdTemplate.Adapt<DynamicCriterionTemplateDto>();
        }

        public async Task<bool> UpdateDynamicCriterionTemplateAsync(DynamicCriterionTemplateUpdateDto dto, string updatedByUserId)
        {
            if (!await CanUpdateDynamicCriterionTemplateAsync(dto.Id))
            {
                throw new InvalidOperationException("Cannot update this dynamic criterion template");
            }

            var existingTemplate = await _criteriaStore.GetDynamicCriterionTemplateByIdAsync(dto.Id);
            if (existingTemplate == null)
            {
                return false;
            }

            // Map updates to existing template
            dto.Adapt(existingTemplate);
            existingTemplate.UpdatedByUserId = updatedByUserId;

            return await _criteriaStore.UpdateDynamicCriterionTemplateAsync(dto.Id, existingTemplate);
        }

        public async Task<bool> UpdateDynamicCriterionTemplateStatusAsync(string id, string status, string updatedByUserId)
        {
            var validStatuses = new[] { "Draft", "Active", "Inactive" };
            if (!validStatuses.Contains(status))
            {
                throw new ArgumentException("Invalid status value");
            }

            var existingTemplate = await _criteriaStore.GetDynamicCriterionTemplateByIdAsync(id);
            if (existingTemplate == null)
            {
                return false;
            }

            existingTemplate.Status = status;
            existingTemplate.UpdatedByUserId = updatedByUserId;

            return await _criteriaStore.UpdateDynamicCriterionTemplateStatusAsync(id, status);
        }

        public async Task<bool> DeleteDynamicCriterionTemplateAsync(string id)
        {
            if (!await CanDeleteDynamicCriterionTemplateAsync(id))
            {
                throw new InvalidOperationException("Cannot delete this dynamic criterion template");
            }

            return await _criteriaStore.DeleteDynamicCriterionTemplateAsync(id);
        }

        // Pagination metodu
        public async Task<PagedListDto<DynamicCriterionTemplateDto>> GetDynamicCriterionTemplatesAsync(PagedListCo<GetDynamicCriterionTemplatesCo> co)
        {
            var pagedTemplates = await _criteriaStore.GetDynamicCriterionTemplatesAsync(co);

            // Entity'leri DTO'ya çevir
            var templateDtos = pagedTemplates.Data?.ToList().Adapt<List<DynamicCriterionTemplateDto>>() ?? new List<DynamicCriterionTemplateDto>();

            return new PagedListDto<DynamicCriterionTemplateDto>
            {
                Count = pagedTemplates.Count,
                Page = pagedTemplates.Page,
                Size = pagedTemplates.Size,
                Data = templateDtos
            };
        }

        // Status-based Pagination Methods
        public async Task<PagedListDto<DynamicCriterionTemplateDto>> GetDynamicCriterionTemplatesByStatusAsync(string status, PagedListCo<GetStatusFilterCo> co)
        {
            // Status kriterini GetDynamicCriterionTemplatesCo'ya çevir
            var dynamicCo = new PagedListCo<GetDynamicCriterionTemplatesCo>
            {
                Pager = co.Pager,
                Sort = co.Sort,
                Criteria = new GetDynamicCriterionTemplatesCo
                {
                    Status = status,
                    NameContains = co.Criteria?.NameContains,
                    CreatedAfter = co.Criteria?.CreatedAfter,
                    CreatedBefore = co.Criteria?.CreatedBefore
                }
            };

            return await GetDynamicCriterionTemplatesAsync(dynamicCo);
        }

        public async Task<PagedListDto<DynamicCriterionTemplateDto>> GetActiveDynamicCriterionTemplatesAsync(PagedListCo<GetStatusFilterCo> co)
        {
            return await GetDynamicCriterionTemplatesByStatusAsync("Active", co);
        }

        #endregion

        #region Static Criteria Management

        public async Task<List<StaticCriterionDefinitionDto>> GetStaticCriterionDefinitionsAsync(string? culture = null)
        {
            var definitions = await _criteriaStore.GetStaticCriterionDefinitionsAsync();
            var dtos = definitions.Adapt<List<StaticCriterionDefinitionDto>>();

            // Attach localizations
            await dtos.AttachLocalizationsAsync(
                d => d.StaticCriterionSystemId,
                ids => _localizationManager.GetLocalizationsAsync(new GetRlxLocalizationsCo
                {
                    Culture = culture ?? "en",
                    ReferenceIds = ids,
                }),
                l => l.ReferenceId,
                (d, l) => d.Localizations = l.ToList()
            );

            return dtos;
        }

        public async Task<StaticCriterionDefinitionDto?> GetStaticCriterionDefinitionByIdAsync(string staticCriterionSystemId)
        {
            var definition = await _criteriaStore.GetStaticCriterionDefinitionByIdAsync(staticCriterionSystemId);
            return definition?.Adapt<StaticCriterionDefinitionDto>();
        }

        public async Task<List<StaticCriterionDefinitionDto>> GetActiveStaticCriterionDefinitionsAsync()
        {
            var definitions = await _criteriaStore.GetActiveStaticCriterionDefinitionsAsync();
            return definitions.Adapt<List<StaticCriterionDefinitionDto>>();
        }

        public async Task<bool> UpdateStaticCriterionDefinitionStatusAsync(StaticCriterionDefinitionUpdateDto dto)
        {
            return await _criteriaStore.UpdateStaticCriterionDefinitionStatusAsync(dto.StaticCriterionSystemId, dto.IsActive);
        }

        // Pagination metodu
        public async Task<PagedListDto<StaticCriterionDefinitionDto>> GetStaticCriterionDefinitionsAsync(PagedListCo<GetStaticCriterionDefinitionsCo> co, string? culture = null)
        {
            var pagedDefinitions = await _criteriaStore.GetStaticCriterionDefinitionsAsync(co);

            // Entity'leri DTO'ya çevir
            var definitionDtos = pagedDefinitions.Data?.ToList().Adapt<List<StaticCriterionDefinitionDto>>() ?? new List<StaticCriterionDefinitionDto>();

            // Localization ekle
            if (definitionDtos.Any())
            {
                await definitionDtos.AttachLocalizationsAsync(
                    d => d.StaticCriterionSystemId,
                    ids => _localizationManager.GetLocalizationsAsync(new GetRlxLocalizationsCo
                    {
                        Culture = culture ?? "en",
                        ReferenceIds = ids,
                    }),
                    l => l.ReferenceId,
                    (d, l) => d.Localizations = l.ToList()
                );
            }

            return new PagedListDto<StaticCriterionDefinitionDto>
            {
                Count = pagedDefinitions.Count,
                Page = pagedDefinitions.Page,
                Size = pagedDefinitions.Size,
                Data = definitionDtos
            };
        }

        // Status-based Pagination Method for Static Criteria
        public async Task<PagedListDto<StaticCriterionDefinitionDto>> GetActiveStaticCriterionDefinitionsAsync(PagedListCo<GetStatusFilterCo> co)
        {
            // Active kriterini GetStaticCriterionDefinitionsCo'ya çevir
            var staticCo = new PagedListCo<GetStaticCriterionDefinitionsCo>
            {
                Pager = co.Pager,
                Sort = co.Sort,
                Criteria = new GetStaticCriterionDefinitionsCo
                {
                    OnlyActive = true, // Sadece aktif olanları getir
                    NameContains = co.Criteria?.NameContains,
                    Culture = "en" // Varsayılan culture
                }
            };

            return await GetStaticCriterionDefinitionsAsync(staticCo, "en");
        }

        #endregion

        #region Validation

        public Task<bool> ValidateDynamicCriterionTemplateAsync(DynamicCriterionTemplateCreateDto dto)
        {
            // Basic validation
            if (string.IsNullOrWhiteSpace(dto.Name))
                return Task.FromResult(false);

            if (dto.InputFields == null || !dto.InputFields.Any())
                return Task.FromResult(false);

            // Validate input fields
            foreach (var field in dto.InputFields)
            {
                if (string.IsNullOrWhiteSpace(field.FieldId) || string.IsNullOrWhiteSpace(field.FieldName))
                    return Task.FromResult(false);

                var validFieldTypes = new[] { "Text", "Number", "Date", "File", "Select" };
                if (!validFieldTypes.Contains(field.FieldType))
                    return Task.FromResult(false);
            }

            return Task.FromResult(true);
        }

        public async Task<bool> CanUpdateDynamicCriterionTemplateAsync(string id)
        {
            var template = await _criteriaStore.GetDynamicCriterionTemplateByIdAsync(id);
            if (template == null)
                return false;

            // Can only update Draft or Inactive templates
            return template.Status == "Draft" || template.Status == "Inactive";
        }

        public async Task<bool> CanDeleteDynamicCriterionTemplateAsync(string id)
        {
            var template = await _criteriaStore.GetDynamicCriterionTemplateByIdAsync(id);
            if (template == null)
                return false;

            // Can only delete Draft templates
            return template.Status == "Draft";
        }

        #endregion
    }
}
