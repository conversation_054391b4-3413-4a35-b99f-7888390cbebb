using Mapster;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AcademicPerformance.Managers
{
    /// <summary>
    /// Generic Data Entry Manager implementation
    /// Epic 5 - Specialized Workflows için generic veri girişi yönetimi
    /// </summary>
    public class GenericDataEntryManager : IGenericDataEntryManager
    {
        private readonly IGenericDataEntryStore _store;
        private readonly ILogger<GenericDataEntryManager> _logger;

        public GenericDataEntryManager(
            IGenericDataEntryStore store,
            ILogger<GenericDataEntryManager> logger)
        {
            _store = store;
            _logger = logger;
        }

        #region Definition Management

        public async Task<GenericDataEntryDefinitionDto> CreateDefinitionAsync(GenericDataEntryDefinitionCreateDto dto, string createdByUserId)
        {
            try
            {
                _logger.LogInformation("Generic data entry definition oluşturuluyor: {Name}", dto.Name);

                // Validation
                var validationResult = await ValidateDefinitionAsync(dto);
                if (!validationResult.IsValid)
                {
                    throw new ArgumentException($"Validation hatası: {string.Join(", ", validationResult.Errors)}");
                }

                // Name benzersizlik kontrolü
                var isUnique = await _store.IsDefinitionNameUniqueAsync(dto.Name);
                if (!isUnique)
                {
                    throw new ArgumentException($"Definition adı '{dto.Name}' zaten kullanımda");
                }

                var entity = new GenericDataEntryDefinitionEntity
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = dto.Name,
                    Description = dto.Description,
                    Category = dto.Category,
                    FieldDefinitions = dto.FieldDefinitions,
                    ValidationRules = dto.ValidationRules,
                    IsActive = dto.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    CreatedByUserId = createdByUserId
                };

                var createdEntity = await _store.CreateDefinitionAsync(entity);
                var result = createdEntity.Adapt<GenericDataEntryDefinitionDto>();

                _logger.LogInformation("Generic data entry definition başarıyla oluşturuldu: {Id}", result.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition oluşturma hatası: {Name}", dto.Name);
                throw;
            }
        }

        public async Task<bool> UpdateDefinitionAsync(string definitionId, GenericDataEntryDefinitionUpdateDto dto, string updatedByUserId)
        {
            try
            {
                _logger.LogInformation("Generic data entry definition güncelleniyor: {Id}", definitionId);

                var existingEntity = await _store.GetDefinitionByIdAsync(definitionId);
                if (existingEntity == null)
                {
                    throw new ArgumentException($"Definition bulunamadı: {definitionId}");
                }

                // Name benzersizlik kontrolü (eğer name değişiyorsa)
                if (!string.IsNullOrEmpty(dto.Name) && dto.Name != existingEntity.Name)
                {
                    var isUnique = await _store.IsDefinitionNameUniqueAsync(dto.Name, definitionId);
                    if (!isUnique)
                    {
                        throw new ArgumentException($"Definition adı '{dto.Name}' zaten kullanımda");
                    }
                    existingEntity.Name = dto.Name;
                }

                // Diğer alanları güncelle
                if (!string.IsNullOrEmpty(dto.Description))
                    existingEntity.Description = dto.Description;

                if (!string.IsNullOrEmpty(dto.Category))
                    existingEntity.Category = dto.Category;

                if (!string.IsNullOrEmpty(dto.FieldDefinitions))
                    existingEntity.FieldDefinitions = dto.FieldDefinitions;

                if (!string.IsNullOrEmpty(dto.ValidationRules))
                    existingEntity.ValidationRules = dto.ValidationRules;

                if (dto.IsActive.HasValue)
                    existingEntity.IsActive = dto.IsActive.Value;

                existingEntity.UpdatedAt = DateTime.UtcNow;
                existingEntity.UpdatedByUserId = updatedByUserId;

                var result = await _store.UpdateDefinitionAsync(existingEntity);

                _logger.LogInformation("Generic data entry definition başarıyla güncellendi: {Id}", definitionId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition güncelleme hatası: {Id}", definitionId);
                throw;
            }
        }

        public async Task<bool> DeleteDefinitionAsync(string definitionId, string deletedByUserId)
        {
            try
            {
                _logger.LogInformation("Generic data entry definition siliniyor: {Id}", definitionId);

                // Kullanımda mı kontrol et
                var isInUse = await _store.IsDefinitionInUseAsync(definitionId);
                if (isInUse)
                {
                    throw new InvalidOperationException("Definition kullanımda olduğu için silinemez");
                }

                var result = await _store.DeleteDefinitionAsync(definitionId);

                _logger.LogInformation("Generic data entry definition başarıyla silindi: {Id}", definitionId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition silme hatası: {Id}", definitionId);
                throw;
            }
        }

        public async Task<GenericDataEntryDefinitionDto?> GetDefinitionByIdAsync(string definitionId)
        {
            try
            {
                var entity = await _store.GetDefinitionByIdAsync(definitionId);
                return entity?.Adapt<GenericDataEntryDefinitionDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition getirme hatası: {Id}", definitionId);
                throw;
            }
        }

        public async Task<PagedListDto<GenericDataEntryDefinitionDto>> GetDefinitionsAsync(PagedListCo<GenericDataEntryFilterCo> co)
        {
            try
            {
                var entities = await _store.GetDefinitionsAsync(co);
                var dtos = entities.Data?.Adapt<List<GenericDataEntryDefinitionDto>>() ?? new List<GenericDataEntryDefinitionDto>();

                return new PagedListDto<GenericDataEntryDefinitionDto>
                {
                    Data = dtos,
                    Count = entities.Count,
                    TotalCount = entities.TotalCount,
                    Page = entities.Page,
                    Size = entities.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition'ları listeleme hatası");
                throw;
            }
        }

        #endregion

        #region Record Management

        public async Task<GenericDataEntryRecordDto> CreateRecordAsync(GenericDataEntryRecordCreateDto dto, string createdByUserId)
        {
            try
            {
                _logger.LogInformation("Generic data entry record oluşturuluyor: {DefinitionId}", dto.DefinitionId);

                // Definition var mı kontrol et
                var definition = await _store.GetDefinitionByIdAsync(dto.DefinitionId);
                if (definition == null)
                {
                    throw new ArgumentException($"Definition bulunamadı: {dto.DefinitionId}");
                }

                // Record validation
                var validationResult = await ValidateRecordAsync(dto, dto.DefinitionId);
                if (!validationResult.IsValid)
                {
                    throw new ArgumentException($"Validation hatası: {string.Join(", ", validationResult.Errors)}");
                }

                var entity = new GenericDataEntryRecordEntity
                {
                    Id = Guid.NewGuid().ToString(),
                    DefinitionId = dto.DefinitionId,
                    RecordData = dto.RecordData,
                    Status = dto.Status,
                    CreatedAt = DateTime.UtcNow,
                    CreatedByUserId = createdByUserId
                };

                var createdEntity = await _store.CreateRecordAsync(entity);
                var result = createdEntity.Adapt<GenericDataEntryRecordDto>();
                result.DefinitionName = definition.Name;

                _logger.LogInformation("Generic data entry record başarıyla oluşturuldu: {Id}", result.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record oluşturma hatası: {DefinitionId}", dto.DefinitionId);
                throw;
            }
        }

        public async Task<bool> UpdateRecordAsync(string recordId, GenericDataEntryRecordUpdateDto dto, string updatedByUserId)
        {
            try
            {
                _logger.LogInformation("Generic data entry record güncelleniyor: {Id}", recordId);

                var existingEntity = await _store.GetRecordByIdAsync(recordId);
                if (existingEntity == null)
                {
                    throw new ArgumentException($"Record bulunamadı: {recordId}");
                }

                // Alanları güncelle
                if (!string.IsNullOrEmpty(dto.RecordData))
                    existingEntity.RecordData = dto.RecordData;

                if (!string.IsNullOrEmpty(dto.Status))
                    existingEntity.Status = dto.Status;

                existingEntity.UpdatedAt = DateTime.UtcNow;
                existingEntity.UpdatedByUserId = updatedByUserId;

                var result = await _store.UpdateRecordAsync(existingEntity);

                _logger.LogInformation("Generic data entry record başarıyla güncellendi: {Id}", recordId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record güncelleme hatası: {Id}", recordId);
                throw;
            }
        }

        public async Task<bool> DeleteRecordAsync(string recordId, string deletedByUserId)
        {
            try
            {
                _logger.LogInformation("Generic data entry record siliniyor: {Id}", recordId);

                var result = await _store.DeleteRecordAsync(recordId);

                _logger.LogInformation("Generic data entry record başarıyla silindi: {Id}", recordId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record silme hatası: {Id}", recordId);
                throw;
            }
        }

        public async Task<GenericDataEntryRecordDto?> GetRecordByIdAsync(string recordId)
        {
            try
            {
                var entity = await _store.GetRecordByIdAsync(recordId);
                if (entity == null) return null;

                var result = entity.Adapt<GenericDataEntryRecordDto>();

                // Definition name'i getir
                var definition = await _store.GetDefinitionByIdAsync(entity.DefinitionId);
                if (definition != null)
                {
                    result.DefinitionName = definition.Name;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record getirme hatası: {Id}", recordId);
                throw;
            }
        }

        public async Task<PagedListDto<GenericDataEntryRecordDto>> GetRecordsAsync(PagedListCo<GenericDataEntryFilterCo> co)
        {
            try
            {
                var entities = await _store.GetRecordsAsync(co);
                var dtos = entities.Data?.Adapt<List<GenericDataEntryRecordDto>>() ?? new List<GenericDataEntryRecordDto>();

                // Definition name'lerini set et
                foreach (var dto in dtos)
                {
                    var definition = await _store.GetDefinitionByIdAsync(dto.DefinitionId);
                    if (definition != null)
                    {
                        dto.DefinitionName = definition.Name;
                    }
                }

                return new PagedListDto<GenericDataEntryRecordDto>
                {
                    Data = dtos,
                    Count = entities.Count,
                    TotalCount = entities.TotalCount,
                    Page = entities.Page,
                    Size = entities.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record'ları listeleme hatası");
                throw;
            }
        }

        public async Task<PagedListDto<GenericDataEntryRecordDto>> GetRecordsByDefinitionAsync(string definitionId, PagedListCo<GenericDataEntryFilterCo> co)
        {
            try
            {
                var entities = await _store.GetRecordsByDefinitionAsync(definitionId, co);
                var dtos = entities.Data?.Adapt<List<GenericDataEntryRecordDto>>() ?? new List<GenericDataEntryRecordDto>();

                // Definition name'i set et
                var definition = await _store.GetDefinitionByIdAsync(definitionId);
                var definitionName = definition?.Name ?? "";

                foreach (var dto in dtos)
                {
                    dto.DefinitionName = definitionName;
                }

                return new PagedListDto<GenericDataEntryRecordDto>
                {
                    Data = dtos,
                    Count = entities.Count,
                    TotalCount = entities.TotalCount,
                    Page = entities.Page,
                    Size = entities.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Definition için record'ları listeleme hatası: {DefinitionId}", definitionId);
                throw;
            }
        }

        #endregion

        #region Validation and Business Logic

        public async Task<ValidationResultDto> ValidateDefinitionAsync(GenericDataEntryDefinitionCreateDto dto)
        {
            var result = new ValidationResultDto { IsValid = true };

            try
            {
                // Name validation
                if (string.IsNullOrWhiteSpace(dto.Name))
                {
                    result.Errors.Add("Definition adı gerekli");
                    result.IsValid = false;
                }

                // Category validation
                if (string.IsNullOrWhiteSpace(dto.Category))
                {
                    result.Errors.Add("Kategori gerekli");
                    result.IsValid = false;
                }

                // Field definitions JSON validation
                if (string.IsNullOrWhiteSpace(dto.FieldDefinitions))
                {
                    result.Errors.Add("Field definitions gerekli");
                    result.IsValid = false;
                }
                else
                {
                    try
                    {
                        JsonDocument.Parse(dto.FieldDefinitions);
                    }
                    catch
                    {
                        result.Errors.Add("Field definitions geçerli JSON formatında değil");
                        result.IsValid = false;
                    }
                }

                // Validation rules JSON validation
                if (!string.IsNullOrWhiteSpace(dto.ValidationRules))
                {
                    try
                    {
                        JsonDocument.Parse(dto.ValidationRules);
                    }
                    catch
                    {
                        result.Errors.Add("Validation rules geçerli JSON formatında değil");
                        result.IsValid = false;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Definition validation hatası");
                result.Errors.Add("Validation sırasında hata oluştu");
                result.IsValid = false;
                return result;
            }
        }

        public async Task<ValidationResultDto> ValidateRecordAsync(GenericDataEntryRecordCreateDto dto, string definitionId)
        {
            var result = new ValidationResultDto { IsValid = true };

            try
            {
                // Record data JSON validation
                if (string.IsNullOrWhiteSpace(dto.RecordData))
                {
                    result.Errors.Add("Record data gerekli");
                    result.IsValid = false;
                }
                else
                {
                    try
                    {
                        JsonDocument.Parse(dto.RecordData);
                    }
                    catch
                    {
                        result.Errors.Add("Record data geçerli JSON formatında değil");
                        result.IsValid = false;
                    }
                }

                // Status validation
                var validStatuses = new[] { "Draft", "Published", "Archived" };
                if (!validStatuses.Contains(dto.Status))
                {
                    result.Warnings.Add($"Geçersiz status: {dto.Status}. Geçerli değerler: {string.Join(", ", validStatuses)}");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Record validation hatası");
                result.Errors.Add("Validation sırasında hata oluştu");
                result.IsValid = false;
                return result;
            }
        }

        public async Task<bool> IsDefinitionInUseAsync(string definitionId)
        {
            try
            {
                return await _store.IsDefinitionInUseAsync(definitionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Definition kullanım kontrolü hatası: {DefinitionId}", definitionId);
                throw;
            }
        }

        #endregion

        #region Statistics and Analytics

        public async Task<GenericDataEntryStatisticsDto> GetDefinitionStatisticsAsync(string definitionId)
        {
            try
            {
                var definition = await _store.GetDefinitionByIdAsync(definitionId);
                if (definition == null)
                {
                    throw new ArgumentException($"Definition bulunamadı: {definitionId}");
                }

                var totalRecords = await _store.GetRecordCountByDefinitionAsync(definitionId);
                var recentRecords = await _store.GetRecentRecordsAsync(10);
                var lastRecordDate = recentRecords.Where(r => r.DefinitionId == definitionId)
                                                 .OrderByDescending(r => r.CreatedAt)
                                                 .FirstOrDefault()?.CreatedAt;

                return new GenericDataEntryStatisticsDto
                {
                    DefinitionId = definitionId,
                    DefinitionName = definition.Name,
                    TotalRecords = totalRecords,
                    DraftRecords = 0, // Bu bilgiyi store'dan almak gerekir
                    PublishedRecords = 0, // Bu bilgiyi store'dan almak gerekir
                    LastRecordDate = lastRecordDate
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Definition istatistikleri hatası: {DefinitionId}", definitionId);
                throw;
            }
        }

        public async Task<GenericDataEntryOverallStatisticsDto> GetOverallStatisticsAsync()
        {
            try
            {
                var totalDefinitions = await _store.GetTotalDefinitionCountAsync();
                var totalRecords = await _store.GetTotalRecordCountAsync();

                return new GenericDataEntryOverallStatisticsDto
                {
                    TotalDefinitions = totalDefinitions,
                    ActiveDefinitions = totalDefinitions, // Bu bilgiyi store'dan almak gerekir
                    TotalRecords = totalRecords,
                    RecordsThisMonth = 0, // Bu bilgiyi store'dan almak gerekir
                    MostUsedDefinitions = new List<GenericDataEntryStatisticsDto>()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Genel istatistikler hatası");
                throw;
            }
        }

        #endregion
    }
}
