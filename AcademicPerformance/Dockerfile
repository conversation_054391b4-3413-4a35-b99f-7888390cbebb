FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

COPY ["AcademicPerformance.csproj", "./"]
COPY ["nuget.config", "./"]
RUN dotnet restore "./AcademicPerformance.csproj"

COPY . .
WORKDIR "/src/."
RUN dotnet build "AcademicPerformance.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "AcademicPerformance.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "AcademicPerformance.dll"]

EXPOSE 8080
EXPOSE 5122

