﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AcademicianProfiles",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    UniversityUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Surname = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    FullName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Department = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    DepartmentId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    FacultyId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    AcademicCadre = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Email = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Title = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    LastSyncedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    SyncNotes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AcademicianProfiles", x => x.Id);
                    table.UniqueConstraint("AK_AcademicianProfiles_UniversityUserId", x => x.UniversityUserId);
                });

            migrationBuilder.CreateTable(
                name: "ApdysRoles",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    RoleName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ApdysRoles", x => x.Id);
                    table.UniqueConstraint("AK_ApdysRoles_AutoIncrementId", x => x.AutoIncrementId);
                });

            migrationBuilder.CreateTable(
                name: "DepartmentPerformances",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    DepartmentId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    DepartmentName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    FacultyId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FacultyName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Period = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    EvaluationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    OverallScore = table.Column<decimal>(type: "numeric(5,2)", nullable: false),
                    AcademicStaffPerformance = table.Column<decimal>(type: "numeric(5,2)", nullable: false),
                    ResearchPerformance = table.Column<decimal>(type: "numeric(5,2)", nullable: false),
                    PublicationPerformance = table.Column<decimal>(type: "numeric(5,2)", nullable: false),
                    StudentSatisfactionScore = table.Column<decimal>(type: "numeric(5,2)", nullable: false),
                    InfrastructureScore = table.Column<decimal>(type: "numeric(5,2)", nullable: false),
                    BudgetEfficiencyScore = table.Column<decimal>(type: "numeric(5,2)", nullable: false),
                    TotalAcademicStaff = table.Column<int>(type: "integer", nullable: false),
                    TotalStudents = table.Column<int>(type: "integer", nullable: false),
                    CompletedSubmissions = table.Column<int>(type: "integer", nullable: false),
                    PendingSubmissions = table.Column<int>(type: "integer", nullable: false),
                    CompletionRate = table.Column<decimal>(type: "numeric(5,2)", nullable: false),
                    Ranking = table.Column<int>(type: "integer", nullable: true),
                    TotalDepartmentsInRanking = table.Column<int>(type: "integer", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Trend = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Notes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    AdditionalData = table.Column<string>(type: "jsonb", nullable: true),
                    CalculationDetails = table.Column<string>(type: "jsonb", nullable: true),
                    CreatedByUserId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    CreatedByUserName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    UpdatedByUserName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApprovedByUserId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ApprovedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsApproved = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DepartmentPerformances", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DepartmentStrategicIndicatorDefinitions",
                columns: table => new
                {
                    IndicatorSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DataType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsHigherBetter = table.Column<bool>(type: "boolean", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DepartmentStrategicIndicatorDefinitions", x => x.IndicatorSystemId);
                    table.CheckConstraint("CK_DepartmentStrategicIndicator_DataType", "\"DataType\" IN ('Integer', 'Decimal', 'Boolean', 'String', 'Date')");
                });

            migrationBuilder.CreateTable(
                name: "EvaluationForms",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ApplicableAcademicCadresJson = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    EvaluationPeriodStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EvaluationPeriodEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    SubmissionDeadline = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EvaluationForms", x => x.Id);
                    table.UniqueConstraint("AK_EvaluationForms_AutoIncrementId", x => x.AutoIncrementId);
                    table.CheckConstraint("CK_EvaluationForm_Status", "\"Status\" IN ('Draft', 'Active', 'Archived')");
                });

            migrationBuilder.CreateTable(
                name: "GenericDataEntryDefinitions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Category = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    FieldDefinitions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ValidationRules = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GenericDataEntryDefinitions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PerformanceDistributions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    DistributionType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    EntityId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    EntityName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    PeriodStart = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PeriodEnd = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExcellentCount = table.Column<int>(type: "integer", nullable: false),
                    GoodCount = table.Column<int>(type: "integer", nullable: false),
                    AverageCount = table.Column<int>(type: "integer", nullable: false),
                    PoorCount = table.Column<int>(type: "integer", nullable: false),
                    TotalCount = table.Column<int>(type: "integer", nullable: false),
                    ExcellentPercentage = table.Column<double>(type: "double precision", precision: 5, scale: 2, nullable: false),
                    GoodPercentage = table.Column<double>(type: "double precision", precision: 5, scale: 2, nullable: false),
                    AveragePercentage = table.Column<double>(type: "double precision", precision: 5, scale: 2, nullable: false),
                    PoorPercentage = table.Column<double>(type: "double precision", precision: 5, scale: 2, nullable: false),
                    AverageScore = table.Column<double>(type: "double precision", precision: 8, scale: 4, nullable: false),
                    MedianScore = table.Column<double>(type: "double precision", precision: 8, scale: 4, nullable: false),
                    StandardDeviation = table.Column<double>(type: "double precision", precision: 8, scale: 4, nullable: false),
                    MinScore = table.Column<double>(type: "double precision", precision: 8, scale: 4, nullable: false),
                    MaxScore = table.Column<double>(type: "double precision", precision: 8, scale: 4, nullable: false),
                    CalculationMethod = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Weighted"),
                    CalculationParameters = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "Active"),
                    CalculatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CalculatedBy = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    LastUpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastUpdatedBy = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Metadata = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PerformanceDistributions", x => x.Id);
                    table.CheckConstraint("CK_PerformanceDistribution_Counts", "\"ExcellentCount\" >= 0 AND \"GoodCount\" >= 0 AND \"AverageCount\" >= 0 AND \"PoorCount\" >= 0");
                    table.CheckConstraint("CK_PerformanceDistribution_DistributionType", "\"DistributionType\" IN ('Department', 'Faculty', 'University', 'Form', 'Category')");
                    table.CheckConstraint("CK_PerformanceDistribution_Percentages", "\"ExcellentPercentage\" >= 0 AND \"ExcellentPercentage\" <= 100");
                    table.CheckConstraint("CK_PerformanceDistribution_Status", "\"Status\" IN ('Active', 'Archived', 'Draft')");
                });

            migrationBuilder.CreateTable(
                name: "PortfolioChecklistItemDefinitions",
                columns: table => new
                {
                    ItemSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Category = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    RequiresEbysVerification = table.Column<bool>(type: "boolean", nullable: false),
                    EbysDataSourceHint = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PortfolioChecklistItemDefinitions", x => x.ItemSystemId);
                });

            migrationBuilder.CreateTable(
                name: "RlxEnums",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Code = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RlxEnums", x => x.Id);
                    table.UniqueConstraint("AK_RlxEnums_AutoIncrementId", x => x.AutoIncrementId);
                });

            migrationBuilder.CreateTable(
                name: "StaffCompetencyDefinitions",
                columns: table => new
                {
                    CompetencySystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    RatingScaleJson = table.Column<string>(type: "character varying(4000)", maxLength: 4000, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StaffCompetencyDefinitions", x => x.CompetencySystemId);
                });

            migrationBuilder.CreateTable(
                name: "StaffCompetencyEvaluations",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicianUniveristyUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    EvaluatingManagerUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    EvaluationContextId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    OverallComments = table.Column<string>(type: "character varying(4000)", maxLength: 4000, nullable: true),
                    DepartmentId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    SubmittedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StaffCompetencyEvaluations", x => x.Id);
                    table.UniqueConstraint("AK_StaffCompetencyEvaluations_AutoIncrementId", x => x.AutoIncrementId);
                });

            migrationBuilder.CreateTable(
                name: "StaticCriterionCoefficients",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    StaticCriterionSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CriterionName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Coefficient = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    MaximumLimit = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    MinimumLimit = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Category = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DataSource = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    LastUpdated = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    CoefficientHistory = table.Column<string>(type: "text", nullable: true),
                    IsApproved = table.Column<bool>(type: "boolean", nullable: false),
                    ApprovedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ApprovedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EffectiveTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StaticCriterionCoefficients", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "StaticCriterionDefinitions",
                columns: table => new
                {
                    StaticCriterionSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DataType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    DataSourceHint = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CalculationLogic = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StaticCriterionDefinitions", x => x.StaticCriterionSystemId);
                    table.CheckConstraint("CK_StaticCriterion_DataType", "\"DataType\" IN ('Integer', 'Decimal', 'Boolean', 'String', 'Date')");
                });

            migrationBuilder.CreateTable(
                name: "TrendDataPoints",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    TrendType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    EntityType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    EntityId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    EntityName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    DataDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PeriodStart = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PeriodEnd = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PeriodType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "Monthly"),
                    Value = table.Column<double>(type: "double precision", precision: 18, scale: 6, nullable: false),
                    MovingAverage = table.Column<double>(type: "double precision", precision: 18, scale: 6, nullable: true),
                    TrendDirection = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "Stable"),
                    TrendStrength = table.Column<double>(type: "double precision", precision: 8, scale: 4, nullable: false),
                    ChangeRate = table.Column<double>(type: "double precision", precision: 8, scale: 4, nullable: true),
                    ChangeAmount = table.Column<double>(type: "double precision", precision: 18, scale: 6, nullable: true),
                    PreviousValue = table.Column<double>(type: "double precision", precision: 18, scale: 6, nullable: true),
                    DataQualityScore = table.Column<double>(type: "double precision", precision: 5, scale: 2, nullable: false),
                    ConfidenceLowerBound = table.Column<double>(type: "double precision", precision: 18, scale: 6, nullable: true),
                    ConfidenceUpperBound = table.Column<double>(type: "double precision", precision: 18, scale: 6, nullable: true),
                    ConfidenceLevel = table.Column<double>(type: "double precision", precision: 4, scale: 3, nullable: false),
                    IsAnomaly = table.Column<bool>(type: "boolean", nullable: false),
                    AnomalyScore = table.Column<double>(type: "double precision", precision: 5, scale: 2, nullable: true),
                    AnomalyType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    SeasonalityFactor = table.Column<double>(type: "double precision", precision: 8, scale: 4, nullable: true),
                    DataSource = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Calculated"),
                    CalculationMethod = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "Active"),
                    AdditionalMetrics = table.Column<string>(type: "text", nullable: true),
                    Metadata = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TrendDataPoints", x => x.Id);
                    table.CheckConstraint("CK_TrendDataPoint_DataSource", "\"DataSource\" IN ('Calculated', 'Manual', 'Imported', 'Predicted')");
                    table.CheckConstraint("CK_TrendDataPoint_EntityType", "\"EntityType\" IN ('Academician', 'Department', 'Faculty', 'Form', 'Category')");
                    table.CheckConstraint("CK_TrendDataPoint_PeriodType", "\"PeriodType\" IN ('Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly')");
                    table.CheckConstraint("CK_TrendDataPoint_Status", "\"Status\" IN ('Active', 'Archived', 'Draft', 'Validated')");
                    table.CheckConstraint("CK_TrendDataPoint_TrendDirection", "\"TrendDirection\" IN ('Increasing', 'Decreasing', 'Stable')");
                    table.CheckConstraint("CK_TrendDataPoint_TrendType", "\"TrendType\" IN ('Performance', 'Completion', 'Quality', 'Engagement')");
                });

            migrationBuilder.CreateTable(
                name: "CoursePortfolioVerifications",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicianTc = table.Column<string>(type: "character varying(11)", maxLength: 11, nullable: false),
                    CourseCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PeriodName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CourseName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    ExamPapersStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    AnswerKeyStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    ExamRecordStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    AttendanceSheetStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    CourseSyllabusStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    WeeklyAttendanceStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    MakeupExamGradesStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    UzemRecordsStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    VerificationNotes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    LastVerifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastVerifiedByArchivistId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    EbysReferenceId = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AcademicianProfileId = table.Column<string>(type: "text", nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CoursePortfolioVerifications", x => x.Id);
                    table.CheckConstraint("CK_CoursePortfolioVerification_AnswerKeyStatus", "\"AnswerKeyStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_AttendanceSheetStatus", "\"AttendanceSheetStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_CourseSyllabusStatus", "\"CourseSyllabusStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_ExamPapersStatus", "\"ExamPapersStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_ExamRecordStatus", "\"ExamRecordStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_MakeupExamGradesStatus", "\"MakeupExamGradesStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_UzemRecordsStatus", "\"UzemRecordsStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_WeeklyAttendanceStatus", "\"WeeklyAttendanceStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.ForeignKey(
                        name: "FK_CoursePortfolioVerifications_AcademicianProfiles_Academicia~",
                        column: x => x.AcademicianProfileId,
                        principalTable: "AcademicianProfiles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "UserApdysRoleMappings",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    UniversityUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    ApdysRoleAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    AssignedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    AssignedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    RevokedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RevokedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserApdysRoleMappings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserApdysRoleMappings_ApdysRoles_ApdysRoleAutoIncrementId",
                        column: x => x.ApdysRoleAutoIncrementId,
                        principalTable: "ApdysRoles",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DepartmentStrategicPerformanceData",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    DepartmentId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    AssessmentPeriodId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    IndicatorSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ActualValue = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    TargetValue = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Notes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    SubmittedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    SubmittedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DepartmentStrategicPerformanceData", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DepartmentStrategicPerformanceData_DepartmentStrategicIndic~",
                        column: x => x.IndicatorSystemId,
                        principalTable: "DepartmentStrategicIndicatorDefinitions",
                        principalColumn: "IndicatorSystemId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AcademicSubmissions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicianUniveristyUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    EvaluationFormAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    SubmittedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApprovedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApprovedByControllerUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    RejectedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RejectedByControllerUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ApprovalComments = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    RejectionComments = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AcademicSubmissions", x => x.Id);
                    table.UniqueConstraint("AK_AcademicSubmissions_AutoIncrementId", x => x.AutoIncrementId);
                    table.CheckConstraint("CK_AcademicSubmission_Status", "\"Status\" IN ('Draft', 'Submitted', 'UnderReview', 'Approved', 'Rejected', 'RequiresRevision')");
                    table.ForeignKey(
                        name: "FK_AcademicSubmissions_AcademicianProfiles_AcademicianUniveris~",
                        column: x => x.AcademicianUniveristyUserId,
                        principalTable: "AcademicianProfiles",
                        principalColumn: "UniversityUserId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AcademicSubmissions_EvaluationForms_EvaluationFormAutoIncre~",
                        column: x => x.EvaluationFormAutoIncrementId,
                        principalTable: "EvaluationForms",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "FormCategories",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    EvaluationFormAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false),
                    Weight = table.Column<double>(type: "double precision", precision: 5, scale: 4, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FormCategories", x => x.Id);
                    table.UniqueConstraint("AK_FormCategories_AutoIncrementId", x => x.AutoIncrementId);
                    table.ForeignKey(
                        name: "FK_FormCategories_EvaluationForms_EvaluationFormAutoIncrementId",
                        column: x => x.EvaluationFormAutoIncrementId,
                        principalTable: "EvaluationForms",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "GenericDataEntryRecords",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    DefinitionId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    RecordData = table.Column<string>(type: "text", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GenericDataEntryRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GenericDataEntryRecords_GenericDataEntryDefinitions_Definit~",
                        column: x => x.DefinitionId,
                        principalTable: "GenericDataEntryDefinitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "PortfolioVerificationLogs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicianUniveristyUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    ItemSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    VerificationStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    VerificationNotes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    LastVerifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastVerifiedByArchivistUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    EbysReferenceId = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PortfolioVerificationLogs", x => x.Id);
                    table.CheckConstraint("CK_PortfolioVerification_Status", "\"VerificationStatus\" IN ('VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.ForeignKey(
                        name: "FK_PortfolioVerificationLogs_PortfolioChecklistItemDefinitions~",
                        column: x => x.ItemSystemId,
                        principalTable: "PortfolioChecklistItemDefinitions",
                        principalColumn: "ItemSystemId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RlxEnumValues",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EnumId = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Code = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    WhichRow = table.Column<int>(type: "integer", nullable: true),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RlxEnumValues", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RlxEnumValues_RlxEnums_EnumId",
                        column: x => x.EnumId,
                        principalTable: "RlxEnums",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CompetencyRatings",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    StaffCompetencyEvaluationAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    CompetencySystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Rating = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Comments = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompetencyRatings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompetencyRatings_StaffCompetencyDefinitions_CompetencySyst~",
                        column: x => x.CompetencySystemId,
                        principalTable: "StaffCompetencyDefinitions",
                        principalColumn: "CompetencySystemId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CompetencyRatings_StaffCompetencyEvaluations_StaffCompetenc~",
                        column: x => x.StaffCompetencyEvaluationAutoIncrementId,
                        principalTable: "StaffCompetencyEvaluations",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EvidenceFiles",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicSubmissionAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    FormCriterionLinkId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    SubmittedDynamicDataInstanceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    FileName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    StoredFilePath = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    SizeBytes = table.Column<long>(type: "bigint", nullable: false),
                    ContentType = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    UploadedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UploadedByUniveristyUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    MinioBucketName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    MinioObjectName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    MinioETag = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    StorageType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Local"),
                    MinioPresignedUrlExpiry = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AccessUrl = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    FileChecksum = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    OriginalFileName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EvidenceFiles", x => x.Id);
                    table.CheckConstraint("CK_EvidenceFile_Size", "SizeBytes > 0");
                    table.CheckConstraint("CK_EvidenceFile_StorageType", "StorageType IN ('Local', 'MinIO')");
                    table.ForeignKey(
                        name: "FK_EvidenceFiles_AcademicSubmissions_AcademicSubmissionAutoInc~",
                        column: x => x.AcademicSubmissionAutoIncrementId,
                        principalTable: "AcademicSubmissions",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FeedbackEntries",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    SubmissionId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    FeedbackType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Comments = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Active"),
                    Priority = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "Medium"),
                    Category = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Rating = table.Column<int>(type: "integer", nullable: true),
                    ResponseTimeHours = table.Column<double>(type: "double precision", nullable: true),
                    ResolvedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ResolutionNote = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    FormId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CriterionId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    AcademicianUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    DepartmentId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Metadata = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FeedbackEntries", x => x.Id);
                    table.CheckConstraint("CK_FeedbackEntry_FeedbackType", "\"FeedbackType\" IN ('Approval', 'Rejection', 'RevisionRequest', 'Comment')");
                    table.CheckConstraint("CK_FeedbackEntry_Priority", "\"Priority\" IN ('Low', 'Medium', 'High', 'Critical')");
                    table.CheckConstraint("CK_FeedbackEntry_Rating", "\"Rating\" IS NULL OR (\"Rating\" >= 1 AND \"Rating\" <= 10)");
                    table.CheckConstraint("CK_FeedbackEntry_Status", "\"Status\" IN ('Active', 'Resolved', 'Superseded')");
                    table.ForeignKey(
                        name: "FK_FeedbackEntries_AcademicSubmissions_SubmissionId",
                        column: x => x.SubmissionId,
                        principalTable: "AcademicSubmissions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_FeedbackEntries_EvaluationForms_FormId",
                        column: x => x.FormId,
                        principalTable: "EvaluationForms",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "SubmissionAudits",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicSubmissionAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    Action = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PerformedByUserId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    PerformedByUserRole = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PerformedByUserName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Comments = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    PerformedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    OldValue = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    NewValue = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    EntityType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    EntityId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Category = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    UserAgent = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Metadata = table.Column<string>(type: "text", nullable: true),
                    IsSuccessful = table.Column<bool>(type: "boolean", nullable: false),
                    ErrorMessage = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DurationMs = table.Column<long>(type: "bigint", nullable: true),
                    SessionId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    RequestId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubmissionAudits", x => x.Id);
                    table.CheckConstraint("CK_SubmissionAudit_DurationMs", "DurationMs >= 0");
                    table.ForeignKey(
                        name: "FK_SubmissionAudits_AcademicSubmissions_AcademicSubmissionAuto~",
                        column: x => x.AcademicSubmissionAutoIncrementId,
                        principalTable: "AcademicSubmissions",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SubmissionFeedbacks",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    SubmissionId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    FeedbackType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ControllerUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Message = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    Comments = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    SubmissionStatusAtTime = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    OverallRating = table.Column<int>(type: "integer", nullable: true),
                    Highlights = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    FutureSuggestions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    RevisionDeadline = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Priority = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "Medium"),
                    AcademicianResponse = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    AcademicianResponseDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    NotificationSent = table.Column<bool>(type: "boolean", nullable: false),
                    NotificationSentAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubmissionFeedbacks", x => x.Id);
                    table.CheckConstraint("CK_SubmissionFeedback_FeedbackType", "\"FeedbackType\" IN ('Approval', 'Rejection', 'RevisionRequest')");
                    table.CheckConstraint("CK_SubmissionFeedback_Priority", "\"Priority\" IN ('Low', 'Medium', 'High')");
                    table.ForeignKey(
                        name: "FK_SubmissionFeedbacks_AcademicSubmissions_SubmissionId",
                        column: x => x.SubmissionId,
                        principalTable: "AcademicSubmissions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FormCriterionLinks",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    FormCategoryAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    StaticCriterionSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    DynamicCriterionTemplateId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CriterionType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false),
                    WeightPercentage = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    IsRequired = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FormCriterionLinks", x => x.Id);
                    table.CheckConstraint("CK_FormCriterionLink_CriterionType", "\"CriterionType\" IN ('Static', 'Dynamic')");
                    table.ForeignKey(
                        name: "FK_FormCriterionLinks_FormCategories_FormCategoryAutoIncrement~",
                        column: x => x.FormCategoryAutoIncrementId,
                        principalTable: "FormCategories",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_FormCriterionLinks_StaticCriterionDefinitions_StaticCriteri~",
                        column: x => x.StaticCriterionSystemId,
                        principalTable: "StaticCriterionDefinitions",
                        principalColumn: "StaticCriterionSystemId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "FeedbackNotificationLogs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    SubmissionFeedbackId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    NotificationType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    RecipientUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Subject = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Content = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "Pending"),
                    SentAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ErrorMessage = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    RetryCount = table.Column<int>(type: "integer", nullable: false),
                    LastRetryAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FeedbackNotificationLogs", x => x.Id);
                    table.CheckConstraint("CK_FeedbackNotificationLog_NotificationType", "\"NotificationType\" IN ('Email', 'SMS', 'InApp')");
                    table.CheckConstraint("CK_FeedbackNotificationLog_RetryCount", "\"RetryCount\" >= 0");
                    table.CheckConstraint("CK_FeedbackNotificationLog_Status", "\"Status\" IN ('Pending', 'Sent', 'Failed')");
                    table.ForeignKey(
                        name: "FK_FeedbackNotificationLogs_SubmissionFeedbacks_SubmissionFeed~",
                        column: x => x.SubmissionFeedbackId,
                        principalTable: "SubmissionFeedbacks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CriterionFeedbacks",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    SubmissionFeedbackId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CriterionLinkId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CriterionId = table.Column<string>(type: "text", nullable: false),
                    FeedbackMessage = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Comments = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    RequiresRevision = table.Column<bool>(type: "boolean", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Rating = table.Column<int>(type: "integer", nullable: true),
                    RevisionType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    IsRequired = table.Column<bool>(type: "boolean", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CriterionFeedbacks", x => x.Id);
                    table.CheckConstraint("CK_CriterionFeedback_Rating", "\"Rating\" IS NULL OR (\"Rating\" >= 1 AND \"Rating\" <= 10)");
                    table.CheckConstraint("CK_CriterionFeedback_RevisionType", "\"RevisionType\" IS NULL OR \"RevisionType\" IN ('DataCorrection', 'EvidenceUpdate', 'AdditionalInfo')");
                    table.CheckConstraint("CK_CriterionFeedback_Status", "\"Status\" IN ('Approved', 'NeedsRevision', 'Rejected', 'Excellent', 'Good', 'Satisfactory', 'NeedsImprovement')");
                    table.ForeignKey(
                        name: "FK_CriterionFeedbacks_FormCriterionLinks_CriterionLinkId",
                        column: x => x.CriterionLinkId,
                        principalTable: "FormCriterionLinks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CriterionFeedbacks_SubmissionFeedbacks_SubmissionFeedbackId",
                        column: x => x.SubmissionFeedbackId,
                        principalTable: "SubmissionFeedbacks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_AcademicCadre",
                table: "AcademicianProfiles",
                column: "AcademicCadre");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_AutoIncrementId",
                table: "AcademicianProfiles",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_Department",
                table: "AcademicianProfiles",
                column: "Department");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_DepartmentId",
                table: "AcademicianProfiles",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_DepartmentId_AcademicCadre",
                table: "AcademicianProfiles",
                columns: new[] { "DepartmentId", "AcademicCadre" });

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_FacultyId",
                table: "AcademicianProfiles",
                column: "FacultyId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_FacultyId_DepartmentId",
                table: "AcademicianProfiles",
                columns: new[] { "FacultyId", "DepartmentId" });

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_IsActive",
                table: "AcademicianProfiles",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_IsActive_AcademicCadre",
                table: "AcademicianProfiles",
                columns: new[] { "IsActive", "AcademicCadre" });

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_LastSyncedAt",
                table: "AcademicianProfiles",
                column: "LastSyncedAt");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_UniversityUserId",
                table: "AcademicianProfiles",
                column: "UniversityUserId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_AcademicianUniveristyUserId",
                table: "AcademicSubmissions",
                column: "AcademicianUniveristyUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_AcademicianUniveristyUserId_EvaluationF~",
                table: "AcademicSubmissions",
                columns: new[] { "AcademicianUniveristyUserId", "EvaluationFormAutoIncrementId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_AcademicianUniveristyUserId_Status",
                table: "AcademicSubmissions",
                columns: new[] { "AcademicianUniveristyUserId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_ApprovedByControllerUserId",
                table: "AcademicSubmissions",
                column: "ApprovedByControllerUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_AutoIncrementId",
                table: "AcademicSubmissions",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_CreatedAt",
                table: "AcademicSubmissions",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_CreatedAt_Status",
                table: "AcademicSubmissions",
                columns: new[] { "CreatedAt", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_EvaluationFormAutoIncrementId",
                table: "AcademicSubmissions",
                column: "EvaluationFormAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_EvaluationFormAutoIncrementId_Status",
                table: "AcademicSubmissions",
                columns: new[] { "EvaluationFormAutoIncrementId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_RejectedByControllerUserId",
                table: "AcademicSubmissions",
                column: "RejectedByControllerUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_Status",
                table: "AcademicSubmissions",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_Status_SubmittedAt",
                table: "AcademicSubmissions",
                columns: new[] { "Status", "SubmittedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_SubmittedAt",
                table: "AcademicSubmissions",
                column: "SubmittedAt");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_UpdatedAt",
                table: "AcademicSubmissions",
                column: "UpdatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ApdysRoles_AutoIncrementId",
                table: "ApdysRoles",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ApdysRoles_RoleName",
                table: "ApdysRoles",
                column: "RoleName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CompetencyRatings_AutoIncrementId",
                table: "CompetencyRatings",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CompetencyRatings_CompetencySystemId",
                table: "CompetencyRatings",
                column: "CompetencySystemId");

            migrationBuilder.CreateIndex(
                name: "IX_CompetencyRatings_StaffCompetencyEvaluationAutoIncrementId",
                table: "CompetencyRatings",
                column: "StaffCompetencyEvaluationAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_CompetencyRatings_StaffCompetencyEvaluationAutoIncrementId_~",
                table: "CompetencyRatings",
                columns: new[] { "StaffCompetencyEvaluationAutoIncrementId", "CompetencySystemId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_AcademicianProfileId",
                table: "CoursePortfolioVerifications",
                column: "AcademicianProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_AcademicianTc",
                table: "CoursePortfolioVerifications",
                column: "AcademicianTc");

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_AcademicianTc_CourseCode_Perio~",
                table: "CoursePortfolioVerifications",
                columns: new[] { "AcademicianTc", "CourseCode", "PeriodName" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_AutoIncrementId",
                table: "CoursePortfolioVerifications",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_CourseCode",
                table: "CoursePortfolioVerifications",
                column: "CourseCode");

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_LastVerifiedAt",
                table: "CoursePortfolioVerifications",
                column: "LastVerifiedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_LastVerifiedByArchivistId",
                table: "CoursePortfolioVerifications",
                column: "LastVerifiedByArchivistId");

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_PeriodName",
                table: "CoursePortfolioVerifications",
                column: "PeriodName");

            migrationBuilder.CreateIndex(
                name: "IX_CriterionFeedbacks_AutoIncrementId",
                table: "CriterionFeedbacks",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CriterionFeedbacks_CreatedAt",
                table: "CriterionFeedbacks",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CriterionFeedbacks_CreatedAt_Status",
                table: "CriterionFeedbacks",
                columns: new[] { "CreatedAt", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_CriterionFeedbacks_CriterionLinkId",
                table: "CriterionFeedbacks",
                column: "CriterionLinkId");

            migrationBuilder.CreateIndex(
                name: "IX_CriterionFeedbacks_CriterionLinkId_Status",
                table: "CriterionFeedbacks",
                columns: new[] { "CriterionLinkId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_CriterionFeedbacks_IsActive",
                table: "CriterionFeedbacks",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_CriterionFeedbacks_IsRequired",
                table: "CriterionFeedbacks",
                column: "IsRequired");

            migrationBuilder.CreateIndex(
                name: "IX_CriterionFeedbacks_Status",
                table: "CriterionFeedbacks",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_CriterionFeedbacks_Status_IsRequired",
                table: "CriterionFeedbacks",
                columns: new[] { "Status", "IsRequired" });

            migrationBuilder.CreateIndex(
                name: "IX_CriterionFeedbacks_SubmissionFeedbackId",
                table: "CriterionFeedbacks",
                column: "SubmissionFeedbackId");

            migrationBuilder.CreateIndex(
                name: "IX_CriterionFeedbacks_SubmissionFeedbackId_CriterionLinkId",
                table: "CriterionFeedbacks",
                columns: new[] { "SubmissionFeedbackId", "CriterionLinkId" });

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicIndicatorDefinitions_IndicatorSystemId",
                table: "DepartmentStrategicIndicatorDefinitions",
                column: "IndicatorSystemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicIndicatorDefinitions_IsActive",
                table: "DepartmentStrategicIndicatorDefinitions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_AssessmentPeriodId",
                table: "DepartmentStrategicPerformanceData",
                column: "AssessmentPeriodId");

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_AutoIncrementId",
                table: "DepartmentStrategicPerformanceData",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_DepartmentId",
                table: "DepartmentStrategicPerformanceData",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_DepartmentId_AssessmentP~",
                table: "DepartmentStrategicPerformanceData",
                columns: new[] { "DepartmentId", "AssessmentPeriodId", "IndicatorSystemId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_IndicatorSystemId",
                table: "DepartmentStrategicPerformanceData",
                column: "IndicatorSystemId");

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_SubmittedAt",
                table: "DepartmentStrategicPerformanceData",
                column: "SubmittedAt");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_AutoIncrementId",
                table: "EvaluationForms",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_CreatedAt",
                table: "EvaluationForms",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_EvaluationPeriodEndDate",
                table: "EvaluationForms",
                column: "EvaluationPeriodEndDate");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_EvaluationPeriodStartDate",
                table: "EvaluationForms",
                column: "EvaluationPeriodStartDate");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_EvaluationPeriodStartDate_EvaluationPeriodE~",
                table: "EvaluationForms",
                columns: new[] { "EvaluationPeriodStartDate", "EvaluationPeriodEndDate" });

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_Status",
                table: "EvaluationForms",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_Status_EvaluationPeriodStartDate",
                table: "EvaluationForms",
                columns: new[] { "Status", "EvaluationPeriodStartDate" });

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_Status_SubmissionDeadline",
                table: "EvaluationForms",
                columns: new[] { "Status", "SubmissionDeadline" });

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_SubmissionDeadline",
                table: "EvaluationForms",
                column: "SubmissionDeadline");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_AcademicSubmissionAutoIncrementId",
                table: "EvidenceFiles",
                column: "AcademicSubmissionAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_AcademicSubmissionAutoIncrementId_UploadedAt",
                table: "EvidenceFiles",
                columns: new[] { "AcademicSubmissionAutoIncrementId", "UploadedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_AutoIncrementId",
                table: "EvidenceFiles",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_ContentType",
                table: "EvidenceFiles",
                column: "ContentType");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_MinioBucketName",
                table: "EvidenceFiles",
                column: "MinioBucketName");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_MinioObjectName",
                table: "EvidenceFiles",
                column: "MinioObjectName");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_StorageType",
                table: "EvidenceFiles",
                column: "StorageType");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_StorageType_UploadedAt",
                table: "EvidenceFiles",
                columns: new[] { "StorageType", "UploadedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_UploadedAt",
                table: "EvidenceFiles",
                column: "UploadedAt");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_UploadedByUniveristyUserId",
                table: "EvidenceFiles",
                column: "UploadedByUniveristyUserId");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_UploadedByUniveristyUserId_UploadedAt",
                table: "EvidenceFiles",
                columns: new[] { "UploadedByUniveristyUserId", "UploadedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_AcademicianUserId",
                table: "FeedbackEntries",
                column: "AcademicianUserId");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_AcademicianUserId_CreatedAt",
                table: "FeedbackEntries",
                columns: new[] { "AcademicianUserId", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_AcademicianUserId_FeedbackType_Status",
                table: "FeedbackEntries",
                columns: new[] { "AcademicianUserId", "FeedbackType", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_AutoIncrementId",
                table: "FeedbackEntries",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_CreatedAt",
                table: "FeedbackEntries",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_CreatedAt_Status",
                table: "FeedbackEntries",
                columns: new[] { "CreatedAt", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_CreatedBy",
                table: "FeedbackEntries",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_DepartmentId",
                table: "FeedbackEntries",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_DepartmentId_CreatedAt",
                table: "FeedbackEntries",
                columns: new[] { "DepartmentId", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_FeedbackType",
                table: "FeedbackEntries",
                column: "FeedbackType");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_FeedbackType_Status",
                table: "FeedbackEntries",
                columns: new[] { "FeedbackType", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_FormId",
                table: "FeedbackEntries",
                column: "FormId");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_Status",
                table: "FeedbackEntries",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_Status_Priority",
                table: "FeedbackEntries",
                columns: new[] { "Status", "Priority" });

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_SubmissionId",
                table: "FeedbackEntries",
                column: "SubmissionId");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackEntries_SubmissionId_FeedbackType",
                table: "FeedbackEntries",
                columns: new[] { "SubmissionId", "FeedbackType" });

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackNotificationLogs_AutoIncrementId",
                table: "FeedbackNotificationLogs",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackNotificationLogs_CreatedAt",
                table: "FeedbackNotificationLogs",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackNotificationLogs_NotificationType",
                table: "FeedbackNotificationLogs",
                column: "NotificationType");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackNotificationLogs_NotificationType_Status",
                table: "FeedbackNotificationLogs",
                columns: new[] { "NotificationType", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackNotificationLogs_RecipientUserId",
                table: "FeedbackNotificationLogs",
                column: "RecipientUserId");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackNotificationLogs_RecipientUserId_CreatedAt",
                table: "FeedbackNotificationLogs",
                columns: new[] { "RecipientUserId", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackNotificationLogs_RetryCount",
                table: "FeedbackNotificationLogs",
                column: "RetryCount");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackNotificationLogs_SentAt",
                table: "FeedbackNotificationLogs",
                column: "SentAt");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackNotificationLogs_Status",
                table: "FeedbackNotificationLogs",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackNotificationLogs_Status_RetryCount",
                table: "FeedbackNotificationLogs",
                columns: new[] { "Status", "RetryCount" });

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackNotificationLogs_SubmissionFeedbackId",
                table: "FeedbackNotificationLogs",
                column: "SubmissionFeedbackId");

            migrationBuilder.CreateIndex(
                name: "IX_FeedbackNotificationLogs_SubmissionFeedbackId_NotificationT~",
                table: "FeedbackNotificationLogs",
                columns: new[] { "SubmissionFeedbackId", "NotificationType" });

            migrationBuilder.CreateIndex(
                name: "IX_FormCategories_AutoIncrementId",
                table: "FormCategories",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FormCategories_CreatedAt",
                table: "FormCategories",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_FormCategories_EvaluationFormAutoIncrementId",
                table: "FormCategories",
                column: "EvaluationFormAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_FormCategories_EvaluationFormAutoIncrementId_DisplayOrder",
                table: "FormCategories",
                columns: new[] { "EvaluationFormAutoIncrementId", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_FormCategories_EvaluationFormAutoIncrementId_Weight",
                table: "FormCategories",
                columns: new[] { "EvaluationFormAutoIncrementId", "Weight" });

            migrationBuilder.CreateIndex(
                name: "IX_FormCategories_UpdatedAt",
                table: "FormCategories",
                column: "UpdatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_FormCriterionLinks_AutoIncrementId",
                table: "FormCriterionLinks",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FormCriterionLinks_FormCategoryAutoIncrementId",
                table: "FormCriterionLinks",
                column: "FormCategoryAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_FormCriterionLinks_FormCategoryAutoIncrementId_DisplayOrder",
                table: "FormCriterionLinks",
                columns: new[] { "FormCategoryAutoIncrementId", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_FormCriterionLinks_StaticCriterionSystemId",
                table: "FormCriterionLinks",
                column: "StaticCriterionSystemId");

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryDefinitions_Category",
                table: "GenericDataEntryDefinitions",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryDefinitions_IsActive",
                table: "GenericDataEntryDefinitions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryDefinitions_Name",
                table: "GenericDataEntryDefinitions",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryRecords_AutoIncrementId",
                table: "GenericDataEntryRecords",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryRecords_CreatedAt",
                table: "GenericDataEntryRecords",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryRecords_DefinitionId",
                table: "GenericDataEntryRecords",
                column: "DefinitionId");

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryRecords_Status",
                table: "GenericDataEntryRecords",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceDistributions_AutoIncrementId",
                table: "PerformanceDistributions",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceDistributions_CalculatedAt",
                table: "PerformanceDistributions",
                column: "CalculatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceDistributions_DistributionType",
                table: "PerformanceDistributions",
                column: "DistributionType");

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceDistributions_DistributionType_CalculatedAt",
                table: "PerformanceDistributions",
                columns: new[] { "DistributionType", "CalculatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceDistributions_DistributionType_EntityId",
                table: "PerformanceDistributions",
                columns: new[] { "DistributionType", "EntityId" });

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceDistributions_DistributionType_Status_Calculated~",
                table: "PerformanceDistributions",
                columns: new[] { "DistributionType", "Status", "CalculatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceDistributions_EntityId",
                table: "PerformanceDistributions",
                column: "EntityId");

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceDistributions_EntityId_PeriodStart_PeriodEnd",
                table: "PerformanceDistributions",
                columns: new[] { "EntityId", "PeriodStart", "PeriodEnd" });

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceDistributions_Status",
                table: "PerformanceDistributions",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceDistributions_Status_CalculatedAt",
                table: "PerformanceDistributions",
                columns: new[] { "Status", "CalculatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioChecklistItemDefinitions_Category",
                table: "PortfolioChecklistItemDefinitions",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioChecklistItemDefinitions_IsActive",
                table: "PortfolioChecklistItemDefinitions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioChecklistItemDefinitions_ItemSystemId",
                table: "PortfolioChecklistItemDefinitions",
                column: "ItemSystemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_AcademicianUniveristyUserId",
                table: "PortfolioVerificationLogs",
                column: "AcademicianUniveristyUserId");

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_AcademicianUniveristyUserId_ItemS~",
                table: "PortfolioVerificationLogs",
                columns: new[] { "AcademicianUniveristyUserId", "ItemSystemId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_AutoIncrementId",
                table: "PortfolioVerificationLogs",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_ItemSystemId",
                table: "PortfolioVerificationLogs",
                column: "ItemSystemId");

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_LastVerifiedByArchivistUserId",
                table: "PortfolioVerificationLogs",
                column: "LastVerifiedByArchivistUserId");

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_VerificationStatus",
                table: "PortfolioVerificationLogs",
                column: "VerificationStatus");

            migrationBuilder.CreateIndex(
                name: "IX_RlxEnums_AutoIncrementId",
                table: "RlxEnums",
                column: "AutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_RlxEnumValues_AutoIncrementId",
                table: "RlxEnumValues",
                column: "AutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_RlxEnumValues_EnumId",
                table: "RlxEnumValues",
                column: "EnumId");

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyDefinitions_CompetencySystemId",
                table: "StaffCompetencyDefinitions",
                column: "CompetencySystemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyDefinitions_IsActive",
                table: "StaffCompetencyDefinitions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_AcademicianUniveristyUserId",
                table: "StaffCompetencyEvaluations",
                column: "AcademicianUniveristyUserId");

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_AcademicianUniveristyUserId_Eval~",
                table: "StaffCompetencyEvaluations",
                columns: new[] { "AcademicianUniveristyUserId", "EvaluationContextId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_AutoIncrementId",
                table: "StaffCompetencyEvaluations",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_EvaluationContextId",
                table: "StaffCompetencyEvaluations",
                column: "EvaluationContextId");

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_SubmittedAt",
                table: "StaffCompetencyEvaluations",
                column: "SubmittedAt");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_Category",
                table: "StaticCriterionCoefficients",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_DisplayOrder",
                table: "StaticCriterionCoefficients",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_EffectiveFrom",
                table: "StaticCriterionCoefficients",
                column: "EffectiveFrom");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_EffectiveTo",
                table: "StaticCriterionCoefficients",
                column: "EffectiveTo");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_IsActive",
                table: "StaticCriterionCoefficients",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_IsApproved",
                table: "StaticCriterionCoefficients",
                column: "IsApproved");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_StaticCriterionSystemId",
                table: "StaticCriterionCoefficients",
                column: "StaticCriterionSystemId");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionDefinitions_IsActive",
                table: "StaticCriterionDefinitions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionDefinitions_StaticCriterionSystemId",
                table: "StaticCriterionDefinitions",
                column: "StaticCriterionSystemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_AcademicSubmissionAutoIncrementId",
                table: "SubmissionAudits",
                column: "AcademicSubmissionAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_AcademicSubmissionAutoIncrementId_Performe~",
                table: "SubmissionAudits",
                columns: new[] { "AcademicSubmissionAutoIncrementId", "PerformedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_Action",
                table: "SubmissionAudits",
                column: "Action");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_Action_PerformedAt",
                table: "SubmissionAudits",
                columns: new[] { "Action", "PerformedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_AutoIncrementId",
                table: "SubmissionAudits",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_Category",
                table: "SubmissionAudits",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_Category_PerformedAt",
                table: "SubmissionAudits",
                columns: new[] { "Category", "PerformedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_EntityType",
                table: "SubmissionAudits",
                column: "EntityType");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_EntityType_EntityId_PerformedAt",
                table: "SubmissionAudits",
                columns: new[] { "EntityType", "EntityId", "PerformedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_IsSuccessful",
                table: "SubmissionAudits",
                column: "IsSuccessful");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_IsSuccessful_PerformedAt",
                table: "SubmissionAudits",
                columns: new[] { "IsSuccessful", "PerformedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_PerformedAt",
                table: "SubmissionAudits",
                column: "PerformedAt");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_PerformedByUserId",
                table: "SubmissionAudits",
                column: "PerformedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_PerformedByUserId_PerformedAt",
                table: "SubmissionAudits",
                columns: new[] { "PerformedByUserId", "PerformedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionFeedbacks_AutoIncrementId",
                table: "SubmissionFeedbacks",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionFeedbacks_ControllerUserId",
                table: "SubmissionFeedbacks",
                column: "ControllerUserId");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionFeedbacks_ControllerUserId_CreatedAt",
                table: "SubmissionFeedbacks",
                columns: new[] { "ControllerUserId", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionFeedbacks_CreatedAt",
                table: "SubmissionFeedbacks",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionFeedbacks_CreatedAt_IsActive",
                table: "SubmissionFeedbacks",
                columns: new[] { "CreatedAt", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionFeedbacks_FeedbackType",
                table: "SubmissionFeedbacks",
                column: "FeedbackType");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionFeedbacks_FeedbackType_IsActive",
                table: "SubmissionFeedbacks",
                columns: new[] { "FeedbackType", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionFeedbacks_IsActive",
                table: "SubmissionFeedbacks",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionFeedbacks_NotificationSent",
                table: "SubmissionFeedbacks",
                column: "NotificationSent");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionFeedbacks_SubmissionId",
                table: "SubmissionFeedbacks",
                column: "SubmissionId");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionFeedbacks_SubmissionId_FeedbackType",
                table: "SubmissionFeedbacks",
                columns: new[] { "SubmissionId", "FeedbackType" });

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_AutoIncrementId",
                table: "TrendDataPoints",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_DataDate",
                table: "TrendDataPoints",
                column: "DataDate");

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_EntityId",
                table: "TrendDataPoints",
                column: "EntityId");

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_EntityId_DataDate",
                table: "TrendDataPoints",
                columns: new[] { "EntityId", "DataDate" });

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_EntityId_PeriodStart_PeriodEnd",
                table: "TrendDataPoints",
                columns: new[] { "EntityId", "PeriodStart", "PeriodEnd" });

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_EntityType",
                table: "TrendDataPoints",
                column: "EntityType");

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_EntityType_DataDate",
                table: "TrendDataPoints",
                columns: new[] { "EntityType", "DataDate" });

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_IsAnomaly",
                table: "TrendDataPoints",
                column: "IsAnomaly");

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_IsAnomaly_DataDate",
                table: "TrendDataPoints",
                columns: new[] { "IsAnomaly", "DataDate" });

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_PeriodType_DataDate",
                table: "TrendDataPoints",
                columns: new[] { "PeriodType", "DataDate" });

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_Status",
                table: "TrendDataPoints",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_Status_DataDate",
                table: "TrendDataPoints",
                columns: new[] { "Status", "DataDate" });

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_TrendDirection_DataDate",
                table: "TrendDataPoints",
                columns: new[] { "TrendDirection", "DataDate" });

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_TrendType",
                table: "TrendDataPoints",
                column: "TrendType");

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_TrendType_DataDate",
                table: "TrendDataPoints",
                columns: new[] { "TrendType", "DataDate" });

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_TrendType_EntityType_DataDate",
                table: "TrendDataPoints",
                columns: new[] { "TrendType", "EntityType", "DataDate" });

            migrationBuilder.CreateIndex(
                name: "IX_TrendDataPoints_TrendType_EntityType_EntityId",
                table: "TrendDataPoints",
                columns: new[] { "TrendType", "EntityType", "EntityId" });

            migrationBuilder.CreateIndex(
                name: "IX_UserApdysRoleMappings_ApdysRoleAutoIncrementId",
                table: "UserApdysRoleMappings",
                column: "ApdysRoleAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_UserApdysRoleMappings_AssignedAt",
                table: "UserApdysRoleMappings",
                column: "AssignedAt");

            migrationBuilder.CreateIndex(
                name: "IX_UserApdysRoleMappings_AutoIncrementId",
                table: "UserApdysRoleMappings",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserApdysRoleMappings_UniversityUserId_ApdysRoleAutoIncreme~",
                table: "UserApdysRoleMappings",
                columns: new[] { "UniversityUserId", "ApdysRoleAutoIncrementId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CompetencyRatings");

            migrationBuilder.DropTable(
                name: "CoursePortfolioVerifications");

            migrationBuilder.DropTable(
                name: "CriterionFeedbacks");

            migrationBuilder.DropTable(
                name: "DepartmentPerformances");

            migrationBuilder.DropTable(
                name: "DepartmentStrategicPerformanceData");

            migrationBuilder.DropTable(
                name: "EvidenceFiles");

            migrationBuilder.DropTable(
                name: "FeedbackEntries");

            migrationBuilder.DropTable(
                name: "FeedbackNotificationLogs");

            migrationBuilder.DropTable(
                name: "GenericDataEntryRecords");

            migrationBuilder.DropTable(
                name: "PerformanceDistributions");

            migrationBuilder.DropTable(
                name: "PortfolioVerificationLogs");

            migrationBuilder.DropTable(
                name: "RlxEnumValues");

            migrationBuilder.DropTable(
                name: "StaticCriterionCoefficients");

            migrationBuilder.DropTable(
                name: "SubmissionAudits");

            migrationBuilder.DropTable(
                name: "TrendDataPoints");

            migrationBuilder.DropTable(
                name: "UserApdysRoleMappings");

            migrationBuilder.DropTable(
                name: "StaffCompetencyDefinitions");

            migrationBuilder.DropTable(
                name: "StaffCompetencyEvaluations");

            migrationBuilder.DropTable(
                name: "FormCriterionLinks");

            migrationBuilder.DropTable(
                name: "DepartmentStrategicIndicatorDefinitions");

            migrationBuilder.DropTable(
                name: "SubmissionFeedbacks");

            migrationBuilder.DropTable(
                name: "GenericDataEntryDefinitions");

            migrationBuilder.DropTable(
                name: "PortfolioChecklistItemDefinitions");

            migrationBuilder.DropTable(
                name: "RlxEnums");

            migrationBuilder.DropTable(
                name: "ApdysRoles");

            migrationBuilder.DropTable(
                name: "FormCategories");

            migrationBuilder.DropTable(
                name: "StaticCriterionDefinitions");

            migrationBuilder.DropTable(
                name: "AcademicSubmissions");

            migrationBuilder.DropTable(
                name: "AcademicianProfiles");

            migrationBuilder.DropTable(
                name: "EvaluationForms");
        }
    }
}
