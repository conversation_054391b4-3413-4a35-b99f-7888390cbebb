﻿// <auto-generated />
using System;
using AcademicPerformance.DbContexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AcademicPerformance.Migrations
{
    [DbContext(typeof(AcademicPerformanceDbContext))]
    partial class AcademicPerformanceDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianUniveristyUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ApprovalComments")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ApprovedByControllerUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("EvaluationFormAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("RejectedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RejectedByControllerUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("RejectionComments")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianUniveristyUserId");

                    b.HasIndex("ApprovedByControllerUserId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("CreatedAt");

                    b.HasIndex("EvaluationFormAutoIncrementId");

                    b.HasIndex("RejectedByControllerUserId");

                    b.HasIndex("Status");

                    b.HasIndex("SubmittedAt");

                    b.HasIndex("UpdatedAt");

                    b.HasIndex("AcademicianUniveristyUserId", "EvaluationFormAutoIncrementId")
                        .IsUnique();

                    b.HasIndex("AcademicianUniveristyUserId", "Status");

                    b.HasIndex("CreatedAt", "Status");

                    b.HasIndex("EvaluationFormAutoIncrementId", "Status");

                    b.HasIndex("Status", "SubmittedAt");

                    b.ToTable("AcademicSubmissions", t =>
                        {
                            t.HasCheckConstraint("CK_AcademicSubmission_Status", "\"Status\" IN ('Draft', 'Submitted', 'UnderReview', 'Approved', 'Rejected', 'RequiresRevision')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicianProfileEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicCadre")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Department")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("DepartmentId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Email")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("FacultyId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("FullName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastSyncedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Surname")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("SyncNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Title")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("UniversityUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicCadre");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("Department");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("FacultyId");

                    b.HasIndex("IsActive");

                    b.HasIndex("LastSyncedAt");

                    b.HasIndex("UniversityUserId")
                        .IsUnique();

                    b.HasIndex("DepartmentId", "AcademicCadre");

                    b.HasIndex("FacultyId", "DepartmentId");

                    b.HasIndex("IsActive", "AcademicCadre");

                    b.ToTable("AcademicianProfiles");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysRoleEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("RoleName")
                        .IsUnique();

                    b.ToTable("ApdysRoles");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.CompetencyRatingEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Comments")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("CompetencySystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Rating")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("StaffCompetencyEvaluationAutoIncrementId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("CompetencySystemId");

                    b.HasIndex("Rating");

                    b.HasIndex("StaffCompetencyEvaluationAutoIncrementId");

                    b.HasIndex("StaffCompetencyEvaluationAutoIncrementId", "CompetencySystemId")
                        .IsUnique();

                    b.ToTable("CompetencyRatings");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.CoursePortfolioVerificationEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianProfileId")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianTc")
                        .IsRequired()
                        .HasMaxLength(11)
                        .HasColumnType("character varying(11)");

                    b.Property<string>("AnswerKeyStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<string>("AttendanceSheetStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("CourseCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CourseName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("CourseSyllabusStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("EbysReferenceId")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ExamPapersStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<string>("ExamRecordStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<DateTime?>("LastVerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastVerifiedByArchivistId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("MakeupExamGradesStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<string>("PeriodName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("UzemRecordsStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<string>("VerificationNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("WeeklyAttendanceStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianProfileId");

                    b.HasIndex("AcademicianTc");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("CourseCode");

                    b.HasIndex("LastVerifiedAt");

                    b.HasIndex("LastVerifiedByArchivistId");

                    b.HasIndex("PeriodName");

                    b.HasIndex("AcademicianTc", "CourseCode", "PeriodName")
                        .IsUnique();

                    b.ToTable("CoursePortfolioVerifications", t =>
                        {
                            t.HasCheckConstraint("CK_CoursePortfolioVerification_AnswerKeyStatus", "\"AnswerKeyStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_AttendanceSheetStatus", "\"AttendanceSheetStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_CourseSyllabusStatus", "\"CourseSyllabusStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_ExamPapersStatus", "\"ExamPapersStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_ExamRecordStatus", "\"ExamRecordStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_MakeupExamGradesStatus", "\"MakeupExamGradesStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_UzemRecordsStatus", "\"UzemRecordsStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_WeeklyAttendanceStatus", "\"WeeklyAttendanceStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.CriterionFeedbackEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Comments")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("CriterionId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CriterionLinkId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("FeedbackMessage")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<int?>("Rating")
                        .HasColumnType("integer");

                    b.Property<bool>("RequiresRevision")
                        .HasColumnType("boolean");

                    b.Property<string>("RevisionType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SubmissionFeedbackId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CriterionLinkId");

                    b.HasIndex("IsActive");

                    b.HasIndex("IsRequired");

                    b.HasIndex("Status");

                    b.HasIndex("SubmissionFeedbackId");

                    b.HasIndex("CreatedAt", "Status");

                    b.HasIndex("CriterionLinkId", "Status");

                    b.HasIndex("Status", "IsRequired");

                    b.HasIndex("SubmissionFeedbackId", "CriterionLinkId");

                    b.ToTable("CriterionFeedbacks", t =>
                        {
                            t.HasCheckConstraint("CK_CriterionFeedback_Rating", "\"Rating\" IS NULL OR (\"Rating\" >= 1 AND \"Rating\" <= 10)");

                            t.HasCheckConstraint("CK_CriterionFeedback_RevisionType", "\"RevisionType\" IS NULL OR \"RevisionType\" IN ('DataCorrection', 'EvidenceUpdate', 'AdditionalInfo')");

                            t.HasCheckConstraint("CK_CriterionFeedback_Status", "\"Status\" IN ('Approved', 'NeedsRevision', 'Rejected', 'Excellent', 'Good', 'Satisfactory', 'NeedsImprovement')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.DepartmentPerformanceEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<decimal>("AcademicStaffPerformance")
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("AdditionalData")
                        .HasColumnType("jsonb");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ApprovedByUserId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("AutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<decimal>("BudgetEfficiencyScore")
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("CalculationDetails")
                        .HasColumnType("jsonb");

                    b.Property<int>("CompletedSubmissions")
                        .HasColumnType("integer");

                    b.Property<decimal>("CompletionRate")
                        .HasColumnType("decimal(5,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("CreatedByUserName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("DepartmentId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("DepartmentName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("EvaluationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FacultyId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FacultyName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal>("InfrastructureScore")
                        .HasColumnType("decimal(5,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("boolean");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<decimal>("OverallScore")
                        .HasColumnType("decimal(5,2)");

                    b.Property<int>("PendingSubmissions")
                        .HasColumnType("integer");

                    b.Property<string>("Period")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("PublicationPerformance")
                        .HasColumnType("decimal(5,2)");

                    b.Property<int?>("Ranking")
                        .HasColumnType("integer");

                    b.Property<decimal>("ResearchPerformance")
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("Status")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("StudentSatisfactionScore")
                        .HasColumnType("decimal(5,2)");

                    b.Property<int>("TotalAcademicStaff")
                        .HasColumnType("integer");

                    b.Property<int?>("TotalDepartmentsInRanking")
                        .HasColumnType("integer");

                    b.Property<int>("TotalStudents")
                        .HasColumnType("integer");

                    b.Property<string>("Trend")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("UpdatedByUserName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.ToTable("DepartmentPerformances");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.DepartmentStrategicIndicatorDefinitionEntity", b =>
                {
                    b.Property<string>("IndicatorSystemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHigherBetter")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.HasKey("IndicatorSystemId");

                    b.HasIndex("IndicatorSystemId")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.ToTable("DepartmentStrategicIndicatorDefinitions", t =>
                        {
                            t.HasCheckConstraint("CK_DepartmentStrategicIndicator_DataType", "\"DataType\" IN ('Integer', 'Decimal', 'Boolean', 'String', 'Date')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.DepartmentStrategicPerformanceDataEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ActualValue")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("AssessmentPeriodId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("DepartmentId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("IndicatorSystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SubmittedByUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("TargetValue")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentPeriodId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("DepartmentId");

                    b.HasIndex("IndicatorSystemId");

                    b.HasIndex("SubmittedAt");

                    b.HasIndex("DepartmentId", "AssessmentPeriodId", "IndicatorSystemId")
                        .IsUnique();

                    b.ToTable("DepartmentStrategicPerformanceData");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ApplicableAcademicCadresJson")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("EvaluationPeriodEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("EvaluationPeriodStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("SubmissionDeadline")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("CreatedAt");

                    b.HasIndex("EvaluationPeriodEndDate");

                    b.HasIndex("EvaluationPeriodStartDate");

                    b.HasIndex("Status");

                    b.HasIndex("SubmissionDeadline");

                    b.HasIndex("EvaluationPeriodStartDate", "EvaluationPeriodEndDate");

                    b.HasIndex("Status", "EvaluationPeriodStartDate");

                    b.HasIndex("Status", "SubmissionDeadline");

                    b.ToTable("EvaluationForms", t =>
                        {
                            t.HasCheckConstraint("CK_EvaluationForm_Status", "\"Status\" IN ('Draft', 'Active', 'Archived')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvidenceFileEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AcademicSubmissionAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<string>("AccessUrl")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("FileChecksum")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FormCriterionLinkId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("MinioBucketName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("MinioETag")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("MinioObjectName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("MinioPresignedUrlExpiry")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OriginalFileName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("SizeBytes")
                        .HasColumnType("bigint");

                    b.Property<string>("StorageType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Local");

                    b.Property<string>("StoredFilePath")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("SubmittedDynamicDataInstanceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UploadedByUniveristyUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicSubmissionAutoIncrementId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("ContentType");

                    b.HasIndex("MinioBucketName");

                    b.HasIndex("MinioObjectName");

                    b.HasIndex("StorageType");

                    b.HasIndex("UploadedAt");

                    b.HasIndex("UploadedByUniveristyUserId");

                    b.HasIndex("AcademicSubmissionAutoIncrementId", "UploadedAt");

                    b.HasIndex("StorageType", "UploadedAt");

                    b.HasIndex("UploadedByUniveristyUserId", "UploadedAt");

                    b.ToTable("EvidenceFiles", t =>
                        {
                            t.HasCheckConstraint("CK_EvidenceFile_Size", "SizeBytes > 0");

                            t.HasCheckConstraint("CK_EvidenceFile_StorageType", "StorageType IN ('Local', 'MinIO')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FeedbackEntryEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Category")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Comments")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("CriterionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("DepartmentId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("FeedbackType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("FormId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Metadata")
                        .HasColumnType("text");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("Medium");

                    b.Property<int?>("Rating")
                        .HasColumnType("integer");

                    b.Property<string>("ResolutionNote")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("ResolvedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double?>("ResponseTimeHours")
                        .HasColumnType("double precision");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Active");

                    b.Property<string>("SubmissionId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianUserId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("FeedbackType");

                    b.HasIndex("FormId");

                    b.HasIndex("Status");

                    b.HasIndex("SubmissionId");

                    b.HasIndex("AcademicianUserId", "CreatedAt");

                    b.HasIndex("CreatedAt", "Status");

                    b.HasIndex("DepartmentId", "CreatedAt");

                    b.HasIndex("FeedbackType", "Status");

                    b.HasIndex("Status", "Priority");

                    b.HasIndex("SubmissionId", "FeedbackType");

                    b.HasIndex("AcademicianUserId", "FeedbackType", "Status");

                    b.ToTable("FeedbackEntries", t =>
                        {
                            t.HasCheckConstraint("CK_FeedbackEntry_FeedbackType", "\"FeedbackType\" IN ('Approval', 'Rejection', 'RevisionRequest', 'Comment')");

                            t.HasCheckConstraint("CK_FeedbackEntry_Priority", "\"Priority\" IN ('Low', 'Medium', 'High', 'Critical')");

                            t.HasCheckConstraint("CK_FeedbackEntry_Rating", "\"Rating\" IS NULL OR (\"Rating\" >= 1 AND \"Rating\" <= 10)");

                            t.HasCheckConstraint("CK_FeedbackEntry_Status", "\"Status\" IN ('Active', 'Resolved', 'Superseded')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FeedbackNotificationLogEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("LastRetryAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NotificationType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("RecipientUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("SentAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("Pending");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("SubmissionFeedbackId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("CreatedAt");

                    b.HasIndex("NotificationType");

                    b.HasIndex("RecipientUserId");

                    b.HasIndex("RetryCount");

                    b.HasIndex("SentAt");

                    b.HasIndex("Status");

                    b.HasIndex("SubmissionFeedbackId");

                    b.HasIndex("NotificationType", "Status");

                    b.HasIndex("RecipientUserId", "CreatedAt");

                    b.HasIndex("Status", "RetryCount");

                    b.HasIndex("SubmissionFeedbackId", "NotificationType");

                    b.ToTable("FeedbackNotificationLogs", t =>
                        {
                            t.HasCheckConstraint("CK_FeedbackNotificationLog_NotificationType", "\"NotificationType\" IN ('Email', 'SMS', 'InApp')");

                            t.HasCheckConstraint("CK_FeedbackNotificationLog_RetryCount", "\"RetryCount\" >= 0");

                            t.HasCheckConstraint("CK_FeedbackNotificationLog_Status", "\"Status\" IN ('Pending', 'Sent', 'Failed')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCategoryEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<int>("EvaluationFormAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<double>("Weight")
                        .HasPrecision(5, 4)
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("CreatedAt");

                    b.HasIndex("EvaluationFormAutoIncrementId");

                    b.HasIndex("UpdatedAt");

                    b.HasIndex("EvaluationFormAutoIncrementId", "DisplayOrder");

                    b.HasIndex("EvaluationFormAutoIncrementId", "Weight");

                    b.ToTable("FormCategories");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCriterionLinkEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("CriterionType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<string>("DynamicCriterionTemplateId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("FormCategoryAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("StaticCriterionSystemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("WeightPercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("FormCategoryAutoIncrementId");

                    b.HasIndex("StaticCriterionSystemId");

                    b.HasIndex("FormCategoryAutoIncrementId", "DisplayOrder");

                    b.ToTable("FormCriterionLinks", t =>
                        {
                            t.HasCheckConstraint("CK_FormCriterionLink_CriterionType", "\"CriterionType\" IN ('Static', 'Dynamic')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.GenericDataEntryDefinitionEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("FieldDefinitions")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ValidationRules")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("IsActive");

                    b.HasIndex("Name");

                    b.ToTable("GenericDataEntryDefinitions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.GenericDataEntryRecordEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("DefinitionId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("RecordData")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DefinitionId");

                    b.HasIndex("Status");

                    b.ToTable("GenericDataEntryRecords");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PerformanceDistributionEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<int>("AverageCount")
                        .HasColumnType("integer");

                    b.Property<double>("AveragePercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("double precision");

                    b.Property<double>("AverageScore")
                        .HasPrecision(8, 4)
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CalculatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CalculatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("CalculationMethod")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Weighted");

                    b.Property<string>("CalculationParameters")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("DistributionType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("EntityId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("EntityName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("ExcellentCount")
                        .HasColumnType("integer");

                    b.Property<double>("ExcellentPercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("double precision");

                    b.Property<int>("GoodCount")
                        .HasColumnType("integer");

                    b.Property<double>("GoodPercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastUpdatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<double>("MaxScore")
                        .HasPrecision(8, 4)
                        .HasColumnType("double precision");

                    b.Property<double>("MedianScore")
                        .HasPrecision(8, 4)
                        .HasColumnType("double precision");

                    b.Property<string>("Metadata")
                        .HasColumnType("text");

                    b.Property<double>("MinScore")
                        .HasPrecision(8, 4)
                        .HasColumnType("double precision");

                    b.Property<DateTime>("PeriodEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("PeriodStart")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("PoorCount")
                        .HasColumnType("integer");

                    b.Property<double>("PoorPercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("double precision");

                    b.Property<double>("StandardDeviation")
                        .HasPrecision(8, 4)
                        .HasColumnType("double precision");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("Active");

                    b.Property<int>("TotalCount")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("CalculatedAt");

                    b.HasIndex("DistributionType");

                    b.HasIndex("EntityId");

                    b.HasIndex("Status");

                    b.HasIndex("DistributionType", "CalculatedAt");

                    b.HasIndex("DistributionType", "EntityId");

                    b.HasIndex("Status", "CalculatedAt");

                    b.HasIndex("DistributionType", "Status", "CalculatedAt");

                    b.HasIndex("EntityId", "PeriodStart", "PeriodEnd");

                    b.ToTable("PerformanceDistributions", t =>
                        {
                            t.HasCheckConstraint("CK_PerformanceDistribution_Counts", "\"ExcellentCount\" >= 0 AND \"GoodCount\" >= 0 AND \"AverageCount\" >= 0 AND \"PoorCount\" >= 0");

                            t.HasCheckConstraint("CK_PerformanceDistribution_DistributionType", "\"DistributionType\" IN ('Department', 'Faculty', 'University', 'Form', 'Category')");

                            t.HasCheckConstraint("CK_PerformanceDistribution_Percentages", "\"ExcellentPercentage\" >= 0 AND \"ExcellentPercentage\" <= 100");

                            t.HasCheckConstraint("CK_PerformanceDistribution_Status", "\"Status\" IN ('Active', 'Archived', 'Draft')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PortfolioChecklistItemDefinitionEntity", b =>
                {
                    b.Property<string>("ItemSystemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("EbysDataSourceHint")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<bool>("RequiresEbysVerification")
                        .HasColumnType("boolean");

                    b.HasKey("ItemSystemId");

                    b.HasIndex("Category");

                    b.HasIndex("IsActive");

                    b.HasIndex("ItemSystemId")
                        .IsUnique();

                    b.ToTable("PortfolioChecklistItemDefinitions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PortfolioVerificationLogEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianUniveristyUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("EbysReferenceId")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ItemSystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastVerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastVerifiedByArchivistUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("VerificationNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("VerificationStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianUniveristyUserId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("ItemSystemId");

                    b.HasIndex("LastVerifiedByArchivistUserId");

                    b.HasIndex("VerificationStatus");

                    b.HasIndex("AcademicianUniveristyUserId", "ItemSystemId")
                        .IsUnique();

                    b.ToTable("PortfolioVerificationLogs", t =>
                        {
                            t.HasCheckConstraint("CK_PortfolioVerification_Status", "\"VerificationStatus\" IN ('VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyDefinitionEntity", b =>
                {
                    b.Property<string>("CompetencySystemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<string>("RatingScaleJson")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.HasKey("CompetencySystemId");

                    b.HasIndex("CompetencySystemId")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.ToTable("StaffCompetencyDefinitions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyEvaluationEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianUniveristyUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("DepartmentId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("EvaluatingManagerUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("EvaluationContextId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("EvaluatorUserId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("OverallComments")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianUniveristyUserId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("DepartmentId");

                    b.HasIndex("Disabled");

                    b.HasIndex("EvaluationContextId");

                    b.HasIndex("EvaluatorUserId");

                    b.HasIndex("Status");

                    b.HasIndex("SubmittedAt");

                    b.HasIndex("AcademicianUniveristyUserId", "EvaluationContextId")
                        .IsUnique();

                    b.HasIndex("AcademicianUniveristyUserId", "Status");

                    b.HasIndex("DepartmentId", "EvaluationContextId");

                    b.HasIndex("EvaluatorUserId", "EvaluationContextId");

                    b.HasIndex("Status", "DepartmentId");

                    b.HasIndex("SubmittedAt", "Status");

                    b.HasIndex("DepartmentId", "Status", "EvaluationContextId");

                    b.ToTable("StaffCompetencyEvaluations");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaticCriterionCoefficientEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ApprovedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Category")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<decimal>("Coefficient")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("CoefficientHistory")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CriterionName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("DataSource")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("EffectiveFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastUpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("MaximumLimit")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("MinimumLimit")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("StaticCriterionSystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("DisplayOrder");

                    b.HasIndex("EffectiveFrom");

                    b.HasIndex("EffectiveTo");

                    b.HasIndex("IsActive");

                    b.HasIndex("IsApproved");

                    b.HasIndex("StaticCriterionSystemId");

                    b.ToTable("StaticCriterionCoefficients");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaticCriterionDefinitionEntity", b =>
                {
                    b.Property<string>("StaticCriterionSystemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CalculationLogic")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("DataSourceHint")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("StaticCriterionSystemId");

                    b.HasIndex("IsActive");

                    b.HasIndex("StaticCriterionSystemId")
                        .IsUnique();

                    b.ToTable("StaticCriterionDefinitions", t =>
                        {
                            t.HasCheckConstraint("CK_StaticCriterion_DataType", "\"DataType\" IN ('Integer', 'Decimal', 'Boolean', 'String', 'Date')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.SubmissionAuditEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AcademicSubmissionAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Category")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Comments")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<long?>("DurationMs")
                        .HasColumnType("bigint");

                    b.Property<string>("EntityId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("EntityType")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsSuccessful")
                        .HasColumnType("boolean");

                    b.Property<string>("Metadata")
                        .HasColumnType("text");

                    b.Property<string>("NewValue")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("OldValue")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("PerformedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PerformedByUserId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("PerformedByUserName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("PerformedByUserRole")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("RequestId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("SessionId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicSubmissionAutoIncrementId");

                    b.HasIndex("Action");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("Category");

                    b.HasIndex("EntityType");

                    b.HasIndex("IsSuccessful");

                    b.HasIndex("PerformedAt");

                    b.HasIndex("PerformedByUserId");

                    b.HasIndex("AcademicSubmissionAutoIncrementId", "PerformedAt");

                    b.HasIndex("Action", "PerformedAt");

                    b.HasIndex("Category", "PerformedAt");

                    b.HasIndex("IsSuccessful", "PerformedAt");

                    b.HasIndex("PerformedByUserId", "PerformedAt");

                    b.HasIndex("EntityType", "EntityId", "PerformedAt");

                    b.ToTable("SubmissionAudits", t =>
                        {
                            t.HasCheckConstraint("CK_SubmissionAudit_DurationMs", "DurationMs >= 0");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.SubmissionFeedbackEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianResponse")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("AcademicianResponseDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Comments")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("ControllerUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("FeedbackType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("FutureSuggestions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Highlights")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("NotificationSent")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("NotificationSentAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("OverallRating")
                        .HasColumnType("integer");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("Medium");

                    b.Property<DateTime?>("RevisionDeadline")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SubmissionId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("SubmissionStatusAtTime")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("ControllerUserId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("FeedbackType");

                    b.HasIndex("IsActive");

                    b.HasIndex("NotificationSent");

                    b.HasIndex("SubmissionId");

                    b.HasIndex("ControllerUserId", "CreatedAt");

                    b.HasIndex("CreatedAt", "IsActive");

                    b.HasIndex("FeedbackType", "IsActive");

                    b.HasIndex("SubmissionId", "FeedbackType");

                    b.ToTable("SubmissionFeedbacks", t =>
                        {
                            t.HasCheckConstraint("CK_SubmissionFeedback_FeedbackType", "\"FeedbackType\" IN ('Approval', 'Rejection', 'RevisionRequest')");

                            t.HasCheckConstraint("CK_SubmissionFeedback_Priority", "\"Priority\" IN ('Low', 'Medium', 'High')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.TrendDataPointEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AdditionalMetrics")
                        .HasColumnType("text");

                    b.Property<double?>("AnomalyScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("double precision");

                    b.Property<string>("AnomalyType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("CalculationMethod")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<double?>("ChangeAmount")
                        .HasPrecision(18, 6)
                        .HasColumnType("double precision");

                    b.Property<double?>("ChangeRate")
                        .HasPrecision(8, 4)
                        .HasColumnType("double precision");

                    b.Property<double>("ConfidenceLevel")
                        .HasPrecision(4, 3)
                        .HasColumnType("double precision");

                    b.Property<double?>("ConfidenceLowerBound")
                        .HasPrecision(18, 6)
                        .HasColumnType("double precision");

                    b.Property<double?>("ConfidenceUpperBound")
                        .HasPrecision(18, 6)
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("DataDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("DataQualityScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("double precision");

                    b.Property<string>("DataSource")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Calculated");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("EntityId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("EntityName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsAnomaly")
                        .HasColumnType("boolean");

                    b.Property<string>("Metadata")
                        .HasColumnType("text");

                    b.Property<double?>("MovingAverage")
                        .HasPrecision(18, 6)
                        .HasColumnType("double precision");

                    b.Property<DateTime>("PeriodEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("PeriodStart")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PeriodType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("Monthly");

                    b.Property<double?>("PreviousValue")
                        .HasPrecision(18, 6)
                        .HasColumnType("double precision");

                    b.Property<double?>("SeasonalityFactor")
                        .HasPrecision(8, 4)
                        .HasColumnType("double precision");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("Active");

                    b.Property<string>("TrendDirection")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("Stable");

                    b.Property<double>("TrendStrength")
                        .HasPrecision(8, 4)
                        .HasColumnType("double precision");

                    b.Property<string>("TrendType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<double>("Value")
                        .HasPrecision(18, 6)
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("DataDate");

                    b.HasIndex("EntityId");

                    b.HasIndex("EntityType");

                    b.HasIndex("IsAnomaly");

                    b.HasIndex("Status");

                    b.HasIndex("TrendType");

                    b.HasIndex("EntityId", "DataDate");

                    b.HasIndex("EntityType", "DataDate");

                    b.HasIndex("IsAnomaly", "DataDate");

                    b.HasIndex("PeriodType", "DataDate");

                    b.HasIndex("Status", "DataDate");

                    b.HasIndex("TrendDirection", "DataDate");

                    b.HasIndex("TrendType", "DataDate");

                    b.HasIndex("EntityId", "PeriodStart", "PeriodEnd");

                    b.HasIndex("TrendType", "EntityType", "DataDate");

                    b.HasIndex("TrendType", "EntityType", "EntityId");

                    b.ToTable("TrendDataPoints", t =>
                        {
                            t.HasCheckConstraint("CK_TrendDataPoint_DataSource", "\"DataSource\" IN ('Calculated', 'Manual', 'Imported', 'Predicted')");

                            t.HasCheckConstraint("CK_TrendDataPoint_EntityType", "\"EntityType\" IN ('Academician', 'Department', 'Faculty', 'Form', 'Category')");

                            t.HasCheckConstraint("CK_TrendDataPoint_PeriodType", "\"PeriodType\" IN ('Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly')");

                            t.HasCheckConstraint("CK_TrendDataPoint_Status", "\"Status\" IN ('Active', 'Archived', 'Draft', 'Validated')");

                            t.HasCheckConstraint("CK_TrendDataPoint_TrendDirection", "\"TrendDirection\" IN ('Increasing', 'Decreasing', 'Stable')");

                            t.HasCheckConstraint("CK_TrendDataPoint_TrendType", "\"TrendType\" IN ('Performance', 'Completion', 'Quality', 'Engagement')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.UserApdysRoleMappingEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("ApdysRoleAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("AssignedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssignedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime?>("RevokedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RevokedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("UniversityUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("ApdysRoleAutoIncrementId");

                    b.HasIndex("AssignedAt");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("UniversityUserId", "ApdysRoleAutoIncrementId")
                        .IsUnique();

                    b.ToTable("UserApdysRoleMappings");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Code")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId");

                    b.ToTable("RlxEnums");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Code")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("EnumId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("WhichRow")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId");

                    b.HasIndex("EnumId");

                    b.ToTable("RlxEnumValues");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxLocalizationDbContextModels.RlxLocalization", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<string>("Culture")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReferenceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable((string)null);

                    b.ToView("RlxLocalizations", (string)null);
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicianProfileEntity", "AcademicianProfile")
                        .WithMany("Submissions")
                        .HasForeignKey("AcademicianUniveristyUserId")
                        .HasPrincipalKey("UniversityUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", "EvaluationForm")
                        .WithMany("Submissions")
                        .HasForeignKey("EvaluationFormAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("AcademicianProfile");

                    b.Navigation("EvaluationForm");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.CompetencyRatingEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyDefinitionEntity", "CompetencyDefinition")
                        .WithMany("CompetencyRatings")
                        .HasForeignKey("CompetencySystemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyEvaluationEntity", "Evaluation")
                        .WithMany("CompetencyRatings")
                        .HasForeignKey("StaffCompetencyEvaluationAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CompetencyDefinition");

                    b.Navigation("Evaluation");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.CoursePortfolioVerificationEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicianProfileEntity", "AcademicianProfile")
                        .WithMany()
                        .HasForeignKey("AcademicianProfileId");

                    b.Navigation("AcademicianProfile");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.CriterionFeedbackEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCriterionLinkEntity", "CriterionLink")
                        .WithMany()
                        .HasForeignKey("CriterionLinkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.SubmissionFeedbackEntity", "SubmissionFeedback")
                        .WithMany("CriterionFeedbacks")
                        .HasForeignKey("SubmissionFeedbackId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CriterionLink");

                    b.Navigation("SubmissionFeedback");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.DepartmentStrategicPerformanceDataEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.DepartmentStrategicIndicatorDefinitionEntity", "IndicatorDefinition")
                        .WithMany("PerformanceData")
                        .HasForeignKey("IndicatorSystemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("IndicatorDefinition");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvidenceFileEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", "AcademicSubmission")
                        .WithMany("EvidenceFiles")
                        .HasForeignKey("AcademicSubmissionAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AcademicSubmission");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FeedbackEntryEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", "EvaluationForm")
                        .WithMany()
                        .HasForeignKey("FormId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", "AcademicSubmission")
                        .WithMany()
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AcademicSubmission");

                    b.Navigation("EvaluationForm");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FeedbackNotificationLogEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.SubmissionFeedbackEntity", "SubmissionFeedback")
                        .WithMany()
                        .HasForeignKey("SubmissionFeedbackId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SubmissionFeedback");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCategoryEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", "EvaluationForm")
                        .WithMany("Categories")
                        .HasForeignKey("EvaluationFormAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EvaluationForm");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCriterionLinkEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCategoryEntity", "FormCategory")
                        .WithMany("CriterionLinks")
                        .HasForeignKey("FormCategoryAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaticCriterionDefinitionEntity", "StaticCriterionDefinition")
                        .WithMany("FormCriterionLinks")
                        .HasForeignKey("StaticCriterionSystemId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("FormCategory");

                    b.Navigation("StaticCriterionDefinition");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.GenericDataEntryRecordEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.GenericDataEntryDefinitionEntity", "Definition")
                        .WithMany("DataEntryRecords")
                        .HasForeignKey("DefinitionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Definition");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PortfolioVerificationLogEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PortfolioChecklistItemDefinitionEntity", "ChecklistItemDefinition")
                        .WithMany("VerificationLogs")
                        .HasForeignKey("ItemSystemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ChecklistItemDefinition");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.SubmissionAuditEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", "AcademicSubmission")
                        .WithMany()
                        .HasForeignKey("AcademicSubmissionAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AcademicSubmission");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.SubmissionFeedbackEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", "Submission")
                        .WithMany()
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.UserApdysRoleMappingEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysRoleEntity", "Role")
                        .WithMany("UserMappings")
                        .HasForeignKey("ApdysRoleAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", b =>
                {
                    b.HasOne("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", "RlxEnum")
                        .WithMany("RlxEnumValues")
                        .HasForeignKey("EnumId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RlxEnum");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", b =>
                {
                    b.Navigation("EvidenceFiles");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicianProfileEntity", b =>
                {
                    b.Navigation("Submissions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysRoleEntity", b =>
                {
                    b.Navigation("UserMappings");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.DepartmentStrategicIndicatorDefinitionEntity", b =>
                {
                    b.Navigation("PerformanceData");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", b =>
                {
                    b.Navigation("Categories");

                    b.Navigation("Submissions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCategoryEntity", b =>
                {
                    b.Navigation("CriterionLinks");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.GenericDataEntryDefinitionEntity", b =>
                {
                    b.Navigation("DataEntryRecords");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PortfolioChecklistItemDefinitionEntity", b =>
                {
                    b.Navigation("VerificationLogs");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyDefinitionEntity", b =>
                {
                    b.Navigation("CompetencyRatings");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyEvaluationEntity", b =>
                {
                    b.Navigation("CompetencyRatings");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaticCriterionDefinitionEntity", b =>
                {
                    b.Navigation("FormCriterionLinks");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.SubmissionFeedbackEntity", b =>
                {
                    b.Navigation("CriterionFeedbacks");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", b =>
                {
                    b.Navigation("RlxEnumValues");
                });
#pragma warning restore 612, 618
        }
    }
}
