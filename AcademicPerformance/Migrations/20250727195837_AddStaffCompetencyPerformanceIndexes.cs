﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class AddStaffCompetencyPerformanceIndexes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "EvaluatorUserId",
                table: "StaffCompetencyEvaluations",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_AcademicianUniveristyUserId_Stat~",
                table: "StaffCompetencyEvaluations",
                columns: new[] { "AcademicianUniveristyUserId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_DepartmentId",
                table: "StaffCompetencyEvaluations",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_DepartmentId_EvaluationContextId",
                table: "StaffCompetencyEvaluations",
                columns: new[] { "DepartmentId", "EvaluationContextId" });

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_DepartmentId_Status_EvaluationCo~",
                table: "StaffCompetencyEvaluations",
                columns: new[] { "DepartmentId", "Status", "EvaluationContextId" });

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_Disabled",
                table: "StaffCompetencyEvaluations",
                column: "Disabled");

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_EvaluatorUserId",
                table: "StaffCompetencyEvaluations",
                column: "EvaluatorUserId");

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_EvaluatorUserId_EvaluationContex~",
                table: "StaffCompetencyEvaluations",
                columns: new[] { "EvaluatorUserId", "EvaluationContextId" });

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_Status",
                table: "StaffCompetencyEvaluations",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_Status_DepartmentId",
                table: "StaffCompetencyEvaluations",
                columns: new[] { "Status", "DepartmentId" });

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_SubmittedAt_Status",
                table: "StaffCompetencyEvaluations",
                columns: new[] { "SubmittedAt", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_CompetencyRatings_Rating",
                table: "CompetencyRatings",
                column: "Rating");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_StaffCompetencyEvaluations_AcademicianUniveristyUserId_Stat~",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropIndex(
                name: "IX_StaffCompetencyEvaluations_DepartmentId",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropIndex(
                name: "IX_StaffCompetencyEvaluations_DepartmentId_EvaluationContextId",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropIndex(
                name: "IX_StaffCompetencyEvaluations_DepartmentId_Status_EvaluationCo~",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropIndex(
                name: "IX_StaffCompetencyEvaluations_Disabled",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropIndex(
                name: "IX_StaffCompetencyEvaluations_EvaluatorUserId",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropIndex(
                name: "IX_StaffCompetencyEvaluations_EvaluatorUserId_EvaluationContex~",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropIndex(
                name: "IX_StaffCompetencyEvaluations_Status",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropIndex(
                name: "IX_StaffCompetencyEvaluations_Status_DepartmentId",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropIndex(
                name: "IX_StaffCompetencyEvaluations_SubmittedAt_Status",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropIndex(
                name: "IX_CompetencyRatings_Rating",
                table: "CompetencyRatings");

            migrationBuilder.DropColumn(
                name: "EvaluatorUserId",
                table: "StaffCompetencyEvaluations");
        }
    }
}
