using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace AcademicPerformance.Models.MongoDocuments;

public class DynamicCriterionTemplate
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string? Id { get; set; }

    [BsonElement("name")]
    [BsonRequired]
    public required string Name { get; set; }

    [BsonElement("description")]
    public string? Description { get; set; }

    [BsonElement("status")]
    [BsonRequired]
    public required string Status { get; set; } // Draft, Active, Inactive

    [BsonElement("inputFields")]
    [BsonRequired]
    public required List<DynamicInputField> InputFields { get; set; }

    [BsonElement("validationRules")]
    public List<ValidationRule>? ValidationRules { get; set; }

    [BsonElement("calculationLogic")]
    public string? CalculationLogic { get; set; }

    [BsonElement("coefficient")]
    public double? Coefficient { get; set; }

    [BsonElement("maxLimit")]
    public int? MaxLimit { get; set; }

    [BsonElement("createdAt")]
    [BsonRequired]
    public DateTime CreatedAt { get; set; }

    [BsonElement("createdByUserId")]
    [BsonRequired]
    public required string CreatedByUserId { get; set; }

    [BsonElement("updatedAt")]
    public DateTime? UpdatedAt { get; set; }

    [BsonElement("updatedByUserId")]
    public string? UpdatedByUserId { get; set; }

    [BsonElement("version")]
    public int Version { get; set; } = 1;

    [BsonElement("tags")]
    public List<string>? Tags { get; set; }
}

public class DynamicInputField
{
    [BsonElement("fieldId")]
    [BsonRequired]
    public required string FieldId { get; set; }

    [BsonElement("fieldName")]
    [BsonRequired]
    public required string FieldName { get; set; }

    [BsonElement("fieldType")]
    [BsonRequired]
    public required string FieldType { get; set; } // Text, Number, Date, Boolean, Select, MultiSelect, File

    [BsonElement("isRequired")]
    public bool IsRequired { get; set; }

    [BsonElement("displayOrder")]
    public int DisplayOrder { get; set; }

    [BsonElement("placeholder")]
    public string? Placeholder { get; set; }

    [BsonElement("helpText")]
    public string? HelpText { get; set; }

    [BsonElement("options")]
    public List<FieldOption>? Options { get; set; } // For Select/MultiSelect fields

    [BsonElement("constraints")]
    public FieldConstraints? Constraints { get; set; }
}

public class FieldOption
{
    [BsonElement("value")]
    [BsonRequired]
    public required string Value { get; set; }

    [BsonElement("label")]
    [BsonRequired]
    public required string Label { get; set; }

    [BsonElement("isDefault")]
    public bool IsDefault { get; set; }
}

public class FieldConstraints
{
    [BsonElement("minLength")]
    public int? MinLength { get; set; }

    [BsonElement("maxLength")]
    public int? MaxLength { get; set; }

    [BsonElement("minValue")]
    public decimal? MinValue { get; set; }

    [BsonElement("maxValue")]
    public decimal? MaxValue { get; set; }

    [BsonElement("pattern")]
    public string? Pattern { get; set; } // Regex pattern

    [BsonElement("allowedFileTypes")]
    public List<string>? AllowedFileTypes { get; set; }

    [BsonElement("maxFileSize")]
    public long? MaxFileSize { get; set; } // in bytes
}

public class ValidationRule
{
    [BsonElement("ruleType")]
    [BsonRequired]
    public required string RuleType { get; set; } // Required, Range, Pattern, Custom

    [BsonElement("fieldId")]
    [BsonRequired]
    public required string FieldId { get; set; }

    [BsonElement("errorMessage")]
    [BsonRequired]
    public required string ErrorMessage { get; set; }

    [BsonElement("ruleParameters")]
    public BsonDocument? RuleParameters { get; set; }
}
