using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Email notification DTO'su
    /// </summary>
    public class EmailNotificationDto
    {
        /// <summary>
        /// Alıcı email adresi
        /// </summary>
        [Required]
        [EmailAddress]
        public required string To { get; set; }

        /// <summary>
        /// Email konusu
        /// </summary>
        [Required]
        [StringLength(200)]
        public required string Subject { get; set; }

        /// <summary>
        /// Email içeriği
        /// </summary>
        [Required]
        public required string Body { get; set; }

        /// <summary>
        /// HTML formatında mı?
        /// </summary>
        public bool IsHtml { get; set; } = false;

        /// <summary>
        /// Öncelik seviyesi
        /// </summary>
        public string Priority { get; set; } = "Normal";

        /// <summary>
        /// Template adı (varsa)
        /// </summary>
        public string? TemplateName { get; set; }

        /// <summary>
        /// Template verileri
        /// </summary>
        public Dictionary<string, object>? TemplateData { get; set; }
    }

    /// <summary>
    /// Submission onaylandı notification template data
    /// </summary>
    public class SubmissionApprovedTemplateData
    {
        public required string AcademicianName { get; set; }
        public required string SubmissionId { get; set; }
        public required string FormName { get; set; }
        public required string ControllerName { get; set; }
        public DateTime ApprovalDate { get; set; }
        public string? ApprovalNotes { get; set; }
    }

    /// <summary>
    /// Submission reddedildi notification template data
    /// </summary>
    public class SubmissionRejectedTemplateData
    {
        public required string AcademicianName { get; set; }
        public required string SubmissionId { get; set; }
        public required string FormName { get; set; }
        public required string ControllerName { get; set; }
        public DateTime RejectionDate { get; set; }
        public required string RejectionReason { get; set; }
        public string? RevisionInstructions { get; set; }
    }

    /// <summary>
    /// Yeni submission notification template data
    /// </summary>
    public class NewSubmissionTemplateData
    {
        public required string ControllerName { get; set; }
        public required string AcademicianName { get; set; }
        public required string SubmissionId { get; set; }
        public required string FormName { get; set; }
        public DateTime SubmissionDate { get; set; }
        public string? Department { get; set; }
    }

    /// <summary>
    /// Revision request notification template data
    /// </summary>
    public class RevisionRequestTemplateData
    {
        public required string AcademicianName { get; set; }
        public required string SubmissionId { get; set; }
        public required string FormName { get; set; }
        public required string RevisionNotes { get; set; }
        public DateTime Deadline { get; set; }
        public required string ControllerName { get; set; }
    }

    /// <summary>
    /// Deadline reminder notification template data
    /// </summary>
    public class DeadlineReminderTemplateData
    {
        public required string AcademicianName { get; set; }
        public required string SubmissionId { get; set; }
        public required string FormName { get; set; }
        public int DaysRemaining { get; set; }
        public DateTime Deadline { get; set; }
    }

    /// <summary>
    /// Portfolio verification notification template data
    /// </summary>
    public class PortfolioVerificationTemplateData
    {
        public required string ArchivistName { get; set; }
        public required string AcademicianName { get; set; }
        public required string CourseId { get; set; }
        public required string CourseName { get; set; }
        public required string Period { get; set; }
        public DateTime VerificationDate { get; set; }
    }

    /// <summary>
    /// Staff competency evaluation notification template data
    /// </summary>
    public class StaffCompetencyEvaluationTemplateData
    {
        public required string ManagerName { get; set; }
        public required string StaffName { get; set; }
        public required string EvaluationId { get; set; }
        public required string EvaluationPeriod { get; set; }
        public DateTime EvaluationDate { get; set; }
        public string? Department { get; set; }
    }

    /// <summary>
    /// Department performance notification template data
    /// </summary>
    public class DepartmentPerformanceTemplateData
    {
        public required string StrategicOfficeName { get; set; }
        public required string DepartmentName { get; set; }
        public required string Period { get; set; }
        public DateTime DataEntryDate { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Email gönderim sonucu
    /// </summary>
    public class EmailSendResultDto
    {
        /// <summary>
        /// Gönderim başarılı mı?
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Hata mesajı (varsa)
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gönderim zamanı
        /// </summary>
        public DateTime SentAt { get; set; }

        /// <summary>
        /// Email ID (tracking için)
        /// </summary>
        public string? EmailId { get; set; }
    }

    /// <summary>
    /// SMTP configuration DTO
    /// </summary>
    public class SmtpConfigurationDto
    {
        public required string Host { get; set; }
        public int Port { get; set; }
        public bool EnableSsl { get; set; }
        public required string Username { get; set; }
        public required string Password { get; set; }
        public required string FromEmail { get; set; }
        public required string FromName { get; set; }
    }
}
