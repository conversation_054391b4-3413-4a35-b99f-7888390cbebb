using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Bölüm performans raporu DTO'su
    /// </summary>
    public class DepartmentReportDto
    {
        /// <summary>
        /// Rapor ID'si
        /// </summary>
        public string ReportId { get; set; } = string.Empty;

        /// <summary>
        /// Bölüm bilgileri
        /// </summary>
        public DepartmentSummaryDto Department { get; set; } = new();

        /// <summary>
        /// Bölüm adı (backward compatibility)
        /// </summary>
        public string DepartmentName => Department?.DepartmentName ?? string.Empty;

        /// <summary>
        /// Rapor dönemi
        /// </summary>
        public ReportPeriodDto Period { get; set; } = new();

        /// <summary>
        /// Rapor dönemi (backward compatibility)
        /// </summary>
        public ReportPeriodDto ReportPeriod => Period;

        /// <summary>
        /// Bölüm genel performans skoru (0-100)
        /// </summary>
        public double OverallScore { get; set; }

        /// <summary>
        /// Ortalama skor (backward compatibility)
        /// </summary>
        public double AverageScore => OverallScore;

        /// <summary>
        /// Bölüm performans seviyesi
        /// </summary>
        public string PerformanceLevel { get; set; } = string.Empty;

        /// <summary>
        /// Akademisyen sayısı
        /// </summary>
        public int TotalAcademicians { get; set; }

        /// <summary>
        /// Aktif akademisyen sayısı
        /// </summary>
        public int ActiveAcademicians { get; set; }

        /// <summary>
        /// Akademisyen performans dağılımı
        /// </summary>
        public PerformanceDistributionDto PerformanceDistribution { get; set; } = new();

        /// <summary>
        /// Kategori bazında bölüm performansı
        /// </summary>
        public List<CategoryPerformanceDto> CategoryPerformances { get; set; } = new();

        /// <summary>
        /// En iyi performans gösteren akademisyenler (top 5)
        /// </summary>
        public List<AcademicianPerformanceSummaryDto> TopPerformers { get; set; } = new();

        /// <summary>
        /// Gelişim gerektiren akademisyenler
        /// </summary>
        public List<AcademicianPerformanceSummaryDto> NeedsImprovement { get; set; } = new();

        /// <summary>
        /// Tüm akademisyen performansları (backward compatibility)
        /// </summary>
        public List<AcademicianPerformanceSummaryDto> AcademicianPerformances =>
            TopPerformers.Concat(NeedsImprovement).ToList();

        /// <summary>
        /// Form tamamlanma istatistikleri
        /// </summary>
        public FormCompletionStatsDto FormStats { get; set; } = new();

        /// <summary>
        /// Feedback istatistikleri
        /// </summary>
        public DepartmentFeedbackStatsDto FeedbackStats { get; set; } = new();

        /// <summary>
        /// Trend analizi (önceki dönemle karşılaştırma)
        /// </summary>
        public TrendAnalysisDto TrendAnalysis { get; set; } = new();

        /// <summary>
        /// Rapor oluşturulma tarihi
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Raporu oluşturan kullanıcı
        /// </summary>
        public string GeneratedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// Bölüm özet bilgileri
    /// </summary>
    public class DepartmentSummaryDto
    {
        public string DepartmentId { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string FacultyName { get; set; } = string.Empty;
        public string DepartmentHead { get; set; } = string.Empty;
        public string DepartmentCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// Performans dağılımı
    /// </summary>
    public class PerformanceDistributionDto
    {
        public int ExcellentCount { get; set; } // 90-100
        public int GoodCount { get; set; } // 70-89
        public int AverageCount { get; set; } // 50-69
        public int PoorCount { get; set; } // 0-49

        public double ExcellentPercentage { get; set; }
        public double GoodPercentage { get; set; }
        public double AveragePercentage { get; set; }
        public double PoorPercentage { get; set; }
    }

    /// <summary>
    /// Akademisyen performans özeti
    /// </summary>
    public class AcademicianPerformanceSummaryDto
    {
        public string UserId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string AcademicCadre { get; set; } = string.Empty;
        public double OverallScore { get; set; }
        public string PerformanceLevel { get; set; } = string.Empty;
        public int CompletedForms { get; set; }
        public int TotalForms { get; set; }
        public double CompletionRate { get; set; }
    }

    /// <summary>
    /// Form tamamlanma istatistikleri
    /// </summary>
    public class FormCompletionStatsDto
    {
        public int TotalForms { get; set; }
        public int CompletedForms { get; set; }
        public int InProgressForms { get; set; }
        public int PendingForms { get; set; }
        public double OverallCompletionRate { get; set; }
        public double AverageCompletionTime { get; set; } // gün cinsinden
        public DateTime? EarliestSubmission { get; set; }
        public DateTime? LatestSubmission { get; set; }
    }

    /// <summary>
    /// Bölüm feedback istatistikleri
    /// </summary>
    public class DepartmentFeedbackStatsDto
    {
        public int TotalFeedbacks { get; set; }
        public int ApprovalCount { get; set; }
        public int RejectionCount { get; set; }
        public int RevisionRequestCount { get; set; }
        public double AverageResponseTime { get; set; }
        public double ApprovalRate { get; set; }
        public double RejectionRate { get; set; }
    }

    /// <summary>
    /// Trend analizi
    /// </summary>
    public class TrendAnalysisDto
    {
        public double ScoreChange { get; set; } // önceki döneme göre değişim
        public double CompletionRateChange { get; set; }
        public string TrendDirection { get; set; } = string.Empty; // Improving, Declining, Stable
        public List<MonthlyTrendDto> MonthlyTrends { get; set; } = new();
    }

    /// <summary>
    /// Aylık trend verisi
    /// </summary>
    public class MonthlyTrendDto
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public double AverageScore { get; set; }
        public double CompletionRate { get; set; }
        public int ActiveAcademicians { get; set; }
    }
}
