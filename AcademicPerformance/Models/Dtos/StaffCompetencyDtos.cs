using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Personel yetkinlik değerlendirmesi DTO'su
    /// </summary>
    public class StaffCompetencyDto
    {
        public string Id { get; set; } = string.Empty;
        public int AutoIncrementId { get; set; }
        public string StaffId { get; set; } = string.Empty;
        public string StaffName { get; set; } = string.Empty;
        public string StaffEmail { get; set; } = string.Empty;
        public string DepartmentId { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public string AcademicTitle { get; set; } = string.Empty;
        public string EvaluationPeriod { get; set; } = string.Empty;
        public DateTime EvaluationDate { get; set; }

        // Yetkinlik Skorları
        public double OverallCompetencyScore { get; set; }
        public double TeachingCompetency { get; set; }
        public double ResearchCompetency { get; set; }
        public double ServiceCompetency { get; set; }
        public double LeadershipCompetency { get; set; }
        public double CommunicationCompetency { get; set; }
        public double TechnicalCompetency { get; set; }
        public double InnovationCompetency { get; set; }
        public double CollaborationCompetency { get; set; }

        // Performans Göstergeleri
        public int PublicationCount { get; set; }
        public int ProjectCount { get; set; }
        public double StudentEvaluationScore { get; set; }
        public double PeerEvaluationScore { get; set; }
        public double SelfEvaluationScore { get; set; }
        public double SupervisorEvaluationScore { get; set; }

        // Gelişim Alanları
        public List<string> StrengthAreas { get; set; } = new();
        public List<string> ImprovementAreas { get; set; } = new();
        public List<string> DevelopmentGoals { get; set; } = new();
        public string? Comments { get; set; }

        // Durum Bilgileri
        public string EvaluationStatus { get; set; } = string.Empty; // Draft, InReview, Completed, Approved
        public string? EvaluatorId { get; set; }
        public string? EvaluatorName { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public string? ApprovedByUserId { get; set; }
        public string? ApprovedByUserName { get; set; }

        // Metadata
        public string CreatedByUserId { get; set; } = string.Empty;
        public string CreatedByUserName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string? UpdatedByUserId { get; set; }
        public string? UpdatedByUserName { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Personel yetkinlik değerlendirmesi oluşturma DTO'su
    /// </summary>
    public class StaffCompetencyCreateDto
    {
        [Required]
        [StringLength(100)]
        public string StaffId { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string EvaluationPeriod { get; set; } = string.Empty;

        [Required]
        public DateTime EvaluationDate { get; set; }

        // Yetkinlik Skorları (0-100 arası)
        [Range(0, 100)]
        public double TeachingCompetency { get; set; }

        [Range(0, 100)]
        public double ResearchCompetency { get; set; }

        [Range(0, 100)]
        public double ServiceCompetency { get; set; }

        [Range(0, 100)]
        public double LeadershipCompetency { get; set; }

        [Range(0, 100)]
        public double CommunicationCompetency { get; set; }

        [Range(0, 100)]
        public double TechnicalCompetency { get; set; }

        [Range(0, 100)]
        public double InnovationCompetency { get; set; }

        [Range(0, 100)]
        public double CollaborationCompetency { get; set; }

        // Performans Göstergeleri
        [Range(0, int.MaxValue)]
        public int PublicationCount { get; set; }

        [Range(0, int.MaxValue)]
        public int ProjectCount { get; set; }

        [Range(0, 100)]
        public double StudentEvaluationScore { get; set; }

        [Range(0, 100)]
        public double PeerEvaluationScore { get; set; }

        [Range(0, 100)]
        public double SelfEvaluationScore { get; set; }

        [Range(0, 100)]
        public double SupervisorEvaluationScore { get; set; }

        // Gelişim Alanları
        public List<string> StrengthAreas { get; set; } = new();
        public List<string> ImprovementAreas { get; set; } = new();
        public List<string> DevelopmentGoals { get; set; } = new();

        [StringLength(2000)]
        public string? Comments { get; set; }

        [StringLength(100)]
        public string? EvaluatorId { get; set; }
    }

    /// <summary>
    /// Personel yetkinlik değerlendirmesi güncelleme DTO'su
    /// </summary>
    public class StaffCompetencyUpdateDto
    {
        [Required]
        [StringLength(100)]
        public string Id { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string EvaluationPeriod { get; set; } = string.Empty;

        [Required]
        public DateTime EvaluationDate { get; set; }

        // Yetkinlik Skorları (0-100 arası)
        [Range(0, 100)]
        public double TeachingCompetency { get; set; }

        [Range(0, 100)]
        public double ResearchCompetency { get; set; }

        [Range(0, 100)]
        public double ServiceCompetency { get; set; }

        [Range(0, 100)]
        public double LeadershipCompetency { get; set; }

        [Range(0, 100)]
        public double CommunicationCompetency { get; set; }

        [Range(0, 100)]
        public double TechnicalCompetency { get; set; }

        [Range(0, 100)]
        public double InnovationCompetency { get; set; }

        [Range(0, 100)]
        public double CollaborationCompetency { get; set; }

        // Performans Göstergeleri
        [Range(0, int.MaxValue)]
        public int PublicationCount { get; set; }

        [Range(0, int.MaxValue)]
        public int ProjectCount { get; set; }

        [Range(0, 100)]
        public double StudentEvaluationScore { get; set; }

        [Range(0, 100)]
        public double PeerEvaluationScore { get; set; }

        [Range(0, 100)]
        public double SelfEvaluationScore { get; set; }

        [Range(0, 100)]
        public double SupervisorEvaluationScore { get; set; }

        // Gelişim Alanları
        public List<string> StrengthAreas { get; set; } = new();
        public List<string> ImprovementAreas { get; set; } = new();
        public List<string> DevelopmentGoals { get; set; } = new();

        [StringLength(2000)]
        public string? Comments { get; set; }

        [StringLength(100)]
        public string? EvaluatorId { get; set; }

        [StringLength(50)]
        public string EvaluationStatus { get; set; } = "Draft";
    }

    /// <summary>
    /// Personel yetkinlik filtreleme DTO'su
    /// </summary>
    public class StaffCompetencyFilterDto
    {
        public string? StaffId { get; set; }
        public string? DepartmentId { get; set; }
        public string? EvaluationPeriod { get; set; }
        public string? EvaluationStatus { get; set; }
        public string? Position { get; set; }
        public string? AcademicTitle { get; set; }
        public DateTime? EvaluationDateFrom { get; set; }
        public DateTime? EvaluationDateTo { get; set; }
        public double? MinOverallScore { get; set; }
        public double? MaxOverallScore { get; set; }
        public string? EvaluatorId { get; set; }
        public bool? IsActive { get; set; } = true;
        public string? SearchTerm { get; set; }
    }

    /// <summary>
    /// Personel yetkinlik dashboard DTO'su
    /// </summary>
    public class StaffCompetencyDashboardDto
    {
        public string DepartmentId { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string Period { get; set; } = string.Empty;

        // Genel İstatistikler
        public int TotalStaffCount { get; set; }
        public int EvaluatedStaffCount { get; set; }
        public int PendingEvaluationCount { get; set; }
        public double EvaluationCompletionRate { get; set; }

        // Ortalama Yetkinlik Skorları
        public double AverageOverallScore { get; set; }
        public double AverageTeachingScore { get; set; }
        public double AverageResearchScore { get; set; }
        public double AverageServiceScore { get; set; }
        public double AverageLeadershipScore { get; set; }

        // Performans Dağılımı
        public int ExcellentPerformers { get; set; } // 90-100
        public int GoodPerformers { get; set; } // 70-89
        public int AveragePerformers { get; set; } // 50-69
        public int BelowAveragePerformers { get; set; } // <50

        // Trend Verileri
        public List<CompetencyTrendDataDto> TrendData { get; set; } = new();

        // En İyi ve En Düşük Performans Alanları
        public List<CompetencyAreaSummaryDto> TopPerformanceAreas { get; set; } = new();
        public List<CompetencyAreaSummaryDto> ImprovementNeededAreas { get; set; } = new();

        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Yetkinlik trend verisi DTO'su
    /// </summary>
    public class CompetencyTrendDataDto
    {
        public string CompetencySystemId { get; set; } = string.Empty;
        public string CompetencyName { get; set; } = string.Empty;
        public List<CompetencyPeriodScoreDto> PeriodScores { get; set; } = new();
        public string TrendDirection { get; set; } = string.Empty; // "Increasing", "Decreasing", "Stable"
        public double GrowthRate { get; set; }
        public double CurrentScore { get; set; }
        public double InitialScore { get; set; }

        // Backward compatibility için eski property'ler
        public string Period
        {
            get => PeriodScores.LastOrDefault()?.Period ?? string.Empty;
            set { /* Backward compatibility için boş */ }
        }
        public DateTime EvaluationDate
        {
            get => PeriodScores.LastOrDefault()?.EvaluationDate ?? DateTime.MinValue;
            set { /* Backward compatibility için boş */ }
        }
        public double AverageOverallScore
        {
            get => CurrentScore;
            set => CurrentScore = value;
        }
        public double AverageTeachingScore { get; set; }
        public double AverageResearchScore { get; set; }
        public double AverageServiceScore { get; set; }
        public int EvaluatedStaffCount { get; set; }
    }

    /// <summary>
    /// Yetkinlik dönem skoru DTO'su
    /// </summary>
    public class CompetencyPeriodScoreDto
    {
        public string Period { get; set; } = string.Empty;
        public double Score { get; set; }
        public DateTime EvaluationDate { get; set; }
    }

    /// <summary>
    /// Yetkinlik alanı özet DTO'su
    /// </summary>
    public class CompetencyAreaSummaryDto
    {
        public string CompetencySystemId { get; set; } = string.Empty;
        public string CompetencyName { get; set; } = string.Empty;
        public int TotalEvaluations { get; set; }
        public double AverageScore { get; set; }
        public double MinScore { get; set; }
        public double MaxScore { get; set; }
        public double StandardDeviation { get; set; }
        public Dictionary<string, int> DistributionByRating { get; set; } = new();

        // Eski property'ler backward compatibility için
        public string AreaName
        {
            get => CompetencyName;
            set => CompetencyName = value;
        }
        public int StaffCount
        {
            get => TotalEvaluations;
            set => TotalEvaluations = value;
        }
        public string PerformanceLevel { get; set; } = string.Empty; // Excellent, Good, Average, BelowAverage
        public double ImprovementRate { get; set; } // Önceki döneme göre değişim
    }

    /// <summary>
    /// Personel yetkinlik karşılaştırma DTO'su
    /// </summary>
    public class StaffCompetencyComparisonDto
    {
        public List<string> StaffIds { get; set; } = new();
        public string ComparisonPeriod { get; set; } = string.Empty;
        public List<StaffCompetencyComparisonItemDto> ComparisonData { get; set; } = new();
        public CompetencyBenchmarkDto Benchmark { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
    }

    /// <summary>
    /// Personel yetkinlik karşılaştırma öğesi DTO'su
    /// </summary>
    public class StaffCompetencyComparisonItemDto
    {
        public string StaffId { get; set; } = string.Empty;
        public string StaffName { get; set; } = string.Empty;
        public double OverallScore { get; set; }
        public Dictionary<string, double> CompetencyScores { get; set; } = new();
        public DateTime EvaluationDate { get; set; }
        public int Ranking { get; set; }
    }



    /// <summary>
    /// Yetkinlik benchmark DTO'su
    /// </summary>
    public class CompetencyBenchmarkDto
    {
        public string Period { get; set; } = string.Empty;
        public string DepartmentId { get; set; } = string.Empty;

        // Benchmark Skorları
        public double AverageOverallScore { get; set; }
        public double TopQuartileScore { get; set; }
        public double MedianScore { get; set; }
        public double BottomQuartileScore { get; set; }
        public double BestScore { get; set; }
        public double WorstScore { get; set; }

        // İstatistikler
        public int TotalStaffEvaluated { get; set; }
        public double StandardDeviation { get; set; }
        public string TopPerformer { get; set; } = string.Empty;
        public string BottomPerformer { get; set; } = string.Empty;

        public DateTime BenchmarkDate { get; set; }
    }

    /// <summary>
    /// Personel yetkinlik analiz DTO'su
    /// </summary>
    public class StaffCompetencyAnalysisDto
    {
        public string AnalysisType { get; set; } = string.Empty; // Individual, Department, Faculty
        public string TargetId { get; set; } = string.Empty;
        public string Period { get; set; } = string.Empty;

        // Analiz Sonuçları
        public CompetencyAnalysisResultDto AnalysisResult { get; set; } = new();
        public List<CompetencyGapAnalysisDto> GapAnalysis { get; set; } = new();
        public List<CompetencyRecommendationDto> Recommendations { get; set; } = new();

        public DateTime AnalysisDate { get; set; }
        public string AnalyzedByUserId { get; set; } = string.Empty;
        public string AnalyzedByUserName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Yetkinlik analiz sonucu DTO'su
    /// </summary>
    public class CompetencyAnalysisResultDto
    {
        public double OverallCompetencyLevel { get; set; }
        public string CompetencyRating { get; set; } = string.Empty; // Excellent, Good, Satisfactory, NeedsImprovement
        public List<string> KeyStrengths { get; set; } = new();
        public List<string> CriticalGaps { get; set; } = new();
        public double GrowthPotential { get; set; }
        public string CareerRecommendation { get; set; } = string.Empty;
    }

    /// <summary>
    /// Yetkinlik açık analizi DTO'su
    /// </summary>
    public class CompetencyGapAnalysisDto
    {
        public string CompetencyArea { get; set; } = string.Empty;
        public double CurrentLevel { get; set; }
        public double RequiredLevel { get; set; }
        public double GapSize { get; set; }
        public string GapSeverity { get; set; } = string.Empty; // Critical, High, Medium, Low
        public string ImpactArea { get; set; } = string.Empty;
        public int PriorityLevel { get; set; }
    }

    /// <summary>
    /// Yetkinlik önerisi DTO'su
    /// </summary>
    public class CompetencyRecommendationDto
    {
        public string RecommendationType { get; set; } = string.Empty; // Training, Mentoring, Assignment, Development
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string TargetCompetencyArea { get; set; } = string.Empty;
        public double ExpectedImprovement { get; set; }
        public int EstimatedDuration { get; set; } // Gün cinsinden
        public string Priority { get; set; } = string.Empty; // High, Medium, Low
        public List<string> RequiredResources { get; set; } = new();
    }

    /// <summary>
    /// Personel yetkinlik raporu DTO'su
    /// </summary>
    public class StaffCompetencyReportDto
    {
        public string ReportId { get; set; } = string.Empty;
        public string ReportType { get; set; } = string.Empty; // Individual, Department, Faculty, University
        public string ReportTitle { get; set; } = string.Empty;
        public string Period { get; set; } = string.Empty;
        public DateTime ReportDate { get; set; }

        // Rapor Kapsamı
        public string ScopeId { get; set; } = string.Empty; // StaffId, DepartmentId, FacultyId
        public string ScopeName { get; set; } = string.Empty;

        // Rapor İçeriği
        public StaffCompetencyReportSummaryDto Summary { get; set; } = new();
        public List<StaffCompetencyReportDetailDto> Details { get; set; } = new();
        public List<StaffCompetencyReportChartDto> Charts { get; set; } = new();

        // Metadata
        public string GeneratedByUserId { get; set; } = string.Empty;
        public string GeneratedByUserName { get; set; } = string.Empty;
        public string ReportFormat { get; set; } = string.Empty; // PDF, Excel, HTML
        public string? ExportPath { get; set; }
    }

    /// <summary>
    /// Personel yetkinlik rapor özeti DTO'su
    /// </summary>
    public class StaffCompetencyReportSummaryDto
    {
        public int TotalEvaluations { get; set; }
        public double AverageOverallScore { get; set; }
        public string PerformanceTrend { get; set; } = string.Empty; // Improving, Stable, Declining
        public double YearOverYearChange { get; set; }
        public List<string> KeyFindings { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public string ExecutiveSummary { get; set; } = string.Empty;
    }

    /// <summary>
    /// Personel yetkinlik rapor detayı DTO'su
    /// </summary>
    public class StaffCompetencyReportDetailDto
    {
        public string CategoryName { get; set; } = string.Empty;
        public double AverageScore { get; set; }
        public string PerformanceLevel { get; set; } = string.Empty;
        public double YearOverYearChange { get; set; }
        public List<string> Observations { get; set; } = new();
        public List<string> ImprovementSuggestions { get; set; } = new();
    }

    /// <summary>
    /// Personel yetkinlik rapor grafiği DTO'su
    /// </summary>
    public class StaffCompetencyReportChartDto
    {
        public string ChartTitle { get; set; } = string.Empty;
        public string ChartType { get; set; } = string.Empty; // Bar, Line, Pie, Radar
        public string ChartDescription { get; set; } = string.Empty;
        public List<string> Labels { get; set; } = new();
        public List<ChartDataSeriesDto> DataSeries { get; set; } = new();
    }

    /// <summary>
    /// Grafik veri serisi DTO'su
    /// </summary>
    public class ChartDataSeriesDto
    {
        public string SeriesName { get; set; } = string.Empty;
        public List<double> Values { get; set; } = new();
        public string Color { get; set; } = string.Empty;
    }

    /// <summary>
    /// Personel yetkinlik değerlendirme formu DTO'su
    /// </summary>
    public class CompetencyEvaluationFormDto
    {
        public string Id { get; set; } = string.Empty;
        public string StaffId { get; set; } = string.Empty;
        public string StaffName { get; set; } = string.Empty;
        public string EvaluatorId { get; set; } = string.Empty;
        public string EvaluatorName { get; set; } = string.Empty;
        public string EvaluationType { get; set; } = string.Empty; // Self, Peer, Supervisor, Student
        public string Period { get; set; } = string.Empty;
        public DateTime EvaluationDate { get; set; }

        // Değerlendirme Kategorileri
        public List<CompetencyEvaluationCategoryDto> Categories { get; set; } = new();

        // Genel Değerlendirme
        public double OverallScore { get; set; }
        public string OverallRating { get; set; } = string.Empty;
        public string GeneralComments { get; set; } = string.Empty;
        public List<string> StrengthAreas { get; set; } = new();
        public List<string> ImprovementAreas { get; set; } = new();

        // Durum
        public string Status { get; set; } = string.Empty; // Draft, Submitted, Reviewed, Approved
        public DateTime? SubmissionDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public string? ApprovedByUserId { get; set; }
    }

    /// <summary>
    /// Yetkinlik değerlendirme kategorisi DTO'su
    /// </summary>
    public class CompetencyEvaluationCategoryDto
    {
        public string CategoryId { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Weight { get; set; } // 0-1 arası

        // Kategori Kriterleri
        public List<CompetencyEvaluationCriterionDto> Criteria { get; set; } = new();

        // Kategori Sonuçları
        public double CategoryScore { get; set; }
        public string CategoryRating { get; set; } = string.Empty;
        public string? Comments { get; set; }
    }

    /// <summary>
    /// Yetkinlik değerlendirme kriteri DTO'su
    /// </summary>
    public class CompetencyEvaluationCriterionDto
    {
        public string CriterionId { get; set; } = string.Empty;
        public string CriterionName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Weight { get; set; } // 0-1 arası

        // Değerlendirme Ölçeği
        public int ScaleMin { get; set; } = 1;
        public int ScaleMax { get; set; } = 5;
        public List<string> ScaleDescriptions { get; set; } = new();

        // Değerlendirme Sonucu
        public int Rating { get; set; }
        public double Score { get; set; }
        public string? Comments { get; set; }
        public List<string>? Evidence { get; set; }
    }

    /// <summary>
    /// Yetkinlik değerlendirme formu oluşturma DTO'su
    /// </summary>
    public class CompetencyEvaluationFormCreateDto
    {
        [Required]
        [StringLength(100)]
        public string StaffId { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string EvaluatorId { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string EvaluationType { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Period { get; set; } = string.Empty;

        [Required]
        public DateTime EvaluationDate { get; set; }

        [Required]
        public List<CompetencyEvaluationCategoryCreateDto> Categories { get; set; } = new();

        [StringLength(2000)]
        public string? GeneralComments { get; set; }

        public List<string>? StrengthAreas { get; set; }

        public List<string>? ImprovementAreas { get; set; }
    }

    /// <summary>
    /// Yetkinlik değerlendirme kategorisi oluşturma DTO'su
    /// </summary>
    public class CompetencyEvaluationCategoryCreateDto
    {
        [Required]
        [StringLength(100)]
        public string CategoryId { get; set; } = string.Empty;

        [Range(0, 1)]
        public double Weight { get; set; }

        [Required]
        public List<CompetencyEvaluationCriterionCreateDto> Criteria { get; set; } = new();

        [StringLength(1000)]
        public string? Comments { get; set; }
    }

    /// <summary>
    /// Yetkinlik değerlendirme kriteri oluşturma DTO'su
    /// </summary>
    public class CompetencyEvaluationCriterionCreateDto
    {
        [Required]
        [StringLength(100)]
        public string CriterionId { get; set; } = string.Empty;

        [Range(0, 1)]
        public double Weight { get; set; }

        [Range(1, 5)]
        public int Rating { get; set; }

        [StringLength(1000)]
        public string? Comments { get; set; }

        public List<string>? Evidence { get; set; }
    }

    /// <summary>
    /// Personel yetkinlik karşılaştırma isteği DTO'su
    /// </summary>
    public class StaffCompetencyComparisonRequestDto
    {
        [Required]
        public List<string> StaffIds { get; set; } = new();

        [Required]
        public string Period { get; set; } = string.Empty;
    }
}
