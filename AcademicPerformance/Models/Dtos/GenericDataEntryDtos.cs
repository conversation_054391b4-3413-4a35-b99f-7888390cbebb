using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Generic Data Entry Definition DTO
    /// </summary>
    public class GenericDataEntryDefinitionDto
    {
        /// <summary>
        /// Definition ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Definition adı
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Definition açıklaması
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Definition kategorisi
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// Field definitions (JSON)
        /// </summary>
        public string FieldDefinitions { get; set; } = string.Empty;

        /// <summary>
        /// Validation rules (JSON)
        /// </summary>
        public string ValidationRules { get; set; } = string.Empty;

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Oluşturma tarihi
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Oluşturan kullanıcı ID
        /// </summary>
        public string CreatedByUserId { get; set; } = string.Empty;

        /// <summary>
        /// Güncelleme tarihi
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Güncelleyen kullanıcı ID
        /// </summary>
        public string? UpdatedByUserId { get; set; }
    }

    /// <summary>
    /// Generic Data Entry Definition Create DTO
    /// </summary>
    public class GenericDataEntryDefinitionCreateDto
    {
        /// <summary>
        /// Definition adı
        /// </summary>
        [Required]
        [StringLength(200)]
        public required string Name { get; set; }

        /// <summary>
        /// Definition açıklaması
        /// </summary>
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Definition kategorisi
        /// </summary>
        [Required]
        [StringLength(100)]
        public required string Category { get; set; }

        /// <summary>
        /// Field definitions (JSON)
        /// </summary>
        [Required]
        public required string FieldDefinitions { get; set; }

        /// <summary>
        /// Validation rules (JSON)
        /// </summary>
        public string ValidationRules { get; set; } = "{}";

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Generic Data Entry Definition Update DTO
    /// </summary>
    public class GenericDataEntryDefinitionUpdateDto
    {
        /// <summary>
        /// Definition adı
        /// </summary>
        [StringLength(200)]
        public string? Name { get; set; }

        /// <summary>
        /// Definition açıklaması
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// Definition kategorisi
        /// </summary>
        [StringLength(100)]
        public string? Category { get; set; }

        /// <summary>
        /// Field definitions (JSON)
        /// </summary>
        public string? FieldDefinitions { get; set; }

        /// <summary>
        /// Validation rules (JSON)
        /// </summary>
        public string? ValidationRules { get; set; }

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// Generic Data Entry Record DTO
    /// </summary>
    public class GenericDataEntryRecordDto
    {
        /// <summary>
        /// Record ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Definition ID
        /// </summary>
        public string DefinitionId { get; set; } = string.Empty;

        /// <summary>
        /// Definition adı
        /// </summary>
        public string DefinitionName { get; set; } = string.Empty;

        /// <summary>
        /// Record data (JSON)
        /// </summary>
        public string RecordData { get; set; } = string.Empty;

        /// <summary>
        /// Record status
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Oluşturma tarihi
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Oluşturan kullanıcı ID
        /// </summary>
        public string CreatedByUserId { get; set; } = string.Empty;

        /// <summary>
        /// Güncelleme tarihi
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Güncelleyen kullanıcı ID
        /// </summary>
        public string? UpdatedByUserId { get; set; }
    }

    /// <summary>
    /// Generic Data Entry Record Create DTO
    /// </summary>
    public class GenericDataEntryRecordCreateDto
    {
        /// <summary>
        /// Definition ID
        /// </summary>
        [Required]
        public required string DefinitionId { get; set; }

        /// <summary>
        /// Record data (JSON)
        /// </summary>
        [Required]
        public required string RecordData { get; set; }

        /// <summary>
        /// Record status
        /// </summary>
        [StringLength(50)]
        public string Status { get; set; } = "Draft";
    }

    /// <summary>
    /// Generic Data Entry Record Update DTO
    /// </summary>
    public class GenericDataEntryRecordUpdateDto
    {
        /// <summary>
        /// Record data (JSON)
        /// </summary>
        public string? RecordData { get; set; }

        /// <summary>
        /// Record status
        /// </summary>
        [StringLength(50)]
        public string? Status { get; set; }
    }

    /// <summary>
    /// Generic Data Entry Statistics DTO
    /// </summary>
    public class GenericDataEntryStatisticsDto
    {
        /// <summary>
        /// Definition ID
        /// </summary>
        public string DefinitionId { get; set; } = string.Empty;

        /// <summary>
        /// Definition adı
        /// </summary>
        public string DefinitionName { get; set; } = string.Empty;

        /// <summary>
        /// Toplam record sayısı
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// Draft record sayısı
        /// </summary>
        public int DraftRecords { get; set; }

        /// <summary>
        /// Published record sayısı
        /// </summary>
        public int PublishedRecords { get; set; }

        /// <summary>
        /// Son record tarihi
        /// </summary>
        public DateTime? LastRecordDate { get; set; }
    }

    /// <summary>
    /// Generic Data Entry Overall Statistics DTO
    /// </summary>
    public class GenericDataEntryOverallStatisticsDto
    {
        /// <summary>
        /// Toplam definition sayısı
        /// </summary>
        public int TotalDefinitions { get; set; }

        /// <summary>
        /// Aktif definition sayısı
        /// </summary>
        public int ActiveDefinitions { get; set; }

        /// <summary>
        /// Toplam record sayısı
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// Bu ay eklenen record sayısı
        /// </summary>
        public int RecordsThisMonth { get; set; }

        /// <summary>
        /// En çok kullanılan definition'lar
        /// </summary>
        public List<GenericDataEntryStatisticsDto> MostUsedDefinitions { get; set; } = new();
    }


}
