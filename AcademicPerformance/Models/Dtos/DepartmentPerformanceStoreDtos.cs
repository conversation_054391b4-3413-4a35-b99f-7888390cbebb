namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Bölüm sıralama item DTO'su
    /// </summary>
    public class DepartmentRankingItemDto
    {
        public string DepartmentId { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public double Score { get; set; }
        public int Ranking { get; set; }
        public double ScoreGapToNext { get; set; }
        public double ScoreGapToTop { get; set; }
    }

    /// <summary>
    /// Bölüm performans toplam DTO'su
    /// </summary>
    public class DepartmentPerformanceAggregateDto
    {
        public string DepartmentId { get; set; } = string.Empty;
        public double AverageOverallScore { get; set; }
        public double TotalScore { get; set; }
        public int RecordCount { get; set; }
        public double BestScore { get; set; }
        public double WorstScore { get; set; }
        public string BestPeriod { get; set; } = string.Empty;
        public string WorstPeriod { get; set; } = string.Empty;
        public Dictionary<string, double> MetricAverages { get; set; } = new();
    }

    /// <summary>
    /// Fakülte performans toplam DTO'su
    /// </summary>
    public class FacultyPerformanceAggregateDto
    {
        public string FacultyId { get; set; } = string.Empty;
        public double AverageOverallScore { get; set; }
        public int DepartmentCount { get; set; }
        public double BestDepartmentScore { get; set; }
        public double WorstDepartmentScore { get; set; }
        public string BestDepartment { get; set; } = string.Empty;
        public string WorstDepartment { get; set; } = string.Empty;
        public Dictionary<string, double> MetricAverages { get; set; } = new();
    }

    /// <summary>
    /// Veri tutarlılık raporu DTO'su
    /// </summary>
    public class DataConsistencyReportDto
    {
        public bool IsConsistent { get; set; }
        public List<string> Issues { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, object> ValidationResults { get; set; } = new();
        public DateTime CheckedAt { get; set; }
    }
}
