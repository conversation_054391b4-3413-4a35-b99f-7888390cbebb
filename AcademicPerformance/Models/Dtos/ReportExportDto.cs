using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Rapor export sonucu DTO'su
    /// </summary>
    public class ReportExportDto
    {
        /// <summary>
        /// Export ID'si
        /// </summary>
        public string ExportId { get; set; } = string.Empty;

        /// <summary>
        /// Dosya adı
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Dosya formatı (PDF, Excel, CSV)
        /// </summary>
        public string Format { get; set; } = string.Empty;

        /// <summary>
        /// Dosya boyutu (byte)
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// Download URL'i
        /// </summary>
        public string DownloadUrl { get; set; } = string.Empty;

        /// <summary>
        /// Export durumu (Processing, Completed, Failed)
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Export ba<PERSON><PERSON><PERSON> zamanı
        /// </summary>
        public DateTime StartedAt { get; set; }

        /// <summary>
        /// Export tamamlanma zamanı
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// Export süresi (saniye)
        /// </summary>
        public double? ProcessingTime { get; set; }

        /// <summary>
        /// Hata mesajı (varsa)
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Export eden kullanıcı
        /// </summary>
        public string ExportedBy { get; set; } = string.Empty;

        /// <summary>
        /// Dosya geçerlilik süresi
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// İndirme sayısı
        /// </summary>
        public int DownloadCount { get; set; }
    }

    /// <summary>
    /// Kriter analiz raporu DTO'su
    /// </summary>
    public class CriterionAnalysisReportDto
    {
        /// <summary>
        /// Rapor ID'si
        /// </summary>
        public string ReportId { get; set; } = string.Empty;

        /// <summary>
        /// Kriter bilgileri
        /// </summary>
        public CriterionSummaryDto Criterion { get; set; } = new();

        /// <summary>
        /// Analiz dönemi
        /// </summary>
        public ReportPeriodDto Period { get; set; } = new();

        /// <summary>
        /// Genel istatistikler
        /// </summary>
        public CriterionStatisticsDto Statistics { get; set; } = new();

        /// <summary>
        /// Bölüm bazında analiz
        /// </summary>
        public List<DepartmentCriterionAnalysisDto> DepartmentAnalyses { get; set; } = new();

        /// <summary>
        /// Akademisyen bazında performans
        /// </summary>
        public List<AcademicianCriterionPerformanceDto> AcademicianPerformances { get; set; } = new();

        /// <summary>
        /// Zaman bazında trend
        /// </summary>
        public List<CriterionTrendDto> TrendData { get; set; } = new();

        /// <summary>
        /// Karşılaştırmalı analiz
        /// </summary>
        public CriterionComparisonDto Comparison { get; set; } = new();

        /// <summary>
        /// Öneriler
        /// </summary>
        public List<CriterionRecommendationDto> Recommendations { get; set; } = new();

        /// <summary>
        /// Rapor oluşturulma tarihi
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Kriter özet bilgileri
    /// </summary>
    public class CriterionSummaryDto
    {
        public string CriterionId { get; set; } = string.Empty;
        public string CriterionName { get; set; } = string.Empty;
        public string CriterionType { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double MaxScore { get; set; }
        public double Weight { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// Kriter istatistikleri
    /// </summary>
    public class CriterionStatisticsDto
    {
        public int TotalSubmissions { get; set; }
        public int CompletedSubmissions { get; set; }
        public double CompletionRate { get; set; }
        public double AverageScore { get; set; }
        public double MedianScore { get; set; }
        public double StandardDeviation { get; set; }
        public double MinScore { get; set; }
        public double MaxScore { get; set; }
        public int UniqueAcademicians { get; set; }
        public int UniqueDepartments { get; set; }
    }

    /// <summary>
    /// Bölüm kriter analizi
    /// </summary>
    public class DepartmentCriterionAnalysisDto
    {
        public string DepartmentId { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public int AcademicianCount { get; set; }
        public int CompletedCount { get; set; }
        public double CompletionRate { get; set; }
        public double AverageScore { get; set; }
        public double BestScore { get; set; }
        public double WorstScore { get; set; }
        public string PerformanceLevel { get; set; } = string.Empty;
        public int DepartmentRank { get; set; }
    }

    /// <summary>
    /// Akademisyen kriter performansı
    /// </summary>
    public class AcademicianCriterionPerformanceDto
    {
        public string AcademicianId { get; set; } = string.Empty;
        public string AcademicianName { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public string AcademicCadre { get; set; } = string.Empty;
        public double Score { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime? CompletedAt { get; set; }
        public int DaysToComplete { get; set; }
        public string PerformanceLevel { get; set; } = string.Empty;
        public int Rank { get; set; }
    }

    /// <summary>
    /// Kriter trend verisi
    /// </summary>
    public class CriterionTrendDto
    {
        public DateTime Date { get; set; }
        public string Period { get; set; } = string.Empty;
        public double AverageScore { get; set; }
        public double CompletionRate { get; set; }
        public int SubmissionCount { get; set; }
        public int CompletedCount { get; set; }
    }

    /// <summary>
    /// Kriter karşılaştırması
    /// </summary>
    public class CriterionComparisonDto
    {
        public double CurrentPeriodAverage { get; set; }
        public double PreviousPeriodAverage { get; set; }
        public double ChangePercentage { get; set; }
        public string TrendDirection { get; set; } = string.Empty;
        public double CurrentCompletionRate { get; set; }
        public double PreviousCompletionRate { get; set; }
        public double CompletionRateChange { get; set; }
        public List<CategoryComparisonDto> CategoryComparisons { get; set; } = new();
    }

    /// <summary>
    /// Kriter önerisi
    /// </summary>
    public class CriterionRecommendationDto
    {
        public string Type { get; set; } = string.Empty; // Improvement, Optimization, Alert
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public List<string> ActionItems { get; set; } = new();
        public string ExpectedImpact { get; set; } = string.Empty;
        public List<string> TargetDepartments { get; set; } = new();
        public List<string> TargetAcademicians { get; set; } = new();
    }

    /// <summary>
    /// Trend analizi raporu DTO'su
    /// </summary>
    public class TrendAnalysisReportDto
    {
        public string ReportId { get; set; } = string.Empty;
        public string AnalysisType { get; set; } = string.Empty;
        public string TimeInterval { get; set; } = string.Empty;
        public ReportPeriodDto Period { get; set; } = new();
        public TrendSummaryDto Summary { get; set; } = new();
        public List<TrendDataPointDto> TrendData { get; set; } = new();
        public TrendForecastDto? Forecast { get; set; }
        public List<TrendInsightDto> Insights { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Trend özeti
    /// </summary>
    public class TrendSummaryDto
    {
        public string OverallTrend { get; set; } = string.Empty;
        public double TrendStrength { get; set; }
        public double ChangePercentage { get; set; }
        public string SignificanceLevel { get; set; } = string.Empty;
        public DateTime PeakDate { get; set; }
        public double PeakValue { get; set; }
        public DateTime LowestDate { get; set; }
        public double LowestValue { get; set; }
    }

    /// <summary>
    /// Trend veri noktası
    /// </summary>
    public class TrendDataPointDto
    {
        public DateTime Date { get; set; }
        public string Period { get; set; } = string.Empty;
        public double Value { get; set; }
        public double MovingAverage { get; set; }
        public string TrendDirection { get; set; } = string.Empty;
        public bool IsSignificant { get; set; }
    }

    /// <summary>
    /// Trend tahmini
    /// </summary>
    public class TrendForecastDto
    {
        public List<ForecastDataPointDto> ForecastData { get; set; } = new();
        public double ConfidenceLevel { get; set; }
        public string ForecastMethod { get; set; } = string.Empty;
        public DateTime ForecastGeneratedAt { get; set; }
    }

    /// <summary>
    /// Tahmin veri noktası
    /// </summary>
    public class ForecastDataPointDto
    {
        public DateTime Date { get; set; }
        public double PredictedValue { get; set; }
        public double UpperBound { get; set; }
        public double LowerBound { get; set; }
        public double Confidence { get; set; }
    }

    /// <summary>
    /// Trend içgörüsü
    /// </summary>
    public class TrendInsightDto
    {
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public DateTime DetectedAt { get; set; }
        public List<string> AffectedAreas { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }
}
