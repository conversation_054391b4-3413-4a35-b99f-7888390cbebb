namespace AcademicPerformance.Models.Dtos
{
    // Person related DTOs
    public class PersonDto
    {
        public string? Id { get; set; }
        public int? AutoIncrementId { get; set; }
        public string? IdentityNumber { get; set; }
        public string? Name { get; set; }
        public string? Surname { get; set; }
        public string? MiddleName { get; set; }
        public DateTime DateOfBirth { get; set; }
        public int CountryId { get; set; }
        public int? TitleId { get; set; }
        public CountryDto? Country { get; set; }
        public List<PersonUserDto>? PersonUsers { get; set; }
        public List<PersonPositionDto>? PersonPositions { get; set; }
    }

    public class PersonUserDto
    {
        public string? Id { get; set; }
        public int PersonId { get; set; }
        public int UserId { get; set; }
        public PersonDto? Person { get; set; }
    }

    public class PersonPositionDto
    {
        public string? Id { get; set; }
        public int PersonId { get; set; }
        public int PositionId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsPrimary { get; set; }
        public PersonDto? Person { get; set; }
        public PositionDto? Position { get; set; }
    }

    // Unit and Position related DTOs
    public class UnitDto
    {
        public string? Id { get; set; }
        public int? AutoIncrementId { get; set; }
        public string? Name { get; set; }
        public string? Code { get; set; }
        public int? ParentId { get; set; }
        public int UnitTypeId { get; set; }
        public UnitDto? Parent { get; set; }
        public List<UnitDto>? Children { get; set; }
        public List<PositionDto>? Positions { get; set; }
    }

    public class PositionDto
    {
        public string? Id { get; set; }
        public int? AutoIncrementId { get; set; }
        public string? Name { get; set; }
        public string? Code { get; set; }
        public int UnitId { get; set; }
        public int PositionTypeId { get; set; }
        public UnitDto? Unit { get; set; }
        public List<PersonPositionDto>? PersonPositions { get; set; }
    }

    // Location related DTOs
    public class CountryDto
    {
        public string? Id { get; set; }
        public int? AutoIncrementId { get; set; }
        public string? Name { get; set; }
        public string? IsoCode2 { get; set; }
        public string? IsoCode3 { get; set; }
    }

    public class StateDto
    {
        public string? Id { get; set; }
        public int? AutoIncrementId { get; set; }
        public string? Name { get; set; }
        public string? Code { get; set; }
        public int CountryId { get; set; }
        public CountryDto? Country { get; set; }
    }

    public class CityDto
    {
        public string? Id { get; set; }
        public int? AutoIncrementId { get; set; }
        public string? Name { get; set; }
        public int StateId { get; set; }
        public StateDto? State { get; set; }
    }
}
