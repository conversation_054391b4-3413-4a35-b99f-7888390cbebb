using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class DepartmentStrategicIndicatorDefinitionEntity
{
    [Key]
    [StringLength(100)]
    public required string IndicatorSystemId { get; set; } // e.g., "STUDENT_SATISFACTION_RATE"
    
    [Required]
    [StringLength(250)]
    public required string Name { get; set; }
    
    [StringLength(1000)]
    public string? Description { get; set; }
    
    [Required]
    [StringLength(50)]
    public required string DataType { get; set; } // Integer, Decimal, Boolean, String, Date
    
    public bool IsHigherBetter { get; set; } = true;
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public string? CreatedByUserId { get; set; }
    
    // Navigation properties
    public virtual ICollection<DepartmentStrategicPerformanceDataEntity>? PerformanceData { get; set; }
}
