using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class CompetencyRatingEntity : EntityBaseModel
{
    public int StaffCompetencyEvaluationAutoIncrementId { get; set; } // FK
    
    [Required]
    [StringLength(100)]
    public required string CompetencySystemId { get; set; } // FK
    
    [Required]
    [StringLength(100)]
    public required string Rating { get; set; }
    
    [StringLength(1000)]
    public string? Comments { get; set; }
    
    // Navigation properties
    public virtual StaffCompetencyEvaluationEntity? Evaluation { get; set; }
    public virtual StaffCompetencyDefinitionEntity? CompetencyDefinition { get; set; }
}
