using Rlx.Shared.Models;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class FormCriterionLinkEntity : EntityBaseModel
{
    public int FormCategoryAutoIncrementId { get; set; }
    public string? StaticCriterionSystemId { get; set; } // FK to StaticCriterionDefinitionEntity
    public string? DynamicCriterionTemplateId { get; set; }
    public required string CriterionType { get; set; } // Static, Dynamic
    public int DisplayOrder { get; set; }
    public decimal? WeightPercentage { get; set; }
    public bool IsRequired { get; set; }

    // Audit fields
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedByUserId { get; set; }

    public virtual FormCategoryEntity? FormCategory { get; set; }
    public virtual StaticCriterionDefinitionEntity? StaticCriterionDefinition { get; set; }
}
