using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class StaffCompetencyEvaluationEntity : EntityBaseModel
{
    [Required]
    public required string AcademicianUniveristyUserId { get; set; }

    [Required]
    public required string EvaluatingManagerUserId { get; set; }

    [Required]
    [StringLength(100)]
    public required string EvaluationContextId { get; set; }

    [StringLength(4000)]
    public string? OverallComments { get; set; }

    [StringLength(100)]
    public string? DepartmentId { get; set; }

    [StringLength(50)]
    public string Status { get; set; } = "Draft";

    [StringLength(100)]
    public string? EvaluatorUserId { get; set; }

    public DateTime SubmittedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<CompetencyRatingEntity>? CompetencyRatings { get; set; } = new List<CompetencyRatingEntity>();
}
