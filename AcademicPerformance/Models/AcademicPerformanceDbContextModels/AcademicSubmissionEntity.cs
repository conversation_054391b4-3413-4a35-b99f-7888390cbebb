using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class AcademicSubmissionEntity : EntityBaseModel
{
    public required string AcademicianUniveristyUserId { get; set; }
    public int EvaluationFormAutoIncrementId { get; set; }
    public required string Status { get; set; } // Draft, Submitted, UnderReview, Approved, Rejected, RequiresRevision
    public DateTime? SubmittedAt { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public string? ApprovedByControllerUserId { get; set; }
    public DateTime? RejectedAt { get; set; }
    public string? RejectedByControllerUserId { get; set; }
    [StringLength(2000)]
    public string? ApprovalComments { get; set; }
    [StringLength(2000)]
    public string? RejectionComments { get; set; }

    // Audit fields
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedByUserId { get; set; }
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public string? UpdatedByUserId { get; set; }

    public virtual EvaluationFormEntity? EvaluationForm { get; set; }
    public virtual AcademicianProfileEntity? AcademicianProfile { get; set; }
    public virtual ICollection<EvidenceFileEntity>? EvidenceFiles { get; set; }
}
