using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

/// <summary>
/// Trend data point entity - Trend analizi veri noktalarını saklar
/// Time series data için optimize edilmiş
/// </summary>
public class TrendDataPointEntity : EntityBaseModel
{
    /// <summary>
    /// Trend tipi (Performance, Completion, Quality, Engagement)
    /// </summary>
    [Required]
    [StringLength(50)]
    public required string TrendType { get; set; }

    /// <summary>
    /// İlgili entity tipi (Academician, Department, Faculty, Form, Category)
    /// </summary>
    [Required]
    [StringLength(50)]
    public required string EntityType { get; set; }

    /// <summary>
    /// İlgili entity ID'si
    /// </summary>
    [Required]
    public required string EntityId { get; set; }

    /// <summary>
    /// Entity adı (kolay raporlama için)
    /// </summary>
    [StringLength(200)]
    public string? EntityName { get; set; }

    /// <summary>
    /// Veri noktası tarihi
    /// </summary>
    public DateTime DataDate { get; set; }

    /// <summary>
    /// Dönem başlangıcı
    /// </summary>
    public DateTime PeriodStart { get; set; }

    /// <summary>
    /// Dönem bitişi
    /// </summary>
    public DateTime PeriodEnd { get; set; }

    /// <summary>
    /// Dönem tipi (Daily, Weekly, Monthly, Quarterly, Yearly)
    /// </summary>
    [StringLength(20)]
    public string PeriodType { get; set; } = "Monthly";

    /// <summary>
    /// Ana değer (performans skoru, tamamlama oranı, etc.)
    /// </summary>
    public double Value { get; set; }

    /// <summary>
    /// Hareketli ortalama değeri
    /// </summary>
    public double? MovingAverage { get; set; }

    /// <summary>
    /// Trend yönü (Increasing, Decreasing, Stable)
    /// </summary>
    [StringLength(20)]
    public string TrendDirection { get; set; } = "Stable";

    /// <summary>
    /// Trend gücü (0-100 arası)
    /// </summary>
    public double TrendStrength { get; set; }

    /// <summary>
    /// Değişim oranı (önceki döneme göre %)
    /// </summary>
    public double? ChangeRate { get; set; }

    /// <summary>
    /// Değişim miktarı (mutlak değer)
    /// </summary>
    public double? ChangeAmount { get; set; }

    /// <summary>
    /// Önceki dönem değeri
    /// </summary>
    public double? PreviousValue { get; set; }

    /// <summary>
    /// Veri kalitesi skoru (0-100)
    /// </summary>
    public double DataQualityScore { get; set; } = 100;

    /// <summary>
    /// Güven aralığı alt sınırı
    /// </summary>
    public double? ConfidenceLowerBound { get; set; }

    /// <summary>
    /// Güven aralığı üst sınırı
    /// </summary>
    public double? ConfidenceUpperBound { get; set; }

    /// <summary>
    /// Güven seviyesi (0.90, 0.95, 0.99)
    /// </summary>
    public double ConfidenceLevel { get; set; } = 0.95;

    /// <summary>
    /// Anomali durumu
    /// </summary>
    public bool IsAnomaly { get; set; } = false;

    /// <summary>
    /// Anomali skoru (0-100)
    /// </summary>
    public double? AnomalyScore { get; set; }

    /// <summary>
    /// Anomali tipi (Outlier, Seasonal, Trend, Level)
    /// </summary>
    [StringLength(50)]
    public string? AnomalyType { get; set; }

    /// <summary>
    /// Mevsimsellik faktörü
    /// </summary>
    public double? SeasonalityFactor { get; set; }

    /// <summary>
    /// Veri kaynağı (Calculated, Manual, Imported, Predicted)
    /// </summary>
    [StringLength(50)]
    public string DataSource { get; set; } = "Calculated";

    /// <summary>
    /// Hesaplama yöntemi
    /// </summary>
    [StringLength(100)]
    public string? CalculationMethod { get; set; }

    /// <summary>
    /// Veri durumu (Active, Archived, Draft, Validated)
    /// </summary>
    [StringLength(20)]
    public string Status { get; set; } = "Active";

    /// <summary>
    /// Ek metrikler (JSON format)
    /// </summary>
    public string? AdditionalMetrics { get; set; }

    /// <summary>
    /// Metadata (JSON format)
    /// </summary>
    public string? Metadata { get; set; }

    // Audit fields
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }

    // Navigation properties removed due to conditional relationships
    // Use EntityType and EntityId to determine the related entity
    // EntityType = 'Academician' -> EntityId is UniversityUserId
    // EntityType = 'Form' -> EntityId is Form Id
    // EntityType = 'Department' -> EntityId is Department Id
    // EntityType = 'Faculty' -> EntityId is Faculty Id
}
