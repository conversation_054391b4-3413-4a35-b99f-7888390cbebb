using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

/// <summary>
/// Performance distribution entity - Performans dağılım verilerini saklar
/// Reporting ve analytics için optimize edilmiş
/// </summary>
public class PerformanceDistributionEntity : EntityBaseModel
{
    /// <summary>
    /// Dağılım tipi (Department, Faculty, University, Form, Category)
    /// </summary>
    [Required]
    [StringLength(50)]
    public required string DistributionType { get; set; }

    /// <summary>
    /// İlgili entity ID'si (departmentId, facultyId, formId, etc.)
    /// </summary>
    [Required]
    public required string EntityId { get; set; }

    /// <summary>
    /// Entity adı (kolay raporlama için)
    /// </summary>
    [StringLength(200)]
    public string? EntityName { get; set; }

    /// <summary>
    /// Hesaplama dönemi ba<PERSON><PERSON>ı
    /// </summary>
    public DateTime PeriodStart { get; set; }

    /// <summary>
    /// Hesaplama dönemi bitişi
    /// </summary>
    public DateTime PeriodEnd { get; set; }

    /// <summary>
    /// Mükemmel performans sayısı (90-100)
    /// </summary>
    public int ExcellentCount { get; set; }

    /// <summary>
    /// İyi performans sayısı (75-89)
    /// </summary>
    public int GoodCount { get; set; }

    /// <summary>
    /// Ortalama performans sayısı (60-74)
    /// </summary>
    public int AverageCount { get; set; }

    /// <summary>
    /// Zayıf performans sayısı (0-59)
    /// </summary>
    public int PoorCount { get; set; }

    /// <summary>
    /// Toplam kayıt sayısı
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Mükemmel performans yüzdesi
    /// </summary>
    public double ExcellentPercentage { get; set; }

    /// <summary>
    /// İyi performans yüzdesi
    /// </summary>
    public double GoodPercentage { get; set; }

    /// <summary>
    /// Ortalama performans yüzdesi
    /// </summary>
    public double AveragePercentage { get; set; }

    /// <summary>
    /// Zayıf performans yüzdesi
    /// </summary>
    public double PoorPercentage { get; set; }

    /// <summary>
    /// Ortalama puan
    /// </summary>
    public double AverageScore { get; set; }

    /// <summary>
    /// Medyan puan
    /// </summary>
    public double MedianScore { get; set; }

    /// <summary>
    /// Standart sapma
    /// </summary>
    public double StandardDeviation { get; set; }

    /// <summary>
    /// Minimum puan
    /// </summary>
    public double MinScore { get; set; }

    /// <summary>
    /// Maksimum puan
    /// </summary>
    public double MaxScore { get; set; }

    /// <summary>
    /// Hesaplama yöntemi (Weighted, Simple, Custom)
    /// </summary>
    [StringLength(50)]
    public string CalculationMethod { get; set; } = "Weighted";

    /// <summary>
    /// Hesaplama parametreleri (JSON format)
    /// </summary>
    public string? CalculationParameters { get; set; }

    /// <summary>
    /// Dağılım durumu (Active, Archived, Draft)
    /// </summary>
    [StringLength(20)]
    public string Status { get; set; } = "Active";

    /// <summary>
    /// Hesaplama tarihi
    /// </summary>
    public DateTime CalculatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Hesaplayan kullanıcı ID'si
    /// </summary>
    public string? CalculatedBy { get; set; }

    /// <summary>
    /// Son güncelleme tarihi
    /// </summary>
    public DateTime? LastUpdatedAt { get; set; }

    /// <summary>
    /// Güncelleyen kullanıcı ID'si
    /// </summary>
    public string? LastUpdatedBy { get; set; }

    /// <summary>
    /// Ek metadata (JSON format)
    /// </summary>
    public string? Metadata { get; set; }

    // Audit fields
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
}
