using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class PortfolioChecklistItemDefinitionEntity
{
    [Key]
    [StringLength(100)]
    public required string ItemSystemId { get; set; } // örn., "PUBLICATION_RECORD"

    [Required]
    [StringLength(250)]
    public required string Name { get; set; }

    [StringLength(1000)]
    public string? Description { get; set; }

    [Required]
    [StringLength(100)]
    public required string Category { get; set; } // örn., "Academic", "Administrative", "Research"

    public bool RequiresEbysVerification { get; set; } = false;

    [StringLength(500)]
    public string? EbysDataSourceHint { get; set; }

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public string? CreatedByUserId { get; set; }

    // Navigation property'leri
    public virtual ICollection<PortfolioVerificationLogEntity>? VerificationLogs { get; set; }
}
