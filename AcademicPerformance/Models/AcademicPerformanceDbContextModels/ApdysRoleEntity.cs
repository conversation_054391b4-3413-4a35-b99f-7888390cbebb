using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class ApdysRoleEntity : EntityBaseModel
{
    [Required]
    [StringLength(100)]
    public required string RoleName { get; set; }

    [StringLength(500)]
    public string? Description { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public virtual ICollection<UserApdysRoleMappingEntity>? UserMappings { get; set; }
}
