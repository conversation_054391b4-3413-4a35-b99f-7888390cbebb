using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels
{
    /// <summary>
    /// Statik kriter katsayıları entity'si
    /// UI'dan değiştirilebilir katsayı değerlerini saklar
    /// </summary>
    [Table("StaticCriterionCoefficients")]
    public class StaticCriterionCoefficientEntity
    {
        /// <summary>
        /// Birincil anahtar
        /// </summary>
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Statik kriter sistem ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public required string StaticCriterionSystemId { get; set; }

        /// <summary>
        /// Kriter adı
        /// </summary>
        [Required]
        [StringLength(200)]
        public required string CriterionName { get; set; }

        /// <summary>
        /// <PERSON><PERSON><PERSON> de<PERSON> (UI'dan de<PERSON>iştirilebilir)
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal Coefficient { get; set; }

        /// <summary>
        /// Maksimum limit (opsiyonel)
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? MaximumLimit { get; set; }

        /// <summary>
        /// Minimum limit (opsiyonel)
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? MinimumLimit { get; set; }

        /// <summary>
        /// Aktif durumu
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Kriter kategorisi (A, B, C, vb.)
        /// </summary>
        [StringLength(10)]
        public string? Category { get; set; }

        /// <summary>
        /// Kriter sırası
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Açıklama
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Veri kaynağı
        /// </summary>
        [StringLength(100)]
        public string? DataSource { get; set; }

        /// <summary>
        /// Son güncelleme tarihi
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Son güncelleyen kullanıcı
        /// </summary>
        [StringLength(100)]
        public string? LastUpdatedBy { get; set; }

        /// <summary>
        /// Oluşturulma tarihi
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Oluşturan kullanıcı
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// Versiyon (optimistic locking için)
        /// </summary>
        [Timestamp]
        public byte[]? RowVersion { get; set; }

        /// <summary>
        /// Katsayı geçmişi JSON formatında
        /// </summary>
        [Column(TypeName = "text")]
        public string? CoefficientHistory { get; set; }

        /// <summary>
        /// Onay durumu
        /// </summary>
        public bool IsApproved { get; set; } = false;

        /// <summary>
        /// Onaylayan kullanıcı
        /// </summary>
        [StringLength(100)]
        public string? ApprovedBy { get; set; }

        /// <summary>
        /// Onay tarihi
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// Geçerlilik başlangıç tarihi
        /// </summary>
        public DateTime? EffectiveFrom { get; set; }

        /// <summary>
        /// Geçerlilik bitiş tarihi
        /// </summary>
        public DateTime? EffectiveTo { get; set; }
    }

    /// <summary>
    /// Statik kriter katsayısı DTO'su
    /// </summary>
    public class StaticCriterionCoefficientDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Statik kriter sistem ID'si
        /// </summary>
        public required string StaticCriterionSystemId { get; set; }

        /// <summary>
        /// Kriter adı
        /// </summary>
        public required string CriterionName { get; set; }

        /// <summary>
        /// Katsayı değeri
        /// </summary>
        public decimal Coefficient { get; set; }

        /// <summary>
        /// Maksimum limit
        /// </summary>
        public decimal? MaximumLimit { get; set; }

        /// <summary>
        /// Minimum limit
        /// </summary>
        public decimal? MinimumLimit { get; set; }

        /// <summary>
        /// Aktif durumu
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Kriter kategorisi
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// Kriter sırası
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Açıklama
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Veri kaynağı
        /// </summary>
        public string? DataSource { get; set; }

        /// <summary>
        /// Son güncelleme tarihi
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// Son güncelleyen kullanıcı
        /// </summary>
        public string? LastUpdatedBy { get; set; }

        /// <summary>
        /// Onay durumu
        /// </summary>
        public bool IsApproved { get; set; }

        /// <summary>
        /// Onaylayan kullanıcı
        /// </summary>
        public string? ApprovedBy { get; set; }

        /// <summary>
        /// Onay tarihi
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// Geçerlilik başlangıç tarihi
        /// </summary>
        public DateTime? EffectiveFrom { get; set; }

        /// <summary>
        /// Geçerlilik bitiş tarihi
        /// </summary>
        public DateTime? EffectiveTo { get; set; }
    }

    /// <summary>
    /// Statik kriter katsayısı güncelleme DTO'su
    /// </summary>
    public class UpdateStaticCriterionCoefficientDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Yeni katsayı değeri
        /// </summary>
        [Required]
        [Range(0, 1000)]
        public decimal Coefficient { get; set; }

        /// <summary>
        /// Maksimum limit
        /// </summary>
        [Range(0, 10000)]
        public decimal? MaximumLimit { get; set; }

        /// <summary>
        /// Minimum limit
        /// </summary>
        [Range(0, 10000)]
        public decimal? MinimumLimit { get; set; }

        /// <summary>
        /// Açıklama
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Geçerlilik başlangıç tarihi
        /// </summary>
        public DateTime? EffectiveFrom { get; set; }

        /// <summary>
        /// Geçerlilik bitiş tarihi
        /// </summary>
        public DateTime? EffectiveTo { get; set; }

        /// <summary>
        /// Güncelleme nedeni
        /// </summary>
        [StringLength(500)]
        public string? UpdateReason { get; set; }
    }

    /// <summary>
    /// Toplu katsayı güncelleme DTO'su
    /// </summary>
    public class BulkUpdateStaticCriterionCoefficientsDto
    {
        /// <summary>
        /// Güncellenecek katsayılar
        /// </summary>
        public required List<UpdateStaticCriterionCoefficientDto> Coefficients { get; set; }

        /// <summary>
        /// Toplu güncelleme nedeni
        /// </summary>
        [StringLength(500)]
        public string? BulkUpdateReason { get; set; }

        /// <summary>
        /// Onay gerekli mi?
        /// </summary>
        public bool RequireApproval { get; set; } = true;
    }

    /// <summary>
    /// Katsayı geçmişi DTO'su
    /// </summary>
    public class CoefficientHistoryDto
    {
        /// <summary>
        /// Eski değer
        /// </summary>
        public decimal OldValue { get; set; }

        /// <summary>
        /// Yeni değer
        /// </summary>
        public decimal NewValue { get; set; }

        /// <summary>
        /// Değişiklik tarihi
        /// </summary>
        public DateTime ChangeDate { get; set; }

        /// <summary>
        /// Değiştiren kullanıcı
        /// </summary>
        public string? ChangedBy { get; set; }

        /// <summary>
        /// Değişiklik nedeni
        /// </summary>
        public string? ChangeReason { get; set; }

        /// <summary>
        /// Onay durumu
        /// </summary>
        public bool IsApproved { get; set; }

        /// <summary>
        /// Onaylayan kullanıcı
        /// </summary>
        public string? ApprovedBy { get; set; }

        /// <summary>
        /// Onay tarihi
        /// </summary>
        public DateTime? ApprovedAt { get; set; }
    }
}
