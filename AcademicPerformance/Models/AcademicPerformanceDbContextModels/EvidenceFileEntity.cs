using Rlx.Shared.Models;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class EvidenceFileEntity : EntityBaseModel
{
    public int AcademicSubmissionAutoIncrementId { get; set; }
    public string? FormCriterionLinkId { get; set; }
    public string? SubmittedDynamicDataInstanceId { get; set; } // FK to MongoDB document ID
    public required string FileName { get; set; }
    public required string StoredFilePath { get; set; } // Renamed from FilePath
    public long SizeBytes { get; set; } // Renamed from FileSize
    public required string ContentType { get; set; }
    public DateTime UploadedAt { get; set; }
    public string? UploadedByUniveristyUserId { get; set; } // Renamed from UploadedByUserId
    public string? Description { get; set; }

    // MinIO specific fields
    /// <summary>
    /// MinIO bucket name where the file is stored
    /// </summary>
    public string? MinioBucketName { get; set; }

    /// <summary>
    /// MinIO object name (unique identifier in bucket)
    /// </summary>
    public string? MinioObjectName { get; set; }

    /// <summary>
    /// MinIO ETag for file integrity verification
    /// </summary>
    public string? MinioETag { get; set; }

    /// <summary>
    /// File storage type (Local, MinIO, etc.)
    /// </summary>
    public string StorageType { get; set; } = "Local"; // Default to Local for backward compatibility

    /// <summary>
    /// MinIO presigned URL expiry time (if applicable)
    /// </summary>
    public DateTime? MinioPresignedUrlExpiry { get; set; }

    /// <summary>
    /// File access URL (can be presigned URL or direct path)
    /// </summary>
    public string? AccessUrl { get; set; }

    /// <summary>
    /// File checksum for integrity verification
    /// </summary>
    public string? FileChecksum { get; set; }

    /// <summary>
    /// Original file name before processing
    /// </summary>
    public string? OriginalFileName { get; set; }
    public virtual AcademicSubmissionEntity? AcademicSubmission { get; set; }
}
