using Rlx.Shared.Models;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class PortfolioVerificationLogEntity : EntityBaseModel
{
    public required string AcademicianUniveristyUserId { get; set; }
    public required string ItemSystemId { get; set; }
    public required string VerificationStatus { get; set; } // VerifiedInEbys, Missing, DiscrepancyFound, NotApplicable
    public string? VerificationNotes { get; set; }
    public DateTime? LastVerifiedAt { get; set; }
    public string? LastVerifiedByArchivistUserId { get; set; }
    public string? EbysReferenceId { get; set; }
    public virtual PortfolioChecklistItemDefinitionEntity? ChecklistItemDefinition { get; set; }
}
