using Rlx.Shared.Models;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class FormCategoryEntity : EntityBaseModel
{
    public int EvaluationFormAutoIncrementId { get; set; }
    public required string Name { get; set; }
    public string? Description { get; set; }
    public int DisplayOrder { get; set; }
    public double Weight { get; set; } // e.g., 0.4 for 40%

    // Audit fields
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedByUserId { get; set; }
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public string? UpdatedByUserId { get; set; }

    public virtual EvaluationFormEntity? EvaluationForm { get; set; }
    public virtual ICollection<FormCriterionLinkEntity>? CriterionLinks { get; set; }
}
