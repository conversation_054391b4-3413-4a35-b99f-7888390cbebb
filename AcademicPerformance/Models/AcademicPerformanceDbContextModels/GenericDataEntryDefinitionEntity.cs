using System.ComponentModel.DataAnnotations;
using Rlx.Shared.Models;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class GenericDataEntryDefinitionEntity : EntityBaseModel
{
    [Required]
    [StringLength(250)]
    public required string Name { get; set; }

    [StringLength(1000)]
    public string Description { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public required string Category { get; set; }

    [StringLength(500)]
    public string FieldDefinitions { get; set; } = string.Empty;

    [StringLength(500)]
    public string ValidationRules { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public string? CreatedByUserId { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public string? UpdatedByUserId { get; set; }

    // Navigation properties
    public virtual ICollection<GenericDataEntryRecordEntity>? DataEntryRecords { get; set; }
}
