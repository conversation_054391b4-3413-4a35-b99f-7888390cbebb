using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels
{
    /// <summary>
    /// Submission audit trail entity - Epic 4 Task 3.1
    /// Submission işlemlerinin audit trail'inin tutulması
    /// </summary>
    public class SubmissionAuditEntity : EntityBaseModel
    {
        /// <summary>
        /// Academic submission AutoIncrement ID'si
        /// </summary>
        public int AcademicSubmissionAutoIncrementId { get; set; }

        /// <summary>
        /// Academic submission entity navigation property
        /// </summary>
        public virtual AcademicSubmissionEntity? AcademicSubmission { get; set; }

        /// <summary>
        /// Yapılan action (Created, Submitted, Approved, Rejected, FileUploaded, etc.)
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// Action'ı gerçekleştiren kullanıcı ID'si
        /// </summary>
        [Required]
        [StringLength(255)]
        public string PerformedByUserId { get; set; } = string.Empty;

        /// <summary>
        /// Action'ı gerçekleştiren kullanıcının rolü
        /// </summary>
        [StringLength(100)]
        public string? PerformedByUserRole { get; set; }

        /// <summary>
        /// Action'ı gerçekleştiren kullanıcının adı
        /// </summary>
        [StringLength(255)]
        public string? PerformedByUserName { get; set; }

        /// <summary>
        /// Action açıklaması/yorumları
        /// </summary>
        [StringLength(2000)]
        public string? Comments { get; set; }

        /// <summary>
        /// Action'ın gerçekleştirildiği tarih
        /// </summary>
        public DateTime PerformedAt { get; set; }

        /// <summary>
        /// Eski değer (status change için)
        /// </summary>
        [StringLength(500)]
        public string? OldValue { get; set; }

        /// <summary>
        /// Yeni değer (status change için)
        /// </summary>
        [StringLength(500)]
        public string? NewValue { get; set; }

        /// <summary>
        /// İlgili entity tipi (Submission, EvidenceFile, CriterionData, etc.)
        /// </summary>
        [StringLength(100)]
        public string? EntityType { get; set; }

        /// <summary>
        /// İlgili entity ID'si
        /// </summary>
        [StringLength(255)]
        public string? EntityId { get; set; }

        /// <summary>
        /// Action kategorisi (StatusChange, FileOperation, DataInput, etc.)
        /// </summary>
        [StringLength(100)]
        public string? Category { get; set; }

        /// <summary>
        /// Action'ın gerçekleştirildiği IP adresi
        /// </summary>
        [StringLength(45)] // IPv6 için yeterli
        public string? IpAddress { get; set; }

        /// <summary>
        /// User agent bilgisi
        /// </summary>
        [StringLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// Ek metadata (JSON format)
        /// </summary>
        public string? Metadata { get; set; }

        /// <summary>
        /// Action'ın başarılı olup olmadığı
        /// </summary>
        public bool IsSuccessful { get; set; } = true;

        /// <summary>
        /// Hata mesajı (başarısız action'lar için)
        /// </summary>
        [StringLength(1000)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Action'ın süresi (milliseconds)
        /// </summary>
        public long? DurationMs { get; set; }

        /// <summary>
        /// Session ID'si
        /// </summary>
        [StringLength(255)]
        public string? SessionId { get; set; }

        /// <summary>
        /// Request ID'si (correlation için)
        /// </summary>
        [StringLength(255)]
        public string? RequestId { get; set; }
    }

    /// <summary>
    /// Audit action constants
    /// </summary>
    public static class AuditActions
    {
        // Submission lifecycle
        public const string SubmissionCreated = "SubmissionCreated";
        public const string SubmissionSubmitted = "SubmissionSubmitted";
        public const string SubmissionApproved = "SubmissionApproved";
        public const string SubmissionRejected = "SubmissionRejected";
        public const string SubmissionReturned = "SubmissionReturned";
        public const string SubmissionCancelled = "SubmissionCancelled";
        public const string SubmissionDeleted = "SubmissionDeleted";

        // Review process
        public const string ReviewStarted = "ReviewStarted";
        public const string ReviewCompleted = "ReviewCompleted";
        public const string ReviewAssigned = "ReviewAssigned";
        public const string ReviewUnassigned = "ReviewUnassigned";

        // File operations
        public const string FileUploaded = "FileUploaded";
        public const string FileDeleted = "FileDeleted";
        public const string FileDownloaded = "FileDownloaded";
        public const string FileReplaced = "FileReplaced";

        // Data operations
        public const string CriterionDataInputted = "CriterionDataInputted";
        public const string CriterionDataUpdated = "CriterionDataUpdated";
        public const string CriterionDataDeleted = "CriterionDataDeleted";

        // Access operations
        public const string SubmissionViewed = "SubmissionViewed";
        public const string DashboardAccessed = "DashboardAccessed";
        public const string ReportGenerated = "ReportGenerated";

        // System operations
        public const string SystemBackup = "SystemBackup";
        public const string SystemMaintenance = "SystemMaintenance";
        public const string ConfigurationChanged = "ConfigurationChanged";
    }

    /// <summary>
    /// Audit categories
    /// </summary>
    public static class AuditCategories
    {
        public const string StatusChange = "StatusChange";
        public const string FileOperation = "FileOperation";
        public const string DataInput = "DataInput";
        public const string AccessControl = "AccessControl";
        public const string SystemOperation = "SystemOperation";
        public const string UserAction = "UserAction";
        public const string SecurityEvent = "SecurityEvent";
    }

    /// <summary>
    /// Entity types for audit
    /// </summary>
    public static class AuditEntityTypes
    {
        public const string Submission = "Submission";
        public const string EvidenceFile = "EvidenceFile";
        public const string CriterionData = "CriterionData";
        public const string Form = "Form";
        public const string User = "User";
        public const string System = "System";
    }
}
