namespace AcademicPerformance.Models.Configurations;

/// <summary>
/// Database performance configuration settings
/// </summary>
public class DatabasePerformanceConfiguration
{
    /// <summary>
    /// Enable query logging for debugging
    /// </summary>
    public bool EnableQueryLogging { get; set; } = false;

    /// <summary>
    /// Enable sensitive data logging (only for development)
    /// </summary>
    public bool EnableSensitiveDataLogging { get; set; } = false;

    /// <summary>
    /// Query timeout in seconds
    /// </summary>
    public int QueryTimeout { get; set; } = 120;

    /// <summary>
    /// Batch size for bulk operations
    /// </summary>
    public int BatchSize { get; set; } = 1000;

    /// <summary>
    /// Maximum retry count for failed operations
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// Maximum retry delay
    /// </summary>
    public TimeSpan MaxRetryDelay { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Enable service provider caching
    /// </summary>
    public bool EnableServiceProviderCaching { get; set; } = true;

    /// <summary>
    /// Enable query splitting for better performance
    /// </summary>
    public bool EnableQuerySplitting { get; set; } = true;

    /// <summary>
    /// Default tracking behavior (TrackAll, NoTracking, NoTrackingWithIdentityResolution)
    /// </summary>
    public string TrackingBehavior { get; set; } = "NoTracking";
}

/// <summary>
/// Pagination configuration settings
/// </summary>
public class PaginationConfiguration
{
    /// <summary>
    /// Default page size
    /// </summary>
    public int DefaultPageSize { get; set; } = 20;

    /// <summary>
    /// Maximum allowed page size
    /// </summary>
    public int MaxPageSize { get; set; } = 100;

    /// <summary>
    /// Enable total count optimization for large datasets
    /// </summary>
    public bool EnableTotalCountOptimization { get; set; } = true;

    /// <summary>
    /// Cache paged results
    /// </summary>
    public bool CachePagedResults { get; set; } = true;

    /// <summary>
    /// Cache expiration time for paged results
    /// </summary>
    public TimeSpan CacheExpiration { get; set; } = TimeSpan.FromMinutes(15);
}

/// <summary>
/// Redis cache configuration settings
/// </summary>
public class RedisCacheConfiguration
{
    /// <summary>
    /// Redis connection string
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Default expiration time
    /// </summary>
    public TimeSpan DefaultExpiration { get; set; } = TimeSpan.FromHours(1);

    /// <summary>
    /// Sliding expiration time
    /// </summary>
    public TimeSpan SlidingExpiration { get; set; } = TimeSpan.FromMinutes(30);

    /// <summary>
    /// Absolute expiration time
    /// </summary>
    public TimeSpan AbsoluteExpiration { get; set; } = TimeSpan.FromHours(24);
}
