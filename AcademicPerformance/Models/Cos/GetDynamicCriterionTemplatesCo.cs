namespace AcademicPerformance.Models.Cos
{
    /// <summary>
    /// Dinamik kriter şablonları için arama kriterleri
    /// </summary>
    public class GetDynamicCriterionTemplatesCo
    {
        /// <summary>
        /// Şablon adında arama yapılacak metin
        /// </summary>
        public string? NameContains { get; set; }

        /// <summary>
        /// Şablon durumu (Draft, Active, Inactive)
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Oluşturan kullanıcı ID'si
        /// </summary>
        public string? CreatedByUserId { get; set; }

        /// <summary>
        /// Güncelleyen kullanıcı ID'si
        /// </summary>
        public string? UpdatedByUserId { get; set; }

        /// <summary>
        /// Başlangıç tarihi (CreatedAt >= bu tarih)
        /// </summary>
        public DateTime? CreatedAfter { get; set; }

        /// <summary>
        /// Bitiş tarihi (CreatedAt <= bu tarih)
        /// </summary>
        public DateTime? CreatedBefore { get; set; }

        /// <summary>
        /// Güncelleme başlangıç tarihi (UpdatedAt >= bu tarih)
        /// </summary>
        public DateTime? UpdatedAfter { get; set; }

        /// <summary>
        /// Güncelleme bitiş tarihi (UpdatedAt <= bu tarih)
        /// </summary>
        public DateTime? UpdatedBefore { get; set; }

        /// <summary>
        /// Sadece aktif şablonları getir
        /// </summary>
        public bool? OnlyActive { get; set; }

        /// <summary>
        /// Sadece taslak şablonları getir
        /// </summary>
        public bool? OnlyDrafts { get; set; }

        /// <summary>
        /// Sadece inaktif şablonları getir
        /// </summary>
        public bool? OnlyInactive { get; set; }

        /// <summary>
        /// Tag'lerde arama yapılacak metin
        /// </summary>
        public string? TagContains { get; set; }

        /// <summary>
        /// Belirli tag'lere sahip şablonları getir
        /// </summary>
        public List<string>? Tags { get; set; }

        /// <summary>
        /// Minimum katsayı değeri
        /// </summary>
        public double? MinCoefficient { get; set; }

        /// <summary>
        /// Maksimum katsayı değeri
        /// </summary>
        public double? MaxCoefficient { get; set; }

        /// <summary>
        /// Minimum limit değeri
        /// </summary>
        public int? MinLimit { get; set; }

        /// <summary>
        /// Maksimum limit değeri
        /// </summary>
        public int? MaxLimit { get; set; }

        /// <summary>
        /// Belirli versiyon numarası
        /// </summary>
        public int? Version { get; set; }
    }
}
