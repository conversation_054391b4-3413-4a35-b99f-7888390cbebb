namespace AcademicPerformance.Models.Cos
{
    /// <summary>
    /// Akademik submission'lar için arama kriterleri
    /// </summary>
    public class GetAcademicSubmissionsCo
    {
        /// <summary>
        /// Submission durumu (Draft, Submitted, UnderReview, Approved, Rejected)
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Form ID'si
        /// </summary>
        public string? FormId { get; set; }

        /// <summary>
        /// Form adında arama yapılacak metin
        /// </summary>
        public string? FormNameContains { get; set; }

        /// <summary>
        /// Başlangıç tarihi (CreatedAt >= bu tarih)
        /// </summary>
        public DateTime? CreatedAfter { get; set; }

        /// <summary>
        /// Bitiş tarihi (CreatedAt <= bu tarih)
        /// </summary>
        public DateTime? CreatedBefore { get; set; }

        /// <summary>
        /// Güncelleme ba<PERSON><PERSON><PERSON><PERSON> tarihi (UpdatedAt >= bu tarih)
        /// </summary>
        public DateTime? UpdatedAfter { get; set; }

        /// <summary>
        /// Güncelleme bitiş tarihi (UpdatedAt <= bu tarih)
        /// </summary>
        public DateTime? UpdatedBefore { get; set; }

        /// <summary>
        /// Submission başlangıç tarihi (SubmittedAt >= bu tarih)
        /// </summary>
        public DateTime? SubmittedAfter { get; set; }

        /// <summary>
        /// Submission bitiş tarihi (SubmittedAt <= bu tarih)
        /// </summary>
        public DateTime? SubmittedBefore { get; set; }

        /// <summary>
        /// Sadece taslak submission'ları getir
        /// </summary>
        public bool? OnlyDrafts { get; set; }

        /// <summary>
        /// Sadece teslim edilmiş submission'ları getir
        /// </summary>
        public bool? OnlySubmitted { get; set; }

        /// <summary>
        /// Sadece onaylanmış submission'ları getir
        /// </summary>
        public bool? OnlyApproved { get; set; }

        /// <summary>
        /// Sadece reddedilmiş submission'ları getir
        /// </summary>
        public bool? OnlyRejected { get; set; }

        /// <summary>
        /// Son teslim tarihi yaklaşan submission'lar (X gün içinde)
        /// </summary>
        public int? DeadlineWithinDays { get; set; }

        /// <summary>
        /// Belirli durumları getir
        /// </summary>
        public List<string>? Statuses { get; set; }

        /// <summary>
        /// Belirli form ID'lerini getir
        /// </summary>
        public List<string>? FormIds { get; set; }
    }
}
