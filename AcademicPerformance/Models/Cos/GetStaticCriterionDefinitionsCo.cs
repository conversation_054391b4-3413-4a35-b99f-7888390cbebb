namespace AcademicPerformance.Models.Cos
{
    /// <summary>
    /// Statik kriter tanımları için arama kriterleri
    /// </summary>
    public class GetStaticCriterionDefinitionsCo
    {
        /// <summary>
        /// Kriter adında arama yapılacak metin
        /// </summary>
        public string? NameContains { get; set; }

        /// <summary>
        /// Sistem ID'sinde arama yapılacak metin
        /// </summary>
        public string? SystemIdContains { get; set; }

        /// <summary>
        /// Açıklamada arama yapılacak metin
        /// </summary>
        public string? DescriptionContains { get; set; }

        /// <summary>
        /// Veri tipi (Integer, Decimal, Boolean, String, Date)
        /// </summary>
        public string? DataType { get; set; }

        /// <summary>
        /// Veri kaynağı ipucunda arama yapılacak metin
        /// </summary>
        public string? DataSourceHintContains { get; set; }

        /// <summary>
        /// Hesaplama mantığında arama yapılacak metin
        /// </summary>
        public string? CalculationLogicContains { get; set; }

        /// <summary>
        /// Sadece aktif kriterleri getir
        /// </summary>
        public bool? OnlyActive { get; set; }

        /// <summary>
        /// Sadece inaktif kriterleri getir
        /// </summary>
        public bool? OnlyInactive { get; set; }

        /// <summary>
        /// Aktiflik durumu (true: aktif, false: inaktif, null: hepsi)
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Belirli veri tiplerini getir
        /// </summary>
        public List<string>? DataTypes { get; set; }

        /// <summary>
        /// Belirli sistem ID'lerini getir
        /// </summary>
        public List<string>? SystemIds { get; set; }

        /// <summary>
        /// Hesaplama mantığı olan kriterleri getir
        /// </summary>
        public bool? HasCalculationLogic { get; set; }

        /// <summary>
        /// Veri kaynağı ipucu olan kriterleri getir
        /// </summary>
        public bool? HasDataSourceHint { get; set; }

        /// <summary>
        /// Lokalizasyon kültürü (varsayılan: "en")
        /// </summary>
        public string? Culture { get; set; } = "en";
    }
}
