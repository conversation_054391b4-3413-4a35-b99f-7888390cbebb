using AcademicPerformance.Models.Dtos;
namespace AcademicPerformance.Models.Cos
{
    public class UserContextCo
    {
        public string UserId { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public UserDepartmentDto[] Departments { get; set; } = Array.Empty<UserDepartmentDto>();
        public string AcademicCadre { get; set; } = string.Empty;
    }
}
