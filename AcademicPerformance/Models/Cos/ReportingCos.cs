using System.ComponentModel.DataAnnotations;
using Rlx.Shared.Models.Cos;
using AcademicPerformance.Models.Dtos;

namespace AcademicPerformance.Models.Cos
{
    /// <summary>
    /// Performans raporu filtreleme kriterleri
    /// </summary>
    public class PerformanceReportFilterCo
    {
        /// <summary>
        /// Akademisyen kullanıcı ID'si (belirli bir akademisyen için)
        /// </summary>
        public string? AcademicianUserId { get; set; }

        /// <summary>
        /// Bölüm ID'si
        /// </summary>
        public string? DepartmentId { get; set; }

        /// <summary>
        /// Fakülte ID'si
        /// </summary>
        public string? FacultyId { get; set; }

        /// <summary>
        /// Akademik kadro (Prof, Doç, Dr.Öğr.Üyesi, vb.)
        /// </summary>
        public string? AcademicCadre { get; set; }

        /// <summary>
        /// <PERSON>or başlangıç tarihi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Rapor bitiş tarihi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Performans seviyesi filtresi (Excellent, Good, Average, Poor)
        /// </summary>
        public string? PerformanceLevel { get; set; }

        /// <summary>
        /// Minimum performans skoru
        /// </summary>
        public double? MinScore { get; set; }

        /// <summary>
        /// Maksimum performans skoru
        /// </summary>
        public double? MaxScore { get; set; }

        /// <summary>
        /// Form tamamlanma durumu (Completed, InProgress, Pending)
        /// </summary>
        public string? CompletionStatus { get; set; }

        /// <summary>
        /// Minimum tamamlanma oranı (%)
        /// </summary>
        public double? MinCompletionRate { get; set; }

        /// <summary>
        /// Belirli form ID'si
        /// </summary>
        public string? FormId { get; set; }

        /// <summary>
        /// Belirli kategori ID'si
        /// </summary>
        public string? CategoryId { get; set; }

        /// <summary>
        /// Sıralama kriteri (Score, CompletionRate, Name, Department)
        /// </summary>
        public string SortBy { get; set; } = "Score";

        /// <summary>
        /// Sıralama yönü (asc, desc)
        /// </summary>
        public string SortDirection { get; set; } = "desc";

        /// <summary>
        /// Rapor formatı (Summary, Detailed, Export)
        /// </summary>
        public string ReportFormat { get; set; } = "Summary";
    }

    /// <summary>
    /// Bölüm raporu filtreleme kriterleri
    /// </summary>
    public class DepartmentReportFilterCo
    {
        /// <summary>
        /// Bölüm ID'si
        /// </summary>
        public string? DepartmentId { get; set; }

        /// <summary>
        /// Fakülte ID'si
        /// </summary>
        public string? FacultyId { get; set; }

        /// <summary>
        /// Rapor başlangıç tarihi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Rapor bitiş tarihi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Minimum bölüm performans skoru
        /// </summary>
        public double? MinDepartmentScore { get; set; }

        /// <summary>
        /// Minimum akademisyen sayısı
        /// </summary>
        public int? MinAcademicianCount { get; set; }

        /// <summary>
        /// Trend analizi dahil edilsin mi
        /// </summary>
        public bool IncludeTrendAnalysis { get; set; } = true;

        /// <summary>
        /// Pasif akademisyenler dahil edilsin mi
        /// </summary>
        public bool IncludeInactiveAcademicians { get; set; } = false;

        /// <summary>
        /// Top performer sayısı
        /// </summary>
        public int TopPerformerCount { get; set; } = 5;

        /// <summary>
        /// Gelişim gerektiren akademisyen sayısı
        /// </summary>
        public int ImprovementNeededCount { get; set; } = 5;

        /// <summary>
        /// Sıralama kriteri
        /// </summary>
        public string SortBy { get; set; } = "OverallScore";

        /// <summary>
        /// Sıralama yönü
        /// </summary>
        public string SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// Rapor export kriterleri
    /// </summary>
    public class ReportExportCo
    {
        /// <summary>
        /// Export formatı (PDF, Excel, CSV)
        /// </summary>
        [Required]
        public string Format { get; set; } = "PDF";

        /// <summary>
        /// Rapor türü (Performance, Department, Academician)
        /// </summary>
        [Required]
        public string ReportType { get; set; } = string.Empty;

        /// <summary>
        /// Rapor ID'si
        /// </summary>
        public string? ReportId { get; set; }

        /// <summary>
        /// Filtreleme kriterleri (JSON string)
        /// </summary>
        public string? FilterCriteria { get; set; }

        /// <summary>
        /// Dahil edilecek bölümler
        /// </summary>
        public List<string> IncludeSections { get; set; } = new();

        /// <summary>
        /// Grafik dahil edilsin mi
        /// </summary>
        public bool IncludeCharts { get; set; } = true;

        /// <summary>
        /// Detay veriler dahil edilsin mi
        /// </summary>
        public bool IncludeDetailData { get; set; } = true;

        /// <summary>
        /// Dosya adı (opsiyonel)
        /// </summary>
        public string? FileName { get; set; }
    }

    /// <summary>
    /// Kriter analiz raporu filtreleme
    /// </summary>
    public class CriterionAnalysisFilterCo
    {
        /// <summary>
        /// Kriter ID'si
        /// </summary>
        public string? CriterionId { get; set; }

        /// <summary>
        /// Kriter türü (Static, Dynamic)
        /// </summary>
        public string? CriterionType { get; set; }

        /// <summary>
        /// Kategori ID'si
        /// </summary>
        public string? CategoryId { get; set; }

        /// <summary>
        /// Bölüm ID'si
        /// </summary>
        public string? DepartmentId { get; set; }

        /// <summary>
        /// Analiz başlangıç tarihi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Analiz bitiş tarihi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Minimum tamamlanma oranı
        /// </summary>
        public double? MinCompletionRate { get; set; }

        /// <summary>
        /// Minimum ortalama skor
        /// </summary>
        public double? MinAverageScore { get; set; }

        /// <summary>
        /// İstatistiksel analiz dahil edilsin mi
        /// </summary>
        public bool IncludeStatistics { get; set; } = true;

        /// <summary>
        /// Karşılaştırmalı analiz dahil edilsin mi
        /// </summary>
        public bool IncludeComparison { get; set; } = true;
    }

    /// <summary>
    /// Trend analizi kriterleri
    /// </summary>
    public class TrendAnalysisFilterCo
    {
        /// <summary>
        /// Analiz türü (Performance, Completion, Feedback)
        /// </summary>
        [Required]
        public string AnalysisType { get; set; } = string.Empty;

        /// <summary>
        /// Zaman aralığı (Daily, Weekly, Monthly, Quarterly)
        /// </summary>
        public string TimeInterval { get; set; } = "Monthly";

        /// <summary>
        /// Başlangıç tarihi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Akademisyen ID'si (belirli akademisyen için)
        /// </summary>
        public string? AcademicianUserId { get; set; }

        /// <summary>
        /// Bölüm ID'si
        /// </summary>
        public string? DepartmentId { get; set; }

        /// <summary>
        /// Karşılaştırma dönemi dahil edilsin mi
        /// </summary>
        public bool IncludeComparison { get; set; } = true;

        /// <summary>
        /// Tahmin analizi dahil edilsin mi
        /// </summary>
        public bool IncludeForecast { get; set; } = false;
    }

    /// <summary>
    /// Rapor oluşturma komutu
    /// </summary>
    public class GenerateReportCo
    {
        /// <summary>
        /// Rapor türü
        /// </summary>
        [Required]
        public string ReportType { get; set; } = string.Empty;

        /// <summary>
        /// Rapor adı
        /// </summary>
        [Required]
        public string ReportName { get; set; } = string.Empty;

        /// <summary>
        /// Rapor açıklaması
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Filtreleme kriterleri (JSON)
        /// </summary>
        public string? FilterCriteria { get; set; }

        /// <summary>
        /// Otomatik kaydetme
        /// </summary>
        public bool AutoSave { get; set; } = true;

        /// <summary>
        /// Email ile gönderim
        /// </summary>
        public bool SendByEmail { get; set; } = false;

        /// <summary>
        /// Email adresleri
        /// </summary>
        public List<string> EmailRecipients { get; set; } = new();

        /// <summary>
        /// Zamanlı rapor (periyodik)
        /// </summary>
        public bool IsScheduled { get; set; } = false;

        /// <summary>
        /// Zamanlama bilgisi (cron expression)
        /// </summary>
        public string? ScheduleExpression { get; set; }
    }

    /// <summary>
    /// Gelişmiş rapor filtreleme kriterleri
    /// </summary>
    public class AdvancedReportFilterCo
    {
        /// <summary>
        /// Başlangıç tarihi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Pasif akademisyenleri dahil et
        /// </summary>
        public bool IncludeInactiveAcademicians { get; set; } = false;

        /// <summary>
        /// En iyi performans gösterenler sayısı
        /// </summary>
        public int TopPerformerCount { get; set; } = 5;

        /// <summary>
        /// Gelişim gerektiren akademisyenler sayısı
        /// </summary>
        public int ImprovementNeededCount { get; set; } = 5;

        /// <summary>
        /// Kriter analizi dahil et
        /// </summary>
        public bool IncludeCriterionAnalysis { get; set; } = true;

        /// <summary>
        /// Minimum performans skoru
        /// </summary>
        public double? MinPerformanceScore { get; set; }

        /// <summary>
        /// Maksimum performans skoru
        /// </summary>
        public double? MaxPerformanceScore { get; set; }

        /// <summary>
        /// Belirli kategorileri filtrele
        /// </summary>
        public List<string>? CategoryFilters { get; set; }

        /// <summary>
        /// Akademik kadro filtresi
        /// </summary>
        public List<string>? AcademicCadreFilters { get; set; }
    }

    /// <summary>
    /// Çoklu akademisyen karşılaştırma kriterleri
    /// </summary>
    public class MultiAcademicianComparisonCo
    {
        /// <summary>
        /// Karşılaştırılacak akademisyen ID'leri
        /// </summary>
        [Required]
        public List<string> AcademicianIds { get; set; } = new();

        /// <summary>
        /// Başlangıç tarihi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Karşılaştırma kategorileri
        /// </summary>
        public List<string>? ComparisonCategories { get; set; }

        /// <summary>
        /// Detaylı analiz dahil et
        /// </summary>
        public bool IncludeDetailedAnalysis { get; set; } = true;

        /// <summary>
        /// İstatistiksel analiz dahil et
        /// </summary>
        public bool IncludeStatisticalAnalysis { get; set; } = true;
    }

    /// <summary>
    /// Trend analizi kriterleri
    /// </summary>
    public class TrendAnalysisCo
    {
        /// <summary>
        /// Başlangıç tarihi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Zaman aralığı tipi (Weekly, Monthly, Quarterly, Yearly)
        /// </summary>
        public string? IntervalType { get; set; } = "Monthly";

        /// <summary>
        /// Analiz edilecek kategoriler
        /// </summary>
        public List<string>? AnalysisCategories { get; set; }

        /// <summary>
        /// Trend tahmin modeli dahil et
        /// </summary>
        public bool IncludePrediction { get; set; } = false;

        /// <summary>
        /// Karşılaştırmalı trend analizi
        /// </summary>
        public bool IncludeComparativeTrend { get; set; } = false;

        /// <summary>
        /// Anomali tespiti dahil et
        /// </summary>
        public bool IncludeAnomalyDetection { get; set; } = false;
    }

    /// <summary>
    /// İstatistiksel analiz kriterleri
    /// </summary>
    public class StatisticalAnalysisCo
    {
        /// <summary>
        /// Analiz tipi (Correlation, Regression, ANOVA, etc.)
        /// </summary>
        [Required]
        public string AnalysisType { get; set; } = string.Empty;

        /// <summary>
        /// Analiz edilecek veri seti
        /// </summary>
        public List<string>? DatasetIds { get; set; }

        /// <summary>
        /// Akademisyen ID'leri (belirli akademisyenler için analiz)
        /// </summary>
        public List<string>? AcademicianIds { get; set; }

        /// <summary>
        /// Bölüm ID'leri (belirli bölümler için analiz)
        /// </summary>
        public List<string>? DepartmentIds { get; set; }

        /// <summary>
        /// Başlangıç tarihi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Güven aralığı (0.90, 0.95, 0.99)
        /// </summary>
        public double ConfidenceLevel { get; set; } = 0.95;

        /// <summary>
        /// Analiz parametreleri
        /// </summary>
        public Dictionary<string, object>? AnalysisParameters { get; set; }
    }

    /// <summary>
    /// Dashboard veri toplama kriterleri
    /// </summary>
    public class DashboardDataCo
    {
        /// <summary>
        /// Kullanıcı ID'si
        /// </summary>
        [Required]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// Kullanıcı rolü (Admin, Academician, Manager)
        /// </summary>
        public string UserRole { get; set; } = string.Empty;

        /// <summary>
        /// Dashboard tipi (Overview, Performance, Analytics)
        /// </summary>
        public string DashboardType { get; set; } = "Overview";

        /// <summary>
        /// Zaman aralığı (Last7Days, Last30Days, LastQuarter, LastYear)
        /// </summary>
        public string TimeRange { get; set; } = "Last30Days";

        /// <summary>
        /// Dahil edilecek widget'lar
        /// </summary>
        public List<string>? IncludedWidgets { get; set; }

        /// <summary>
        /// Filtreleme kriterleri
        /// </summary>
        public Dictionary<string, object>? Filters { get; set; }

        /// <summary>
        /// Gerçek zamanlı veri dahil et
        /// </summary>
        public bool IncludeRealtimeData { get; set; } = false;
    }

    /// <summary>
    /// Performans metrikleri hesaplama kriterleri
    /// </summary>
    public class PerformanceMetricsCo
    {
        /// <summary>
        /// Metrik tipi (KPI, Benchmark, Trend, Comparison)
        /// </summary>
        [Required]
        public string MetricType { get; set; } = string.Empty;

        /// <summary>
        /// Hedef varlık ID'leri (akademisyen, bölüm, vs.)
        /// </summary>
        public List<string>? TargetIds { get; set; }

        /// <summary>
        /// Hedef varlık tipi (Academician, Department, Faculty)
        /// </summary>
        public string TargetType { get; set; } = "Academician";

        /// <summary>
        /// Başlangıç tarihi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Hesaplanacak metrikler
        /// </summary>
        public List<string>? MetricNames { get; set; }

        /// <summary>
        /// Karşılaştırma referansı (Previous Period, Benchmark, Average)
        /// </summary>
        public string? ComparisonReference { get; set; }

        /// <summary>
        /// Gruplama kriterleri
        /// </summary>
        public List<string>? GroupByFields { get; set; }
    }

    /// <summary>
    /// Gerçek zamanlı analiz kriterleri
    /// </summary>
    public class RealtimeAnalysisCo
    {
        /// <summary>
        /// Analiz tipi (LivePerformance, ActiveUsers, CurrentTrends)
        /// </summary>
        [Required]
        public string AnalysisType { get; set; } = string.Empty;

        /// <summary>
        /// Hedef varlık ID'leri
        /// </summary>
        public List<string>? TargetIds { get; set; }

        /// <summary>
        /// Analiz penceresi (dakika cinsinden)
        /// </summary>
        public int AnalysisWindowMinutes { get; set; } = 60;

        /// <summary>
        /// Güncelleme sıklığı (saniye cinsinden)
        /// </summary>
        public int RefreshIntervalSeconds { get; set; } = 30;

        /// <summary>
        /// Eşik değerleri
        /// </summary>
        public Dictionary<string, double>? ThresholdValues { get; set; }

        /// <summary>
        /// Alarm koşulları
        /// </summary>
        public List<string>? AlertConditions { get; set; }
    }

    /// <summary>
    /// Batch analiz kriterleri
    /// </summary>
    public class BatchAnalysisCo
    {
        /// <summary>
        /// İş ID'si
        /// </summary>
        public string JobId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Analiz tipi (FullDatasetAnalysis, HistoricalTrend, PredictiveAnalysis)
        /// </summary>
        [Required]
        public string AnalysisType { get; set; } = string.Empty;

        /// <summary>
        /// Veri seti boyutu sınırı
        /// </summary>
        public int? DatasetSizeLimit { get; set; }

        /// <summary>
        /// İşlem önceliği (Low, Normal, High)
        /// </summary>
        public string Priority { get; set; } = "Normal";

        /// <summary>
        /// Başlangıç tarihi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Hedef varlık ID'leri
        /// </summary>
        public List<string>? TargetIds { get; set; }

        /// <summary>
        /// Analiz parametreleri
        /// </summary>
        public Dictionary<string, object>? AnalysisParameters { get; set; }

        /// <summary>
        /// Sonuç bildirim ayarları
        /// </summary>
        public NotificationSettingsDto? NotificationSettings { get; set; }
    }

    /// <summary>
    /// Bildirim ayarları DTO
    /// </summary>
    public class NotificationSettingsDto
    {
        /// <summary>
        /// E-posta bildirimi gönder
        /// </summary>
        public bool SendEmailNotification { get; set; } = false;

        /// <summary>
        /// E-posta adresleri
        /// </summary>
        public List<string>? EmailAddresses { get; set; }

        /// <summary>
        /// Webhook URL'si
        /// </summary>
        public string? WebhookUrl { get; set; }

        /// <summary>
        /// Bildirim mesajı şablonu
        /// </summary>
        public string? MessageTemplate { get; set; }
    }

    /// <summary>
    /// PDF export kriterleri
    /// </summary>
    public class PdfExportCo
    {
        /// <summary>
        /// Rapor tipi
        /// </summary>
        [Required]
        public string ReportType { get; set; } = string.Empty;

        /// <summary>
        /// Rapor verileri
        /// </summary>
        public Dictionary<string, object>? ReportData { get; set; }

        /// <summary>
        /// Template ID'si
        /// </summary>
        public string? TemplateId { get; set; }

        /// <summary>
        /// PDF ayarları
        /// </summary>
        public PdfSettingsDto? PdfSettings { get; set; }

        /// <summary>
        /// Sayfa yönlendirmesi (Portrait, Landscape)
        /// </summary>
        public string Orientation { get; set; } = "Portrait";

        /// <summary>
        /// Sayfa boyutu (A4, A3, Letter)
        /// </summary>
        public string PageSize { get; set; } = "A4";

        /// <summary>
        /// Header dahil et
        /// </summary>
        public bool IncludeHeader { get; set; } = true;

        /// <summary>
        /// Footer dahil et
        /// </summary>
        public bool IncludeFooter { get; set; } = true;

        /// <summary>
        /// Watermark metni
        /// </summary>
        public string? WatermarkText { get; set; }
    }

    /// <summary>
    /// Excel export kriterleri
    /// </summary>
    public class ExcelExportCo
    {
        /// <summary>
        /// Rapor tipi
        /// </summary>
        [Required]
        public string ReportType { get; set; } = string.Empty;

        /// <summary>
        /// Rapor verileri
        /// </summary>
        public Dictionary<string, object>? ReportData { get; set; }

        /// <summary>
        /// Dahil edilecek sheet'ler
        /// </summary>
        public List<string>? IncludeSheets { get; set; }

        /// <summary>
        /// Excel formatı (xlsx, xls)
        /// </summary>
        public string Format { get; set; } = "xlsx";

        /// <summary>
        /// Grafikleri dahil et
        /// </summary>
        public bool IncludeCharts { get; set; } = true;

        /// <summary>
        /// Formülleri dahil et
        /// </summary>
        public bool IncludeFormulas { get; set; } = false;

        /// <summary>
        /// Stil ayarları
        /// </summary>
        public ExcelStyleSettingsDto? StyleSettings { get; set; }

        /// <summary>
        /// Pivot table'ları dahil et
        /// </summary>
        public bool IncludePivotTables { get; set; } = false;
    }

    /// <summary>
    /// CSV export kriterleri
    /// </summary>
    public class CsvExportCo
    {
        /// <summary>
        /// Veri tipi
        /// </summary>
        [Required]
        public string DataType { get; set; } = string.Empty;

        /// <summary>
        /// Export edilecek veriler
        /// </summary>
        public Dictionary<string, object>? Data { get; set; }

        /// <summary>
        /// Sütun ayırıcısı
        /// </summary>
        public string Delimiter { get; set; } = ",";

        /// <summary>
        /// Encoding (UTF-8, UTF-16, ASCII)
        /// </summary>
        public string Encoding { get; set; } = "UTF-8";

        /// <summary>
        /// Header satırını dahil et
        /// </summary>
        public bool IncludeHeaders { get; set; } = true;

        /// <summary>
        /// Dahil edilecek sütunlar
        /// </summary>
        public List<string>? IncludeColumns { get; set; }

        /// <summary>
        /// Hariç tutulacak sütunlar
        /// </summary>
        public List<string>? ExcludeColumns { get; set; }

        /// <summary>
        /// Tarih formatı
        /// </summary>
        public string DateFormat { get; set; } = "yyyy-MM-dd";

        /// <summary>
        /// Sayı formatı
        /// </summary>
        public string NumberFormat { get; set; } = "0.00";
    }

    /// <summary>
    /// Template export kriterleri
    /// </summary>
    public class TemplateExportCo
    {
        /// <summary>
        /// Template ID'si
        /// </summary>
        [Required]
        public string TemplateId { get; set; } = string.Empty;

        /// <summary>
        /// Template verileri
        /// </summary>
        public Dictionary<string, object>? TemplateData { get; set; }

        /// <summary>
        /// Çıktı formatı (PDF, DOCX, HTML)
        /// </summary>
        public string OutputFormat { get; set; } = "PDF";

        /// <summary>
        /// Template parametreleri
        /// </summary>
        public Dictionary<string, object>? Parameters { get; set; }

        /// <summary>
        /// Lokalizasyon ayarları
        /// </summary>
        public string? Locale { get; set; }

        /// <summary>
        /// Custom CSS (HTML output için)
        /// </summary>
        public string? CustomCss { get; set; }
    }

    /// <summary>
    /// Bulk export kriterleri
    /// </summary>
    public class BulkExportCo
    {
        /// <summary>
        /// Export istekleri
        /// </summary>
        [Required]
        public List<BulkExportRequestDto> ExportRequests { get; set; } = new();

        /// <summary>
        /// Zip dosyası oluştur
        /// </summary>
        public bool CreateZipFile { get; set; } = true;

        /// <summary>
        /// Zip dosya adı
        /// </summary>
        public string? ZipFileName { get; set; }

        /// <summary>
        /// Paralel işlem sayısı
        /// </summary>
        public int MaxParallelism { get; set; } = 3;

        /// <summary>
        /// Bildirim ayarları
        /// </summary>
        public NotificationSettingsDto? NotificationSettings { get; set; }
    }

    /// <summary>
    /// PDF ayarları DTO
    /// </summary>
    public class PdfSettingsDto
    {
        /// <summary>
        /// Margin ayarları (mm)
        /// </summary>
        public MarginSettingsDto? Margins { get; set; }

        /// <summary>
        /// Font ayarları
        /// </summary>
        public FontSettingsDto? FontSettings { get; set; }

        /// <summary>
        /// Sayfa numarası göster
        /// </summary>
        public bool ShowPageNumbers { get; set; } = true;

        /// <summary>
        /// İçindekiler tablosu oluştur
        /// </summary>
        public bool GenerateTableOfContents { get; set; } = false;
    }

    /// <summary>
    /// Excel stil ayarları DTO
    /// </summary>
    public class ExcelStyleSettingsDto
    {
        /// <summary>
        /// Header stil ayarları
        /// </summary>
        public CellStyleDto? HeaderStyle { get; set; }

        /// <summary>
        /// Veri stil ayarları
        /// </summary>
        public CellStyleDto? DataStyle { get; set; }

        /// <summary>
        /// Alternatif satır rengi
        /// </summary>
        public string? AlternateRowColor { get; set; }

        /// <summary>
        /// Otomatik sütun genişliği
        /// </summary>
        public bool AutoFitColumns { get; set; } = true;
    }

    /// <summary>
    /// Bulk export isteği DTO
    /// </summary>
    public class BulkExportRequestDto
    {
        /// <summary>
        /// Export tipi (PDF, Excel, CSV)
        /// </summary>
        public string ExportType { get; set; } = string.Empty;

        /// <summary>
        /// Rapor tipi
        /// </summary>
        public string ReportType { get; set; } = string.Empty;

        /// <summary>
        /// Export parametreleri
        /// </summary>
        public Dictionary<string, object>? Parameters { get; set; }

        /// <summary>
        /// Dosya adı
        /// </summary>
        public string? FileName { get; set; }
    }
}
