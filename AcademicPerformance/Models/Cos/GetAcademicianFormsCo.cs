namespace AcademicPerformance.Models.Cos
{
    /// <summary>
    /// Akademisyene atanmış formları getirmek için criteria object
    /// </summary>
    public class GetAcademicianFormsCo
    {
        /// <summary>
        /// Form adında arama yapılacak metin
        /// </summary>
        public string? NameContains { get; set; }

        /// <summary>
        /// Form durumu (Draft, Active, Archived)
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Submission durumu (NotStarted, InProgress, Submitted, Approved)
        /// </summary>
        public string? SubmissionStatus { get; set; }

        /// <summary>
        /// Sadece deadline'ı yaklaşan formları getir
        /// </summary>
        public bool? OnlyApproachingDeadlines { get; set; }

        /// <summary>
        /// Sadece aktif formları getir
        /// </summary>
        public bool? OnlyActive { get; set; }

        /// <summary>
        /// Sadece taslak formları getir
        /// </summary>
        public bool? OnlyDrafts { get; set; }

        /// <summary>
        /// Deadline başlangıç tarihi (SubmissionDeadline >= bu tarih)
        /// </summary>
        public DateTime? DeadlineAfter { get; set; }

        /// <summary>
        /// Deadline bitiş tarihi (SubmissionDeadline <= bu tarih)
        /// </summary>
        public DateTime? DeadlineBefore { get; set; }
    }
}
