using Microsoft.EntityFrameworkCore;
using AcademicPerformance.Models.Configurations;
using AcademicPerformance.Services.Interfaces;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;

namespace AcademicPerformance.Extensions;

/// <summary>
/// Extension methods for bulk database operations
/// </summary>
public static class BulkOperationExtensions
{
    /// <summary>
    /// Bulk insert entities with optimized performance
    /// </summary>
    public static async Task BulkInsertAsync<T>(this DbContext context, IEnumerable<T> entities,
        int? batchSize = null, CancellationToken cancellationToken = default) where T : class
    {
        // Get service provider from context - this requires dependency injection setup
        var performanceConfig = new DatabasePerformanceConfiguration(); // Use default config for now

        var effectiveBatchSize = batchSize ?? performanceConfig.BatchSize;
        var entityList = entities.ToList();

        if (!entityList.Any()) return;

        // Disable change tracking for better performance
        var originalTrackingBehavior = context.ChangeTracker.QueryTrackingBehavior;
        context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

        try
        {
            for (int i = 0; i < entityList.Count; i += effectiveBatchSize)
            {
                var batch = entityList.Skip(i).Take(effectiveBatchSize);
                context.Set<T>().AddRange(batch);
                await context.SaveChangesAsync(cancellationToken);
                context.ChangeTracker.Clear(); // Clear to free memory
            }
        }
        finally
        {
            context.ChangeTracker.QueryTrackingBehavior = originalTrackingBehavior;
        }
    }

    /// <summary>
    /// Bulk update entities with optimized performance
    /// </summary>
    public static async Task BulkUpdateAsync<T>(this DbContext context, IEnumerable<T> entities,
        int? batchSize = null, CancellationToken cancellationToken = default) where T : class
    {
        var performanceConfig = new DatabasePerformanceConfiguration(); // Use default config

        var effectiveBatchSize = batchSize ?? performanceConfig.BatchSize;
        var entityList = entities.ToList();

        if (!entityList.Any()) return;

        for (int i = 0; i < entityList.Count; i += effectiveBatchSize)
        {
            var batch = entityList.Skip(i).Take(effectiveBatchSize);
            context.Set<T>().UpdateRange(batch);
            await context.SaveChangesAsync(cancellationToken);
            context.ChangeTracker.Clear(); // Clear to free memory
        }
    }

    /// <summary>
    /// Bulk delete entities with optimized performance
    /// </summary>
    public static async Task BulkDeleteAsync<T>(this DbContext context, IEnumerable<T> entities,
        int? batchSize = null, CancellationToken cancellationToken = default) where T : class
    {
        var performanceConfig = new DatabasePerformanceConfiguration(); // Use default config

        var effectiveBatchSize = batchSize ?? performanceConfig.BatchSize;
        var entityList = entities.ToList();

        if (!entityList.Any()) return;

        for (int i = 0; i < entityList.Count; i += effectiveBatchSize)
        {
            var batch = entityList.Skip(i).Take(effectiveBatchSize);
            context.Set<T>().RemoveRange(batch);
            await context.SaveChangesAsync(cancellationToken);
            context.ChangeTracker.Clear(); // Clear to free memory
        }
    }

    /// <summary>
    /// Execute query with performance monitoring
    /// </summary>
    public static async Task<List<T>> ExecuteWithMonitoringAsync<T>(this IQueryable<T> query,
        string queryName, IServiceProvider serviceProvider, CancellationToken cancellationToken = default)
    {
        var performanceService = serviceProvider.GetService<IPerformanceMonitoringService>();

        if (performanceService == null)
        {
            return await query.ToListAsync(cancellationToken);
        }

        using var measurement = performanceService.StartMeasurement(queryName);
        return await query.ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Execute single result query with performance monitoring
    /// </summary>
    public static async Task<T?> ExecuteSingleWithMonitoringAsync<T>(this IQueryable<T> query,
        string queryName, IServiceProvider serviceProvider, CancellationToken cancellationToken = default)
    {
        var performanceService = serviceProvider.GetService<IPerformanceMonitoringService>();

        if (performanceService == null)
        {
            return await query.FirstOrDefaultAsync(cancellationToken);
        }

        using var measurement = performanceService.StartMeasurement(queryName);
        return await query.FirstOrDefaultAsync(cancellationToken);
    }

    /// <summary>
    /// Execute count query with performance monitoring
    /// </summary>
    public static async Task<int> ExecuteCountWithMonitoringAsync<T>(this IQueryable<T> query,
        string queryName, IServiceProvider serviceProvider, CancellationToken cancellationToken = default)
    {
        var performanceService = serviceProvider.GetService<IPerformanceMonitoringService>();

        if (performanceService == null)
        {
            return await query.CountAsync(cancellationToken);
        }

        using var measurement = performanceService.StartMeasurement($"{queryName}_Count");
        return await query.CountAsync(cancellationToken);
    }

    /// <summary>
    /// Execute any query with performance monitoring
    /// </summary>
    public static async Task<bool> ExecuteAnyWithMonitoringAsync<T>(this IQueryable<T> query,
        string queryName, IServiceProvider serviceProvider, CancellationToken cancellationToken = default)
    {
        var performanceService = serviceProvider.GetService<IPerformanceMonitoringService>();

        if (performanceService == null)
        {
            return await query.AnyAsync(cancellationToken);
        }

        using var measurement = performanceService.StartMeasurement($"{queryName}_Any");
        return await query.AnyAsync(cancellationToken);
    }

    /// <summary>
    /// Optimize query for large datasets
    /// </summary>
    public static IQueryable<T> OptimizeForLargeDataset<T>(this IQueryable<T> query) where T : class
    {
        return query
            .AsNoTracking() // Disable change tracking
            .AsSplitQuery(); // Use split queries for better performance with includes
    }

    /// <summary>
    /// Add pagination with performance optimization
    /// </summary>
    public static IQueryable<T> AddPagination<T>(this IQueryable<T> query, int pageNumber, int pageSize,
        int maxPageSize = 100) where T : class
    {
        // Ensure page size doesn't exceed maximum
        var effectivePageSize = Math.Min(pageSize, maxPageSize);
        var skip = Math.Max(0, (pageNumber - 1) * effectivePageSize);

        return query
            .Skip(skip)
            .Take(effectivePageSize);
    }

    /// <summary>
    /// Get optimized count for pagination
    /// </summary>
    public static async Task<int> GetOptimizedCountAsync<T>(this IQueryable<T> query,
        bool enableOptimization = true, CancellationToken cancellationToken = default)
    {
        if (!enableOptimization)
        {
            return await query.CountAsync(cancellationToken);
        }

        // For large datasets, use approximate count or limit the count
        const int maxCountThreshold = 10000;

        // First check if we have more than threshold
        var hasMoreThanThreshold = await query.Take(maxCountThreshold + 1).CountAsync(cancellationToken);

        if (hasMoreThanThreshold > maxCountThreshold)
        {
            // Return approximate count for very large datasets
            return maxCountThreshold;
        }

        return hasMoreThanThreshold;
    }
}
