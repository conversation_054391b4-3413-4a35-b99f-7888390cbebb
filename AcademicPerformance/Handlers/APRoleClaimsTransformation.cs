using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Rlx.Shared.Interfaces;
using Microsoft.Extensions.Logging;
using AcademicPerformance.Consts;

namespace AcademicPerformance.Handlers;

/// <summary>
/// AcademicPerformance projesi için özel RoleClaimsTransformation
/// Super admin kullanıcılarına tüm permission'ları otomatik olarak verir
/// </summary>
public class APRoleClaimsTransformation : IClaimsTransformation
{
    private readonly IRlxIdentitySharedManager _rlxIdentitySharedManager;
    private readonly ILogger<APRoleClaimsTransformation> _logger;

    // Super admin kullanıcı ID'leri (test için)
    private readonly HashSet<string> _superAdminUserIds = new()
    {
        "rlx_super_user", // Test kullanıcısı
        "admin",          // Genel admin
        "superadmin"      // Super admin
    };

    // Super admin role isimleri
    private readonly HashSet<string> _superAdminRoles = new()
    {
        "SuperAdmin",
        "Admin",
        "SystemAdmin"
    };

    public APRoleClaimsTransformation(
        IRlxIdentitySharedManager rlxIdentitySharedManager, 
        ILogger<APRoleClaimsTransformation> logger)
    {
        _logger = logger;
        _rlxIdentitySharedManager = rlxIdentitySharedManager;
    }

    public async Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
    {
        var identity = (ClaimsIdentity?)principal.Identity;
        if (identity == null) return principal;

        var userId = identity.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;
        if (userId == null) return principal;

        try
        {
            // Önce normal role claim'leri al
            var rolesClaims = await _rlxIdentitySharedManager.GetUsersRolesClaims(userId);
            if (rolesClaims != null && rolesClaims.Any())
            {
                foreach (var claim in rolesClaims)
                {
                    if (!identity.Claims.Any(c => c.Type == claim.ClaimType && c.Value == claim.ClaimValue))
                    {
                        identity.AddClaim(new Claim(claim.ClaimType!, claim.ClaimValue!));
                    }
                }
            }

            // Super admin kontrolü - User ID bazında
            if (_superAdminUserIds.Contains(userId))
            {
                _logger.LogInformation($"Super admin user detected: {userId}");
                AddSuperAdminClaims(identity);
                return principal;
            }

            // Super admin kontrolü - Role bazında
            var userRoles = identity.Claims.Where(c => c.Type == "role").Select(c => c.Value).ToList();
            if (userRoles.Any(role => _superAdminRoles.Contains(role)))
            {
                _logger.LogInformation($"Super admin role detected for user {userId}: {string.Join(", ", userRoles)}");
                AddSuperAdminClaims(identity);
                return principal;
            }

            return principal;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error in APRoleClaimsTransformation for user {userId}");
            return principal;
        }
    }

    /// <summary>
    /// Super admin kullanıcısına tüm permission claim'lerini ekler
    /// </summary>
    private void AddSuperAdminClaims(ClaimsIdentity identity)
    {
        // SuperAdmin role'ünü ekle (eğer yoksa)
        if (!identity.Claims.Any(c => c.Type == "role" && c.Value == "SuperAdmin"))
        {
            identity.AddClaim(new Claim("role", "SuperAdmin"));
        }

        // Academician role'ünü ekle (academician endpoint'leri için)
        if (!identity.Claims.Any(c => c.Type == "role" && c.Value == "Academician"))
        {
            identity.AddClaim(new Claim("role", "Academician"));
        }

        // Tüm permission'ları bypass eden claim'ler
        var superAdminClaims = new[]
        {
            // Genel bypass claim'leri
            new Claim(APConsts.PermissionActionAll, "all"),
            new Claim(APConsts.PermissionPageAll, "all"),
            new Claim(APConsts.PermissionActionAP, "all"),
            new Claim(APConsts.PermissionPageAP, "all"),
            
            // Spesifik AP action claim'leri
            new Claim(APConsts.PermissionActionAP, "managecriteria"),
            new Claim(APConsts.PermissionActionAP, "submitdata"),
            new Claim(APConsts.PermissionActionAP, "approvesubmissions"),
            new Claim(APConsts.PermissionActionAP, "manageforms"),
            new Claim(APConsts.PermissionActionAP, "viewreports"),
            new Claim(APConsts.PermissionActionAP, "inputdepartmentdata"),
            new Claim(APConsts.PermissionActionAP, "evaluatestaff"),
            new Claim(APConsts.PermissionActionAP, "verifyportfolio"),
            new Claim(APConsts.PermissionActionAP, "allaccess"),
            new Claim(APConsts.PermissionActionAP, "viewstaticdata"),
            new Claim(APConsts.PermissionActionAP, "accessreporting"),
            new Claim(APConsts.PermissionActionAP, "requireadminrole"),
            new Claim(APConsts.PermissionActionAP, "uploadfiles"),
            new Claim(APConsts.PermissionActionAP, "downloadfiles"),
            new Claim(APConsts.PermissionActionAP, "deletefiles"),
            new Claim(APConsts.PermissionActionAP, "managefiles"),
            new Claim(APConsts.PermissionActionAP, "reviewsubmissions"),
            new Claim(APConsts.PermissionActionAP, "viewcontrollerdashboard"),
            new Claim(APConsts.PermissionActionAP, "managesubmissionworkflow"),
            new Claim(APConsts.PermissionActionAP, "accesssubmissiondetails"),
            new Claim(APConsts.PermissionActionAP, "downloadevidencefiles"),
            new Claim(APConsts.PermissionActionAP, "assignsubmissions"),
            new Claim(APConsts.PermissionActionAP, "viewaudittrail"),
            new Claim(APConsts.PermissionActionAP, "managecontrollersettings"),
            
            // Page access claim'leri
            new Claim(APConsts.PermissionPageAP, "ap")
        };

        foreach (var claim in superAdminClaims)
        {
            if (!identity.Claims.Any(c => c.Type == claim.Type && c.Value == claim.Value))
            {
                identity.AddClaim(claim);
            }
        }

        _logger.LogInformation($"Added {superAdminClaims.Length} super admin claims to user identity");
    }
}
