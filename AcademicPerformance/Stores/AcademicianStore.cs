using Microsoft.EntityFrameworkCore;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;

namespace AcademicPerformance.Stores
{
    public class AcademicianStore : IAcademicianStore
    {
        private readonly AcademicPerformanceDbContext _context;

        public AcademicianStore(AcademicPerformanceDbContext context)
        {
            _context = context;
        }

        #region CRUD Operations

        public async Task<List<AcademicianProfileEntity>> GetAcademicianProfilesAsync()
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => !ap.Deleted)
                .OrderBy(ap => ap.Name)
                .ThenBy(ap => ap.Surname)
                .ToListAsync();
        }

        public async Task<AcademicianProfileEntity?> GetAcademicianProfileByIdAsync(string id)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Include(ap => ap.Submissions)
                .FirstOrDefaultAsync(ap => ap.Id == id && !ap.Deleted);
        }

        public async Task<AcademicianProfileEntity?> GetAcademicianProfileByUniversityUserIdAsync(string universityUserId)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Include(ap => ap.Submissions)
                .FirstOrDefaultAsync(ap => ap.UniversityUserId == universityUserId && !ap.Deleted);
        }

        public async Task<AcademicianProfileEntity?> GetAcademicianProfileForUpdateAsync(string id)
        {
            return await _context.AcademicianProfiles
                .FirstOrDefaultAsync(ap => ap.Id == id && !ap.Deleted);
        }

        public async Task<AcademicianProfileEntity> CreateAcademicianProfileAsync(AcademicianProfileEntity entity, bool saveChanges = true)
        {
            entity.CreatedAt = DateTime.UtcNow;
            entity.UpdatedAt = DateTime.UtcNow;
            entity.LastSyncedAt = DateTime.UtcNow;
            entity.IsActive = true;
            entity.Deleted = false;
            entity.Disabled = false;

            // FullName oluştur
            entity.FullName = $"{entity.Name} {entity.Surname}".Trim();

            _context.AcademicianProfiles.Add(entity);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return entity;
        }

        public async Task<bool> UpdateAcademicianProfileAsync(AcademicianProfileEntity entity, bool saveChanges = true)
        {
            entity.UpdatedAt = DateTime.UtcNow;

            // FullName güncelle
            entity.FullName = $"{entity.Name} {entity.Surname}".Trim();

            _context.AcademicianProfiles.Update(entity);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return true;
        }

        public async Task<bool> DeleteAcademicianProfileAsync(string id)
        {
            var entity = await _context.AcademicianProfiles.FirstOrDefaultAsync(ap => ap.Id == id);
            if (entity == null)
                return false;

            _context.AcademicianProfiles.Remove(entity);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> SoftDeleteAcademicianProfileAsync(string id)
        {
            var entity = await _context.AcademicianProfiles.FirstOrDefaultAsync(ap => ap.Id == id);
            if (entity == null)
                return false;

            entity.Deleted = true;
            entity.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Filtering and Search Operations

        public async Task<List<AcademicianProfileEntity>> GetAcademicianProfilesByDepartmentAsync(string department)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => ap.Department == department && !ap.Deleted)
                .OrderBy(ap => ap.Name)
                .ThenBy(ap => ap.Surname)
                .ToListAsync();
        }

        public async Task<List<AcademicianProfileEntity>> GetAcademicianProfilesByAcademicCadreAsync(string academicCadre)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => ap.AcademicCadre == academicCadre && !ap.Deleted)
                .OrderBy(ap => ap.Name)
                .ThenBy(ap => ap.Surname)
                .ToListAsync();
        }

        public async Task<List<AcademicianProfileEntity>> GetActiveAcademicianProfilesAsync()
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => ap.IsActive && !ap.Deleted && !ap.Disabled)
                .OrderBy(ap => ap.Name)
                .ThenBy(ap => ap.Surname)
                .ToListAsync();
        }

        public async Task<List<AcademicianProfileEntity>> GetAcademicianProfilesRequiringSyncAsync(DateTime? olderThan = null)
        {
            var cutoffDate = olderThan ?? DateTime.UtcNow.AddHours(-24);

            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => ap.LastSyncedAt < cutoffDate && ap.IsActive && !ap.Deleted)
                .OrderBy(ap => ap.LastSyncedAt)
                .ToListAsync();
        }

        public async Task<List<AcademicianProfileEntity>> SearchAcademicianProfilesAsync(string searchTerm)
        {
            var lowerSearchTerm = searchTerm.ToLower();

            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => !ap.Deleted && (
                    ap.Name.ToLower().Contains(lowerSearchTerm) ||
                    ap.Surname.ToLower().Contains(lowerSearchTerm) ||
                    (ap.FullName != null && ap.FullName.ToLower().Contains(lowerSearchTerm)) ||
                    (ap.Email != null && ap.Email.ToLower().Contains(lowerSearchTerm)) ||
                    (ap.Department != null && ap.Department.ToLower().Contains(lowerSearchTerm))
                ))
                .OrderBy(ap => ap.Name)
                .ThenBy(ap => ap.Surname)
                .ToListAsync();
        }

        #endregion

        #region Bulk Operations

        public async Task<List<AcademicianProfileEntity>> CreateAcademicianProfilesAsync(List<AcademicianProfileEntity> entities, bool saveChanges = true)
        {
            var now = DateTime.UtcNow;
            foreach (var entity in entities)
            {
                entity.CreatedAt = now;
                entity.UpdatedAt = now;
                entity.LastSyncedAt = now;
                entity.IsActive = true;
                entity.Deleted = false;
                entity.Disabled = false;
                entity.FullName = $"{entity.Name} {entity.Surname}".Trim();
            }

            _context.AcademicianProfiles.AddRange(entities);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return entities;
        }

        public async Task<bool> UpdateAcademicianProfilesAsync(List<AcademicianProfileEntity> entities, bool saveChanges = true)
        {
            var now = DateTime.UtcNow;
            foreach (var entity in entities)
            {
                entity.UpdatedAt = now;
                entity.FullName = $"{entity.Name} {entity.Surname}".Trim();
            }

            _context.AcademicianProfiles.UpdateRange(entities);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return true;
        }

        public async Task<List<AcademicianProfileEntity>> GetAcademicianProfilesByUniversityUserIdsAsync(List<string> universityUserIds)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => universityUserIds.Contains(ap.UniversityUserId) && !ap.Deleted)
                .ToListAsync();
        }

        #endregion

        #region Sync Operations

        public async Task<bool> UpdateLastSyncedAtAsync(string universityUserId, DateTime syncedAt, string? syncNotes = null)
        {
            var entity = await _context.AcademicianProfiles
                .FirstOrDefaultAsync(ap => ap.UniversityUserId == universityUserId);

            if (entity == null)
                return false;

            entity.LastSyncedAt = syncedAt;
            entity.UpdatedAt = DateTime.UtcNow;
            if (!string.IsNullOrEmpty(syncNotes))
            {
                entity.SyncNotes = syncNotes;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateLastSyncedAtBulkAsync(List<string> universityUserIds, DateTime syncedAt, string? syncNotes = null)
        {
            var entities = await _context.AcademicianProfiles
                .Where(ap => universityUserIds.Contains(ap.UniversityUserId))
                .ToListAsync();

            var now = DateTime.UtcNow;
            foreach (var entity in entities)
            {
                entity.LastSyncedAt = syncedAt;
                entity.UpdatedAt = now;
                if (!string.IsNullOrEmpty(syncNotes))
                {
                    entity.SyncNotes = syncNotes;
                }
            }

            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Dashboard Related Operations

        public async Task<List<AcademicianProfileEntity>> GetAcademicianProfilesWithSubmissionsAsync()
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Include(ap => ap.Submissions)
                .ThenInclude(s => s.EvaluationForm)
                .Where(ap => !ap.Deleted)
                .ToListAsync();
        }

        public async Task<AcademicianProfileEntity?> GetAcademicianProfileWithSubmissionsAsync(string universityUserId)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Include(ap => ap.Submissions)
                .ThenInclude(s => s.EvaluationForm)
                .FirstOrDefaultAsync(ap => ap.UniversityUserId == universityUserId && !ap.Deleted);
        }

        #endregion

        #region Statistics and Analytics

        public async Task<int> GetTotalAcademicianCountAsync()
        {
            return await _context.AcademicianProfiles
                .CountAsync(ap => !ap.Deleted);
        }

        public async Task<int> GetActiveAcademicianCountAsync()
        {
            return await _context.AcademicianProfiles
                .CountAsync(ap => ap.IsActive && !ap.Deleted && !ap.Disabled);
        }

        public async Task<Dictionary<string, int>> GetAcademicianCountByDepartmentAsync()
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => !ap.Deleted && ap.Department != null)
                .GroupBy(ap => ap.Department)
                .ToDictionaryAsync(g => g.Key!, g => g.Count());
        }

        public async Task<Dictionary<string, int>> GetAcademicianCountByAcademicCadreAsync()
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => !ap.Deleted && ap.AcademicCadre != null)
                .GroupBy(ap => ap.AcademicCadre)
                .ToDictionaryAsync(g => g.Key!, g => g.Count());
        }

        public async Task<int> GetAcademicianCountRequiringSyncAsync(DateTime? olderThan = null)
        {
            var cutoffDate = olderThan ?? DateTime.UtcNow.AddHours(-24);

            return await _context.AcademicianProfiles
                .CountAsync(ap => ap.LastSyncedAt < cutoffDate && ap.IsActive && !ap.Deleted);
        }

        #endregion

        #region Helper Methods

        public async Task<bool> AcademicianProfileExistsAsync(string id)
        {
            return await _context.AcademicianProfiles.AnyAsync(ap => ap.Id == id && !ap.Deleted);
        }

        public async Task<bool> AcademicianProfileExistsByUniversityUserIdAsync(string universityUserId)
        {
            return await _context.AcademicianProfiles.AnyAsync(ap => ap.UniversityUserId == universityUserId && !ap.Deleted);
        }

        public async Task<IDictionary<string, int>> IdConvertForAcademicianProfile(IEnumerable<string> profileIds)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => profileIds.Contains(ap.Id))
                .ToDictionaryAsync(ap => ap.Id, ap => ap.AutoIncrementId);
        }

        public async Task<IDictionary<string, string>> UniversityUserIdToProfileIdConvertAsync(IEnumerable<string> universityUserIds)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => universityUserIds.Contains(ap.UniversityUserId))
                .ToDictionaryAsync(ap => ap.UniversityUserId, ap => ap.Id);
        }

        #endregion

        #region Validation Methods

        public async Task<bool> ValidateUniversityUserIdUniqueAsync(string universityUserId, string? excludeProfileId = null)
        {
            var query = _context.AcademicianProfiles
                .Where(ap => ap.UniversityUserId == universityUserId && !ap.Deleted);

            if (!string.IsNullOrEmpty(excludeProfileId))
            {
                query = query.Where(ap => ap.Id != excludeProfileId);
            }

            return !await query.AnyAsync();
        }

        public async Task<List<string>> GetDuplicateUniversityUserIdsAsync(List<string> universityUserIds)
        {
            var existingUserIds = await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => universityUserIds.Contains(ap.UniversityUserId) && !ap.Deleted)
                .Select(ap => ap.UniversityUserId)
                .ToListAsync();

            return existingUserIds;
        }

        #endregion
    }
}
