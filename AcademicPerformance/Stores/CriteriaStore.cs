using Microsoft.EntityFrameworkCore;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.MongoDocuments;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Services.Interfaces;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Stores
{
    public class CriteriaStore : ICriteriaStore
    {
        private readonly AcademicPerformanceDbContext _context;
        private readonly IMongoDbService _mongoDbService;

        public CriteriaStore(AcademicPerformanceDbContext context, IMongoDbService mongoDbService)
        {
            _context = context;
            _mongoDbService = mongoDbService;
        }

        #region Dynamic Criteria (MongoDB)

        public async Task<List<DynamicCriterionTemplate>> GetDynamicCriterionTemplatesAsync()
        {
            return await _mongoDbService.GetDynamicCriterionTemplatesAsync();
        }

        public async Task<DynamicCriterionTemplate?> GetDynamicCriterionTemplateByIdAsync(string id)
        {
            return await _mongoDbService.GetDynamicCriterionTemplateByIdAsync(id);
        }

        public async Task<List<DynamicCriterionTemplate>> GetDynamicCriterionTemplatesByStatusAsync(string status)
        {
            return await _mongoDbService.GetDynamicCriterionTemplatesByStatusAsync(status);
        }

        public async Task<DynamicCriterionTemplate> CreateDynamicCriterionTemplateAsync(DynamicCriterionTemplate template)
        {
            template.CreatedAt = DateTime.UtcNow;
            template.UpdatedAt = DateTime.UtcNow;
            return await _mongoDbService.CreateDynamicCriterionTemplateAsync(template);
        }

        public async Task<bool> UpdateDynamicCriterionTemplateAsync(string id, DynamicCriterionTemplate template)
        {
            template.UpdatedAt = DateTime.UtcNow;
            return await _mongoDbService.UpdateDynamicCriterionTemplateAsync(id, template);
        }

        public async Task<bool> DeleteDynamicCriterionTemplateAsync(string id)
        {
            return await _mongoDbService.DeleteDynamicCriterionTemplateAsync(id);
        }

        public async Task<bool> UpdateDynamicCriterionTemplateStatusAsync(string id, string status)
        {
            var template = await _mongoDbService.GetDynamicCriterionTemplateByIdAsync(id);
            if (template == null)
                return false;

            template.Status = status;
            template.UpdatedAt = DateTime.UtcNow;
            return await _mongoDbService.UpdateDynamicCriterionTemplateAsync(id, template);
        }

        // Pagination metodları - Dynamic Criteria
        public async Task<PagedListDto<DynamicCriterionTemplate>> GetDynamicCriterionTemplatesAsync(PagedListCo<GetDynamicCriterionTemplatesCo> co)
        {
            return await _mongoDbService.GetDynamicCriterionTemplatesAsync(co);
        }

        #endregion

        #region Static Criteria (PostgreSQL)

        public async Task<List<StaticCriterionDefinitionEntity>> GetStaticCriterionDefinitionsAsync()
        {
            return await _context.StaticCriterionDefinitions
                .AsNoTracking() // Read-only query optimization
                .ToListAsync();
        }

        public async Task<StaticCriterionDefinitionEntity?> GetStaticCriterionDefinitionByIdAsync(string staticCriterionSystemId)
        {
            return await _context.StaticCriterionDefinitions
                .AsNoTracking() // Read-only query optimization
                .FirstOrDefaultAsync(x => x.StaticCriterionSystemId == staticCriterionSystemId);
        }

        public async Task<List<StaticCriterionDefinitionEntity>> GetActiveStaticCriterionDefinitionsAsync()
        {
            return await _context.StaticCriterionDefinitions
                .AsNoTracking() // Read-only query optimization
                .Where(x => x.IsActive)
                .ToListAsync();
        }

        public async Task<bool> UpdateStaticCriterionDefinitionStatusAsync(string staticCriterionSystemId, bool isActive)
        {
            var entity = await _context.StaticCriterionDefinitions
                .FirstOrDefaultAsync(x => x.StaticCriterionSystemId == staticCriterionSystemId);

            if (entity == null)
                return false;

            entity.IsActive = isActive;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<StaticCriterionDefinitionEntity> CreateStaticCriterionDefinitionAsync(StaticCriterionDefinitionEntity entity, bool saveChanges = true)
        {
            _context.StaticCriterionDefinitions.Add(entity);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return entity;
        }

        public async Task<bool> UpdateStaticCriterionDefinitionAsync(StaticCriterionDefinitionEntity entity, bool saveChanges = true)
        {
            _context.StaticCriterionDefinitions.Update(entity);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return true;
        }

        // Pagination metodları - Static Criteria
        public async Task<PagedListDto<StaticCriterionDefinitionEntity>> GetStaticCriterionDefinitionsAsync(PagedListCo<GetStaticCriterionDefinitionsCo> co)
        {
            return await GetStaticCriterionDefinitionsAsync(co, q => q.Select(x => x));
        }

        private async Task<PagedListDto<T>> GetStaticCriterionDefinitionsAsync<T>(PagedListCo<GetStaticCriterionDefinitionsCo> co, Func<IQueryable<StaticCriterionDefinitionEntity>, IQueryable<T>> selector)
        {
            var pl = new PagedListDto<T>
            {
                Page = co.Pager.Page,
                Size = co.Pager.Size
            };

            var query = _context.StaticCriterionDefinitions.AsQueryable();

            // Filtering
            if (co.Criteria != null)
            {
                if (!string.IsNullOrEmpty(co.Criteria!.NameContains))
                {
                    query = query.Where(x => x.Name.Contains(co.Criteria!.NameContains));
                }

                if (!string.IsNullOrEmpty(co.Criteria!.SystemIdContains))
                {
                    query = query.Where(x => x.StaticCriterionSystemId.Contains(co.Criteria!.SystemIdContains));
                }

                if (!string.IsNullOrEmpty(co.Criteria!.DescriptionContains))
                {
                    query = query.Where(x => x.Description != null && x.Description.Contains(co.Criteria!.DescriptionContains));
                }

                if (!string.IsNullOrEmpty(co.Criteria!.DataType))
                {
                    query = query.Where(x => x.DataType == co.Criteria!.DataType);
                }

                if (!string.IsNullOrEmpty(co.Criteria!.DataSourceHintContains))
                {
                    query = query.Where(x => x.DataSourceHint != null && x.DataSourceHint.Contains(co.Criteria!.DataSourceHintContains));
                }

                if (!string.IsNullOrEmpty(co.Criteria!.CalculationLogicContains))
                {
                    query = query.Where(x => x.CalculationLogic != null && x.CalculationLogic.Contains(co.Criteria!.CalculationLogicContains));
                }

                if (co.Criteria!.IsActive.HasValue)
                {
                    query = query.Where(x => x.IsActive == co.Criteria!.IsActive.Value);
                }

                if (co.Criteria!.OnlyActive == true)
                {
                    query = query.Where(x => x.IsActive == true);
                }

                if (co.Criteria!.OnlyInactive == true)
                {
                    query = query.Where(x => x.IsActive == false);
                }

                if (co.Criteria!.DataTypes != null && co.Criteria!.DataTypes.Any())
                {
                    query = query.Where(x => co.Criteria!.DataTypes.Contains(x.DataType));
                }

                if (co.Criteria!.SystemIds != null && co.Criteria!.SystemIds.Any())
                {
                    query = query.Where(x => co.Criteria!.SystemIds.Contains(x.StaticCriterionSystemId));
                }

                if (co.Criteria!.HasCalculationLogic == true)
                {
                    query = query.Where(x => !string.IsNullOrEmpty(x.CalculationLogic));
                }
                else if (co.Criteria!.HasCalculationLogic == false)
                {
                    query = query.Where(x => string.IsNullOrEmpty(x.CalculationLogic));
                }

                if (co.Criteria!.HasDataSourceHint == true)
                {
                    query = query.Where(x => !string.IsNullOrEmpty(x.DataSourceHint));
                }
                else if (co.Criteria!.HasDataSourceHint == false)
                {
                    query = query.Where(x => string.IsNullOrEmpty(x.DataSourceHint));
                }
            }

            // Count
            pl.Count = await query.CountAsync();

            if (pl.Count != 0)
            {
                // Sorting
                if (!string.IsNullOrEmpty(co.Sort))
                {
                    switch (co.Sort.ToLowerInvariant())
                    {
                        case "name":
                            query = query.OrderBy(x => x.Name);
                            break;
                        case "name_desc":
                            query = query.OrderByDescending(x => x.Name);
                            break;
                        case "systemid":
                            query = query.OrderBy(x => x.StaticCriterionSystemId);
                            break;
                        case "systemid_desc":
                            query = query.OrderByDescending(x => x.StaticCriterionSystemId);
                            break;
                        case "datatype":
                            query = query.OrderBy(x => x.DataType);
                            break;
                        case "datatype_desc":
                            query = query.OrderByDescending(x => x.DataType);
                            break;
                        case "active":
                            query = query.OrderBy(x => x.IsActive);
                            break;
                        case "active_desc":
                        default:
                            query = query.OrderByDescending(x => x.IsActive).ThenBy(x => x.Name);
                            break;
                    }
                }
                else
                {
                    // Varsayılan sıralama: Aktif olanlar önce, sonra isim
                    query = query.OrderByDescending(x => x.IsActive).ThenBy(x => x.Name);
                }

                // Pagination
                pl.Data = await selector(query).Skip(co.Pager.Skip).Take(co.Pager.Size).ToListAsync();
            }

            return pl;
        }

        #endregion
    }
}
