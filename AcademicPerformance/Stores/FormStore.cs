using Microsoft.EntityFrameworkCore;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Stores
{
    public class FormStore : IFormStore
    {
        private readonly AcademicPerformanceDbContext _context;

        public FormStore(AcademicPerformanceDbContext context)
        {
            _context = context;
        }

        #region Evaluation Forms

        public async Task<List<EvaluationFormEntity>> GetEvaluationFormsAsync()
        {
            return await _context.EvaluationForms
                .AsNoTracking() // Read-only query optimization
                .Include(f => f.Categories)
                .OrderByDescending(f => f.CreatedAt)
                .ToListAsync();
        }

        public async Task<EvaluationFormEntity?> GetEvaluationFormByIdAsync(string id)
        {
            return await _context.EvaluationForms
                .AsNoTracking() // Read-only query optimization
                .Include(f => f.Categories)
                .ThenInclude(c => c.CriterionLinks)
                .FirstOrDefaultAsync(f => f.Id == id);
        }

        public async Task<EvaluationFormEntity?> GetEvaluationFormForUpdateAsync(string id)
        {
            return await _context.EvaluationForms
                .FirstOrDefaultAsync(f => f.Id == id);
        }

        public async Task<List<EvaluationFormEntity>> GetEvaluationFormsByStatusAsync(string status)
        {
            return await _context.EvaluationForms
                .AsNoTracking() // Read-only query optimization
                .Where(f => f.Status == status)
                .Include(f => f.Categories)
                .OrderByDescending(f => f.CreatedAt)
                .ToListAsync();
        }

        // Pagination metodları
        public async Task<PagedListDto<EvaluationFormEntity>> GetEvaluationFormsAsync(PagedListCo<GetEvaluationFormsCo> co)
        {
            return await GetEvaluationFormsAsync(co, q => q.Select(x => x));
        }

        public async Task<PagedListDto<EvaluationFormEntity>> GetEvaluationFormsByStatusAsync(PagedListCo<GetEvaluationFormsCo> co, string status)
        {
            // Status kriterini co.Criteria'ya ekle
            if (co.Criteria == null)
                co.Criteria = new GetEvaluationFormsCo();

            co.Criteria!.Status = status;

            return await GetEvaluationFormsAsync(co, q => q.Select(x => x));
        }

        private async Task<PagedListDto<T>> GetEvaluationFormsAsync<T>(PagedListCo<GetEvaluationFormsCo> co, Func<IQueryable<EvaluationFormEntity>, IQueryable<T>> selector)
        {
            var pl = new PagedListDto<T>
            {
                Page = co.Pager.Page,
                Size = co.Pager.Size
            };

            var query = _context.EvaluationForms.AsQueryable();

            // Include navigation properties for entity queries
            if (typeof(T) == typeof(EvaluationFormEntity))
            {
                query = query.Include(f => f.Categories);
            }

            // Filtering
            if (co.Criteria != null)
            {
                if (!string.IsNullOrEmpty(co.Criteria!.NameContains))
                {
                    query = query.Where(x => x.Name.Contains(co.Criteria!.NameContains));
                }

                if (!string.IsNullOrEmpty(co.Criteria!.Status))
                {
                    query = query.Where(x => x.Status == co.Criteria!.Status);
                }

                if (!string.IsNullOrEmpty(co.Criteria!.CreatedByUserId))
                {
                    query = query.Where(x => x.CreatedByUserId == co.Criteria!.CreatedByUserId);
                }

                if (co.Criteria!.CreatedAfter.HasValue)
                {
                    query = query.Where(x => x.CreatedAt >= co.Criteria!.CreatedAfter.Value);
                }

                if (co.Criteria!.CreatedBefore.HasValue)
                {
                    query = query.Where(x => x.CreatedAt <= co.Criteria!.CreatedBefore.Value);
                }

                if (co.Criteria!.EvaluationPeriodAfter.HasValue)
                {
                    query = query.Where(x => x.EvaluationPeriodStartDate >= co.Criteria!.EvaluationPeriodAfter.Value);
                }

                if (co.Criteria!.EvaluationPeriodBefore.HasValue)
                {
                    query = query.Where(x => x.EvaluationPeriodEndDate <= co.Criteria!.EvaluationPeriodBefore.Value);
                }

                if (co.Criteria!.DeadlineWithinDays.HasValue)
                {
                    var deadlineThreshold = DateTime.UtcNow.AddDays(co.Criteria!.DeadlineWithinDays.Value);
                    query = query.Where(x => x.SubmissionDeadline.HasValue && x.SubmissionDeadline <= deadlineThreshold);
                }

                if (co.Criteria!.OnlyActive == true)
                {
                    query = query.Where(x => x.Status == "Active");
                }

                if (co.Criteria!.OnlyDrafts == true)
                {
                    query = query.Where(x => x.Status == "Draft");
                }
            }

            // Count
            pl.Count = await query.CountAsync();

            if (pl.Count != 0)
            {
                // Sorting
                if (!string.IsNullOrEmpty(co.Sort))
                {
                    switch (co.Sort.ToLowerInvariant())
                    {
                        case "name":
                            query = query.OrderBy(x => x.Name);
                            break;
                        case "name_desc":
                            query = query.OrderByDescending(x => x.Name);
                            break;
                        case "status":
                            query = query.OrderBy(x => x.Status);
                            break;
                        case "status_desc":
                            query = query.OrderByDescending(x => x.Status);
                            break;
                        case "created":
                            query = query.OrderBy(x => x.CreatedAt);
                            break;
                        case "created_desc":
                        default:
                            query = query.OrderByDescending(x => x.CreatedAt);
                            break;
                    }
                }
                else
                {
                    // Varsayılan sıralama: En yeni oluşturulanlar önce
                    query = query.OrderByDescending(x => x.CreatedAt);
                }

                // Pagination
                pl.Data = await selector(query).Skip(co.Pager.Skip).Take(co.Pager.Size).ToListAsync();
            }

            return pl;
        }

        public async Task<EvaluationFormEntity> CreateEvaluationFormAsync(EvaluationFormEntity entity, bool saveChanges = true)
        {
            entity.CreatedAt = DateTime.UtcNow;
            entity.Status = "Draft";
            _context.EvaluationForms.Add(entity);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return entity;
        }

        public async Task<bool> UpdateEvaluationFormAsync(EvaluationFormEntity entity, bool saveChanges = true)
        {
            entity.UpdatedAt = DateTime.UtcNow;
            _context.EvaluationForms.Update(entity);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return true;
        }

        public async Task<bool> UpdateEvaluationFormStatusAsync(string id, string status)
        {
            var entity = await _context.EvaluationForms.FirstOrDefaultAsync(f => f.Id == id);
            if (entity == null)
                return false;

            entity.Status = status;
            entity.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteEvaluationFormAsync(string id)
        {
            var entity = await _context.EvaluationForms.FirstOrDefaultAsync(f => f.Id == id);
            if (entity == null)
                return false;

            _context.EvaluationForms.Remove(entity);
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Form Categories

        public async Task<List<FormCategoryEntity>> GetFormCategoriesByFormIdAsync(string evaluationFormId)
        {
            // N+1 Query Elimination: Single query with join instead of two separate queries
            return await _context.FormCategories
                .AsNoTracking() // Read-only query optimization
                .Include(c => c.CriterionLinks)
                .Where(c => c.EvaluationForm.Id == evaluationFormId)
                .OrderBy(c => c.DisplayOrder)
                .ToListAsync();
        }

        public async Task<FormCategoryEntity?> GetFormCategoryByIdAsync(string id)
        {
            return await _context.FormCategories
                .AsNoTracking() // Read-only query optimization
                .Include(c => c.CriterionLinks)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<FormCategoryEntity?> GetFormCategoryForUpdateAsync(string id)
        {
            return await _context.FormCategories
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<FormCategoryEntity> CreateFormCategoryAsync(FormCategoryEntity entity, bool saveChanges = true)
        {
            entity.CreatedAt = DateTime.UtcNow;
            entity.UpdatedAt = DateTime.UtcNow;
            _context.FormCategories.Add(entity);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return entity;
        }

        public async Task<bool> UpdateFormCategoryAsync(FormCategoryEntity entity, bool saveChanges = true)
        {
            entity.UpdatedAt = DateTime.UtcNow;
            _context.FormCategories.Update(entity);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return true;
        }

        public async Task<bool> DeleteFormCategoryAsync(string id)
        {
            var entity = await _context.FormCategories.FirstOrDefaultAsync(c => c.Id == id);
            if (entity == null)
                return false;

            _context.FormCategories.Remove(entity);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ValidateCategoryWeightsAsync(string evaluationFormId, List<(string categoryId, double weight)> categoryWeights)
        {
            var totalWeight = categoryWeights.Sum(cw => cw.weight);
            return Math.Abs(totalWeight - 1.0) < 0.001; // Allow small floating point differences
        }

        #endregion

        #region Form Criterion Links

        public async Task<List<FormCriterionLinkEntity>> GetFormCriterionLinksByCategoryIdAsync(string formCategoryId)
        {
            // N+1 Query Elimination: Single query with join instead of two separate queries
            return await _context.FormCriterionLinks
                .AsNoTracking() // Read-only query optimization
                .Where(l => l.FormCategory.Id == formCategoryId)
                .OrderBy(l => l.DisplayOrder)
                .ToListAsync();
        }

        public async Task<FormCriterionLinkEntity?> GetFormCriterionLinkByIdAsync(string id)
        {
            return await _context.FormCriterionLinks
                .AsNoTracking() // Read-only query optimization
                .FirstOrDefaultAsync(l => l.Id == id);
        }

        public async Task<FormCriterionLinkEntity?> GetFormCriterionLinkForUpdateAsync(string id)
        {
            return await _context.FormCriterionLinks
                .FirstOrDefaultAsync(l => l.Id == id);
        }

        public async Task<FormCriterionLinkEntity> CreateFormCriterionLinkAsync(FormCriterionLinkEntity entity, bool saveChanges = true)
        {
            entity.CreatedAt = DateTime.UtcNow;
            _context.FormCriterionLinks.Add(entity);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return entity;
        }

        public async Task<bool> UpdateFormCriterionLinkAsync(FormCriterionLinkEntity entity, bool saveChanges = true)
        {
            _context.FormCriterionLinks.Update(entity);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return true;
        }

        public async Task<bool> DeleteFormCriterionLinkAsync(string id)
        {
            var entity = await _context.FormCriterionLinks.FirstOrDefaultAsync(l => l.Id == id);
            if (entity == null)
                return false;

            _context.FormCriterionLinks.Remove(entity);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<FormCriterionLinkEntity>> CreateFormCriterionLinksAsync(List<FormCriterionLinkEntity> entities, bool saveChanges = true)
        {
            foreach (var entity in entities)
            {
                entity.CreatedAt = DateTime.UtcNow;
            }
            _context.FormCriterionLinks.AddRange(entities);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return entities;
        }

        #endregion

        #region Helper Methods

        public async Task<IDictionary<string, int>> IdConvertForEvaluationForm(IEnumerable<string> formIds)
        {
            return await _context.EvaluationForms
                .AsNoTracking() // Read-only query optimization
                .Where(f => formIds.Contains(f.Id))
                .ToDictionaryAsync(f => f.Id, f => f.AutoIncrementId);
        }

        public async Task<IDictionary<string, int>> IdConvertForFormCategory(IEnumerable<string> categoryIds)
        {
            return await _context.FormCategories
                .AsNoTracking() // Read-only query optimization
                .Where(c => categoryIds.Contains(c.Id))
                .ToDictionaryAsync(c => c.Id, c => c.AutoIncrementId);
        }

        public async Task<bool> EvaluationFormExistsAsync(string id)
        {
            return await _context.EvaluationForms.AnyAsync(f => f.Id == id);
        }

        public async Task<bool> FormCategoryExistsAsync(string id)
        {
            return await _context.FormCategories.AnyAsync(c => c.Id == id);
        }

        public async Task<bool> FormCriterionLinkExistsAsync(string id)
        {
            return await _context.FormCriterionLinks.AnyAsync(l => l.Id == id);
        }

        public async Task<EvaluationFormEntity?> GetEvaluationFormByAutoIncrementIdAsync(int autoIncrementId)
        {
            return await _context.EvaluationForms
                .FirstOrDefaultAsync(f => f.AutoIncrementId == autoIncrementId);
        }

        #endregion
    }
}
