using AcademicPerformance.DbContexts;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Stores.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Rlx.Shared.Models.Dtos;
using System.Diagnostics;

namespace AcademicPerformance.Stores
{
    /// <summary>
    /// Portfolio kontrol store implementasyonu
    /// Ders bazlı portfolio verification işlemleri için veri erişimi
    /// </summary>
    public class PortfolioControlStore : IPortfolioControlStore
    {
        private readonly AcademicPerformanceDbContext _context;
        private readonly ILogger<PortfolioControlStore> _logger;

        public PortfolioControlStore(
            AcademicPerformanceDbContext context,
            ILogger<PortfolioControlStore> logger)
        {
            _context = context;
            _logger = logger;
        }

        #region Course Portfolio Verification Operations

        /// <summary>
        /// Yeni ders portfolio verification kaydı oluşturur
        /// </summary>
        public async Task<CoursePortfolioVerificationEntity> CreateCourseVerificationAsync(CoursePortfolioVerificationEntity entity)
        {
            try
            {
                _logger.LogInformation("Creating course verification for academician: {AcademicianTc}, course: {CourseCode}, period: {Period}",
                    entity.AcademicianTc, entity.CourseCode, entity.PeriodName);

                entity.CreatedAt = DateTime.UtcNow;
                entity.UpdatedAt = DateTime.UtcNow;

                _context.CoursePortfolioVerifications.Add(entity);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully created course verification with ID: {Id}", entity.Id);
                return entity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating course verification for academician: {AcademicianTc}, course: {CourseCode}",
                    entity.AcademicianTc, entity.CourseCode);
                throw;
            }
        }

        /// <summary>
        /// Ders portfolio verification kaydını günceller
        /// </summary>
        public async Task<CoursePortfolioVerificationEntity> UpdateCourseVerificationAsync(CoursePortfolioVerificationEntity entity)
        {
            try
            {
                _logger.LogInformation("Updating course verification with ID: {Id}", entity.Id);

                entity.UpdatedAt = DateTime.UtcNow;

                _context.CoursePortfolioVerifications.Update(entity);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully updated course verification with ID: {Id}", entity.Id);
                return entity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating course verification with ID: {Id}", entity.Id);
                throw;
            }
        }

        /// <summary>
        /// ID'ye göre ders portfolio verification kaydını getirir
        /// </summary>
        public async Task<CoursePortfolioVerificationEntity?> GetCourseVerificationByIdAsync(string id)
        {
            try
            {
                _logger.LogInformation("Getting course verification by ID: {Id}", id);

                var entity = await _context.CoursePortfolioVerifications
                    .Include(cv => cv.AcademicianProfile)
                    .FirstOrDefaultAsync(cv => cv.Id == id && !cv.Deleted);

                if (entity == null)
                {
                    _logger.LogWarning("Course verification not found with ID: {Id}", id);
                }

                return entity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting course verification by ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Akademisyen TC'sine göre ders verification kayıtlarını getirir
        /// </summary>
        public async Task<IEnumerable<CoursePortfolioVerificationEntity>> GetCourseVerificationsByAcademicianTcAsync(
            string academicianTc, string? period = null)
        {
            try
            {
                _logger.LogInformation("Getting course verifications for academician: {AcademicianTc}, period: {Period}",
                    academicianTc, period);

                var query = _context.CoursePortfolioVerifications
                    .Include(cv => cv.AcademicianProfile)
                    .Where(cv => cv.AcademicianTc == academicianTc && !cv.Deleted);

                if (!string.IsNullOrEmpty(period))
                {
                    query = query.Where(cv => cv.PeriodName.Contains(period));
                }

                var entities = await query
                    .OrderByDescending(cv => cv.UpdatedAt)
                    .ToListAsync();

                _logger.LogInformation("Found {Count} course verifications for academician: {AcademicianTc}",
                    entities.Count, academicianTc);

                return entities;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting course verifications for academician: {AcademicianTc}, period: {Period}",
                    academicianTc, period);
                throw;
            }
        }

        /// <summary>
        /// Belirli dönemdeki tüm ders verification kayıtlarını getirir
        /// </summary>
        public async Task<IEnumerable<CoursePortfolioVerificationEntity>> GetCourseVerificationsByPeriodAsync(string period)
        {
            try
            {
                _logger.LogInformation("Getting course verifications for period: {Period}", period);

                var entities = await _context.CoursePortfolioVerifications
                    .Include(cv => cv.AcademicianProfile)
                    .Where(cv => cv.PeriodName.Contains(period) && !cv.Deleted)
                    .OrderBy(cv => cv.AcademicianTc)
                    .ThenBy(cv => cv.CourseCode)
                    .ToListAsync();

                _logger.LogInformation("Found {Count} course verifications for period: {Period}",
                    entities.Count, period);

                return entities;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting course verifications for period: {Period}", period);
                throw;
            }
        }

        /// <summary>
        /// Bekleyen verification kayıtlarını getirir
        /// </summary>
        public async Task<IEnumerable<CoursePortfolioVerificationEntity>> GetPendingCourseVerificationsAsync(
            string? archivistId = null, string? period = null)
        {
            try
            {
                _logger.LogInformation("Getting pending course verifications for archivist: {ArchivistId}, period: {Period}",
                    archivistId, period);

                var query = _context.CoursePortfolioVerifications
                    .Include(cv => cv.AcademicianProfile)
                    .Where(cv => !cv.Deleted);

                // Filter by pending status (at least one field is pending)
                query = query.Where(cv =>
                    cv.ExamPapersStatus == "Pending" ||
                    cv.AnswerKeyStatus == "Pending" ||
                    cv.ExamRecordStatus == "Pending" ||
                    cv.AttendanceSheetStatus == "Pending" ||
                    cv.CourseSyllabusStatus == "Pending" ||
                    cv.WeeklyAttendanceStatus == "Pending" ||
                    cv.MakeupExamGradesStatus == "Pending" ||
                    cv.UzemRecordsStatus == "Pending");

                if (!string.IsNullOrEmpty(archivistId))
                {
                    query = query.Where(cv => cv.LastVerifiedByArchivistId == archivistId);
                }

                if (!string.IsNullOrEmpty(period))
                {
                    query = query.Where(cv => cv.PeriodName.Contains(period));
                }

                var entities = await query
                    .OrderBy(cv => cv.CreatedAt)
                    .ToListAsync();

                _logger.LogInformation("Found {Count} pending course verifications", entities.Count);

                return entities;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending course verifications for archivist: {ArchivistId}, period: {Period}",
                    archivistId, period);
                throw;
            }
        }

        /// <summary>
        /// Filtrelenmiş ders verification kayıtları arama
        /// </summary>
        public async Task<PagedListDto<CoursePortfolioVerificationEntity>> SearchCourseVerificationsAsync(
            SearchCourseVerificationRequestDto request)
        {
            try
            {
                _logger.LogInformation("Searching course verifications with filters: {@Request}", request);

                var query = _context.CoursePortfolioVerifications
                    .Include(cv => cv.AcademicianProfile)
                    .Where(cv => !cv.Deleted);

                // Apply filters
                if (!string.IsNullOrEmpty(request.AcademicianTc))
                {
                    query = query.Where(cv => cv.AcademicianTc == request.AcademicianTc);
                }

                if (!string.IsNullOrEmpty(request.CourseCode))
                {
                    query = query.Where(cv => cv.CourseCode.Contains(request.CourseCode));
                }

                if (!string.IsNullOrEmpty(request.Period))
                {
                    query = query.Where(cv => cv.PeriodName.Contains(request.Period));
                }

                if (!string.IsNullOrEmpty(request.ArchivistId))
                {
                    query = query.Where(cv => cv.LastVerifiedByArchivistId == request.ArchivistId);
                }

                if (request.StartDate.HasValue)
                {
                    query = query.Where(cv => cv.UpdatedAt >= request.StartDate.Value);
                }

                if (request.EndDate.HasValue)
                {
                    query = query.Where(cv => cv.UpdatedAt <= request.EndDate.Value);
                }

                // Apply overall status filter
                if (!string.IsNullOrEmpty(request.OverallStatus))
                {
                    switch (request.OverallStatus.ToLower())
                    {
                        case "pending":
                            query = query.Where(cv =>
                                cv.ExamPapersStatus == "Pending" ||
                                cv.AnswerKeyStatus == "Pending" ||
                                cv.ExamRecordStatus == "Pending" ||
                                cv.AttendanceSheetStatus == "Pending" ||
                                cv.CourseSyllabusStatus == "Pending" ||
                                cv.WeeklyAttendanceStatus == "Pending" ||
                                cv.MakeupExamGradesStatus == "Pending" ||
                                cv.UzemRecordsStatus == "Pending");
                            break;
                        case "allverified":
                            query = query.Where(cv =>
                                cv.ExamPapersStatus == "VerifiedInEbys" &&
                                cv.AnswerKeyStatus == "VerifiedInEbys" &&
                                cv.ExamRecordStatus == "VerifiedInEbys" &&
                                cv.AttendanceSheetStatus == "VerifiedInEbys" &&
                                cv.CourseSyllabusStatus == "VerifiedInEbys" &&
                                cv.WeeklyAttendanceStatus == "VerifiedInEbys" &&
                                cv.MakeupExamGradesStatus == "VerifiedInEbys" &&
                                cv.UzemRecordsStatus == "VerifiedInEbys");
                            break;
                        case "hasissues":
                            query = query.Where(cv =>
                                cv.ExamPapersStatus == "Missing" || cv.ExamPapersStatus == "DiscrepancyFound" ||
                                cv.AnswerKeyStatus == "Missing" || cv.AnswerKeyStatus == "DiscrepancyFound" ||
                                cv.ExamRecordStatus == "Missing" || cv.ExamRecordStatus == "DiscrepancyFound" ||
                                cv.AttendanceSheetStatus == "Missing" || cv.AttendanceSheetStatus == "DiscrepancyFound" ||
                                cv.CourseSyllabusStatus == "Missing" || cv.CourseSyllabusStatus == "DiscrepancyFound" ||
                                cv.WeeklyAttendanceStatus == "Missing" || cv.WeeklyAttendanceStatus == "DiscrepancyFound" ||
                                cv.MakeupExamGradesStatus == "Missing" || cv.MakeupExamGradesStatus == "DiscrepancyFound" ||
                                cv.UzemRecordsStatus == "Missing" || cv.UzemRecordsStatus == "DiscrepancyFound");
                            break;
                    }
                }

                // Get total count
                var totalCount = await query.CountAsync();

                // Apply sorting
                var sortBy = request.SortBy?.ToLower() ?? "updatedat";
                var sortDirection = request.SortDirection?.ToLower() ?? "desc";

                query = sortBy switch
                {
                    "academiciantc" => sortDirection == "desc" ? query.OrderByDescending(cv => cv.AcademicianTc) : query.OrderBy(cv => cv.AcademicianTc),
                    "coursecode" => sortDirection == "desc" ? query.OrderByDescending(cv => cv.CourseCode) : query.OrderBy(cv => cv.CourseCode),
                    "period" => sortDirection == "desc" ? query.OrderByDescending(cv => cv.PeriodName) : query.OrderBy(cv => cv.PeriodName),
                    "createdat" => sortDirection == "desc" ? query.OrderByDescending(cv => cv.CreatedAt) : query.OrderBy(cv => cv.CreatedAt),
                    _ => sortDirection == "desc" ? query.OrderByDescending(cv => cv.UpdatedAt) : query.OrderBy(cv => cv.UpdatedAt)
                };

                // Apply pagination
                var items = await query
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToListAsync();

                var result = new PagedListDto<CoursePortfolioVerificationEntity>
                {
                    Data = items,
                    Count = totalCount,
                    TotalCount = totalCount,
                    Page = request.PageNumber,
                    Size = request.PageSize
                };

                _logger.LogInformation("Found {ItemCount} course verifications out of {TotalCount} total",
                    items.Count, totalCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching course verifications with filters: {@Request}", request);
                throw;
            }
        }

        /// <summary>
        /// Akademisyen, ders kodu ve dönem kombinasyonuna göre verification kaydını getirir
        /// </summary>
        public async Task<CoursePortfolioVerificationEntity?> GetCourseVerificationByKeyAsync(
            string academicianTc, string courseCode, string period)
        {
            try
            {
                _logger.LogInformation("Getting course verification by key: {AcademicianTc}, {CourseCode}, {Period}",
                    academicianTc, courseCode, period);

                var entity = await _context.CoursePortfolioVerifications
                    .Include(cv => cv.AcademicianProfile)
                    .FirstOrDefaultAsync(cv =>
                        cv.AcademicianTc == academicianTc &&
                        cv.CourseCode == courseCode &&
                        cv.PeriodName == period &&
                        !cv.Deleted);

                if (entity == null)
                {
                    _logger.LogWarning("Course verification not found for key: {AcademicianTc}, {CourseCode}, {Period}",
                        academicianTc, courseCode, period);
                }

                return entity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting course verification by key: {AcademicianTc}, {CourseCode}, {Period}",
                    academicianTc, courseCode, period);
                throw;
            }
        }

        /// <summary>
        /// Toplu verification güncelleme
        /// </summary>
        public async Task<int> BulkUpdateCourseVerificationsAsync(IEnumerable<CoursePortfolioVerificationEntity> updates)
        {
            try
            {
                _logger.LogInformation("Bulk updating {Count} course verifications", updates.Count());

                var updateCount = 0;
                foreach (var update in updates)
                {
                    update.UpdatedAt = DateTime.UtcNow;
                    _context.CoursePortfolioVerifications.Update(update);
                    updateCount++;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully bulk updated {Count} course verifications", updateCount);
                return updateCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating course verifications");
                throw;
            }
        }

        /// <summary>
        /// Verification kaydını siler
        /// </summary>
        public async Task<bool> DeleteCourseVerificationAsync(string id)
        {
            try
            {
                _logger.LogInformation("Deleting course verification with ID: {Id}", id);

                var entity = await _context.CoursePortfolioVerifications
                    .FirstOrDefaultAsync(cv => cv.Id == id && !cv.Deleted);

                if (entity == null)
                {
                    _logger.LogWarning("Course verification not found for deletion with ID: {Id}", id);
                    return false;
                }

                entity.Deleted = true;
                entity.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully deleted course verification with ID: {Id}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting course verification with ID: {Id}", id);
                throw;
            }
        }

        #endregion

        #region Statistics and Reporting

        /// <summary>
        /// Akademisyen bazlı verification istatistiklerini getirir
        /// </summary>
        public async Task<CourseVerificationStatisticsDto> GetAcademicianVerificationStatisticsAsync(
            string academicianTc, string? period = null)
        {
            try
            {
                _logger.LogInformation("Getting verification statistics for academician: {AcademicianTc}, period: {Period}",
                    academicianTc, period);

                var query = _context.CoursePortfolioVerifications
                    .Where(cv => cv.AcademicianTc == academicianTc && !cv.Deleted);

                if (!string.IsNullOrEmpty(period))
                {
                    query = query.Where(cv => cv.PeriodName.Contains(period));
                }

                var verifications = await query.ToListAsync();

                var statistics = new CourseVerificationStatisticsDto
                {
                    TotalCount = verifications.Count,
                    CompletedCount = verifications.Count(v => v.GetOverallStatus() == "AllVerified"),
                    PendingCount = verifications.Count(v => v.GetPendingCount() > 0),
                    IssueCount = verifications.Count(v => v.GetIssueCount() > 0)
                };

                statistics.CompletionPercentage = statistics.TotalCount > 0
                    ? (double)statistics.CompletedCount / statistics.TotalCount * 100
                    : 0;

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting verification statistics for academician: {AcademicianTc}, period: {Period}",
                    academicianTc, period);
                throw;
            }
        }

        /// <summary>
        /// Dönem bazlı verification istatistiklerini getirir
        /// </summary>
        public async Task<CourseVerificationStatisticsDto> GetPeriodVerificationStatisticsAsync(string period)
        {
            try
            {
                _logger.LogInformation("Getting verification statistics for period: {Period}", period);

                var verifications = await _context.CoursePortfolioVerifications
                    .Where(cv => cv.PeriodName.Contains(period) && !cv.Deleted)
                    .ToListAsync();

                var statistics = new CourseVerificationStatisticsDto
                {
                    TotalCount = verifications.Count,
                    CompletedCount = verifications.Count(v => v.GetOverallStatus() == "AllVerified"),
                    PendingCount = verifications.Count(v => v.GetPendingCount() > 0),
                    IssueCount = verifications.Count(v => v.GetIssueCount() > 0)
                };

                statistics.CompletionPercentage = statistics.TotalCount > 0
                    ? (double)statistics.CompletedCount / statistics.TotalCount * 100
                    : 0;

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting verification statistics for period: {Period}", period);
                throw;
            }
        }

        /// <summary>
        /// Archivist bazlı verification istatistiklerini getirir
        /// </summary>
        public async Task<CourseVerificationStatisticsDto> GetArchivistVerificationStatisticsAsync(
            string archivistId, string? period = null)
        {
            try
            {
                _logger.LogInformation("Getting verification statistics for archivist: {ArchivistId}, period: {Period}",
                    archivistId, period);

                var query = _context.CoursePortfolioVerifications
                    .Where(cv => cv.LastVerifiedByArchivistId == archivistId && !cv.Deleted);

                if (!string.IsNullOrEmpty(period))
                {
                    query = query.Where(cv => cv.PeriodName.Contains(period));
                }

                var verifications = await query.ToListAsync();

                var statistics = new CourseVerificationStatisticsDto
                {
                    TotalCount = verifications.Count,
                    CompletedCount = verifications.Count(v => v.GetOverallStatus() == "AllVerified"),
                    PendingCount = verifications.Count(v => v.GetPendingCount() > 0),
                    IssueCount = verifications.Count(v => v.GetIssueCount() > 0)
                };

                statistics.CompletionPercentage = statistics.TotalCount > 0
                    ? (double)statistics.CompletedCount / statistics.TotalCount * 100
                    : 0;

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting verification statistics for archivist: {ArchivistId}, period: {Period}",
                    archivistId, period);
                throw;
            }
        }

        /// <summary>
        /// Genel verification dashboard istatistiklerini getirir
        /// </summary>
        public async Task<VerificationDashboardStatisticsDto> GetDashboardStatisticsAsync(string? period = null)
        {
            try
            {
                _logger.LogInformation("Getting dashboard statistics for period: {Period}", period);

                var query = _context.CoursePortfolioVerifications.Where(cv => !cv.Deleted);

                if (!string.IsNullOrEmpty(period))
                {
                    query = query.Where(cv => cv.PeriodName.Contains(period));
                }

                var verifications = await query.ToListAsync();

                var dashboard = new VerificationDashboardStatisticsDto();

                // Overall statistics
                dashboard.OverallStatistics = new CourseVerificationStatisticsDto
                {
                    TotalCount = verifications.Count,
                    CompletedCount = verifications.Count(v => v.GetOverallStatus() == "AllVerified"),
                    PendingCount = verifications.Count(v => v.GetPendingCount() > 0),
                    IssueCount = verifications.Count(v => v.GetIssueCount() > 0)
                };

                dashboard.OverallStatistics.CompletionPercentage = dashboard.OverallStatistics.TotalCount > 0
                    ? (double)dashboard.OverallStatistics.CompletedCount / dashboard.OverallStatistics.TotalCount * 100
                    : 0;

                // Period distribution
                var periodGroups = verifications.GroupBy(v => v.PeriodName);
                foreach (var group in periodGroups)
                {
                    var periodStats = new CourseVerificationStatisticsDto
                    {
                        TotalCount = group.Count(),
                        CompletedCount = group.Count(v => v.GetOverallStatus() == "AllVerified"),
                        PendingCount = group.Count(v => v.GetPendingCount() > 0),
                        IssueCount = group.Count(v => v.GetIssueCount() > 0)
                    };
                    periodStats.CompletionPercentage = periodStats.TotalCount > 0
                        ? (double)periodStats.CompletedCount / periodStats.TotalCount * 100
                        : 0;
                    dashboard.PeriodDistribution[group.Key] = group.Count();
                }

                // Recent activity (last 30 days)
                var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);
                var recentVerifications = verifications.Where(v => v.UpdatedAt >= thirtyDaysAgo);
                var activityGroups = recentVerifications.GroupBy(v => v.UpdatedAt.Date);
                foreach (var group in activityGroups)
                {
                    dashboard.RecentActivity.Add($"{group.Key:yyyy-MM-dd}: {group.Count()} verifications");
                }

                return dashboard;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard statistics for period: {Period}", period);
                throw;
            }
        }

        #endregion

        #region Sync Operations

        /// <summary>
        /// ArelBridge'den gelen ders bilgileri ile verification kayıtlarını senkronize eder
        /// </summary>
        public async Task<SyncResultDto> SyncCourseVerificationsAsync(IEnumerable<CourseInformationDto> courseInformations)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new SyncResultDto();

            try
            {
                _logger.LogInformation("Starting sync of {Count} course informations", courseInformations.Count());

                foreach (var courseInfo in courseInformations)
                {
                    try
                    {
                        var existing = await GetCourseVerificationByKeyAsync(
                            courseInfo.AcademicianTc, courseInfo.CourseCode, courseInfo.PeriodName);

                        if (existing == null)
                        {
                            var newVerification = new CoursePortfolioVerificationEntity
                            {
                                Id = Guid.NewGuid().ToString(),
                                AcademicianTc = courseInfo.AcademicianTc,
                                CourseCode = courseInfo.CourseCode,
                                PeriodName = courseInfo.PeriodName,
                                CourseName = courseInfo.CourseName
                            };

                            await CreateCourseVerificationAsync(newVerification);
                            result.CreatedCount++;
                        }
                        else
                        {
                            // Update existing record if course name changed
                            if (existing.CourseName != courseInfo.CourseName)
                            {
                                existing.CourseName = courseInfo.CourseName;
                                await UpdateCourseVerificationAsync(existing);
                                result.UpdatedCount++;
                            }
                            else
                            {
                                result.SkippedCount++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error syncing course: {AcademicianTc}, {CourseCode}, {Period}",
                            courseInfo.AcademicianTc, courseInfo.CourseCode, courseInfo.PeriodName);
                        result.ErrorMessages.Add($"Error syncing {courseInfo.CourseCode}: {ex.Message}");
                    }
                }

                result.IsSuccessful = result.ErrorMessages.Count == 0;
                result.EndTime = DateTime.UtcNow;
                stopwatch.Stop();

                _logger.LogInformation("Sync completed. Created: {Created}, Updated: {Updated}, Skipped: {Skipped}, Errors: {Errors}",
                    result.CreatedCount, result.UpdatedCount, result.SkippedCount, result.ErrorMessages.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during course verification sync");
                result.IsSuccessful = false;
                result.ErrorMessages.Add($"Sync failed: {ex.Message}");
                result.EndTime = DateTime.UtcNow;
                stopwatch.Stop();
                return result;
            }
        }

        /// <summary>
        /// Belirli akademisyen için verification kayıtlarını senkronize eder
        /// </summary>
        public async Task<SyncResultDto> SyncAcademicianCourseVerificationsAsync(
            string academicianTc, IEnumerable<CourseInformationDto> courseInformations)
        {
            try
            {
                _logger.LogInformation("Starting sync for academician: {AcademicianTc} with {Count} courses",
                    academicianTc, courseInformations.Count());

                var academicianCourses = courseInformations.Where(ci => ci.AcademicianTc == academicianTc);
                return await SyncCourseVerificationsAsync(academicianCourses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing courses for academician: {AcademicianTc}", academicianTc);
                throw;
            }
        }

        #endregion

        #region Health Check

        /// <summary>
        /// Store sağlık kontrolü yapar
        /// </summary>
        public async Task<(bool IsHealthy, string Message, Dictionary<string, object> Details)> HealthCheckAsync()
        {
            var details = new Dictionary<string, object>();

            try
            {
                // Database connection test
                await _context.Database.OpenConnectionAsync();
                await _context.Database.CloseConnectionAsync();
                details["DatabaseConnection"] = "Connected";

                // Test basic query
                var count = await _context.CoursePortfolioVerifications.CountAsync();
                details["TotalVerificationRecords"] = count;

                // Test recent activity
                var recentCount = await _context.CoursePortfolioVerifications
                    .Where(cv => cv.UpdatedAt >= DateTime.UtcNow.AddDays(-7))
                    .CountAsync();
                details["RecentActivity"] = recentCount;

                return (true, "Portfolio Control Store is healthy", details);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Portfolio Control Store health check failed");
                details["Error"] = ex.Message;
                return (false, "Portfolio Control Store health check failed", details);
            }
        }

        #endregion
    }
}