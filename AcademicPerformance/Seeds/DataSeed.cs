
using Rlx.Shared.Models.RlxEnumDbContextModels;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.MongoDocuments;
using AcademicPerformance.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Seeds;

public static class DataSeed
{
    public static async Task Initialize(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        try
        {
            var academicContext = scope.ServiceProvider.GetRequiredService<AcademicPerformanceDbContext>();
            var mongoDbService = scope.ServiceProvider.GetRequiredService<IMongoDbService>();

            logger.LogInformation("Starting data seeding process...");

            var canConnect = await academicContext.Database.CanConnectAsync();
            if (!canConnect)
            {
                logger.LogWarning("Cannot connect to PostgreSQL database. Skipping PostgreSQL seeding.");
                logger.LogWarning("Please run 'dotnet ef database update' to create the database and tables first.");
            }
            else
            {
                logger.LogInformation("Database connection successful.");

                try
                {
                    await academicContext.ApdysRoles.AnyAsync();
                    logger.LogInformation("Database tables are available.");

                    await SeedApdysRoles(academicContext);
                    logger.LogInformation("Seeded APDYS roles.");

                    await SeedStaticCriterionDefinitions(academicContext);
                    logger.LogInformation("Seeded static criterion definitions.");

                    await academicContext.SaveChangesAsync();
                    logger.LogInformation("PostgreSQL seeding completed successfully.");

                    // Lokalizasyon verilerini seed et
                    var localizationContext = scope.ServiceProvider.GetRequiredService<AcademicPerformanceLocalizationDbContext>();
                    var localizationManager = scope.ServiceProvider.GetRequiredService<IRlxLocalizationManager<AcademicPerformanceLocalizationDbContext>>();
                    await SeedAcademicEducationStaticCriteriaLocalizations(localizationManager, logger);
                    logger.LogInformation("Seeded academic education static criteria localizations.");

                    // Statik kriter katsayılarını seed et
                    await SeedStaticCriterionCoefficients(academicContext);
                    logger.LogInformation("Seeded static criterion coefficients.");
                }
                catch (Exception dbEx)
                {
                    logger.LogError(dbEx, "Database tables not available or migration not applied: {Message}", dbEx.Message);
                    logger.LogWarning("Please run 'dotnet ef database update' to create the database tables first.");
                    logger.LogWarning("Skipping PostgreSQL seeding for now.");
                }
            }

            await SeedMongoDbData(mongoDbService, logger);
            logger.LogInformation("Data seeding process completed.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Data seeding failed: {Message}", ex.Message);
            logger.LogWarning("Application will continue to start despite seeding failure.");
        }
    }

    private static async Task SeedApdysRoles(AcademicPerformanceDbContext context)
    {
        if (!await context.ApdysRoles.AnyAsync())
        {
            var roles = new[]
            {
                new ApdysRoleEntity { Id = Guid.NewGuid().ToString(), RoleName = "APDYS_Admin", Description = "System Administrator with full access" },
                new ApdysRoleEntity { Id = Guid.NewGuid().ToString(), RoleName = "APDYS_Academician", Description = "Academic staff member who submits performance data" },
                new ApdysRoleEntity { Id = Guid.NewGuid().ToString(), RoleName = "APDYS_Controller", Description = "Controller who reviews and approves submissions" },
                new ApdysRoleEntity { Id = Guid.NewGuid().ToString(), RoleName = "APDYS_StrategicMgmt", Description = "Strategic management role for department-level data" },
                new ApdysRoleEntity { Id = Guid.NewGuid().ToString(), RoleName = "APDYS_Manager", Description = "Manager with oversight capabilities" },
                new ApdysRoleEntity { Id = Guid.NewGuid().ToString(), RoleName = "APDYS_Archivist", Description = "Archivist for portfolio verification" }
            };

            await context.ApdysRoles.AddRangeAsync(roles);
        }
    }





    private static async Task SeedStaticCriterionDefinitions(AcademicPerformanceDbContext context)
    {
        if (!await context.StaticCriterionDefinitions.AnyAsync())
        {
            var criteria = new[]
            {
                // Mevcut örnek kriterler
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "PUBLICATION_COUNT",
                    Name = "Publication Count",
                    Description = "Number of academic publications",
                    DataType = "Integer",
                    DataSourceHint = "EBYS_PUBLICATIONS"
                },
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "CITATION_COUNT",
                    Name = "Citation Count",
                    Description = "Total number of citations",
                    DataType = "Integer",
                    DataSourceHint = "EBYS_CITATIONS"
                },
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "COURSE_LOAD",
                    Name = "Course Load",
                    Description = "Number of courses taught",
                    DataType = "Integer",
                    DataSourceHint = "ACADEMIC_SYSTEM"
                },
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "STUDENT_EVALUATION_SCORE",
                    Name = "Student Evaluation Score",
                    Description = "Average student evaluation score",
                    DataType = "Decimal",
                    DataSourceHint = "STUDENT_EVALUATION_SYSTEM"
                },

                // ArelBridge A Bölümü Akademik Eğitim Faaliyetleri Statik Kriterleri
                // A1 - Ders Yükü
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A1_COURSE_LOAD",
                    Name = "Ders Yükü",
                    Description = "Ön lisans, lisans, lisansüstü yıllık ders saati toplamı",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Tirtil veritabanı akademik_egitim_faliyet_hesapla_a fonksiyonu A1 kriteri"
                },

                // A2_1 - Öğrenci Değerlendirme Anketi
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A2_1_STUDENT_EVALUATION",
                    Name = "Öğrenci Değerlendirme Anketi",
                    Description = "Ortalama öğrenci ders öğretim elemanı değerlendirme anket puanının 4,25 veya üzeri olması",
                    DataType = "Boolean",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Anket puanı 4,25 ve üzerinde ise 1, altında ise 0"
                },

                // A3_1 - Danışmana Atanmış Öğrenci Sayısı
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_1_ASSIGNED_STUDENTS",
                    Name = "Danışmana Atanmış Öğrenci Sayısı",
                    Description = "Akademisyene danışmanlık için atanmış toplam öğrenci sayısı",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Tirtil veritabanı akademik_egitim_faliyet_hesapla_a fonksiyonu A3_1 kriteri"
                },

                // A3_2 - İletişime Geçtiği Öğrenci Sayısı
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_2_STUDENT_COMMUNICATION",
                    Name = "İletişime Geçtiği Öğrenci Sayısı",
                    Description = "İletişime geçtiği kendisine atanmış öğrenci sayısı oranı (ARELIM sistemi üzerinden)",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "İletişime geçtiği öğrenci/tüm kendisine atanmış öğrenci sayısı*100"
                },

                // A3_3 - Öğrenci Memnuniyeti
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_3_STUDENT_SATISFACTION",
                    Name = "Öğrenci Memnuniyeti",
                    Description = "Danışmanlığı yapılan öğrenci memnuniyeti (güz veya bahar yarıyılında alınan en yüksek anket puanı)",
                    DataType = "Decimal",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Tüm öğrencilerin 100 üzerinden verdikleri puanların ortalaması"
                },

                // A3_4 - Danışman Tutundurma Başarısı
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_4_ADVISOR_RETENTION_SUCCESS",
                    Name = "Danışman Tutundurma Başarısı",
                    Description = "Öğrencilerinin yatay geçiş veya farklı nedenle üniversiteden ayrılmaması",
                    DataType = "Boolean",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Öğrenci ayrılması varsa 0, tamamı devam ediyorsa 1"
                },

                // A3_5 - Öğrenci Kulübü Danışmanlığı
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_5_STUDENT_CLUB_ADVISORY",
                    Name = "Öğrenci Kulübü Danışmanlığı",
                    Description = "Danışmanlık yapılan öğrenci kulübü sayısı (En az yılda 2 etkinlik/10+ üye sayısı)",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "1 kulüp: 35 puan, 1'den fazla: 50 puan (En fazla 50 puan)"
                },

                // A3_6 - Öğrenci GPA Ortalaması
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_6_STUDENT_GPA_AVERAGE",
                    Name = "Öğrenci GPA Ortalaması",
                    Description = "Danışmana atanmış öğrencilerin GPA ortalaması (yıllık)",
                    DataType = "Boolean",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "2,00'nin altında olanların oranı %10 ve altındaysa 1, üzerindeyse 0"
                },

                // A3_7 - Onur Öğrenci Sayısı
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_7_HONOR_STUDENTS",
                    Name = "Onur Öğrenci Sayısı",
                    Description = "Danışmanın 'Onur' öğrenci sayısı (GPA 3,00 ve üzeri)",
                    DataType = "Boolean",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "3,00 ve üzeri olanların oranı %10 ve üzerindeyse 1, altındaysa 0"
                },

                // A3_8 - Devamsızlık Oranı
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_8_ABSENTEEISM_RATE",
                    Name = "Devamsızlık Oranı",
                    Description = "Danışmana atanan öğrencilerin devamsızlık yaparak derslerden kalma oranı",
                    DataType = "Boolean",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Devamsızlık oranı yıllık %10 ve altındaysa 1, üzerindeyse 0"
                },

                // A3_9 - Erasmus Katılımı
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_9_ERASMUS_PARTICIPATION",
                    Name = "Erasmus Katılımı",
                    Description = "Danışmana atanmış öğrencilerden Erasmus programına katılanların sayısı",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Sayı X Katsayı"
                },

                // A3_10 - Sosyal Kulüp Üyeliği
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_10_SOCIAL_CLUB_MEMBERSHIP",
                    Name = "Sosyal Kulüp Üyeliği",
                    Description = "Danışmana atanmış öğrencilerden öğrenci sosyal kulüp organizasyonlarına üye olanların sayısı",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Sayı X Katsayı (En fazla 50 puan)"
                },

                // A3_11 - Spor Kulübü Üyeliği
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_11_SPORTS_CLUB_MEMBERSHIP",
                    Name = "Spor Kulübü Üyeliği",
                    Description = "Danışmana atanmış öğrencilerden öğrenci spor klüplerine üye olanların sayısı",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Sayı X Katsayı (En fazla 50 puan)"
                },

                // A3_12 - Çift Ana Dal/Yan Dal
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_12_DOUBLE_MAJOR_MINOR",
                    Name = "Çift Ana Dal/Yan Dal",
                    Description = "Danışmana atanmış öğrencilerin içinde çift ana dal veya yan dal yapanların sayısı",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Sayı X Katsayı"
                },

                // A3_13 - Üniversite Takımları
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_13_UNIVERSITY_TEAMS",
                    Name = "Üniversite Takımları",
                    Description = "Danışmana atanmış üniversite takımlarında yer alan ve GPA'si 2.00'nin üzerinde olan takım öğrencisi sayısı",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Sayı X Katsayı"
                },

                // A3_14 - Teknokent/TTO Projeleri
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_14_TECHNOPARK_TTO_PROJECTS",
                    Name = "Teknokent/TTO Projeleri",
                    Description = "Danışmana atanmış Teknokent veya Teknoloji Transfer Ofisi (TTO) projelerine katılan öğrenci sayısı",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Sayı X Katsayı"
                },

                // A3_15 - TÜBİTAK 2209 Başvuruları
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_15_TUBITAK_2209_APPLICATIONS",
                    Name = "TÜBİTAK 2209 Başvuruları",
                    Description = "Danışmana atanmış TÜBİTAK 2209 Üniversite Öğrencileri Araştırma Projeleri Destekleme Programı başvuru yapan öğrenci sayısı",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Sayı X Katsayı"
                },

                // A4 - Yüksek Lisans Tez Yöneticiliği
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A4_MASTERS_SUPERVISION",
                    Name = "Yüksek Lisans Tez Yöneticiliği",
                    Description = "Yüksek Lisans tez yöneticiliği (1. Danışman) - Onaylanmış tez planı ve en az bir dönem kayıt gerekli",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Sayı X Katsayı (En fazla 3 yıl puan verilebilir)"
                },

                // A5 - Doktora Tez Yöneticiliği
                new StaticCriterionDefinitionEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A5_PHD_SUPERVISION",
                    Name = "Doktora Tez Yöneticiliği",
                    Description = "Doktora tez yöneticiliği (1. Danışman) - Onaylanmış tez planı ve en az bir dönem kayıt gerekli",
                    DataType = "Integer",
                    DataSourceHint = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    CalculationLogic = "Sayı X Katsayı (En fazla 3 yıl puan verilebilir)"
                }
            };

            await context.StaticCriterionDefinitions.AddRangeAsync(criteria);
        }
    }

    private static async Task SeedMongoDbData(IMongoDbService mongoDbService, ILogger logger)
    {
        try
        {
            var existingTemplates = await mongoDbService.GetDynamicCriterionTemplatesAsync();
            if (existingTemplates.Any())
            {
                logger.LogInformation("MongoDB already has data. Skipping seeding.");
                return;
            }

            var templates = new[]
            {
                new DynamicCriterionTemplate
                {
                    Name = "Araştırma Projesi Değerlendirme Formu",
                    Description = "Akademisyenlerin araştırma projelerini değerlendirmek için kullanılan dinamik form",
                    Status = "Active",
                    CreatedByUserId = "system",
                    InputFields = new List<DynamicInputField>
                    {
                        new DynamicInputField
                        {
                            FieldId = "project_title",
                            FieldName = "Proje Başlığı",
                            FieldType = "Text",
                            IsRequired = true,
                            DisplayOrder = 1,
                            Constraints = new FieldConstraints { MaxLength = 200 }
                        },
                        new DynamicInputField
                        {
                            FieldId = "project_budget",
                            FieldName = "Proje Bütçesi (TL)",
                            FieldType = "Number",
                            IsRequired = true,
                            DisplayOrder = 2,
                            Constraints = new FieldConstraints { MinValue = 0, MaxValue = 5000000 }
                        },
                        new DynamicInputField
                        {
                            FieldId = "project_duration",
                            FieldName = "Proje Süresi (Ay)",
                            FieldType = "Number",
                            IsRequired = true,
                            DisplayOrder = 3,
                            Constraints = new FieldConstraints { MinValue = 1, MaxValue = 60 }
                        },
                        new DynamicInputField
                        {
                            FieldId = "project_type",
                            FieldName = "Proje Türü",
                            FieldType = "Select",
                            IsRequired = true,
                            DisplayOrder = 4,
                            Options = new List<FieldOption>
                            {
                                new FieldOption { Value = "tubitak", Label = "TÜBİTAK Projesi" },
                                new FieldOption { Value = "university", Label = "Üniversite İç Projesi" },
                                new FieldOption { Value = "international", Label = "Uluslararası Proje" },
                                new FieldOption { Value = "industry", Label = "Sanayi İşbirliği Projesi" }
                            }
                        }
                    }
                },
                new DynamicCriterionTemplate
                {
                    Name = "Yayın Değerlendirme Formu",
                    Description = "Akademik yayınları detaylı olarak değerlendirmek için kullanılan form",
                    Status = "Active",
                    CreatedByUserId = "system",
                    InputFields = new List<DynamicInputField>
                    {
                        new DynamicInputField
                        {
                            FieldId = "publication_title",
                            FieldName = "Yayın Başlığı",
                            FieldType = "Text",
                            IsRequired = true,
                            DisplayOrder = 1,
                            Constraints = new FieldConstraints { MaxLength = 300 }
                        },
                        new DynamicInputField
                        {
                            FieldId = "journal_name",
                            FieldName = "Dergi Adı",
                            FieldType = "Text",
                            IsRequired = true,
                            DisplayOrder = 2,
                            Constraints = new FieldConstraints { MaxLength = 200 }
                        },
                        new DynamicInputField
                        {
                            FieldId = "impact_factor",
                            FieldName = "Etki Faktörü",
                            FieldType = "Number",
                            IsRequired = false,
                            DisplayOrder = 3,
                            Constraints = new FieldConstraints { MinValue = 0, MaxValue = 100 }
                        },
                        new DynamicInputField
                        {
                            FieldId = "publication_date",
                            FieldName = "Yayın Tarihi",
                            FieldType = "Date",
                            IsRequired = true,
                            DisplayOrder = 4
                        }
                    }
                }
            };

            foreach (var template in templates)
            {
                await mongoDbService.CreateDynamicCriterionTemplateAsync(template);
            }

            logger.LogInformation("MongoDB seeding completed. Created {TemplateCount} dynamic criterion templates.", templates.Length);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "MongoDB seeding failed: {Message}", ex.Message);
        }
    }

    /// <summary>
    /// A bölümü akademik eğitim faaliyetleri statik kriterleri lokalizasyon verilerini seed eder
    /// </summary>
    private static async Task SeedAcademicEducationStaticCriteriaLocalizations(
        IRlxLocalizationManager<AcademicPerformanceLocalizationDbContext> localizationManager,
        ILogger logger)
    {
        try
        {
            // A bölümü statik kriterleri lokalizasyonları
            var criteriaLocalizations = GetAcademicEducationStaticCriteriaLocalizations();
            await localizationManager.SaveLocalizationsAsync(criteriaLocalizations.ToArray());

            // Kategori lokalizasyonları
            var categoryLocalizations = GetCategoriesLocalizations();
            await localizationManager.SaveLocalizationsAsync(categoryLocalizations.ToArray());

            // Veri kaynağı lokalizasyonları
            var dataSourceLocalizations = GetDataSourceLocalizations();
            await localizationManager.SaveLocalizationsAsync(dataSourceLocalizations.ToArray());

            logger.LogInformation("Academic education static criteria localizations seeded successfully. Total: {Count} localizations",
                criteriaLocalizations.Count + categoryLocalizations.Count + dataSourceLocalizations.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Academic education static criteria localizations seeding failed: {Message}", ex.Message);
        }
    }

    /// <summary>
    /// A bölümü statik kriterleri için lokalizasyon kayıtlarını döndürür
    /// </summary>
    private static List<RlxLocalizationSaveDto> GetAcademicEducationStaticCriteriaLocalizations()
    {
        var localizations = new List<RlxLocalizationSaveDto>();

        // A1 - Ders Yükü
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A1_COURSE_LOAD",
            "Ders Yükü",
            "Course Load",
            "Ön lisans, lisans, lisansüstü yıllık ders saati toplamı",
            "Total annual course hours for associate, undergraduate, and graduate levels"
        ));

        // A2_1 - Öğrenci Değerlendirme Anketi
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A2_1_STUDENT_EVALUATION",
            "Öğrenci Değerlendirme Anketi",
            "Student Evaluation Survey",
            "Ortalama öğrenci ders öğretim elemanı değerlendirme anket puanının 4,25 veya üzeri olması",
            "Average student course instructor evaluation survey score of 4.25 or higher"
        ));

        // A3_1 - Danışmana Atanmış Öğrenci Sayısı
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_1_ASSIGNED_STUDENTS",
            "Danışmana Atanmış Öğrenci Sayısı",
            "Number of Students Assigned to Advisor",
            "Akademisyene danışmanlık için atanmış toplam öğrenci sayısı",
            "Total number of students assigned to the academician for advisory"
        ));

        // A3_2 - İletişime Geçtiği Öğrenci Sayısı
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_2_STUDENT_COMMUNICATION",
            "İletişime Geçtiği Öğrenci Sayısı",
            "Number of Students Contacted",
            "İletişime geçtiği kendisine atanmış öğrenci sayısı oranı (ARELIM sistemi üzerinden)",
            "Ratio of assigned students contacted (through ARELIM system)"
        ));

        // A3_3 - Öğrenci Memnuniyeti
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_3_STUDENT_SATISFACTION",
            "Öğrenci Memnuniyeti",
            "Student Satisfaction",
            "Danışmanlığı yapılan öğrenci memnuniyeti (güz veya bahar yarıyılında alınan en yüksek anket puanı)",
            "Student satisfaction for advisory services (highest survey score from fall or spring semester)"
        ));

        // A3_4 - Danışman Tutundurma Başarısı
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_4_ADVISOR_RETENTION_SUCCESS",
            "Danışman Tutundurma Başarısı",
            "Advisor Retention Success",
            "Öğrencilerinin yatay geçiş veya farklı nedenle üniversiteden ayrılmaması",
            "Students not leaving the university due to horizontal transfer or other reasons"
        ));

        // A3_5 - Öğrenci Kulübü Danışmanlığı
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_5_STUDENT_CLUB_ADVISORY",
            "Öğrenci Kulübü Danışmanlığı",
            "Student Club Advisory",
            "Danışmanlık yapılan öğrenci kulübü sayısı (En az yılda 2 etkinlik/10+ üye sayısı)",
            "Number of student clubs advised (Minimum 2 events per year/10+ members)"
        ));

        // A3_6 - Öğrenci GPA Ortalaması
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_6_STUDENT_GPA_AVERAGE",
            "Öğrenci GPA Ortalaması",
            "Student GPA Average",
            "Danışmana atanmış öğrencilerin GPA ortalaması (yıllık)",
            "GPA average of students assigned to advisor (annual)"
        ));

        // A3_7 - Onur Öğrenci Sayısı
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_7_HONOR_STUDENTS",
            "Onur Öğrenci Sayısı",
            "Number of Honor Students",
            "Danışmanın 'Onur' öğrenci sayısı (GPA 3,00 ve üzeri)",
            "Number of advisor's 'Honor' students (GPA 3.00 and above)"
        ));

        // A3_8 - Devamsızlık Oranı
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_8_ABSENTEEISM_RATE",
            "Devamsızlık Oranı",
            "Absenteeism Rate",
            "Danışmana atanan öğrencilerin devamsızlık yaparak derslerden kalma oranı",
            "Rate of students assigned to advisor failing courses due to absenteeism"
        ));

        // A3_9 - Erasmus Katılımı
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_9_ERASMUS_PARTICIPATION",
            "Erasmus Katılımı",
            "Erasmus Participation",
            "Danışmana atanmış öğrencilerden Erasmus programına katılanların sayısı",
            "Number of students assigned to advisor participating in Erasmus program"
        ));

        // A3_10 - Sosyal Kulüp Üyeliği
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_10_SOCIAL_CLUB_MEMBERSHIP",
            "Sosyal Kulüp Üyeliği",
            "Social Club Membership",
            "Danışmana atanmış öğrencilerden öğrenci sosyal kulüp organizasyonlarına üye olanların sayısı",
            "Number of students assigned to advisor who are members of student social club organizations"
        ));

        // A3_11 - Spor Kulübü Üyeliği
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_11_SPORTS_CLUB_MEMBERSHIP",
            "Spor Kulübü Üyeliği",
            "Sports Club Membership",
            "Danışmana atanmış öğrencilerden öğrenci spor klüplerine üye olanların sayısı",
            "Number of students assigned to advisor who are members of student sports clubs"
        ));

        // A3_12 - Çift Ana Dal/Yan Dal
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_12_DOUBLE_MAJOR_MINOR",
            "Çift Ana Dal/Yan Dal",
            "Double Major/Minor",
            "Danışmana atanmış öğrencilerin içinde çift ana dal veya yan dal yapanların sayısı",
            "Number of students assigned to advisor pursuing double major or minor programs"
        ));

        // A3_13 - Üniversite Takımları
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_13_UNIVERSITY_TEAMS",
            "Üniversite Takımları",
            "University Teams",
            "Danışmana atanmış üniversite takımlarında yer alan ve GPA'si 2.00'nin üzerinde olan takım öğrencisi sayısı",
            "Number of team students assigned to advisor who are on university teams and have GPA above 2.00"
        ));

        // A3_14 - Teknokent/TTO Projeleri
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_14_TECHNOPARK_TTO_PROJECTS",
            "Teknokent/TTO Projeleri",
            "Technopark/TTO Projects",
            "Danışmana atanmış Teknokent veya Teknoloji Transfer Ofisi (TTO) projelerine katılan öğrenci sayısı",
            "Number of students assigned to advisor participating in Technopark or Technology Transfer Office (TTO) projects"
        ));

        // A3_15 - TÜBİTAK 2209 Başvuruları
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A3_15_TUBITAK_2209_APPLICATIONS",
            "TÜBİTAK 2209 Başvuruları",
            "TUBITAK 2209 Applications",
            "Danışmana atanmış TÜBİTAK 2209 Üniversite Öğrencileri Araştırma Projeleri Destekleme Programı başvuru yapan öğrenci sayısı",
            "Number of students assigned to advisor applying to TUBITAK 2209 University Students Research Projects Support Program"
        ));

        // A4 - Yüksek Lisans Tez Yöneticiliği
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A4_MASTERS_SUPERVISION",
            "Yüksek Lisans Tez Yöneticiliği",
            "Master's Thesis Supervision",
            "Yüksek Lisans tez yöneticiliği (1. Danışman) - Onaylanmış tez planı ve en az bir dönem kayıt gerekli",
            "Master's thesis supervision (1st Advisor) - Approved thesis plan and at least one semester registration required"
        ));

        // A5 - Doktora Tez Yöneticiliği
        localizations.AddRange(CreateCriterionLocalizations(
            "ARELBRIDGE_A5_PHD_SUPERVISION",
            "Doktora Tez Yöneticiliği",
            "PhD Thesis Supervision",
            "Doktora tez yöneticiliği (1. Danışman) - Onaylanmış tez planı ve en az bir dönem kayıt gerekli",
            "PhD thesis supervision (1st Advisor) - Approved thesis plan and at least one semester registration required"
        ));

        return localizations;
    }

    /// <summary>
    /// Belirli bir kriter için Türkçe ve İngilizce lokalizasyon kayıtları oluşturur
    /// </summary>
    private static List<RlxLocalizationSaveDto> CreateCriterionLocalizations(
        string criterionId,
        string nameTr,
        string nameEn,
        string descriptionTr,
        string descriptionEn)
    {
        return new List<RlxLocalizationSaveDto>
        {
            // Türkçe - Name
            new RlxLocalizationSaveDto
            {
                ReferenceId = criterionId,
                Culture = "tr-TR",
                Key = "Name",
                Value = nameTr
            },
            // İngilizce - Name
            new RlxLocalizationSaveDto
            {
                ReferenceId = criterionId,
                Culture = "en-US",
                Key = "Name",
                Value = nameEn
            },
            // Türkçe - Description
            new RlxLocalizationSaveDto
            {
                ReferenceId = criterionId,
                Culture = "tr-TR",
                Key = "Description",
                Value = descriptionTr
            },
            // İngilizce - Description
            new RlxLocalizationSaveDto
            {
                ReferenceId = criterionId,
                Culture = "en-US",
                Key = "Description",
                Value = descriptionEn
            }
        };
    }

    /// <summary>
    /// Kriter kategorisi lokalizasyonları
    /// </summary>
    private static List<RlxLocalizationSaveDto> GetCategoriesLocalizations()
    {
        return new List<RlxLocalizationSaveDto>
        {
            // A Bölümü - Eğitim/Öğretim Faaliyetleri
            new RlxLocalizationSaveDto
            {
                ReferenceId = "ARELBRIDGE_CATEGORY_A",
                Culture = "tr-TR",
                Key = "Name",
                Value = "A. Eğitim/Öğretim Faaliyetleri"
            },
            new RlxLocalizationSaveDto
            {
                ReferenceId = "ARELBRIDGE_CATEGORY_A",
                Culture = "en-US",
                Key = "Name",
                Value = "A. Education/Teaching Activities"
            },
            new RlxLocalizationSaveDto
            {
                ReferenceId = "ARELBRIDGE_CATEGORY_A",
                Culture = "tr-TR",
                Key = "Description",
                Value = "Akademik eğitim ve öğretim faaliyetleri (30%)"
            },
            new RlxLocalizationSaveDto
            {
                ReferenceId = "ARELBRIDGE_CATEGORY_A",
                Culture = "en-US",
                Key = "Description",
                Value = "Academic education and teaching activities (30%)"
            }
        };
    }

    /// <summary>
    /// Veri kaynağı lokalizasyonları
    /// </summary>
    private static List<RlxLocalizationSaveDto> GetDataSourceLocalizations()
    {
        return new List<RlxLocalizationSaveDto>
        {
            new RlxLocalizationSaveDto
            {
                ReferenceId = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                Culture = "tr-TR",
                Key = "Name",
                Value = "ArelBridge Akademik Eğitim Faaliyetleri"
            },
            new RlxLocalizationSaveDto
            {
                ReferenceId = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                Culture = "en-US",
                Key = "Name",
                Value = "ArelBridge Academic Education Activities"
            },
            new RlxLocalizationSaveDto
            {
                ReferenceId = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                Culture = "tr-TR",
                Key = "Description",
                Value = "Tirtil veritabanı akademik_egitim_faliyet_hesapla_a fonksiyonundan gelen veriler"
            },
            new RlxLocalizationSaveDto
            {
                ReferenceId = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                Culture = "en-US",
                Key = "Description",
                Value = "Data from Tirtil database akademik_egitim_faliyet_hesapla_a function"
            }
        };
    }

    /// <summary>
    /// Statik kriter katsayılarını seed eder
    /// UI'dan değiştirilebilir varsayılan katsayı değerleri
    /// </summary>
    private static async Task SeedStaticCriterionCoefficients(AcademicPerformanceDbContext context)
    {
        try
        {
            // Mevcut katsayıları kontrol et
            if (await context.StaticCriterionCoefficients.AnyAsync())
            {
                return; // Zaten seed edilmiş
            }

            var coefficients = new List<StaticCriterionCoefficientEntity>
            {
                // A1 - Ders Yükü
                new StaticCriterionCoefficientEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A1_COURSE_LOAD",
                    CriterionName = "Ders Yükü",
                    Coefficient = 0.5m,
                    MaximumLimit = 100,
                    MinimumLimit = 0,
                    Category = "A",
                    DisplayOrder = 1,
                    Description = "Ön lisans, lisans, lisansüstü yıllık ders saati toplamı",
                    DataSource = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    IsActive = true,
                    IsApproved = true,
                    CreatedBy = "System",
                    ApprovedBy = "System",
                    ApprovedAt = DateTime.UtcNow,
                    EffectiveFrom = DateTime.UtcNow
                },

                // A2_1 - Öğrenci Değerlendirme Anketi
                new StaticCriterionCoefficientEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A2_1_STUDENT_EVALUATION",
                    CriterionName = "Öğrenci Değerlendirme Anketi",
                    Coefficient = 50m,
                    MaximumLimit = 50,
                    MinimumLimit = 0,
                    Category = "A",
                    DisplayOrder = 2,
                    Description = "Ortalama öğrenci ders öğretim elemanı değerlendirme anket puanının 4,25 veya üzeri olması",
                    DataSource = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    IsActive = true,
                    IsApproved = true,
                    CreatedBy = "System",
                    ApprovedBy = "System",
                    ApprovedAt = DateTime.UtcNow,
                    EffectiveFrom = DateTime.UtcNow
                },

                // A3_1 - Danışmana Atanmış Öğrenci Sayısı
                new StaticCriterionCoefficientEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_1_ASSIGNED_STUDENTS",
                    CriterionName = "Danışmana Atanmış Öğrenci Sayısı",
                    Coefficient = 2m,
                    MaximumLimit = 100,
                    MinimumLimit = 0,
                    Category = "A",
                    DisplayOrder = 3,
                    Description = "Akademisyene danışmanlık için atanmış toplam öğrenci sayısı",
                    DataSource = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    IsActive = true,
                    IsApproved = true,
                    CreatedBy = "System",
                    ApprovedBy = "System",
                    ApprovedAt = DateTime.UtcNow,
                    EffectiveFrom = DateTime.UtcNow
                },

                // A3_2 - İletişime Geçtiği Öğrenci Sayısı
                new StaticCriterionCoefficientEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_2_STUDENT_COMMUNICATION",
                    CriterionName = "İletişime Geçtiği Öğrenci Sayısı",
                    Coefficient = 1m,
                    MaximumLimit = 50,
                    MinimumLimit = 0,
                    Category = "A",
                    DisplayOrder = 4,
                    Description = "İletişime geçtiği kendisine atanmış öğrenci sayısı oranı (ARELIM sistemi üzerinden)",
                    DataSource = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    IsActive = true,
                    IsApproved = true,
                    CreatedBy = "System",
                    ApprovedBy = "System",
                    ApprovedAt = DateTime.UtcNow,
                    EffectiveFrom = DateTime.UtcNow
                },

                // A3_3 - Öğrenci Memnuniyeti
                new StaticCriterionCoefficientEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_3_STUDENT_SATISFACTION",
                    CriterionName = "Öğrenci Memnuniyeti",
                    Coefficient = 10m,
                    MaximumLimit = 50,
                    MinimumLimit = 0,
                    Category = "A",
                    DisplayOrder = 5,
                    Description = "Danışmanlığı yapılan öğrenci memnuniyeti (güz veya bahar yarıyılında alınan en yüksek anket puanı)",
                    DataSource = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    IsActive = true,
                    IsApproved = true,
                    CreatedBy = "System",
                    ApprovedBy = "System",
                    ApprovedAt = DateTime.UtcNow,
                    EffectiveFrom = DateTime.UtcNow
                },

                // A3_4 - Danışman Tutundurma Başarısı
                new StaticCriterionCoefficientEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_4_ADVISOR_RETENTION_SUCCESS",
                    CriterionName = "Danışman Tutundurma Başarısı",
                    Coefficient = 5m,
                    MaximumLimit = 25,
                    MinimumLimit = 0,
                    Category = "A",
                    DisplayOrder = 6,
                    Description = "Öğrencilerinin yatay geçiş veya farklı nedenle üniversiteden ayrılmaması",
                    DataSource = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    IsActive = true,
                    IsApproved = true,
                    CreatedBy = "System",
                    ApprovedBy = "System",
                    ApprovedAt = DateTime.UtcNow,
                    EffectiveFrom = DateTime.UtcNow
                },

                // A3_5 - Öğrenci Kulübü Danışmanlığı
                new StaticCriterionCoefficientEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_5_STUDENT_CLUB_ADVISORY",
                    CriterionName = "Öğrenci Kulübü Danışmanlığı",
                    Coefficient = 10m,
                    MaximumLimit = 30,
                    MinimumLimit = 0,
                    Category = "A",
                    DisplayOrder = 7,
                    Description = "Danışmanlık yapılan öğrenci kulübü sayısı (En az yılda 2 etkinlik/10+ üye sayısı)",
                    DataSource = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    IsActive = true,
                    IsApproved = true,
                    CreatedBy = "System",
                    ApprovedBy = "System",
                    ApprovedAt = DateTime.UtcNow,
                    EffectiveFrom = DateTime.UtcNow
                },

                // A3_6 - Öğrenci GPA Ortalaması
                new StaticCriterionCoefficientEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_6_STUDENT_GPA_AVERAGE",
                    CriterionName = "Öğrenci GPA Ortalaması",
                    Coefficient = 20m,
                    MaximumLimit = 20,
                    MinimumLimit = 0,
                    Category = "A",
                    DisplayOrder = 8,
                    Description = "Danışmana atanmış öğrencilerin GPA ortalaması (yıllık)",
                    DataSource = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    IsActive = true,
                    IsApproved = true,
                    CreatedBy = "System",
                    ApprovedBy = "System",
                    ApprovedAt = DateTime.UtcNow,
                    EffectiveFrom = DateTime.UtcNow
                },

                // A3_7 - Onur Öğrenci Sayısı
                new StaticCriterionCoefficientEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_7_HONOR_STUDENTS",
                    CriterionName = "Onur Öğrenci Sayısı",
                    Coefficient = 15m,
                    MaximumLimit = 15,
                    MinimumLimit = 0,
                    Category = "A",
                    DisplayOrder = 9,
                    Description = "Danışmanın 'Onur' öğrenci sayısı (GPA 3,00 ve üzeri)",
                    DataSource = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    IsActive = true,
                    IsApproved = true,
                    CreatedBy = "System",
                    ApprovedBy = "System",
                    ApprovedAt = DateTime.UtcNow,
                    EffectiveFrom = DateTime.UtcNow
                },

                // A3_8 - Devamsızlık Oranı
                new StaticCriterionCoefficientEntity
                {
                    StaticCriterionSystemId = "ARELBRIDGE_A3_8_ABSENTEEISM_RATE",
                    CriterionName = "Devamsızlık Oranı",
                    Coefficient = 10m,
                    MaximumLimit = 10,
                    MinimumLimit = 0,
                    Category = "A",
                    DisplayOrder = 10,
                    Description = "Danışmana atanan öğrencilerin devamsızlık yaparak derslerden kalma oranı",
                    DataSource = "ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY",
                    IsActive = true,
                    IsApproved = true,
                    CreatedBy = "System",
                    ApprovedBy = "System",
                    ApprovedAt = DateTime.UtcNow,
                    EffectiveFrom = DateTime.UtcNow
                }
            };

            context.StaticCriterionCoefficients.AddRange(coefficients);
            await context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            throw new Exception($"Statik kriter katsayıları seed edilirken hata oluştu: {ex.Message}", ex);
        }
    }
}
