using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using AcademicPerformance.Controllers.Base;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Consts;
using Rlx.Shared.Resources;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Controllers
{
    /// <summary>
    /// Generic Data Entry Controller
    /// Epic 5 - Specialized Workflows için generic veri girişi endpoint'leri
    /// </summary>
    [Route("[controller]/[action]")]
    [ApiController]
    public class GenericDataEntryController : BaseApiController
    {
        private readonly IGenericDataEntryManager _manager;
        private readonly IUserContextHelper _userContextHelper;
        private readonly IRlxSystemLogHelper<GenericDataEntryController> _systemLogHelper;
        private readonly ILogger<GenericDataEntryController> _logger;

        public GenericDataEntryController(
            IGenericDataEntryManager manager,
            IUserContextHelper userContextHelper,
            IRlxSystemLogHelper<GenericDataEntryController> systemLogHelper,
            ILogger<GenericDataEntryController> logger,
            IStringLocalizer<SharedResource> localizer) : base(localizer)
        {
            _manager = manager;
            _userContextHelper = userContextHelper;
            _systemLogHelper = systemLogHelper;
            _logger = logger;
        }

        #region Definition Management

        /// <summary>
        /// Generic data entry definition oluştur
        /// </summary>
        /// <param name="dto">Definition DTO</param>
        /// <returns>Oluşturulan definition</returns>
        [HttpPost]
        [Authorize(APConsts.Policies.ManageForms)]
        public async Task<IActionResult> CreateDefinition([FromBody] GenericDataEntryDefinitionCreateDto dto)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"Generic data entry definition oluşturuluyor: {dto.Name}, User: {userId}");

                var result = await _manager.CreateDefinitionAsync(dto, userId);

                await _systemLogHelper.LogInfoAsync($"Generic data entry definition başarıyla oluşturuldu: {result.Id}");
                return SuccessResponse(result, _localizer["DefinitionCreatedSuccessfully"].Value);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Generic data entry definition oluşturma validation hatası: {Name}", dto.Name);
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition oluşturma hatası: {Name}", dto.Name);
                return HandleException(ex, _localizer["ErrorCreatingDefinition"].Value);
            }
        }

        /// <summary>
        /// Generic data entry definition güncelle
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <param name="dto">Güncelleme DTO</param>
        /// <returns>Güncelleme sonucu</returns>
        [HttpPut]
        [Authorize(APConsts.Policies.ManageForms)]
        public async Task<IActionResult> UpdateDefinition(string definitionId, [FromBody] GenericDataEntryDefinitionUpdateDto dto)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"Generic data entry definition güncelleniyor: {definitionId}, User: {userId}");

                var result = await _manager.UpdateDefinitionAsync(definitionId, dto, userId);

                if (result)
                {
                    await _systemLogHelper.LogInfoAsync($"Generic data entry definition başarıyla güncellendi: {definitionId}");
                    return SuccessResponse(result, _localizer["DefinitionUpdatedSuccessfully"].Value);
                }
                else
                {
                    return BadRequestResponse(_localizer["DefinitionUpdateFailed"].Value);
                }
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Generic data entry definition güncelleme validation hatası: {Id}", definitionId);
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition güncelleme hatası: {Id}", definitionId);
                return HandleException(ex, _localizer["ErrorUpdatingDefinition"].Value);
            }
        }

        /// <summary>
        /// Generic data entry definition sil
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <returns>Silme sonucu</returns>
        [HttpDelete]
        [Authorize(APConsts.Policies.ManageForms)]
        public async Task<IActionResult> DeleteDefinition(string definitionId)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"Generic data entry definition siliniyor: {definitionId}, User: {userId}");

                var result = await _manager.DeleteDefinitionAsync(definitionId, userId);

                if (result)
                {
                    await _systemLogHelper.LogInfoAsync($"Generic data entry definition başarıyla silindi: {definitionId}");
                    return SuccessResponse(result, _localizer["DefinitionDeletedSuccessfully"].Value);
                }
                else
                {
                    return BadRequestResponse(_localizer["DefinitionDeleteFailed"].Value);
                }
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Generic data entry definition silme business rule hatası: {Id}", definitionId);
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition silme hatası: {Id}", definitionId);
                return HandleException(ex, _localizer["ErrorDeletingDefinition"].Value);
            }
        }

        /// <summary>
        /// Generic data entry definition getir
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <returns>Definition DTO</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> GetDefinition(string definitionId)
        {
            try
            {
                await _systemLogHelper.LogInfoAsync($"Generic data entry definition getiriliyor: {definitionId}");

                var result = await _manager.GetDefinitionByIdAsync(definitionId);

                if (result != null)
                {
                    return SuccessResponse(result, _localizer["DefinitionRetrievedSuccessfully"].Value);
                }
                else
                {
                    return NotFoundResponse(_localizer["DefinitionNotFound"].Value);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition getirme hatası: {Id}", definitionId);
                return HandleException(ex, _localizer["ErrorRetrievingDefinition"].Value);
            }
        }

        /// <summary>
        /// Generic data entry definition'ları listele
        /// </summary>
        /// <param name="co">Pagination ve filtreleme</param>
        /// <returns>Sayfalanmış definition listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> GetDefinitions([FromQuery] PagedListCo<GenericDataEntryFilterCo> co)
        {
            try
            {
                await _systemLogHelper.LogInfoAsync("Generic data entry definition'ları listeleniyor");

                var result = await _manager.GetDefinitionsAsync(co);

                await _systemLogHelper.LogInfoAsync($"Generic data entry definition'ları başarıyla listelendi: {result.TotalCount} adet");
                return SuccessResponse(result, _localizer["DefinitionsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition'ları listeleme hatası");
                return HandleException(ex, _localizer["ErrorRetrievingDefinitions"].Value);
            }
        }

        #endregion

        #region Record Management

        /// <summary>
        /// Generic data entry record oluştur
        /// </summary>
        /// <param name="dto">Record DTO</param>
        /// <returns>Oluşturulan record</returns>
        [HttpPost]
        [Authorize(APConsts.Policies.SubmitData)]
        public async Task<IActionResult> CreateRecord([FromBody] GenericDataEntryRecordCreateDto dto)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"Generic data entry record oluşturuluyor: {dto.DefinitionId}, User: {userId}");

                var result = await _manager.CreateRecordAsync(dto, userId);

                await _systemLogHelper.LogInfoAsync($"Generic data entry record başarıyla oluşturuldu: {result.Id}");
                return SuccessResponse(result, _localizer["RecordCreatedSuccessfully"].Value);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Generic data entry record oluşturma validation hatası: {DefinitionId}", dto.DefinitionId);
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record oluşturma hatası: {DefinitionId}", dto.DefinitionId);
                return HandleException(ex, _localizer["ErrorCreatingRecord"].Value);
            }
        }

        /// <summary>
        /// Generic data entry record güncelle
        /// </summary>
        /// <param name="recordId">Record ID</param>
        /// <param name="dto">Güncelleme DTO</param>
        /// <returns>Güncelleme sonucu</returns>
        [HttpPut]
        [Authorize(APConsts.Policies.EditData)]
        public async Task<IActionResult> UpdateRecord(string recordId, [FromBody] GenericDataEntryRecordUpdateDto dto)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"Generic data entry record güncelleniyor: {recordId}, User: {userId}");

                var result = await _manager.UpdateRecordAsync(recordId, dto, userId);

                if (result)
                {
                    await _systemLogHelper.LogInfoAsync($"Generic data entry record başarıyla güncellendi: {recordId}");
                    return SuccessResponse(result, _localizer["RecordUpdatedSuccessfully"].Value);
                }
                else
                {
                    return BadRequestResponse(_localizer["RecordUpdateFailed"].Value);
                }
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Generic data entry record güncelleme validation hatası: {Id}", recordId);
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record güncelleme hatası: {Id}", recordId);
                return HandleException(ex, _localizer["ErrorUpdatingRecord"].Value);
            }
        }

        /// <summary>
        /// Generic data entry record sil
        /// </summary>
        /// <param name="recordId">Record ID</param>
        /// <returns>Silme sonucu</returns>
        [HttpDelete]
        [Authorize(APConsts.Policies.EditData)]
        public async Task<IActionResult> DeleteRecord(string recordId)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"Generic data entry record siliniyor: {recordId}, User: {userId}");

                var result = await _manager.DeleteRecordAsync(recordId, userId);

                if (result)
                {
                    await _systemLogHelper.LogInfoAsync($"Generic data entry record başarıyla silindi: {recordId}");
                    return SuccessResponse(result, _localizer["RecordDeletedSuccessfully"].Value);
                }
                else
                {
                    return BadRequestResponse(_localizer["RecordDeleteFailed"].Value);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record silme hatası: {Id}", recordId);
                return HandleException(ex, _localizer["ErrorDeletingRecord"].Value);
            }
        }

        /// <summary>
        /// Generic data entry record getir
        /// </summary>
        /// <param name="recordId">Record ID</param>
        /// <returns>Record DTO</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> GetRecord(string recordId)
        {
            try
            {
                await _systemLogHelper.LogInfoAsync($"Generic data entry record getiriliyor: {recordId}");

                var result = await _manager.GetRecordByIdAsync(recordId);

                if (result != null)
                {
                    return SuccessResponse(result, _localizer["RecordRetrievedSuccessfully"].Value);
                }
                else
                {
                    return NotFoundResponse(_localizer["RecordNotFound"].Value);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record getirme hatası: {Id}", recordId);
                return HandleException(ex, _localizer["ErrorRetrievingRecord"].Value);
            }
        }

        /// <summary>
        /// Generic data entry record'ları listele
        /// </summary>
        /// <param name="co">Pagination ve filtreleme</param>
        /// <returns>Sayfalanmış record listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> GetRecords([FromQuery] PagedListCo<GenericDataEntryFilterCo> co)
        {
            try
            {
                await _systemLogHelper.LogInfoAsync("Generic data entry record'ları listeleniyor");

                var result = await _manager.GetRecordsAsync(co);

                await _systemLogHelper.LogInfoAsync($"Generic data entry record'ları başarıyla listelendi: {result.TotalCount} adet");
                return SuccessResponse(result, _localizer["RecordsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record'ları listeleme hatası");
                return HandleException(ex, _localizer["ErrorRetrievingRecords"].Value);
            }
        }

        /// <summary>
        /// Belirli definition için record'ları getir
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <param name="co">Pagination ve filtreleme</param>
        /// <returns>Sayfalanmış record listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> GetRecordsByDefinition(string definitionId, [FromQuery] PagedListCo<GenericDataEntryFilterCo> co)
        {
            try
            {
                await _systemLogHelper.LogInfoAsync($"Definition için record'lar listeleniyor: {definitionId}");

                var result = await _manager.GetRecordsByDefinitionAsync(definitionId, co);

                await _systemLogHelper.LogInfoAsync($"Definition için record'lar başarıyla listelendi: {result.TotalCount} adet");
                return SuccessResponse(result, _localizer["RecordsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Definition için record'ları listeleme hatası: {DefinitionId}", definitionId);
                return HandleException(ex, _localizer["ErrorRetrievingRecords"].Value);
            }
        }

        #endregion

        #region Statistics and Analytics

        /// <summary>
        /// Definition istatistikleri
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <returns>İstatistik DTO</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewReports)]
        public async Task<IActionResult> GetDefinitionStatistics(string definitionId)
        {
            try
            {
                await _systemLogHelper.LogInfoAsync($"Definition istatistikleri getiriliyor: {definitionId}");

                var result = await _manager.GetDefinitionStatisticsAsync(definitionId);

                return SuccessResponse(result, _localizer["StatisticsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Definition istatistikleri hatası: {DefinitionId}", definitionId);
                return HandleException(ex, _localizer["ErrorRetrievingStatistics"].Value);
            }
        }

        /// <summary>
        /// Genel istatistikler
        /// </summary>
        /// <returns>Genel istatistikler</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewReports)]
        public async Task<IActionResult> GetOverallStatistics()
        {
            try
            {
                await _systemLogHelper.LogInfoAsync("Genel istatistikler getiriliyor");

                var result = await _manager.GetOverallStatisticsAsync();

                return SuccessResponse(result, _localizer["StatisticsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Genel istatistikler hatası");
                return HandleException(ex, _localizer["ErrorRetrievingStatistics"].Value);
            }
        }

        #endregion
    }
}
