using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Rlx.Shared.Resources;
using Rlx.Shared.Bases;
using Rlx.Shared.Models;
using Rlx.Shared.Helpers;
namespace AcademicPerformance.Controllers.Base
{
    public abstract class BaseApiController : Rlx.Shared.Bases.BaseApiController
    {
        protected BaseApiController(IStringLocalizer<SharedResource> localizer) : base(localizer)
        {
        }
        // Rlx.Shared'de olmayan ek response metodları
        protected IActionResult NotFoundResponse(string? message = null)
        {
            var response = RlxApiResponser.Error(message ?? _localizer["ResourceNotFound"].Value);
            return NotFound(response);
        }
        protected IActionResult UnauthorizedResponse(string? message = null)
        {
            var response = RlxApiResponser.Error(message ?? _localizer["UnauthorizedAccess"].Value);
            return Unauthorized(response);
        }
        protected IActionResult BadRequestResponse(string message, object? data = null)
        {
            var response = RlxApiResponser.Error(message);
            return BadRequest(response);
        }
        protected IActionResult HandleException(Exception ex, string? customMessage = null)
        {
            var message = customMessage ?? _localizer["AnInternalServerErrorOccurred"].Value;
            var response = RlxApiResponser.Error(message);
            return StatusCode(500, response);
        }
    }
}
