using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using AcademicPerformance.Controllers.Base;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Models.MongoDocuments;
using AcademicPerformance.Consts;
using Rlx.Shared.Resources;
using Rlx.Shared.Interfaces;

namespace AcademicPerformance.Controllers
{
    /// <summary>
    /// MongoDB document size validation test controller'ı
    /// </summary>
    [Route("[controller]/[action]")]
    [ApiController]
    public class MongoValidationController : BaseApiController
    {
        private readonly IMongoDbService _mongoDbService;
        private readonly IRlxSystemLogHelper<MongoValidationController> _systemLogHelper;
        private readonly ILogger<MongoValidationController> _logger;

        public MongoValidationController(
            IMongoDbService mongoDbService,
            IRlxSystemLogHelper<MongoValidationController> systemLogHelper,
            ILogger<MongoValidationController> logger,
            IStringLocalizer<SharedResource> localizer) : base(localizer)
        {
            _mongoDbService = mongoDbService;
            _systemLogHelper = systemLogHelper;
            _logger = logger;
        }

        /// <summary>
        /// Test document'ının boyutunu hesapla
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> CalculateDocumentSize([FromBody] object testDocument)
        {
            try
            {
                await _systemLogHelper.LogInfoAsync("Document size calculation test başlatılıyor");

                var documentSize = await _mongoDbService.GetDocumentSizeAsync(testDocument);
                var sizeInMB = documentSize / (1024.0 * 1024.0);

                var result = new
                {
                    DocumentType = testDocument.GetType().Name,
                    SizeInBytes = documentSize,
                    SizeInMB = Math.Round(sizeInMB, 2),
                    IsWithinLimit = documentSize <= (16 * 1024 * 1024), // 16MB
                    MongoDBLimit = "16 MB",
                    WarningThreshold = "10 MB",
                    IsApproachingLimit = documentSize > (10 * 1024 * 1024),
                    Timestamp = DateTime.UtcNow
                };

                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Document size calculation hatası");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Document size validation test
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> ValidateDocumentSize([FromBody] object testDocument)
        {
            try
            {
                await _systemLogHelper.LogInfoAsync("Document size validation test başlatılıyor");

                var isValid = await _mongoDbService.ValidateDocumentSizeAsync(testDocument);
                var documentSize = await _mongoDbService.GetDocumentSizeAsync(testDocument);
                var sizeInMB = documentSize / (1024.0 * 1024.0);

                var result = new
                {
                    IsValid = isValid,
                    DocumentType = testDocument.GetType().Name,
                    SizeInBytes = documentSize,
                    SizeInMB = Math.Round(sizeInMB, 2),
                    ValidationResult = isValid ? "PASSED" : "FAILED",
                    Reason = isValid ? "Document size is within MongoDB limits" : "Document size exceeds MongoDB 16MB limit",
                    Timestamp = DateTime.UtcNow
                };

                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Document size validation hatası");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Büyük test document oluştur ve boyutunu test et
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> CreateLargeTestDocument([FromQuery] int sizeInMB = 1)
        {
            try
            {
                await _systemLogHelper.LogInfoAsync($"Large test document oluşturuluyor: {sizeInMB} MB");

                // Test için büyük bir document oluştur
                var largeData = new string('A', sizeInMB * 1024 * 1024); // Yaklaşık sizeInMB MB

                var testDocument = new DynamicCriterionTemplate
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Large Test Document",
                    Description = largeData, // Büyük veri buraya
                    Status = "Test",
                    CreatedAt = DateTime.UtcNow,
                    CreatedByUserId = "test-user",
                    Version = 1,
                    Coefficient = 1.0,
                    MaxLimit = 100,
                    Tags = new List<string> { "test", "large-document" },
                    InputFields = new List<DynamicInputField>
                    {
                        new DynamicInputField
                        {
                            FieldId = "test-field",
                            FieldName = "Test Field",
                            FieldType = "Text",
                            IsRequired = false,
                            DisplayOrder = 1
                        }
                    },
                    ValidationRules = new List<ValidationRule>()
                };

                var documentSize = await _mongoDbService.GetDocumentSizeAsync(testDocument);
                var actualSizeInMB = documentSize / (1024.0 * 1024.0);
                var isValid = await _mongoDbService.ValidateDocumentSizeAsync(testDocument);

                var result = new
                {
                    RequestedSizeMB = sizeInMB,
                    ActualSizeBytes = documentSize,
                    ActualSizeMB = Math.Round(actualSizeInMB, 2),
                    IsValid = isValid,
                    ValidationResult = isValid ? "PASSED" : "FAILED",
                    CanBeInserted = isValid,
                    MongoDBLimit = "16 MB",
                    Timestamp = DateTime.UtcNow,
                    Warning = actualSizeInMB > 10 ? "Document size is approaching MongoDB limit" : null
                };

                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Large test document oluşturma hatası");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Size validation ile document insert test
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> TestInsertWithValidation([FromQuery] int sizeInMB = 1)
        {
            try
            {
                await _systemLogHelper.LogInfoAsync($"Insert with validation test başlatılıyor: {sizeInMB} MB");

                // Test document oluştur
                var testData = sizeInMB <= 15 ? new string('T', sizeInMB * 1024 * 1024) : new string('T', 17 * 1024 * 1024); // 17MB için limit aşımı

                var testDocument = new DynamicCriterionTemplate
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = $"Validation Test Document {sizeInMB}MB",
                    Description = testData,
                    Status = "Test",
                    CreatedAt = DateTime.UtcNow,
                    CreatedByUserId = "test-user",
                    Version = 1,
                    Coefficient = 1.0,
                    MaxLimit = 100,
                    Tags = new List<string> { "test", "validation" },
                    InputFields = new List<DynamicInputField>
                    {
                        new DynamicInputField
                        {
                            FieldId = "validation-field",
                            FieldName = "Validation Field",
                            FieldType = "Text",
                            IsRequired = false,
                            DisplayOrder = 1
                        }
                    },
                    ValidationRules = new List<ValidationRule>()
                };

                var documentSize = await _mongoDbService.GetDocumentSizeAsync(testDocument);
                var actualSizeInMB = documentSize / (1024.0 * 1024.0);

                bool insertSuccess = false;
                string insertResult = "";
                string errorMessage = null;

                try
                {
                    // Size validation ile insert dene
                    await _mongoDbService.InsertDocumentWithSizeValidationAsync(testDocument);
                    insertSuccess = true;
                    insertResult = "SUCCESS";

                    // Test document'ı temizle
                    await _mongoDbService.DeleteDynamicCriterionTemplateAsync(testDocument.Id);
                }
                catch (InvalidOperationException ex)
                {
                    insertSuccess = false;
                    insertResult = "VALIDATION_FAILED";
                    errorMessage = ex.Message;
                }
                catch (Exception ex)
                {
                    insertSuccess = false;
                    insertResult = "ERROR";
                    errorMessage = ex.Message;
                }

                var result = new
                {
                    RequestedSizeMB = sizeInMB,
                    ActualSizeBytes = documentSize,
                    ActualSizeMB = Math.Round(actualSizeInMB, 2),
                    InsertSuccess = insertSuccess,
                    InsertResult = insertResult,
                    ErrorMessage = errorMessage,
                    ValidationWorking = !insertSuccess && actualSizeInMB > 16,
                    Timestamp = DateTime.UtcNow
                };

                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Insert with validation test hatası");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// MongoDB document size validation system health check
        /// </summary>
        [HttpGet]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> HealthCheck()
        {
            try
            {
                await _systemLogHelper.LogInfoAsync("MongoDB validation system health check başlatılıyor");

                // Küçük test document ile validation test
                var smallTestDoc = new { test = "small document", size = "minimal" };
                var smallDocSize = await _mongoDbService.GetDocumentSizeAsync(smallTestDoc);
                var smallDocValid = await _mongoDbService.ValidateDocumentSizeAsync(smallTestDoc);

                var healthStatus = new
                {
                    Status = "Healthy",
                    Timestamp = DateTime.UtcNow,
                    ValidationSystem = new
                    {
                        Status = "OK",
                        MongoDBLimit = "16 MB (16,777,216 bytes)",
                        WarningThreshold = "10 MB (10,485,760 bytes)",
                        SmallDocumentTest = new
                        {
                            Size = smallDocSize,
                            IsValid = smallDocValid,
                            Result = smallDocValid ? "PASSED" : "FAILED"
                        }
                    },
                    Features = new
                    {
                        DocumentSizeCalculation = "Available",
                        SizeValidation = "Available",
                        InsertWithValidation = "Available",
                        ReplaceWithValidation = "Available",
                        WarningThreshold = "Available"
                    }
                };

                return SuccessResponse(healthStatus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MongoDB validation health check hatası");
                return HandleException(ex);
            }
        }
    }
}
