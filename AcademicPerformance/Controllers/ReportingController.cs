using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Controllers.Base;
using AcademicPerformance.Consts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Resources;

namespace AcademicPerformance.Controllers
{
    /// <summary>
    /// Akademik performans raporlama endpoint'leri
    /// </summary>
    [ApiController]
    [Route("[controller]/[action]")]
    [Authorize(APConsts.Policies.AccessReporting)]
    public class ReportingController : BaseApiController
    {
        private readonly IReportingManager _reportingManager;
        private readonly ILogger<ReportingController> _logger;

        public ReportingController(
            IReportingManager reportingManager,
            ILogger<ReportingController> logger,
            IStringLocalizer<SharedResource> localizer) : base(localizer)
        {
            _reportingManager = reportingManager ?? throw new ArgumentNullException(nameof(reportingManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region Akademisyen Performans Raporları

        /// <summary>
        /// Akademisyen performans raporu oluştur
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Akademisyen performans raporu</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateAcademicianPerformanceReport(
            [FromQuery] string academicianUserId,
            [FromBody] PerformanceReportFilterCo filterCo)
        {
            try
            {
                _logger.LogInformation("Akademisyen {AcademicianUserId} için performans raporu oluşturuluyor", academicianUserId);

                if (string.IsNullOrEmpty(academicianUserId))
                    return BadRequestResponse("Akademisyen kullanıcı ID'si gereklidir");

                var report = await _reportingManager.GenerateAcademicianPerformanceReportAsync(academicianUserId, filterCo);

                _logger.LogInformation("Akademisyen {AcademicianUserId} için performans raporu başarıyla oluşturuldu", academicianUserId);
                return SuccessResponse(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Akademisyen {AcademicianUserId} için performans raporu oluşturulurken hata oluştu", academicianUserId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Çoklu akademisyen performans raporları oluştur (sayfalanmış)
        /// </summary>
        /// <param name="co">Sayfalama ve filtreleme kriterleri</param>
        /// <returns>Sayfalanmış akademisyen performans raporları</returns>
        [HttpPost]

        public async Task<IActionResult> GenerateMultiplePerformanceReports([FromBody] PagedListCo<PerformanceReportFilterCo> co)
        {
            try
            {
                _logger.LogInformation("Çoklu akademisyen performans raporları oluşturuluyor - Sayfa: {Page}, Boyut: {Size}", co.Pager.Page, co.Pager.Size);

                var reports = await _reportingManager.GenerateMultiplePerformanceReportsAsync(co);

                _logger.LogInformation("{Count} akademisyen performans raporu başarıyla oluşturuldu", reports.Count);
                return SuccessResponse(reports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çoklu akademisyen performans raporları oluşturulurken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Detaylı akademisyen raporu oluştur
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Detaylı akademisyen raporu</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateDetailedAcademicianReport(
            [FromQuery] string academicianUserId,
            [FromBody] PerformanceReportFilterCo filterCo)
        {
            try
            {
                _logger.LogInformation("Akademisyen {AcademicianUserId} için detaylı rapor oluşturuluyor", academicianUserId);

                if (string.IsNullOrEmpty(academicianUserId))
                    return BadRequestResponse("Akademisyen kullanıcı ID'si gereklidir");

                var report = await _reportingManager.GenerateDetailedAcademicianReportAsync(academicianUserId, filterCo);

                _logger.LogInformation("Akademisyen {AcademicianUserId} için detaylı rapor başarıyla oluşturuldu", academicianUserId);
                return SuccessResponse(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Akademisyen {AcademicianUserId} için detaylı rapor oluşturulurken hata oluştu", academicianUserId);
                return HandleException(ex);
            }
        }

        #endregion

        #region Bölüm Performans Raporları

        /// <summary>
        /// Bölüm performans raporu oluştur
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Bölüm performans raporu</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateDepartmentReport(
            [FromQuery] string departmentId,
            [FromBody] DepartmentReportFilterCo filterCo)
        {
            try
            {
                _logger.LogInformation("Bölüm {DepartmentId} için performans raporu oluşturuluyor", departmentId);

                if (string.IsNullOrEmpty(departmentId))
                    return BadRequestResponse("Bölüm ID'si gereklidir");

                var report = await _reportingManager.GenerateDepartmentReportAsync(departmentId, filterCo);

                _logger.LogInformation("Bölüm {DepartmentId} için performans raporu başarıyla oluşturuldu", departmentId);
                return SuccessResponse(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm {DepartmentId} için performans raporu oluşturulurken hata oluştu", departmentId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Çoklu bölüm performans raporları oluştur (sayfalanmış)
        /// </summary>
        /// <param name="co">Sayfalama ve filtreleme kriterleri</param>
        /// <returns>Sayfalanmış bölüm performans raporları</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateMultipleDepartmentReports([FromBody] PagedListCo<DepartmentReportFilterCo> co)
        {
            try
            {
                _logger.LogInformation("Çoklu bölüm performans raporları oluşturuluyor - Sayfa: {Page}, Boyut: {Size}", co.Pager.Page, co.Pager.Size);

                var reports = await _reportingManager.GenerateMultipleDepartmentReportsAsync(co);

                _logger.LogInformation("{Count} bölüm performans raporu başarıyla oluşturuldu", reports.Count);
                return SuccessResponse(reports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çoklu bölüm performans raporları oluşturulurken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Fakülte bölüm karşılaştırma raporu oluştur
        /// </summary>
        /// <param name="facultyId">Fakülte ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Fakülte bölüm karşılaştırma raporu</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateFacultyDepartmentComparison(
            [FromQuery] string facultyId,
            [FromBody] DepartmentReportFilterCo filterCo)
        {
            try
            {
                _logger.LogInformation("Fakülte {FacultyId} için bölüm karşılaştırma raporu oluşturuluyor", facultyId);

                if (string.IsNullOrEmpty(facultyId))
                    return BadRequestResponse("Fakülte ID'si gereklidir");

                var report = await _reportingManager.GenerateFacultyDepartmentComparisonAsync(facultyId, filterCo);

                _logger.LogInformation("Fakülte {FacultyId} için bölüm karşılaştırma raporu başarıyla oluşturuldu", facultyId);
                return SuccessResponse(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fakülte {FacultyId} için bölüm karşılaştırma raporu oluşturulurken hata oluştu", facultyId);
                return HandleException(ex);
            }
        }

        #endregion

        #region Kriter Analiz Raporları

        /// <summary>
        /// Kriter analiz raporu oluştur
        /// </summary>
        /// <param name="criterionId">Kriter ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Kriter analiz raporu</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateCriterionAnalysisReport(
            [FromQuery] string criterionId,
            [FromBody] CriterionAnalysisFilterCo filterCo)
        {
            try
            {
                _logger.LogInformation("Kriter {CriterionId} için analiz raporu oluşturuluyor", criterionId);

                if (string.IsNullOrEmpty(criterionId))
                    return BadRequestResponse("Kriter ID'si gereklidir");

                var report = await _reportingManager.GenerateCriterionAnalysisReportAsync(criterionId, filterCo);

                _logger.LogInformation("Kriter {CriterionId} için analiz raporu başarıyla oluşturuldu", criterionId);
                return SuccessResponse(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Kriter {CriterionId} için analiz raporu oluşturulurken hata oluştu", criterionId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Çoklu kriter analiz raporları oluştur (sayfalanmış)
        /// </summary>
        /// <param name="co">Sayfalama ve filtreleme kriterleri</param>
        /// <returns>Sayfalanmış kriter analiz raporları</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateMultipleCriterionAnalysis([FromBody] PagedListCo<CriterionAnalysisFilterCo> co)
        {
            try
            {
                _logger.LogInformation("Çoklu kriter analiz raporları oluşturuluyor - Sayfa: {Page}, Boyut: {Size}", co.Pager.Page, co.Pager.Size);

                var reports = await _reportingManager.GenerateMultipleCriterionAnalysisAsync(co);

                _logger.LogInformation("{Count} kriter analiz raporu başarıyla oluşturuldu", reports.Count);
                return SuccessResponse(reports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çoklu kriter analiz raporları oluşturulurken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Kategori bazında kriter performans analizi
        /// </summary>
        /// <param name="categoryId">Kategori ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Kategori kriter analiz raporları</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateCategoryAnalysis(
            [FromQuery] string categoryId,
            [FromBody] CriterionAnalysisFilterCo filterCo)
        {
            try
            {
                _logger.LogInformation("Kategori {CategoryId} için kriter analizi oluşturuluyor", categoryId);

                if (string.IsNullOrEmpty(categoryId))
                    return BadRequestResponse("Kategori ID'si gereklidir");

                var reports = await _reportingManager.GenerateCategoryAnalysisAsync(categoryId, filterCo);

                _logger.LogInformation("Kategori {CategoryId} için {Count} kriter analizi başarıyla oluşturuldu", categoryId, reports.Count);
                return SuccessResponse(reports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Kategori {CategoryId} için kriter analizi oluşturulurken hata oluştu", categoryId);
                return HandleException(ex);
            }
        }

        #endregion

        #region Trend Analiz Raporları

        /// <summary>
        /// Trend analizi raporu oluştur
        /// </summary>
        /// <param name="filterCo">Trend analizi filtreleme kriterleri</param>
        /// <returns>Trend analizi raporu</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateTrendAnalysisReport([FromBody] TrendAnalysisFilterCo filterCo)
        {
            try
            {
                _logger.LogInformation("Trend analizi raporu oluşturuluyor - Tip: {AnalysisType}", filterCo.AnalysisType);

                var report = await _reportingManager.GenerateTrendAnalysisReportAsync(filterCo);

                _logger.LogInformation("Trend analizi raporu başarıyla oluşturuldu");
                return SuccessResponse(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Trend analizi raporu oluşturulurken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Akademisyen performans trendi analizi
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="filterCo">Trend analizi filtreleme kriterleri</param>
        /// <returns>Akademisyen trend analizi raporu</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateAcademicianTrendAnalysis(
            [FromQuery] string academicianUserId,
            [FromBody] TrendAnalysisFilterCo filterCo)
        {
            try
            {
                _logger.LogInformation("Akademisyen {AcademicianUserId} için trend analizi oluşturuluyor", academicianUserId);

                if (string.IsNullOrEmpty(academicianUserId))
                    return BadRequestResponse("Akademisyen kullanıcı ID'si gereklidir");

                var report = await _reportingManager.GenerateAcademicianTrendAnalysisAsync(academicianUserId, filterCo);

                _logger.LogInformation("Akademisyen {AcademicianUserId} için trend analizi başarıyla oluşturuldu", academicianUserId);
                return SuccessResponse(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Akademisyen {AcademicianUserId} için trend analizi oluşturulurken hata oluştu", academicianUserId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm performans trendi analizi
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="filterCo">Trend analizi filtreleme kriterleri</param>
        /// <returns>Bölüm trend analizi raporu</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateDepartmentTrendAnalysis(
            [FromQuery] string departmentId,
            [FromBody] TrendAnalysisFilterCo filterCo)
        {
            try
            {
                _logger.LogInformation("Bölüm {DepartmentId} için trend analizi oluşturuluyor", departmentId);

                if (string.IsNullOrEmpty(departmentId))
                    return BadRequestResponse("Bölüm ID'si gereklidir");

                var report = await _reportingManager.GenerateDepartmentTrendAnalysisAsync(departmentId, filterCo);

                _logger.LogInformation("Bölüm {DepartmentId} için trend analizi başarıyla oluşturuldu", departmentId);
                return SuccessResponse(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm {DepartmentId} için trend analizi oluşturulurken hata oluştu", departmentId);
                return HandleException(ex);
            }
        }

        #endregion

        #region Performans Hesaplama Endpoint'leri

        /// <summary>
        /// Akademisyen genel performans skoru hesapla
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Genel performans skoru</returns>
        [HttpGet]
        public async Task<IActionResult> CalculateAcademicianOverallScore(
            [FromQuery] string academicianUserId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Akademisyen {AcademicianUserId} için genel performans skoru hesaplanıyor", academicianUserId);

                if (string.IsNullOrEmpty(academicianUserId))
                    return BadRequestResponse("Akademisyen kullanıcı ID'si gereklidir");

                var score = await _reportingManager.CalculateAcademicianOverallScoreAsync(academicianUserId, startDate, endDate);

                _logger.LogInformation("Akademisyen {AcademicianUserId} için genel performans skoru: {Score}", academicianUserId, score);
                return SuccessResponse(score);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Akademisyen {AcademicianUserId} için genel performans skoru hesaplanırken hata oluştu", academicianUserId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm genel performans skoru hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Bölüm genel performans skoru</returns>
        [HttpGet]
        public async Task<IActionResult> CalculateDepartmentOverallScore(
            [FromQuery] string departmentId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Bölüm {DepartmentId} için genel performans skoru hesaplanıyor", departmentId);

                if (string.IsNullOrEmpty(departmentId))
                    return BadRequestResponse("Bölüm ID'si gereklidir");

                var score = await _reportingManager.CalculateDepartmentOverallScoreAsync(departmentId, startDate, endDate);

                _logger.LogInformation("Bölüm {DepartmentId} için genel performans skoru: {Score}", departmentId, score);
                return SuccessResponse(score);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm {DepartmentId} için genel performans skoru hesaplanırken hata oluştu", departmentId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Kategori bazında performans skoru hesapla
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="categoryId">Kategori ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Kategori performans skoru</returns>
        [HttpGet]
        public async Task<IActionResult> CalculateCategoryScore(
            [FromQuery] string academicianUserId,
            [FromQuery] string categoryId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Akademisyen {AcademicianUserId} için kategori {CategoryId} skoru hesaplanıyor", academicianUserId, categoryId);

                if (string.IsNullOrEmpty(academicianUserId))
                    return BadRequestResponse("Akademisyen kullanıcı ID'si gereklidir");

                if (string.IsNullOrEmpty(categoryId))
                    return BadRequestResponse("Kategori ID'si gereklidir");

                var score = await _reportingManager.CalculateCategoryScoreAsync(academicianUserId, categoryId, startDate, endDate);

                _logger.LogInformation("Akademisyen {AcademicianUserId} için kategori {CategoryId} skoru: {Score}", academicianUserId, categoryId, score);
                return SuccessResponse(score);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Akademisyen {AcademicianUserId} için kategori {CategoryId} skoru hesaplanırken hata oluştu", academicianUserId, categoryId);
                return HandleException(ex);
            }
        }

        #endregion

        #region İstatistik ve Analiz Endpoint'leri

        /// <summary>
        /// Akademisyen istatistikleri hesapla
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Akademisyen istatistikleri</returns>
        [HttpGet]
        public async Task<IActionResult> CalculateAcademicianStatistics(
            [FromQuery] string academicianUserId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Akademisyen {AcademicianUserId} için istatistikler hesaplanıyor", academicianUserId);

                if (string.IsNullOrEmpty(academicianUserId))
                    return BadRequestResponse("Akademisyen kullanıcı ID'si gereklidir");

                var statistics = await _reportingManager.CalculateAcademicianStatisticsAsync(academicianUserId, startDate, endDate);

                _logger.LogInformation("Akademisyen {AcademicianUserId} için istatistikler başarıyla hesaplandı", academicianUserId);
                return SuccessResponse(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Akademisyen {AcademicianUserId} için istatistikler hesaplanırken hata oluştu", academicianUserId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm istatistikleri hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Bölüm istatistikleri</returns>
        [HttpGet]
        public async Task<IActionResult> CalculateDepartmentStatistics(
            [FromQuery] string departmentId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Bölüm {DepartmentId} için istatistikler hesaplanıyor", departmentId);

                if (string.IsNullOrEmpty(departmentId))
                    return BadRequestResponse("Bölüm ID'si gereklidir");

                var statistics = await _reportingManager.CalculateDepartmentStatisticsAsync(departmentId, startDate, endDate);

                _logger.LogInformation("Bölüm {DepartmentId} için istatistikler başarıyla hesaplandı", departmentId);
                return SuccessResponse(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm {DepartmentId} için istatistikler hesaplanırken hata oluştu", departmentId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Performans dağılımı hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Performans dağılımı</returns>
        [HttpGet]
        public async Task<IActionResult> CalculatePerformanceDistribution(
            [FromQuery] string departmentId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Bölüm {DepartmentId} için performans dağılımı hesaplanıyor", departmentId);

                if (string.IsNullOrEmpty(departmentId))
                    return BadRequestResponse("Bölüm ID'si gereklidir");

                var distribution = await _reportingManager.CalculatePerformanceDistributionAsync(departmentId, startDate, endDate);

                _logger.LogInformation("Bölüm {DepartmentId} için performans dağılımı başarıyla hesaplandı", departmentId);
                return SuccessResponse(distribution);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm {DepartmentId} için performans dağılımı hesaplanırken hata oluştu", departmentId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Gelişmiş istatistiksel analiz
        /// </summary>
        /// <param name="analysisCo">Analiz kriterleri</param>
        /// <returns>İstatistiksel analiz sonuçları</returns>
        [HttpPost]
        public async Task<IActionResult> PerformStatisticalAnalysis([FromBody] StatisticalAnalysisCo analysisCo)
        {
            try
            {
                _logger.LogInformation("İstatistiksel analiz yapılıyor - Tip: {AnalysisType}", analysisCo.AnalysisType);

                var analysisResult = await _reportingManager.PerformStatisticalAnalysisAsync(analysisCo);

                _logger.LogInformation("İstatistiksel analiz başarıyla tamamlandı");
                return SuccessResponse(analysisResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "İstatistiksel analiz yapılırken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Dashboard veri toplama
        /// </summary>
        /// <param name="dashboardCo">Dashboard kriterleri</param>
        /// <returns>Dashboard verileri</returns>
        [HttpPost]
        public async Task<IActionResult> AggregateDashboardData([FromBody] DashboardDataCo dashboardCo)
        {
            try
            {
                _logger.LogInformation("Dashboard verileri toplanıyor - Kullanıcı: {UserId}", dashboardCo.UserId);

                var dashboardData = await _reportingManager.AggregateDashboardDataAsync(dashboardCo);

                _logger.LogInformation("Dashboard verileri başarıyla toplandı");
                return SuccessResponse(dashboardData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Dashboard verileri toplanırken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Performans metrikleri hesaplama
        /// </summary>
        /// <param name="metricsCo">Metrik hesaplama kriterleri</param>
        /// <returns>Hesaplanmış performans metrikleri</returns>
        [HttpPost]
        public async Task<IActionResult> CalculatePerformanceMetrics([FromBody] PerformanceMetricsCo metricsCo)
        {
            try
            {
                _logger.LogInformation("Performans metrikleri hesaplanıyor - Tip: {MetricType}", metricsCo.MetricType);

                var metrics = await _reportingManager.CalculatePerformanceMetricsAsync(metricsCo);

                _logger.LogInformation("Performans metrikleri başarıyla hesaplandı");
                return SuccessResponse(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Performans metrikleri hesaplanırken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Gerçek zamanlı analiz
        /// </summary>
        /// <param name="realtimeCo">Gerçek zamanlı analiz kriterleri</param>
        /// <returns>Gerçek zamanlı analiz sonuçları</returns>
        [HttpPost]
        public async Task<IActionResult> PerformRealtimeAnalysis([FromBody] RealtimeAnalysisCo realtimeCo)
        {
            try
            {
                _logger.LogInformation("Gerçek zamanlı analiz yapılıyor - Tip: {AnalysisType}", realtimeCo.AnalysisType);

                var realtimeResult = await _reportingManager.PerformRealtimeAnalysisAsync(realtimeCo);

                _logger.LogInformation("Gerçek zamanlı analiz başarıyla tamamlandı");
                return SuccessResponse(realtimeResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Gerçek zamanlı analiz yapılırken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Batch analiz işlemi
        /// </summary>
        /// <param name="batchCo">Batch analiz kriterleri</param>
        /// <returns>Batch analiz sonuçları</returns>
        [HttpPost]
        public async Task<IActionResult> PerformBatchAnalysis([FromBody] BatchAnalysisCo batchCo)
        {
            try
            {
                _logger.LogInformation("Batch analiz başlatılıyor - İşlem ID: {JobId}", batchCo.JobId);

                var batchResult = await _reportingManager.PerformBatchAnalysisAsync(batchCo);

                _logger.LogInformation("Batch analiz başarıyla başlatıldı - İşlem ID: {JobId}", batchResult.JobId);
                return SuccessResponse(batchResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Batch analiz başlatılırken hata oluştu");
                return HandleException(ex);
            }
        }

        #endregion

        #region Export ve Kaydetme Endpoint'leri

        /// <summary>
        /// Rapor export et
        /// </summary>
        /// <param name="exportCo">Export kriterleri</param>
        /// <returns>Export bilgileri</returns>
        [HttpPost]
        public async Task<IActionResult> ExportReport([FromBody] ReportExportCo exportCo)
        {
            try
            {
                _logger.LogInformation("Rapor export ediliyor - Format: {Format}, Tip: {ReportType}", exportCo.Format, exportCo.ReportType);

                var exportResult = await _reportingManager.ExportReportAsync(exportCo);

                _logger.LogInformation("Rapor export işlemi {ExportId} başarıyla başlatıldı", exportResult.ExportId);
                return SuccessResponse(exportResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Rapor export edilirken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Rapor oluştur ve kaydet
        /// </summary>
        /// <param name="generateCo">Rapor oluşturma kriterleri</param>
        /// <returns>Kaydedilen rapor ID'si</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateAndSaveReport([FromBody] GenerateReportCo generateCo)
        {
            try
            {
                _logger.LogInformation("Rapor oluşturuluyor ve kaydediliyor - Tip: {ReportType}, Ad: {ReportName}", generateCo.ReportType, generateCo.ReportName);

                var reportId = await _reportingManager.GenerateAndSaveReportAsync(generateCo);

                _logger.LogInformation("Rapor {ReportId} başarıyla oluşturuldu ve kaydedildi", reportId);
                return SuccessResponse(reportId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Rapor oluşturulup kaydedilirken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Kaydedilmiş raporu getir
        /// </summary>
        /// <param name="reportId">Rapor ID'si</param>
        /// <returns>Kaydedilmiş rapor</returns>
        [HttpGet]
        public async Task<IActionResult> GetSavedReport([FromQuery] string reportId)
        {
            try
            {
                _logger.LogInformation("Kaydedilmiş rapor {ReportId} getiriliyor", reportId);

                if (string.IsNullOrEmpty(reportId))
                    return BadRequestResponse("Rapor ID'si gereklidir");

                var report = await _reportingManager.GetSavedReportAsync(reportId);

                _logger.LogInformation("Kaydedilmiş rapor {ReportId} başarıyla getirildi", reportId);
                return SuccessResponse(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Kaydedilmiş rapor {ReportId} getirilirken hata oluştu", reportId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Kaydedilmiş raporları listele
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="co">Sayfalama kriterleri</param>
        /// <returns>Sayfalanmış kaydedilmiş raporlar</returns>
        [HttpPost]
        public async Task<IActionResult> GetSavedReports(
            [FromQuery] string userId,
            [FromBody] PagedListCo<object> co)
        {
            try
            {
                _logger.LogInformation("Kullanıcı {UserId} için kaydedilmiş raporlar listeleniyor", userId);

                if (string.IsNullOrEmpty(userId))
                    return BadRequestResponse("Kullanıcı ID'si gereklidir");

                var reports = await _reportingManager.GetSavedReportsAsync(userId, co);

                _logger.LogInformation("Kullanıcı {UserId} için {Count} kaydedilmiş rapor listelendi", userId, reports.Count);
                return SuccessResponse(reports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Kullanıcı {UserId} için kaydedilmiş raporlar listelenirken hata oluştu", userId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// PDF rapor oluştur
        /// </summary>
        /// <param name="pdfExportCo">PDF export kriterleri</param>
        /// <returns>PDF dosya bilgileri</returns>
        [HttpPost]
        public async Task<IActionResult> ExportToPdf([FromBody] PdfExportCo pdfExportCo)
        {
            try
            {
                _logger.LogInformation("PDF rapor oluşturuluyor - Tip: {ReportType}, Template: {Template}",
                    pdfExportCo.ReportType, pdfExportCo.TemplateId);

                var pdfResult = await _reportingManager.ExportToPdfAsync(pdfExportCo);

                _logger.LogInformation("PDF rapor başarıyla oluşturuldu - FileId: {FileId}", pdfResult.FileId);
                return SuccessResponse(pdfResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "PDF rapor oluşturulurken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Excel rapor oluştur
        /// </summary>
        /// <param name="excelExportCo">Excel export kriterleri</param>
        /// <returns>Excel dosya bilgileri</returns>
        [HttpPost]
        public async Task<IActionResult> ExportToExcel([FromBody] ExcelExportCo excelExportCo)
        {
            try
            {
                _logger.LogInformation("Excel rapor oluşturuluyor - Tip: {ReportType}, Sheets: {SheetCount}",
                    excelExportCo.ReportType, excelExportCo.IncludeSheets?.Count ?? 1);

                var excelResult = await _reportingManager.ExportToExcelAsync(excelExportCo);

                _logger.LogInformation("Excel rapor başarıyla oluşturuldu - FileId: {FileId}", excelResult.FileId);
                return SuccessResponse(excelResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Excel rapor oluşturulurken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// CSV veri export et
        /// </summary>
        /// <param name="csvExportCo">CSV export kriterleri</param>
        /// <returns>CSV dosya bilgileri</returns>
        [HttpPost]
        public async Task<IActionResult> ExportToCsv([FromBody] CsvExportCo csvExportCo)
        {
            try
            {
                _logger.LogInformation("CSV veri export ediliyor - Tip: {DataType}, Delimiter: {Delimiter}",
                    csvExportCo.DataType, csvExportCo.Delimiter);

                var csvResult = await _reportingManager.ExportToCsvAsync(csvExportCo);

                _logger.LogInformation("CSV veri başarıyla export edildi - FileId: {FileId}", csvResult.FileId);
                return SuccessResponse(csvResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CSV veri export edilirken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Custom template ile rapor oluştur
        /// </summary>
        /// <param name="templateExportCo">Template export kriterleri</param>
        /// <returns>Template rapor bilgileri</returns>
        [HttpPost]
        public async Task<IActionResult> ExportWithCustomTemplate([FromBody] TemplateExportCo templateExportCo)
        {
            try
            {
                _logger.LogInformation("Custom template rapor oluşturuluyor - Template: {TemplateId}, Format: {OutputFormat}",
                    templateExportCo.TemplateId, templateExportCo.OutputFormat);

                var templateResult = await _reportingManager.ExportWithCustomTemplateAsync(templateExportCo);

                _logger.LogInformation("Custom template rapor başarıyla oluşturuldu - FileId: {FileId}", templateResult.FileId);
                return SuccessResponse(templateResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Custom template rapor oluşturulurken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Export durumunu kontrol et
        /// </summary>
        /// <param name="exportId">Export ID'si</param>
        /// <returns>Export durumu</returns>
        [HttpGet]
        public async Task<IActionResult> GetExportStatus([FromQuery] string exportId)
        {
            try
            {
                _logger.LogInformation("Export durumu kontrol ediliyor - ExportId: {ExportId}", exportId);

                if (string.IsNullOrEmpty(exportId))
                    return BadRequestResponse("Export ID'si gereklidir");

                var status = await _reportingManager.GetExportStatusAsync(exportId);

                return SuccessResponse(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Export durumu kontrol edilirken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Export dosyasını indir
        /// </summary>
        /// <param name="fileId">Dosya ID'si</param>
        /// <returns>Dosya stream</returns>
        [HttpGet]
        public async Task<IActionResult> DownloadExportFile([FromQuery] string fileId)
        {
            try
            {
                _logger.LogInformation("Export dosyası indiriliyor - FileId: {FileId}", fileId);

                if (string.IsNullOrEmpty(fileId))
                    return BadRequestResponse("Dosya ID'si gereklidir");

                var fileResult = await _reportingManager.DownloadExportFileAsync(fileId);

                if (fileResult == null)
                    return NotFound("Dosya bulunamadı");

                return File(fileResult.FileStream, fileResult.ContentType, fileResult.FileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Export dosyası indirilirken hata oluştu");
                return HandleException(ex);
            }
        }

        #endregion

        #region Sıralama ve Karşılaştırma Endpoint'leri

        /// <summary>
        /// Akademisyen sıralaması oluştur
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si (opsiyonel)</param>
        /// <param name="facultyId">Fakülte ID'si (opsiyonel)</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Akademisyen sıralaması</returns>
        [HttpGet]
        public async Task<IActionResult> GetAcademicianRanking(
            [FromQuery] string? departmentId,
            [FromQuery] string? facultyId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Akademisyen sıralaması oluşturuluyor - Bölüm: {DepartmentId}, Fakülte: {FacultyId}", departmentId, facultyId);

                var ranking = await _reportingManager.GetAcademicianRankingAsync(departmentId, facultyId, startDate, endDate);

                _logger.LogInformation("{Count} akademisyen sıralaması başarıyla oluşturuldu", ranking.Count);
                return SuccessResponse(ranking);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Akademisyen sıralaması oluşturulurken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm sıralaması oluştur
        /// </summary>
        /// <param name="facultyId">Fakülte ID'si (opsiyonel)</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Bölüm sıralaması</returns>
        [HttpGet]
        public async Task<IActionResult> GetDepartmentRanking(
            [FromQuery] string? facultyId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Bölüm sıralaması oluşturuluyor - Fakülte: {FacultyId}", facultyId);

                var ranking = await _reportingManager.GetDepartmentRankingAsync(facultyId, startDate, endDate);

                _logger.LogInformation("{Count} bölüm sıralaması başarıyla oluşturuldu", ranking.Count);
                return SuccessResponse(ranking);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm sıralaması oluşturulurken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Karşılaştırmalı analiz yap
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Karşılaştırmalı analiz</returns>
        [HttpGet]
        public async Task<IActionResult> GenerateComparativeAnalysis(
            [FromQuery] string academicianUserId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Akademisyen {AcademicianUserId} için karşılaştırmalı analiz yapılıyor", academicianUserId);

                if (string.IsNullOrEmpty(academicianUserId))
                    return BadRequestResponse("Akademisyen kullanıcı ID'si gereklidir");

                var analysis = await _reportingManager.GenerateComparativeAnalysisAsync(academicianUserId, startDate, endDate);

                _logger.LogInformation("Akademisyen {AcademicianUserId} için karşılaştırmalı analiz başarıyla yapıldı", academicianUserId);
                return SuccessResponse(analysis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Akademisyen {AcademicianUserId} için karşılaştırmalı analiz yapılırken hata oluştu", academicianUserId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Gelişmiş filtreleme ile bölüm performans raporu
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="filterCo">Gelişmiş filtreleme kriterleri</param>
        /// <returns>Filtrelenmiş bölüm performans raporu</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateAdvancedDepartmentReport(
            [FromQuery] string departmentId,
            [FromBody] AdvancedReportFilterCo filterCo)
        {
            try
            {
                _logger.LogInformation("Bölüm {DepartmentId} için gelişmiş performans raporu oluşturuluyor", departmentId);

                if (string.IsNullOrEmpty(departmentId))
                    return BadRequestResponse("Bölüm ID'si gereklidir");

                var report = await _reportingManager.GenerateAdvancedDepartmentReportAsync(departmentId, filterCo);

                _logger.LogInformation("Bölüm {DepartmentId} için gelişmiş performans raporu başarıyla oluşturuldu", departmentId);
                return SuccessResponse(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm {DepartmentId} için gelişmiş performans raporu oluşturulurken hata oluştu", departmentId);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Çoklu akademisyen karşılaştırmalı analiz
        /// </summary>
        /// <param name="academicianIds">Akademisyen ID'leri</param>
        /// <param name="filterCo">Karşılaştırma kriterleri</param>
        /// <returns>Çoklu akademisyen karşılaştırmalı analiz</returns>
        [HttpPost]
        public async Task<IActionResult> GenerateMultiAcademicianComparison(
            [FromBody] MultiAcademicianComparisonCo comparisonCo)
        {
            try
            {
                _logger.LogInformation("Çoklu akademisyen karşılaştırmalı analiz yapılıyor - {Count} akademisyen", comparisonCo.AcademicianIds.Count);

                if (comparisonCo.AcademicianIds == null || !comparisonCo.AcademicianIds.Any())
                    return BadRequestResponse("En az bir akademisyen ID'si gereklidir");

                if (comparisonCo.AcademicianIds.Count > 10)
                    return BadRequestResponse("En fazla 10 akademisyen karşılaştırılabilir");

                var comparison = await _reportingManager.GenerateMultiAcademicianComparisonAsync(comparisonCo);

                _logger.LogInformation("Çoklu akademisyen karşılaştırmalı analiz başarıyla tamamlandı");
                return SuccessResponse(comparison);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çoklu akademisyen karşılaştırmalı analiz yapılırken hata oluştu");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Performans trend analizi
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="trendCo">Trend analiz kriterleri</param>
        /// <returns>Performans trend analizi</returns>
        [HttpPost]
        public async Task<IActionResult> GeneratePerformanceTrendAnalysis(
            [FromQuery] string academicianUserId,
            [FromBody] TrendAnalysisCo trendCo)
        {
            try
            {
                _logger.LogInformation("Akademisyen {AcademicianUserId} için performans trend analizi yapılıyor", academicianUserId);

                if (string.IsNullOrEmpty(academicianUserId))
                    return BadRequestResponse("Akademisyen kullanıcı ID'si gereklidir");

                var trendAnalysis = await _reportingManager.GeneratePerformanceTrendAnalysisAsync(academicianUserId, trendCo);

                _logger.LogInformation("Akademisyen {AcademicianUserId} için performans trend analizi başarıyla tamamlandı", academicianUserId);
                return SuccessResponse(trendAnalysis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Akademisyen {AcademicianUserId} için performans trend analizi yapılırken hata oluştu", academicianUserId);
                return HandleException(ex);
            }
        }

        #endregion

        #region Utility Endpoint'leri

        /// <summary>
        /// API sağlık kontrolü
        /// </summary>
        /// <returns>API durumu</returns>
        [HttpGet]

        public IActionResult HealthCheck()
        {
            try
            {
                _logger.LogInformation("Reporting API sağlık kontrolü yapılıyor");

                var healthStatus = new
                {
                    Status = "Healthy",
                    Timestamp = DateTime.UtcNow,
                    Service = "AcademicPerformance.ReportingController",
                    Version = "1.0.0"
                };

                return SuccessResponse(healthStatus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Reporting API sağlık kontrolü sırasında hata oluştu");
                return HandleException(ex);
            }
        }

        #endregion
    }
}
