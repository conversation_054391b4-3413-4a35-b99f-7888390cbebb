using AcademicPerformance.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Rlx.Shared.Bases;
using Rlx.Shared.Resources;

namespace AcademicPerformance.Controllers;

/// <summary>
/// Performance monitoring and metrics controller
/// </summary>
[ApiController]
[Route("[controller]/[action]")]
[Authorize] // Require authentication for performance data
public class PerformanceController : BaseApiController
{
    private readonly IPerformanceMonitoringService _performanceService;
    private readonly ICacheService _cacheService;

    public PerformanceController(IPerformanceMonitoringService performanceService, ICacheService cacheService, IStringLocalizer<SharedResource> localizer) : base(localizer)
    {
        _performanceService = performanceService;
        _cacheService = cacheService;
    }

    /// <summary>
    /// Get performance metrics for specified time range
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetMetrics([FromQuery] DateTime? from = null, [FromQuery] DateTime? to = null)
    {
        try
        {
            var metrics = await _performanceService.GetMetricsAsync(from, to);
            return SuccessResponse(metrics);
        }
        catch (Exception ex)
        {
            return ErrorResponse(null, ex.Message);
        }
    }

    /// <summary>
    /// Get slow queries report
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetSlowQueries([FromQuery] int thresholdMs = 1000, [FromQuery] int limit = 100)
    {
        try
        {
            var threshold = TimeSpan.FromMilliseconds(thresholdMs);
            var slowQueries = await _performanceService.GetSlowQueriesAsync(threshold, limit);
            return SuccessResponse(slowQueries);
        }
        catch (Exception ex)
        {
            return ErrorResponse(null, ex.Message);
        }
    }

    /// <summary>
    /// Get cache performance report
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetCachePerformance()
    {
        try
        {
            var cachePerformance = await _performanceService.GetCachePerformanceAsync();

            var combinedReport = new
            {
                Performance = cachePerformance
            };

            return SuccessResponse(combinedReport);
        }
        catch (Exception ex)
        {
            return ErrorResponse(null, ex.Message);
        }
    }

    /// <summary>
    /// Get system health status
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetHealthStatus()
    {
        try
        {
            var metrics = await _performanceService.GetMetricsAsync(DateTime.UtcNow.AddHours(-1));
            // Cache stats would be retrieved from performance service

            var healthStatus = new
            {
                Status = "Healthy", // This would be calculated based on thresholds
                Timestamp = DateTime.UtcNow,
                Metrics = new
                {
                    AverageResponseTime = metrics.AverageResponseTime,
                    ErrorRate = metrics.ErrorRate,
                    TotalRequests = metrics.TotalRequests,
                    CacheHitRatio = 0.0 // Would be calculated from cache performance
                },
                Thresholds = new
                {
                    MaxAverageResponseTime = 2000, // ms
                    MaxErrorRate = 0.05, // 5%
                    MinCacheHitRatio = 0.8 // 80%
                }
            };

            return SuccessResponse(healthStatus);
        }
        catch (Exception ex)
        {
            return ErrorResponse(null, ex.Message);
        }
    }

    /// <summary>
    /// Clear old performance data
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin")] // Only admins can clear data
    public async Task<IActionResult> ClearOldData([FromQuery] int daysOld = 7)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);
            await _performanceService.CleanupOldDataAsync(cutoffDate);

            return SuccessResponse(new { Message = $"Cleared performance data older than {daysOld} days", CutoffDate = cutoffDate });
        }
        catch (Exception ex)
        {
            return ErrorResponse(null, ex.Message);
        }
    }

    /// <summary>
    /// Get performance dashboard data
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetDashboard()
    {
        try
        {
            var last24Hours = await _performanceService.GetMetricsAsync(DateTime.UtcNow.AddHours(-24));
            var last7Days = await _performanceService.GetMetricsAsync(DateTime.UtcNow.AddDays(-7));
            var slowQueries = await _performanceService.GetSlowQueriesAsync(TimeSpan.FromMilliseconds(1000), 10);
            var cachePerformance = await _performanceService.GetCachePerformanceAsync();

            var dashboard = new
            {
                Last24Hours = last24Hours,
                Last7Days = last7Days,
                TopSlowQueries = slowQueries.Take(5),
                CachePerformance = new
                {
                    cachePerformance.HitRatio,
                    cachePerformance.TotalRequests,
                    cachePerformance.AverageHitTime,
                    cachePerformance.AverageMissTime
                },
                Summary = new
                {
                    HealthStatus = last24Hours.ErrorRate < 0.05 ? "Healthy" : "Warning",
                    TrendDirection = last24Hours.AverageResponseTime < last7Days.AverageResponseTime ? "Improving" : "Degrading",
                    LastUpdated = DateTime.UtcNow
                }
            };

            return SuccessResponse(dashboard);
        }
        catch (Exception ex)
        {
            return ErrorResponse(null, ex.Message);
        }
    }

    /// <summary>
    /// Get endpoint performance breakdown
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetEndpointPerformance([FromQuery] DateTime? from = null, [FromQuery] DateTime? to = null)
    {
        try
        {
            var metrics = await _performanceService.GetMetricsAsync(from, to);

            var endpointBreakdown = metrics.EndpointPerformance
                .Select(kvp => new
                {
                    Endpoint = kvp.Key,
                    AverageResponseTime = kvp.Value,
                    Category = CategorizeEndpoint(kvp.Key),
                    Status = kvp.Value < 1000 ? "Good" : kvp.Value < 2000 ? "Warning" : "Critical"
                })
                .OrderByDescending(e => e.AverageResponseTime)
                .ToList();

            return SuccessResponse(endpointBreakdown);
        }
        catch (Exception ex)
        {
            return ErrorResponse(null, ex.Message);
        }
    }

    private static string CategorizeEndpoint(string endpoint)
    {
        return endpoint.ToLower() switch
        {
            var e when e.Contains("form") => "Form Management",
            var e when e.Contains("submission") => "Submission Management",
            var e when e.Contains("criteria") => "Criteria Management",
            var e when e.Contains("report") => "Reporting",
            var e when e.Contains("feedback") => "Feedback System",
            var e when e.Contains("performance") => "Performance Monitoring",
            _ => "Other"
        };
    }
}
