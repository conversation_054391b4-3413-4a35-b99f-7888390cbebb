using Rlx.Shared.DbContexts;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace Rlx.Shared.Interfaces;

public interface IRlxEnumStore<TContext> where TContext : RlxEnumDbContext
{
    Task<RlxEnumDto> GetEnumAsync(string id);
    Task<RlxEnum?> GetEnumForUpdateAsync(string id);
    Task<IEnumerable<RlxEnum>> GetEnumsForUpdateAsync(string[] ids);
    Task AddEnumAsync(RlxEnum rlxEnum, bool saveChanges = true);
    Task UpdateEnumAsync(RlxEnum rlxEnum, bool saveChanges = true);
    Task AddEnumsAsync(IEnumerable<RlxEnum> rlxEnums, bool saveChanges = true);
    Task UpdateEnumsAsync(IEnumerable<RlxEnum> rlxEnums, bool saveChanges = true);
    Task<RlxEnumValue?> GetEnumValueForUpdateAsync(string id);
    Task<IEnumerable<RlxEnumValue>?> GetEnumValuesForUpdateAsync(int enumId);
    Task<IEnumerable<RlxEnumValue>> GetEnumValuesForUpdateAsync(string[] ids);
    Task AddEnumValueAsync(RlxEnumValue rlxEnumValue, bool saveChanges = false);
    Task AddEnumValuesAsync(IEnumerable<RlxEnumValue> rlxEnumValues, bool saveChanges = false);
    Task UpdateEnumValuesAsync(IEnumerable<RlxEnumValue> rlxEnumValues, bool saveChanges = false);
    Task<IDictionary<string, int>> IdConvertForEnumValue(IEnumerable<string> enumValueIds);
}