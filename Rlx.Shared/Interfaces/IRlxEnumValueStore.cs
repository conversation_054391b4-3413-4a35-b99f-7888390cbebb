// using Rlx.Shared.DbContexts;
// using Rlx.Shared.Models.Dtos;
// using Rlx.Shared.Models.RlxEnumDbContextModels;
// namespace Rlx.Shared.Interfaces;
// public interface IRlxEnumValueStore<TContext> where TContext : RlxEnumDbContext
// {
//     Task<RlxEnumValue?> GetEnumValueForUpdateAsync(string id);
//     Task<IEnumerable<RlxEnumValue>?> GetEnumValuesForUpdateAsync(int enumId);
//     Task<IEnumerable<RlxEnumValue>> GetEnumValuesForUpdateAsync(string[] ids);
//     Task AddEnumValueAsync(RlxEnumValue rlxEnumValue, bool saveChanges = true);
//     Task AddEnumValuesAsync(IEnumerable<RlxEnumValue> rlxEnumValues, bool saveChanges = true);
//     Task UpdateEnumValuesAsync(IEnumerable<RlxEnumValue> rlxEnumValues, bool saveChanges = true);
// }