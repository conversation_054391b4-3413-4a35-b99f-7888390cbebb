using Rlx.Shared.DbContexts;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.RlxLocalizationDbContextModels;
namespace Rlx.Shared.Interfaces;

public interface IRlxLocalizationStore<TContext> where TContext : RlxLocalizationDbContext
{
   Task AddLocalizationAsync(RlxLocalization data);
   Task<IEnumerable<RlxLocalizationDto>> GetLocalizationsAsync(GetRlxLocalizationsCo co);
   Task<RlxLocalization> GetLocalizationDataForSaveAsync(string referenceId, string culture, string key);
   Task<bool> LocalizationExistsAsync(string ReferenceId, string Culture, string Key);
   Task<bool> LocalizationExistsAsync(string id);
   Task<bool> LocalizationExistsWithDifferentIdAsync(string id, string ReferenceId, string Culture, string Key);
   Task UpdateLocalizationAsync(RlxLocalization data);
   Task<int> GetAutoIncrementIdAsync(string id);
   Task DeleteLocalizationAsync(string id);
}