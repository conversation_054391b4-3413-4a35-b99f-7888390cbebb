using Microsoft.EntityFrameworkCore;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace Rlx.Shared.DbContexts;
public abstract class RlxEnumDbContext : DbContext
{
    public RlxEnumDbContext(DbContextOptions options) : base(options) { }
    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        builder.Entity<RlxEnum>(e =>
        {
            e.<PERSON>(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.Name).HasMaxLength(256);
            e.Property(e2 => e2.Code).HasMaxLength(256);
        });
        builder.Entity<RlxEnum>().HasIndex(u => u.AutoIncrementId);
        builder.Entity<RlxEnumValue>(e =>
        {
            e.<PERSON>(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.Name).HasMaxLength(256);
            e.Property(e2 => e2.Code).HasMaxLength(256);
        });
        builder.Entity<RlxEnumValue>().HasIndex(u => u.AutoIncrementId);
        builder.Entity<RlxEnum>(e =>
        {
            e.HasMany(e2 => e2.RlxEnumValues)
            .WithOne(e2 => e2.RlxEnum)
            .HasForeignKey(e2 => e2.EnumId)
            .HasPrincipalKey(e2 => e2.AutoIncrementId);
        });
    }
    public DbSet<RlxEnum> RlxEnums { get; set; }
    public DbSet<RlxEnumValue> RlxEnumValues { get; set; }
}