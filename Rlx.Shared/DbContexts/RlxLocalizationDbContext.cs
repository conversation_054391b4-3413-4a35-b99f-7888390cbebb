using Microsoft.EntityFrameworkCore;
using Rlx.Shared.Models.RlxLocalizationDbContextModels;
namespace Rlx.Shared.DbContexts;
public abstract class RlxLocalizationDbContext : DbContext
{
    public RlxLocalizationDbContext(DbContextOptions options) : base(options) { }
    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        builder.Entity<RlxLocalization>(b =>
        {
            b.<PERSON><PERSON>(b => b.Id);
            b.Property(u => u.AutoIncrementId).IsRequired().ValueGeneratedOnAdd();
            b.Property(u => u.ReferenceId).IsRequired().HasMaxLength(256);
            b.Property(u => u.Culture).IsRequired().HasMaxLength(256);
            b.Property(u => u.Key).IsRequired().HasMaxLength(256);
            b.Property(u => u.Value).IsRequired();
        });
        builder.Entity<RlxLocalization>().HasIndex(u => new { u.ReferenceId, u.Culture, u.Key }).IsUnique();
        builder.Entity<RlxLocalization>().HasIndex(u => u.AutoIncrementId);
    }
    public DbSet<RlxLocalization> RlxLocalizations { get; set; }
}