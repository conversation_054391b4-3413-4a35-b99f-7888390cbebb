﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PackageId>Rlx.Shared</PackageId>
    <RepositoryUrl>https://github.com/arel-uni/Rlx.Shared</RepositoryUrl>
    <Version>1.0.34</Version>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.8" />
    <PackageReference Include="MongoDB.Driver" Version="3.2.1" />
    <PackageReference Include="RabbitMQ.Client" Version="7.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.Middleware" Version="8.0.8" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.24" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.1" />
  </ItemGroup>
</Project>