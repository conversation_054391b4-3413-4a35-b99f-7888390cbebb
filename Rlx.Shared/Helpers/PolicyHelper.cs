using Microsoft.AspNetCore.Authorization;
using Rlx.Shared.Models;

namespace Rlx.Shared.Helpers;

public static class PolicyHelper
{
    public static void AddRlxPolicy(this AuthorizationOptions options, RlxPolicy[] items)
    {
        options.AddPolicy($"{items.First().ClaimType}.{items.First().ClaimValue}", policy =>
        {
            policy.RequireAssertion(context =>
            items.Any(x => context.User.HasClaim(x.ClaimType, x.ClaimValue)));
        });
    }

    public static string GetPolicyName(string type, string val)
    {
        return $"{type}.{val}";
    }
}