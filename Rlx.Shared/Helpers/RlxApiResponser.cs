using Rlx.Shared.Models;
namespace Rlx.Shared.Helpers;

public class RlxApiResponser<T>
{
    private static RlxApiResponse<T> CreateResponseModel(string status, string? message = null, T? data = default, object? meta = null)
    {
        return new RlxApiResponse<T>
        {
            Status = status,
            Message = message,
            Data = data,
            Meta = meta
        };
    }
    public static RlxApiResponse<T> Success(T? data = default, string? message = null, object? meta = null)
    {
        return CreateResponseModel("Success", message, data, meta);
    }
    public static RlxApiResponse<T> Error(T? data = default, string? message = null, object? meta = null)
    {
        return CreateResponseModel("Error", message, data, meta);
    }
    public static RlxApiResponse<T> Warning(T? data = default, string? message = null, object? meta = null)
    {
        return CreateResponseModel("Warning", message, data, meta);
    }
    public static RlxApiResponse<T> Info(T? data = default, string? message = null, object? meta = null)
    {
        return CreateResponseModel("Info", message, data, meta);
    }
}
public class RlxApiResponser
{
    private static RlxApiResponse CreateResponseModel(string status, string? message = null)
    {
        return new RlxApiResponse
        {
            Status = status,
            Message = message
        };
    }
    public static RlxApiResponse Success(string? message = null)
    {
        return CreateResponseModel("Success", message);
    }
    public static RlxApiResponse Error(string? message = null)
    {
        return CreateResponseModel("Error", message);
    }
    public static RlxApiResponse Warning(string? message = null)
    {
        return CreateResponseModel("Warning", message);
    }
    public static RlxApiResponse Info(string? message = null)
    {
        return CreateResponseModel("Info", message);
    }
}
