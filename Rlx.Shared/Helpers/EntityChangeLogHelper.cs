using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Configuration;
using Rlx.Shared.Factories;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;
namespace Rlx.Shared.Helpers;
public class EntityChangeLogHelper : IEntityChangeLogHelper
{
    private readonly RlxQueueServiceFactory _rlxQueueServiceFactory;
    private readonly IRlxQueueService _rlxQueueService;
    private readonly IConfiguration _configuration;
    private readonly IUserContextHelper _userContextHelper;
    public EntityChangeLogHelper(RlxQueueServiceFactory rlxQueueServiceFactory, IConfiguration configuration, IUserContextHelper userContextHelper)
    {
        _configuration = configuration;
        _userContextHelper = userContextHelper;
        _rlxQueueServiceFactory = rlxQueueServiceFactory;
        _rlxQueueService = _rlxQueueServiceFactory.Create("EntityLog");
    }

    public async Task AddEntityChangeLogAsync(DbContext dbContext)
    {
        var dbName = dbContext.Database.GetDbConnection().Database;
        var entries = dbContext.ChangeTracker.Entries();
        var excludeEntities = _configuration.GetSection("EntityLog:ExcludeEntities").Get<string[]>();
        var messages = new List<string>();
        foreach (var entry in entries)
        {
            if (entry.State == EntityState.Detached || entry.State == EntityState.Unchanged || excludeEntities?.Contains(entry.Metadata.ShortName()) == true)
            {
                continue;
            }
            var rlxEntity = new RlxEntityLogDto
            {
                Date = DateTime.UtcNow,
                UserId = _userContextHelper.GetUserId(),
                EntityId = GetPrimaryKeyValue(entry),
                EntityState = entry.State.ToString(),
                DbName = dbName,
                ModelName = entry.Metadata.GetTableName()
            };
            switch (entry.State)
            {
                case EntityState.Added:
                    rlxEntity.ChangedProperties = entry.Properties
                        .Select(p => new EntityPropertyChangeDetailDto { Name = p.Metadata.Name, Value = p.CurrentValue })
                        .ToList();
                    break;
                case EntityState.Modified:
                    rlxEntity.ChangedProperties = entry.Properties
                    .Where(p => p.OriginalValue == null && p.CurrentValue != null || p.OriginalValue != null && !p.OriginalValue.Equals(p.CurrentValue))
                    .Select(p => new EntityPropertyChangeDetailDto { Name = p.Metadata.Name, Value = p.CurrentValue, OldValue = p.OriginalValue })
                    .ToList();
                    break;
            }
            var message = JsonSerializer.Serialize(rlxEntity);
            messages.Add(message);
        }
        if (messages.Count > 0)
            await _rlxQueueService.SendMessagesAsync("entity_queue", messages.ToArray());
    }
    public string? GetPrimaryKeyValue(EntityEntry entry)
    {
        var primaryKey = entry.Metadata.FindPrimaryKey();
        if (primaryKey == null)
        {
            return null;
        }
        var primaryKeyValue = primaryKey.Properties
            .Select(p => entry.Property(p.Name).CurrentValue)
            .FirstOrDefault()?.ToString();
        return primaryKeyValue;
    }
}
