using System.Text.RegularExpressions;

namespace Rlx.Shared.Helpers;

public static class TextHelper
{
    public static string ToSnakeCase(this string text) => string.Concat(text.Select((x, i) => i > 0 && char.IsUpper(x) ? "_" + x : x.ToString())).ToLower();

    public static string RlxNormalize(this string? text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return "";
        text = text.Trim();
        text = Regex.Replace(text, @"\s+", " ");
        text = text.Replace("ç", "c")
                   .Replace("ğ", "g")
                   .Replace("ı", "i")
                   .Replace("ö", "o")
                   .Replace("ş", "s")
                   .Replace("ü", "u")
                   .Replace("Ç", "C")
                   .Replace("Ğ", "G")
                   .Replace("İ", "I")
                   .Replace("Ö", "O")
                   .Replace("Ş", "S")
                   .Replace("Ü", "U")
                   .Replace(" ", "_");

        text = text.ToLowerInvariant();
        // text=Regex.Replace(text, @"[^a-zA-Z0-9\s]", "");
        return text;


    }

  public static string RlxJoin(params string[] args) => string.Join(".", args);
}