using Microsoft.AspNetCore.Http;
using Rlx.Shared.Interfaces;
using System.IdentityModel.Tokens.Jwt;
namespace Rlx.Shared.Helpers;

public class UserContextHelper : IUserContextHelper
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    public UserContextHelper(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }
    public string? GetUserId()
    {
        var handler = new JwtSecurityTokenHandler();
        var token = _httpContextAccessor.HttpContext?.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
        if (string.IsNullOrEmpty(token))
        {
            return null;
        }

        try
        {
            var jwtToken = handler.ReadJwtToken(token);

            // Farklı claim type'larını kontrol et
            var userIdClaim = jwtToken.Claims.FirstOrDefault(c =>
                c.Type == "sub" ||
                c.Type == "user_id" ||
                c.Type == "uid" ||
                c.Type == "username" ||
                c.Type == "preferred_username" ||
                c.Type == "name" ||
                c.Type == "email");

            return userIdClaim?.Value;
        }
        catch (Exception)
        {
            // JWE token'ları için fallback - HttpContext'ten user claim'lerini al
            var user = _httpContextAccessor.HttpContext?.User;
            if (user?.Identity?.IsAuthenticated == true)
            {
                return user.Claims.FirstOrDefault(c =>
                    c.Type == "sub" ||
                    c.Type == "user_id" ||
                    c.Type == "uid" ||
                    c.Type == "username" ||
                    c.Type == "preferred_username" ||
                    c.Type == "name" ||
                    c.Type == "email")?.Value;
            }
            return null;
        }
        // return _httpContextAccessor.HttpContext?.Request.Headers["UserId"].FirstOrDefault();
    }

    public string? GetUserIp()
    {
        var ipAddress = _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString();
        return ipAddress;
    }

    public static string? GetUserId(HttpContext context)
    {
        var handler = new JwtSecurityTokenHandler();
        var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
        if (string.IsNullOrEmpty(token))
        {
            return null;
        }

        try
        {
            var jwtToken = handler.ReadJwtToken(token);

            // Farklı claim type'larını kontrol et
            var userIdClaim = jwtToken.Claims.FirstOrDefault(c =>
                c.Type == "sub" ||
                c.Type == "user_id" ||
                c.Type == "uid" ||
                c.Type == "username" ||
                c.Type == "preferred_username" ||
                c.Type == "name" ||
                c.Type == "email");

            return userIdClaim?.Value;
        }
        catch (Exception)
        {
            // JWE token'ları için fallback - HttpContext'ten user claim'lerini al
            var user = context.User;
            if (user?.Identity?.IsAuthenticated == true)
            {
                return user.Claims.FirstOrDefault(c =>
                    c.Type == "sub" ||
                    c.Type == "user_id" ||
                    c.Type == "uid" ||
                    c.Type == "username" ||
                    c.Type == "preferred_username" ||
                    c.Type == "name" ||
                    c.Type == "email")?.Value;
            }
            return null;
        }
    }
}