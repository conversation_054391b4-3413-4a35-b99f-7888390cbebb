
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Rlx.Core.Consts;
using Rlx.Shared.Factories;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;

namespace Rlx.Shared.Helpers;

public class RlxSystemLogHelper<T> : IRlxSystemLogHelper<T>
{
    private readonly RlxQueueServiceFactory _rlxQueueServiceFactory;
    private readonly IRlxQueueService _rlxQueueService;
    private readonly IConfiguration _configuration;
    private readonly IUserContextHelper _userContextHelper;
    private readonly string _source;
    private readonly string _module;
    private readonly string? _userId;
    private readonly string? _ip;
    private readonly bool _enabled;

    public RlxSystemLogHelper(RlxQueueServiceFactory rabbitMQServiceFactory, IConfiguration configuration, IUserContextHelper userContextHelper)
    {
        _rlxQueueServiceFactory = rabbitMQServiceFactory;
        _rlxQueueService = _rlxQueueServiceFactory.Create("SystemLog");
        _configuration = configuration;
        _module = _configuration["SystemLog:Module"]!;
        _enabled = _configuration["SystemLog:Enabled"] == "1";
        _source = typeof(T).FullName ?? typeof(T).Name;
        _userContextHelper = userContextHelper;
        _userId = _userContextHelper.GetUserId();
        _ip = _userContextHelper.GetUserIp();
    }



    public async Task LogDebugAsync(string message, string? source = null)
    {
        if (!_enabled)
        {
            return;
        }
        var log = new RlxSystemLogDto
        {
            Source = source ?? _source,
            Type = RlxSystemLogConst.TypeDebug,
            Message = message,
            Date = DateTime.UtcNow,
            Module = _module,
            UserId = _userId,
            IpAddress = _ip
        };
        await _rlxQueueService.SendMessageAsync(RlxSystemLogConst.QueueName, JsonSerializer.Serialize(log));
    }

    public async Task LogErrorAsync(string message, Exception? exception = null, string? source = null)
    {
        if (!_enabled)
        {
            return;
        }
        var log = new RlxSystemLogDto
        {
            Source = source ?? _source,
            Type = RlxSystemLogConst.TypeError,
            Message = message,
            Date = DateTime.UtcNow,
            Module = _module,
            UserId = _userId,
            IpAddress = _ip,
            Exception = exception
        };
        await _rlxQueueService.SendMessageAsync(RlxSystemLogConst.QueueName, JsonSerializer.Serialize(log));
    }

    public async Task LogInfoAsync(string message, string? source = null)
    {
        if (!_enabled)
        {
            return;
        }
        var log = new RlxSystemLogDto
        {
            Source = source ?? _source,
            Type = RlxSystemLogConst.TypeInfo,
            Message = message,
            Date = DateTime.UtcNow,
            Module = _module,
            UserId = _userId,
            IpAddress = _ip
        };
        await _rlxQueueService.SendMessageAsync(RlxSystemLogConst.QueueName, JsonSerializer.Serialize(log));
    }

    public async Task LogWarnAsync(string message, string? source = null)
    {
        if (!_enabled)
        {
            return;
        }
        var log = new RlxSystemLogDto
        {
            Source = source ?? _source,
            Type = RlxSystemLogConst.TypeWarn,
            Message = message,
            Date = DateTime.UtcNow,
            Module = _module,
            UserId = _userId,
            IpAddress = _ip
        };
        await _rlxQueueService.SendMessageAsync(RlxSystemLogConst.QueueName, JsonSerializer.Serialize(log));
    }
}