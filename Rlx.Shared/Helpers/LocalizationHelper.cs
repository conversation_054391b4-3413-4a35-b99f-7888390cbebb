namespace Rlx.Shared.Helpers;

public static class LocalizationHelper
{
    public static async Task AttachLocalizationsAsync<T, TLocalization, TKey>(this
    IEnumerable<T> entities,
    Func<T, TKey> getId,
    Func<IEnumerable<TKey>, Task<IEnumerable<TLocalization>>> fetchLocalizations,
    Func<TLocalization, TKey> getReferenceId,
    Action<T, List<TLocalization>> setLocalizations) where TKey : notnull
    {
        var entityList = entities as IList<T> ?? entities.ToList();
        if (entityList.Count == 0)
            return;
        var ids = entityList.Select(getId).ToArray();
        var localizations = await fetchLocalizations(ids);
        if (localizations == null || !localizations.Any())
            return;
        var entityDict = entityList.ToDictionary(getId);
        var localizationDict = localizations
            .GroupBy(getReferenceId)
            .ToDictionary(g => g.Key, g => g.ToList());
        foreach (var kvp in localizationDict)
        {
            if (entityDict.TryGetValue(kvp.Key, out var entity))
            {
                setLocalizations(entity, kvp.Value);
            }
        }
    }
}
