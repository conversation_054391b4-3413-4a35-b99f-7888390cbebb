using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using Rlx.Shared.Interfaces;
namespace Rlx.Shared.Services;
public class RabbitMQService : IRlxQueueService
{
    private readonly ILogger<IRlxQueueService> _logger;
    private readonly string _host;
    private readonly int _port;
    private readonly string _username;
    private readonly string _password;
    private readonly IConfiguration _configuration;


    public RabbitMQService(ILogger<IRlxQueueService> logger, IConfiguration configuration, string configSection)
    {
        _configuration = configuration;
        _logger = logger;
        _host = _configuration[$"{configSection}:Host"]!;
        _port = int.Parse(_configuration[$"{configSection}:Port"]!);
        _username = _configuration[$"{configSection}:Username"]!;
        _password = _configuration[$"{configSection}:Password"]!;
    }

    public async Task SendMessageAsync(string queue, string message)
    {
        await SendMessagesAsync(queue, [message]);
    }

    public async Task SendMessagesAsync(string queue, string[] messages)
    {
        var factory = new ConnectionFactory() { HostName = _host, Port = _port, UserName = _username, Password = _password };
        using (var connection = await factory.CreateConnectionAsync())
        {
            using (var channel = await connection.CreateChannelAsync())
            {
                await channel.QueueDeclareAsync(queue: queue,
                                                    durable: true,
                                                    exclusive: false,
                                                    autoDelete: false,
                                                    arguments: null);
                foreach (var msg in messages)
                {
                    var body = Encoding.UTF8.GetBytes(msg);
                    await channel.BasicPublishAsync(exchange: "",
                      routingKey: queue,
                      mandatory: false,
                      body: body);
                }

                _logger.LogInformation($"RabbitMQ message sent to {queue}");
            }
        }
    }
}
