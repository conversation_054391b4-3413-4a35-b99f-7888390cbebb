namespace Rlx.Shared.Services;

using Microsoft.Extensions.Logging;
using Rlx.Shared.Interfaces;
using StackExchange.Redis;
public class RedisCacheService : IRlxCacheService
{
    private readonly IDatabase _database;
    private readonly ILogger<RedisCacheService> _logger;
    private readonly IConnectionMultiplexer _redis;
    public RedisCacheService(IConnectionMultiplexer redis, ILogger<RedisCacheService> logger)
    {
        _logger = logger;
        _database = redis.GetDatabase();
        _redis = redis;
    }
    public async Task SetAsync(string key, string value, TimeSpan? expiry = null)
    {
        try
        {
            await _database.StringSetAsync(key, value, expiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis Error");
        }
    }
    public async Task<string?> GetAsync(string key)
    {
        try
        {
            var value = await _database.StringGetAsync(key);
            return value.HasValue ? value.ToString() : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis Error");
            return null;
        }
    }
    public async Task RemoveAsync(string key)
    {
        try
        {
            await _database.KeyDeleteAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis Error");
        }
    }
    public async Task FlushAllAsync()
    {
        try
        {
            // Iterate over all servers (there may be multiple if clustered)
            foreach (var endpoint in _redis.GetEndPoints())
            {
                var server = _redis.GetServer(endpoint);
                await server.FlushAllDatabasesAsync();
                _logger.LogInformation($"FLUSHALL successfully executed on server: {endpoint}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during Redis FLUSHALL operation.");
            throw;
        }
    }
}