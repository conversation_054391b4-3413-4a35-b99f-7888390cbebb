using Rlx.Shared.Interfaces;
using MongoDB.Driver;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

public class MongoCollectionService<T> : IRlxNoSqlService<T>
{
    private readonly IMongoDatabase _database;
    private readonly IMongoCollection<T> _collection;
    private readonly IConfiguration _configuration;
    private readonly ILogger<MongoCollectionService<T>> _logger;

    public MongoCollectionService(ILogger<MongoCollectionService<T>> logger, IConfiguration configuration, string prefix, string collectionName)
    {
        _logger = logger;
        _configuration = configuration;
        var client = new MongoClient(_configuration[$"{prefix}:ConnectionString"]);
        _database = client.GetDatabase(_configuration[$"{prefix}:DatabaseName"]);
        _collection = _database.GetCollection<T>(collectionName);
    }

    public async Task<T?> GetByIdAsync(string id)
    {
        try
        {
            return await _collection.Find(Builders<T>.Filter.Eq("_id", id)).FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting {typeof(T).Name} by id: {id}");
            return default;
        }
    }

    public async Task<IEnumerable<T>> GetAllAsync()
    {
        try
        {
            return await _collection.Find(_ => true).ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting all {typeof(T).Name}");
            return Enumerable.Empty<T>();
        }

    }

    public async Task InsertAsync(T entity)
    {
        try
        {
            await _collection.InsertOneAsync(entity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error inserting {typeof(T).Name}");
        }
    }


    public async Task UpdateAsync(string id, T entity)
    {
        try
        {
            await _collection.ReplaceOneAsync(Builders<T>.Filter.Eq("_id", id), entity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating {typeof(T).Name} by id: {id}");
        }

    }


    public async Task DeleteAsync(string id)
    {
        try
        {
            await _collection.DeleteOneAsync(Builders<T>.Filter.Eq("_id", id));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error deleting {typeof(T).Name} by id: {id}");
        }
    }

}