using Rlx.Shared.Interfaces;
using MongoDB.Driver;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

public class MongoDbService
{
    private readonly IMongoDatabase _database;
    private readonly IConfiguration _configuration;
    private readonly ILogger<MongoDbService> _logger;

    public MongoDbService(ILogger<MongoDbService> logger, IConfiguration configuration, string configSection)
    {
        _logger = logger;
        _configuration = configuration;
        var client = new MongoClient(_configuration[$"{configSection}:ConnectionString"]);
        _database = client.GetDatabase(_configuration[$"{configSection}:DatabaseName"]);

    }

    public async Task<T?> GetByIdAsync<T>(string collectionName, string id)
    {
        try
        {
            var collection = _database.GetCollection<T>(collectionName);
            return await collection.Find(Builders<T>.Filter.Eq("_id", id)).FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting {typeof(T).Name} by id: {id}");
            return default;
        }
    }

    public async Task<IEnumerable<T>> GetAllAsync<T>(string collectionName)
    {
        try
        {
            var collection = _database.GetCollection<T>(collectionName);
            return await collection.Find(_ => true).ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting all {typeof(T).Name}");
            return Enumerable.Empty<T>();
        }

    }

    public async Task InsertAsync<T>(T entity, string collectionName)
    {
        try
        {
            var collection = _database.GetCollection<T>(collectionName);
            await collection.InsertOneAsync(entity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error inserting {typeof(T).Name}");
        }
    }


    public async Task UpdateAsync<T>(string id, T entity, string collectionName)
    {
        try
        {
            var collection = _database.GetCollection<T>(collectionName);
            await collection.ReplaceOneAsync(Builders<T>.Filter.Eq("_id", id), entity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating {typeof(T).Name} by id: {id}");
        }

    }


    public async Task DeleteAsync<T>(string id, string collectionName)
    {
        try
        {
            var collection = _database.GetCollection<T>(collectionName);
            await collection.DeleteOneAsync(Builders<T>.Filter.Eq("_id", id));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error deleting {typeof(T).Name} by id: {id}");
        }
    }

}
