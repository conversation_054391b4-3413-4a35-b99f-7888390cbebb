using System.ComponentModel.DataAnnotations;
using System.Net;
using System.Text.Json;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Rlx.Shared.Helpers;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models;
namespace Rlx.Shared.Handlers;

public class ExceptionHandler : IExceptionHandler
{
    private readonly ILogger _logger;
    private readonly IRlxSystemLogHelper<ExceptionHandler> _rlxSystemLogHelper;
    public ExceptionHandler(ILogger<ExceptionHandler> logger, IRlxSystemLogHelper<ExceptionHandler> rlxSystemLogHelper)
    {
        _logger = logger;
        _rlxSystemLogHelper = rlxSystemLogHelper;
    }
    public async ValueTask<bool> TryHandleAsync(HttpContext context, Exception exception, CancellationToken cancellationToken)
    {
        var exceptionCode = Guid.NewGuid().ToString();
        _logger.LogError(exception, "Error Code: {ExceptionCode}, Error Type: {ExceptionType}", exceptionCode, exception.GetType().Name);
        _logger.LogError(exception.StackTrace, "Error Code: {ExceptionCode}, Error Type: {ExceptionType}", exceptionCode, exception.GetType().Name);
        await _rlxSystemLogHelper.LogErrorAsync($"Error Code: {exceptionCode}, Error Type: {exception.GetType().Name}", exception, "ExceptionHandler");
        var message = exception.Message;
        switch (exception)
        {
            case ValidationException:
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                break;
            case UnauthorizedAccessException:
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                break;
            case KeyNotFoundException:
            case InvalidOperationException:
                context.Response.StatusCode = (int)HttpStatusCode.NotFound;
                break;
            case TimeoutException:
                context.Response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                break;
            case NotImplementedException:
                context.Response.StatusCode = (int)HttpStatusCode.NotImplemented;
                break;
            case ArgumentNullException:
            case ArgumentException:
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                break;
            default:
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                break;
        }
        var response = RlxApiResponser<RlxApiErrorDetail>.Error(new RlxApiErrorDetail { ErrorCode = exceptionCode, StatusCode = context.Response.StatusCode }, message);
        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(JsonSerializer.Serialize(response));
        return true;
    }
}