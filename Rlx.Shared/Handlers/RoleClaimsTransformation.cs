using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Helpers;
using Microsoft.Extensions.Logging;
namespace Rlx.Shared.Handlers;

public class RoleClaimsTransformation : IClaimsTransformation
{
    private readonly IRlxIdentitySharedManager _rlxIdentitySharedManager;
    private readonly ILogger<RoleClaimsTransformation> _logger;
    public RoleClaimsTransformation(IRlxIdentitySharedManager rlxIdentitySharedManager, ILogger<RoleClaimsTransformation> logger)
    {
        _logger = logger;
        _rlxIdentitySharedManager = rlxIdentitySharedManager;
    }
    public async Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
    {
        var identity = (ClaimsIdentity?)principal.Identity;
        if (identity == null) return principal;
        var id = identity.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;
        if (id == null) return principal;
        var rolesClaims = await _rlxIdentitySharedManager.GetUsersRolesClaims(id);
        if (rolesClaims == null || !rolesClaims.Any()) return principal;
        // _logger.LogDebug($"User {id} has {rolesClaims.Count()} role claims.");
        foreach (var claim in rolesClaims)
        {
            if (!identity.Claims.Any(c => c.Type == claim.ClaimType && c.Value == claim.ClaimValue))
            {
                identity.AddClaim(new Claim(claim.ClaimType!, claim.ClaimValue!));
                // _logger.LogInformation($"Added claim {claim.ClaimType} with value {claim.ClaimValue} to user {id}.");
            }
        }
        return principal;
    }
}
