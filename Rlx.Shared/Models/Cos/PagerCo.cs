using System.ComponentModel.DataAnnotations;

namespace Rlx.Shared.Models.Cos;

public class PagerCo
{
    //private int _pageNumber = 1;
    //[Range(1, int.MaxValue)]
    //public int PageNumber
    //{
    //    get => _pageNumber;
    //    set
    //    {
    //        _pageNumber = value < 1 ? 1 : value;
    //    }
    //}



    //private int _pageSize = 20;
    //[Range(1, int.MaxValue)]
    //public int PageSize
    //{
    //    get => _pageSize;
    //    set
    //    {
    //        _pageSize = value < 1 ? 1 : value;
    //    }
    //}

    [Range(1, int.MaxValue)]
    public int Page { get; set; } = 1;
    [Range(1, int.MaxValue)]
    public int Size { get; set; } = 20;
    public int Skip => (Page - 1) * Size;
}