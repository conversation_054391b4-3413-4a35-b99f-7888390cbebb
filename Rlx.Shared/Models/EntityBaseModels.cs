namespace Rlx.Shared.Models;

public class EntityBaseIdLiteModel
{
    public required string Id { get; set; } = Guid.NewGuid().ToString();
}
public class EntityBaseIdModel : EntityBaseIdLiteModel
{
    public int AutoIncrementId { get; set; }
}
public class EntityBaseLiteModel : EntityBaseIdModel
{
    public bool Disabled { get; set; }
}
public class EntityBaseModel : EntityBaseLiteModel
{
    public bool Deleted { get; set; }
}
