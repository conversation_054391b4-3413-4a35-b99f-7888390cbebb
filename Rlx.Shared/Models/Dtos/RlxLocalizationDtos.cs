using System.ComponentModel.DataAnnotations;
namespace Rlx.Shared.Models.Dtos;

public class RlxLocalizationCreateDto
{
    [Required]
    [StringLength(256)]
    public required string ReferenceId { get; set; }
    [Required]
    [StringLength(256)]
    public required string Culture { get; set; }
    [Required]
    [StringLength(256)]
    public required string Key { get; set; }
    public string? Value { get; set; }
}
public class RlxLocalizationUpdateDto : RlxLocalizationCreateDto
{
    [Required]
    public required string Id { get; set; }
}
public class RlxLocalizationDto
{
    public required string Id { get; set; }
    public required string ReferenceId { get; set; }
    public required string Culture { get; set; }
    public required string Key { get; set; }
    public required string Value { get; set; }
}
public class RlxLocalizationViewDto
{
    public required string ReferenceId { get; set; }
    public required string Culture { get; set; }
    public required string Key { get; set; }
    public required string Value { get; set; }
}
public class RlxLocalizationSaveDto
{
    public string? Id { get; set; }
    [Required]
    public required string ReferenceId { get; set; }
    [Required]
    public required string Culture { get; set; }
    [Required]
    public required string Key { get; set; }
    public string? Value { get; set; }
}