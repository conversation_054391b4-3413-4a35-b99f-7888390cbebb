
using System.ComponentModel.DataAnnotations;

namespace Rlx.Shared.Models.Dtos
{
    public class RlxEnumCreateDto
    {
        [Required]
        [StringLength(256)]
        public required string Name { get; set; }
        public bool Disabled { get; set; }

    }
    public class RlxEnumUpdateDto : RlxEnumCreateDto
    {
        [Required]
        public required string Id { get; set; }
        public bool Deleted { get; set; }

    }

    public class RlxActiveEnumDto
    {
        public required string Id { get; set; }
        public required string Name { get; set; }
        public IEnumerable<RlxActiveEnumValueDto>? Values { get; set; }
    }

    public class RlxActiveEnumValueDto
    {
        public required string Id { get; set; }
        public required string Name { get; set; }

    }

    public class RlxEnumDto
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public bool? Disabled { get; set; }
        public bool? Deleted { get; set; }
        public IEnumerable<RlxEnumValueDto>? Values { get; set; }
    }

    public class RlxEnumSaveDto
    {
        public string? Id { get; set; }
        [Required]
        [StringLength(256)]
        public required string Name { get; set; }
        public bool Disabled { get; set; }
        public bool Deleted { get; set; }
        public IEnumerable<RlxEnumValueSaveDto>? Values { get; set; }
    }

    public class RlxEnumValueCreateDto
    {
        [Required]
        [StringLength(256)]
        public required string Name { get; set; }
        public int? EnumId { get; set; }
        public int? WhichRow { get; set; }
        public bool Disabled { get; set; }

    }
    public class RlxEnumValueUpdateDto
    {
        [Required]
        public required string Id { get; set; }
        [Required]
        [StringLength(256)]
        public required string Name { get; set; }
        public int? EnumId { get; set; }
        public int? WhichRow { get; set; }
        public bool Disabled { get; set; }
        public bool Deleted { get; set; }

    }
    public class RlxEnumValueDto
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? EnumId { get; set; }
        public bool? Disabled { get; set; }
        public bool? Deleted { get; set; }
    }

    public class RlxEnumValueSaveDto
    {
        public string? Id { get; set; }
        [Required]
        [StringLength(256)]
        public required string Name { get; set; }
        public int? EnumId { get; set; }
        public int WhichRow { get; set; }
        public bool Disabled { get; set; }
        public bool Deleted { get; set; }
    }
}