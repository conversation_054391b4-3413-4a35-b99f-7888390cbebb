namespace Rlx.Shared.Models.Dtos;
public class RlxEntityLogDto
{
    public DateTime Date { get; set; }
    public string? UserId { get; set; }
    public string? EntityId { get; set; }
    public string? EntityState { get; set; }
    public string? EntityData { get; set; }
    public List<EntityPropertyChangeDetailDto>? ChangedProperties { get; set; }
    public string? DbName { get; set; }
    public string? ModelName { get; set; }
}

public class EntityPropertyChangeDetailDto
{
    public string? Name { get; set; }
    public object? Value { get; set; }
    public object? OldValue { get; set; }
}
