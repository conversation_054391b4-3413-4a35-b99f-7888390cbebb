namespace Rlx.Shared.Models.RlxEnumDbContextModels
{
    public class RlxEnum
    {
        public required string Id { get; set; } = Guid.NewGuid().ToString();
        public required int AutoIncrementId { get; set; }
        public required string Name { get; set; }
        public string? Code { get; set; }
        public bool Disabled { get; set; }
        public bool Deleted { get; set; }
        public virtual ICollection<RlxEnumValue> RlxEnumValues { get; set; } = new HashSet<RlxEnumValue>();
    }
}