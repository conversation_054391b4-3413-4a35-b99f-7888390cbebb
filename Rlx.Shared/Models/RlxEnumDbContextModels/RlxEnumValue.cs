namespace Rlx.Shared.Models.RlxEnumDbContextModels
{
    public class RlxEnumValue
    {
        public required string Id { get; set; } = Guid.NewGuid().ToString();
        public required int AutoIncrementId { get; set; }
        public int EnumId { get; set; }
        public required string Name { get; set; }
        public string? Code { get; set; }
        public int? WhichRow { get; set; }
        public bool Disabled { get; set; }
        public bool Deleted { get; set; }
        public virtual RlxEnum? RlxEnum { get; set; }
    }
}