using System.Text.Json.Serialization;
namespace Rlx.Shared.Models;

public class RlxApiResponse
{
    /// Response status: "Success", "Error", "Warning", "Info"
    public string Status { get; set; } = "Success";
    /// Response message or error description
    public string? Message { get; set; }
    /// Timestamp of the response
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
public class RlxApiResponse<T> : RlxApiResponse
{
    /// Response data
    public T? Data { get; set; }
    /// Additional metadata
    public object? Meta { get; set; }
}
public class RlxApiErrorDetail
{
    /// Error code for debugging (only for errors)
    public string? ErrorCode { get; set; }
    public int? StatusCode { get; set; }
    /// Exception (only for errors)
    public Exception? Exception { get; set; }
}