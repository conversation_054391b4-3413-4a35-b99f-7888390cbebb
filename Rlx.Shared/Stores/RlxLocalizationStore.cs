using Microsoft.EntityFrameworkCore;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.RlxLocalizationDbContextModels;
namespace Rlx.Shared.Stores;

public class RlxLocalizationStore<TContext> : IRlxLocalizationStore<TContext> where TContext : RlxLocalizationDbContext
{
    private readonly TContext _dbContext;
    public RlxLocalizationStore(TContext dbContext)
    {
        _dbContext = dbContext;
    }
    public async Task AddLocalizationAsync(RlxLocalization data)
    {
        _dbContext.Add(data);
        await _dbContext.SaveChangesAsync();
    }
    public async Task<IEnumerable<RlxLocalizationDto>> GetLocalizationsAsync(GetRlxLocalizationsCo co)
    {
        var locDbSet = _dbContext.RlxLocalizations.AsQueryable();
        if (!string.IsNullOrEmpty(co.Culture))
        {
            locDbSet = locDbSet.Where(x => x.Culture == co.Culture);
        }
        var localizations = await locDbSet.Where(l => co.ReferenceIds.Contains(l.ReferenceId))
            .Select(x => new RlxLocalizationDto
            {
                Id = x.Id,
                Culture = x.Culture,
                Key = x.Key,
                ReferenceId = x.ReferenceId,
                Value = x.Value,
            }).ToListAsync();
        return localizations;
    }
    public async Task<bool> LocalizationExistsAsync(string referenceId, string culture, string key)
    {
        return await _dbContext.RlxLocalizations.AnyAsync(x => x.ReferenceId == referenceId && x.Culture == culture && x.Key == key);
    }
    public async Task<bool> LocalizationExistsAsync(string id)
    {
        return await _dbContext.RlxLocalizations.AnyAsync(x => x.Id == id);
    }
    public async Task<bool> LocalizationExistsWithDifferentIdAsync(string id, string referenceId, string culture, string key)
    {
        return await _dbContext.RlxLocalizations.AnyAsync(x => x.Id != id && x.ReferenceId == referenceId && x.Culture == culture && x.Key == key);
    }
    public async Task UpdateLocalizationAsync(RlxLocalization data)
    {
        _dbContext.RlxLocalizations.Update(data);
        await _dbContext.SaveChangesAsync();
    }
    public async Task<int> GetAutoIncrementIdAsync(string id)
    {
        return await _dbContext.RlxLocalizations.Where(x => x.Id == id).Select(x => x.AutoIncrementId).FirstOrDefaultAsync();
    }
    public async Task DeleteLocalizationAsync(string id)
    {
        var data = await _dbContext.RlxLocalizations.FirstAsync(a => a.Id == id);
        _dbContext.RlxLocalizations.Remove(data);
        await _dbContext.SaveChangesAsync();
    }
    public async Task<RlxLocalization> GetLocalizationDataForSaveAsync(string referenceId, string culture, string key)
    {
        var data = await _dbContext.RlxLocalizations.FirstOrDefaultAsync(x => x.ReferenceId == referenceId && x.Culture == culture && x.Key == key);
        if (data == null)
        {
            data = new RlxLocalization
            {
                ReferenceId = referenceId,
                Culture = culture,
                Key = key,
                AutoIncrementId = 0,
                Id = Guid.NewGuid().ToString(),
                Value = string.Empty
            };
        }
        return data;
    }
}