using System.Runtime.InteropServices;
using Microsoft.EntityFrameworkCore;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace Rlx.Shared.Stores;

public class RlxEnumStore<TContext> : IRlxEnumStore<TContext> where TContext : RlxEnumDbContext
{
    private readonly TContext _context;
    public RlxEnumStore(TContext context)
    {
        _context = context;
    }
    public Task<RlxEnumDto> GetEnumAsync(string id)
    {
        throw new NotImplementedException();
    }
    public async Task<IEnumerable<RlxEnumDto>> GetActiveEnums(IEnumerable<string> ids)
    {
        if (ids == null || !ids.Any())
            return Enumerable.Empty<RlxEnumDto>();
        var result = await _context.RlxEnums.Where(x => x.Deleted == false && x.Disabled == false && ids.Contains(x.Id))
        .Select(x => new RlxEnumDto
        {
            Id = x.Id,
            Name = x.Name,
            Values = x.RlxEnumValues.Where(y => y.Deleted == false && y.Disabled == false).Select(y => new RlxEnumValueDto
            {
                Id = y.Id,
                Name = y.Name,
            }).ToList()
        }).ToListAsync();
        return result;
    }
    public async Task<RlxEnum?> GetEnumForUpdateAsync(string id)
    {
        return await _context.RlxEnums.FirstOrDefaultAsync(x => x.Id == id);
    }
    public async Task<IEnumerable<RlxEnum>> GetEnumsForUpdateAsync(string[] ids)
    {
        return await _context.RlxEnums.Where(x => ids.Contains(x.Id)).ToListAsync();
    }
    public async Task AddEnumAsync(RlxEnum rlxEnum, bool saveChanges = true)
    {
        await _context.RlxEnums.AddAsync(rlxEnum);
        if (saveChanges)
            await _context.SaveChangesAsync();
    }
    public async Task UpdateEnumAsync(RlxEnum rlxEnum, bool saveChanges = true)
    {
        _context.RlxEnums.Update(rlxEnum);
        if (saveChanges)
            await _context.SaveChangesAsync();
    }
    public async Task AddEnumsAsync(IEnumerable<RlxEnum> rlxEnums, bool saveChanges = true)
    {
        await _context.RlxEnums.AddRangeAsync(rlxEnums);
        if (saveChanges)
            await _context.SaveChangesAsync();
    }
    public async Task UpdateEnumsAsync(IEnumerable<RlxEnum> rlxEnums, bool saveChanges = true)
    {
        _context.RlxEnums.UpdateRange(rlxEnums);
        if (saveChanges)
            await _context.SaveChangesAsync();
    }
    public async Task<RlxEnumValue?> GetEnumValueForUpdateAsync(string id)
    {
        return await _context.RlxEnumValues.FirstOrDefaultAsync(x => x.Id == id);
    }
    public async Task<IEnumerable<RlxEnumValue>?> GetEnumValuesForUpdateAsync(int enumId)
    {
        return await _context.RlxEnumValues.Where(x => x.EnumId == enumId).ToListAsync();
    }
    public async Task<IEnumerable<RlxEnumValue>> GetEnumValuesForUpdateAsync(string[] ids)
    {
        return await _context.RlxEnumValues.Where(x => ids.Contains(x.Id)).ToListAsync();
    }
    public async Task AddEnumValueAsync(RlxEnumValue rlxEnumValue, bool saveChanges = false)
    {
        await _context.RlxEnumValues.AddAsync(rlxEnumValue);
        if (saveChanges)
            await _context.SaveChangesAsync();
    }
    public async Task AddEnumValuesAsync(IEnumerable<RlxEnumValue> rlxEnumValues, bool saveChanges = false)
    {
        await _context.RlxEnumValues.AddRangeAsync(rlxEnumValues);
        if (saveChanges)
            await _context.SaveChangesAsync();
    }
    public async Task UpdateEnumValuesAsync(IEnumerable<RlxEnumValue> rlxEnumValues, bool saveChanges = false)
    {
        _context.RlxEnumValues.UpdateRange(rlxEnumValues);
        if (saveChanges)
            await _context.SaveChangesAsync();
    }
    public async Task<IDictionary<string, int>> IdConvertForEnumValue(IEnumerable<string> enumValueIds)
    {
        return await _context.RlxEnumValues
             .Where(x => enumValueIds.Contains(x.Id))
             .ToDictionaryAsync(x => x.Id, x => x.AutoIncrementId);
    }
}