using Microsoft.EntityFrameworkCore;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;
namespace Rlx.Shared.Stores;
public class RlxIdentitySharedStore : IRlxIdentitySharedStore
{
    private readonly RlxIdentitySharedDbContext _dbContext;
    public RlxIdentitySharedStore(RlxIdentitySharedDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<IEnumerable<ClaimLiteDto>> GetRoleClaims(string roleId)
    {
        return await _dbContext.Database
            .SqlQueryRaw<ClaimLiteDto>(
                 @"SELECT ""ClaimType"", ""ClaimValue""
      FROM ""AspNetRoleClaims""
      WHERE ""RoleId"" = @p0"
                , [roleId])
            .ToListAsync();
           
    }

    public async Task<IEnumerable<IdNameDto>> GetUserRoles(string userId)
    {

      return await _dbContext.Database
            .SqlQueryRaw<IdNameDto>(
                
                @"SELECT r.""Id"", r.""Name""
      FROM ""AspNetUserRoles"" ur
      INNER JOIN ""AspNetRoles"" r ON ur.""RoleId"" = r.""Id""
      WHERE ur.""UserId"" = @p0 AND r.""Disabled"" = false"
                , [userId]).ToListAsync();



       
    }

    public async Task<ClaimUserDto?> GetUser(string userId)
    {
         return await _dbContext.Database
            .SqlQueryRaw<ClaimUserDto>( @"SELECT ""Id"", ""Disabled"" FROM ""AspNetUsers"" WHERE ""Id"" = @p0",[userId])
            .FirstOrDefaultAsync();
    }


}