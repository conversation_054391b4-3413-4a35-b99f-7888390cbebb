// using Microsoft.EntityFrameworkCore;
// using Rlx.Shared.DbContexts;
// using Rlx.Shared.Interfaces;
// using Rlx.Shared.Models.Dtos;
// using Rlx.Shared.Models.RlxEnumDbContextModels;
// namespace Rlx.Shared.Stores;
// public class RlxEnumValueStore<TContext> : IRlxEnumValueStore<TContext> where TContext : RlxEnumDbContext
// {
//     private readonly TContext _context;
//     public RlxEnumValueStore(TContext context)
//     {
//         _context = context;
//     }
//     // public Task<IEnumerable<RlxEnumValueDto>> GetEnumValuesAsync(string enumId)//co gelecek.
//     // {
//     //     var enumIdInt = _context.RlxEnums
//     //         .Where(x => x.Id == enumId)
//     //         .Select(x => x.AutoIncrementId)
//     //         .FirstOrDefault();
//     //     var query = _context.RlxEnumValues
//     //          .Where(x => x.EnumId == enumIdInt)
//     //         .Select(x => new RlxEnumValueDto
//     //         {
//     //             Id = x.Id,
//     //             EnumId = enumId,
//     //             Name = x.Name,
//     //         });
//     //     return Task.FromResult(query.AsEnumerable());
//     // }
//     public async Task<RlxEnumValue?> GetEnumValueForUpdateAsync(string id)
//     {
//         return await _context.RlxEnumValues.FirstOrDefaultAsync(x => x.Id == id);
//     }
//     public async Task<IEnumerable<RlxEnumValue>?> GetEnumValuesForUpdateAsync(int enumId)
//     {
//         return await _context.RlxEnumValues.Where(x => x.EnumId == enumId).ToListAsync();
//     }
//     public async Task<IEnumerable<RlxEnumValue>> GetEnumValuesForUpdateAsync(string[] ids)
//     {
//         return await _context.RlxEnumValues.Where(x => ids.Contains(x.Id)).ToListAsync();
//     }
//     public async Task AddEnumValueAsync(RlxEnumValue rlxEnumValue, bool saveChanges = true)
//     {
//         await _context.RlxEnumValues.AddAsync(rlxEnumValue);
//         if (saveChanges)
//             await _context.SaveChangesAsync();
//     }
//     public async Task AddEnumValuesAsync(IEnumerable<RlxEnumValue> rlxEnumValues, bool saveChanges = true)
//     {
//         await _context.RlxEnumValues.AddRangeAsync(rlxEnumValues);
//         if (saveChanges)
//             await _context.SaveChangesAsync();
//     }
//     public async Task UpdateEnumValuesAsync(IEnumerable<RlxEnumValue> rlxEnumValues, bool saveChanges = true)
//     {
//         _context.RlxEnumValues.UpdateRange(rlxEnumValues);
//         if (saveChanges)
//             await _context.SaveChangesAsync();
//     }
// }