using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Rlx.Shared.Factories;
using Rlx.Shared.Helpers;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;

namespace Rlx.Shared.Middlewares;

public class RequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestLoggingMiddleware> _logger;
    private readonly IConfiguration _configuration;
    private readonly RlxQueueServiceFactory _rlxQueueServiceFactory;
    private readonly IRlxQueueService _rlxQueueService;


    public RequestLoggingMiddleware(RlxQueueServiceFactory rlxQueueServiceFactory, RequestDelegate next, ILogger<RequestLoggingMiddleware> logger, IConfiguration configuration)
    {
        _next = next;
        _logger = logger;
        _configuration = configuration;
        _rlxQueueServiceFactory = rlxQueueServiceFactory;
        _rlxQueueService = _rlxQueueServiceFactory.Create("RequestLog");
    }

    public async Task Invoke(HttpContext context)
    {
        _logger.LogInformation($"Request: {context.Request.Method} {context.Request.Path}");
        var start = DateTime.UtcNow;
        await _next(context);
        var end = DateTime.UtcNow;

        if (_configuration["RequestLog:Enabled"] == "1")
        {
            var requestLog = new RlxRequestLogDto
            {
                Start = start,
                End = end,
                Method = context.Request.Method,
                Path = context.Request.Path,
                Module = _configuration["RequestLog:Module"],
                QueryString = context.Request.QueryString.ToString(),
                StatusCode = context.Response.StatusCode,
                Duration = end - start,
                UserId = UserContextHelper.GetUserId(context)
            };
            var message = JsonSerializer.Serialize(requestLog);
            await _rlxQueueService.SendMessageAsync("request_queue", message);
        }

        _logger.LogInformation($"Response: {context.Response.StatusCode}");
    }
}