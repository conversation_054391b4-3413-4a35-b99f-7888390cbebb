using System.ComponentModel.DataAnnotations;
using System.Net;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Rlx.Shared.Helpers;
using Rlx.Shared.Models;
namespace Rlx.Shared.Middlewares;

public class ExceptionHandlerMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionHandlerMiddleware> _logger;
    public ExceptionHandlerMiddleware(RequestDelegate next, ILogger<ExceptionHandlerMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }
    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }
    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var exceptionCode = Guid.NewGuid().ToString();
        _logger.LogError(exception, "Error Code: {ExceptionCode}, Error Type: {ExceptionType}", exceptionCode, exception.GetType().Name);
        _logger.LogError(exception.StackTrace, "Error Code: {ExceptionCode}, Error Type: {ExceptionType}", exceptionCode, exception.GetType().Name);
        var message = exception.Message;
        switch (exception)
        {
            case ValidationException:
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                break;
            case UnauthorizedAccessException:
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                break;
            case KeyNotFoundException:
            case InvalidOperationException:
                context.Response.StatusCode = (int)HttpStatusCode.NotFound;
                break;
            case TimeoutException:
                context.Response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                break;
            case NotImplementedException:
                context.Response.StatusCode = (int)HttpStatusCode.NotImplemented;
                break;
            case ArgumentNullException:
            case ArgumentException:
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                break;
            default:
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = "Internal Server Error";
                break;
        }
        // var response = new
        // {
        //     Message = message,
        //     Code = exceptionCode
        // };
        var response = RlxApiResponser<RlxApiErrorDetail>.Error(new RlxApiErrorDetail { ErrorCode = exceptionCode, StatusCode = context.Response.StatusCode }, message);
        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(JsonSerializer.Serialize(response));
    }
}