using Rlx.Shared.DbContexts;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
namespace Rlx.Shared.Managers;

public class RlxLocalizationManager<TContext> : IRlxLocalizationManager<TContext> where TContext : RlxLocalizationDbContext
{
    private readonly IRlxLocalizationStore<TContext> _rlxLocalizationStore;
    public RlxLocalizationManager(IRlxLocalizationStore<TContext> rlxLocalizationStore)
    {
        _rlxLocalizationStore = rlxLocalizationStore;
    }
    public Task<IEnumerable<RlxLocalizationDto>> GetLocalizationsAsync(GetRlxLocalizationsCo co)
    {
        return _rlxLocalizationStore.GetLocalizationsAsync(co);
    }
    public async Task SaveLocalizationsAsync(RlxLocalizationSaveDto[] dto)
    {
        foreach (var item in dto)
        {
            var saveData = await _rlxLocalizationStore.GetLocalizationDataForSaveAsync(item.ReferenceId, item.Culture, item.Key);
            if (saveData.AutoIncrementId == 0)
            {
                if (string.IsNullOrEmpty(item.Value))
                {
                    continue;
                }
                saveData.Value = item.Value;
                await _rlxLocalizationStore.AddLocalizationAsync(saveData);
            }
            else
            {
                if (string.IsNullOrEmpty(item.Value))
                {
                    await _rlxLocalizationStore.DeleteLocalizationAsync(saveData.Id);
                }
                else
                {
                    saveData.Value = item.Value;
                    await _rlxLocalizationStore.UpdateLocalizationAsync(saveData);
                }
            }
        }
    }
}