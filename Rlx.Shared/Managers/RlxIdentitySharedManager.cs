using Rlx.Shared.Consts;
using Rlx.Shared.Helpers;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;
namespace Rlx.Shared.Managers;

public class RlxIdentitySharedManager : IRlxIdentitySharedManager
{
    private readonly IRlxIdentitySharedStore _rlxIdentitySharedStore;
    private readonly IRlxCacheService _rlxCacheService;
    public RlxIdentitySharedManager(IRlxIdentitySharedStore store, IRlxCacheService rlxCacheService)
    {
        _rlxCacheService = rlxCacheService;
        _rlxIdentitySharedStore = store;
    }
    public async Task<IEnumerable<ClaimLiteDto>?> GetUsersRolesClaims(string userId)
    {
        var user = await _rlxIdentitySharedStore.GetUser(userId);
        if (user == null)
            return Enumerable.Empty<ClaimLiteDto>();
        if (user.Disabled)
            return Enumerable.Empty<ClaimLiteDto>();
        var roles = await _rlxIdentitySharedStore.GetUserRoles(userId);
        if (roles == null || !roles.Any())
            return Enumerable.Empty<ClaimLiteDto>();
        var rolesClaims = new List<ClaimLiteDto>();
        foreach (var role in roles)
        {
            var roleClaimsJson = await _rlxCacheService.GetAsync(TextHelper.RlxJoin(CacheConst.RoleClaims, role.Name!));
            var roleClaims = JsonHelper.FromJson<List<ClaimLiteDto>>(roleClaimsJson);
            if (roleClaims == null || roleClaims.Count == 0)
            {
                roleClaims = (await _rlxIdentitySharedStore.GetRoleClaims(role.Id!))?.ToList();
            }
            if (roleClaims == null || roleClaims.Count == 0)
            {
                continue;
            }
            else
            {
                await _rlxCacheService.SetAsync(TextHelper.RlxJoin(CacheConst.RoleClaims, role.Name!), JsonHelper.ToJson(roleClaims));
                rolesClaims.AddRange(roleClaims);
            }
        }
        return rolesClaims.Distinct()?.ToList();
    }
}