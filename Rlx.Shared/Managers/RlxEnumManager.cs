using Mapster;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace Rlx.Shared.Managers;

public class RlxEnumManager<TContext> : IRlxEnumManager<TContext> where TContext : RlxEnumDbContext
{
    private readonly IRlxEnumStore<TContext> _rlxEnumStore;
    private readonly TContext _context;
    public RlxEnumManager(IRlxEnumStore<TContext> rlxEnumStore, TContext context)
    {
        _context = context;
        _rlxEnumStore = rlxEnumStore;
    }
    public async Task<IDictionary<string, int>> IdConvertForEnumValue(IEnumerable<string> enumValueIds)
    {
        if (enumValueIds == null || !enumValueIds.Any())
            return new Dictionary<string, int>();
        return await _rlxEnumStore.IdConvertForEnumValue(enumValueIds);
    }
    public async Task SaveEnumsAsync(IEnumerable<RlxEnumSaveDto> dtos)
    {
        var eAddList = new List<RlxEnum>();
        var eUpdateList = new List<RlxEnum>();
        var savedEnums = Enumerable.Empty<RlxEnum>();
        var savedEnumValues = Enumerable.Empty<RlxEnumValue>();
        var eids = dtos.Where(x => x.Id != null).Select(x => x.Id!).ToArray();
        if (eids.Length > 0)
            savedEnums = await _rlxEnumStore.GetEnumsForUpdateAsync(eids);
        var evids = dtos.SelectMany(x => x.Values?.Where(y => y.Id != null)?.Select(y => y.Id!) ?? Enumerable.Empty<string>()).ToArray();
        if (evids.Length > 0)
            savedEnumValues = await _rlxEnumStore.GetEnumValuesForUpdateAsync(evids);
        foreach (var dto in dtos)
        {
            RlxEnum? saveEnum;
            if (dto.Id == null)
            {
                saveEnum = dto.Adapt<RlxEnum>();
                eAddList.Add(saveEnum);
            }
            else
            {
                saveEnum = savedEnums?.FirstOrDefault(x => x.Id == dto.Id);
                if (saveEnum != null)
                {
                    dto.Adapt(saveEnum);
                    eUpdateList.Add(saveEnum);
                }
            }
            if (dto.Values != null)
            {
                var evAddList = new List<RlxEnumValue>();
                var evUpdateList = new List<RlxEnumValue>();
                foreach (var vDto in dto.Values)
                {
                    RlxEnumValue? saveEnumValue;
                    if (vDto.Id == null)
                    {
                        saveEnumValue = vDto.Adapt<RlxEnumValue>();
                        saveEnumValue.RlxEnum = saveEnum;
                        evAddList.Add(saveEnumValue);
                    }
                    else
                    {
                        saveEnumValue = savedEnumValues?.FirstOrDefault(x => x.Id == vDto.Id);
                        if (saveEnumValue != null)
                        {
                            vDto.Adapt(saveEnumValue);
                            saveEnumValue.RlxEnum = saveEnum;
                            evUpdateList.Add(saveEnumValue);
                        }
                    }
                }
                if (evAddList.Count > 0)
                {
                    await _rlxEnumStore.AddEnumValuesAsync(evAddList, false);
                }
                if (evUpdateList.Count > 0)
                {
                    await _rlxEnumStore.UpdateEnumValuesAsync(evUpdateList);
                }
            }
        }
        if (eAddList.Count > 0)
        {
            await _rlxEnumStore.AddEnumsAsync(eAddList);
        }
        if (eUpdateList.Count > 0)
        {
            await _rlxEnumStore.UpdateEnumsAsync(eUpdateList);
        }
        await _context.SaveChangesAsync();
    }
}