using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Rlx.Shared.Factories;

public class MongoCollectionServiceFactory<T>
{
    // private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;
    private readonly ILogger<MongoCollectionService<T>> _logger;

    public MongoCollectionServiceFactory(IConfiguration configuration, ILogger<MongoCollectionService<T>> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public MongoCollectionService<T> Create(string configSection, string collectionName)
    {
        return new MongoCollectionService<T>(_logger, _configuration, configSection, collectionName);
    }
}