using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Services;

namespace Rlx.Shared.Factories;

public class RlxQueueServiceFactory
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<IRlxQueueService> _logger;

    public RlxQueueServiceFactory(IConfiguration configuration, ILogger<IRlxQueueService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public IRlxQueueService Create(string configSection)
    {
        // apache, rabbitmq kontrolu eklenebilir   
        return new RabbitMQService(_logger, _configuration, configSection);
    }
}