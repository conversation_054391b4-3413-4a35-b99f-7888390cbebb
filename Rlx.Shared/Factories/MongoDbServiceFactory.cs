using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Rlx.Shared.Factories;

public class MongoDbServiceFactory
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<MongoDbService> _logger;

    public MongoDbServiceFactory(IConfiguration configuration, ILogger<MongoDbService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public MongoDbService Create(string configSection)
    {
        return new MongoDbService(_logger, _configuration, configSection);
    }
}