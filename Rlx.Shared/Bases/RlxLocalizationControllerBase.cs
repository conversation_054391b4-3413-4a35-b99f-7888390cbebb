using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
[ApiController]
public abstract class RlxLocalizationControllerBase<TContext> : ControllerBase where TContext : RlxLocalizationDbContext
{
    protected readonly TContext _dbContext;
    protected readonly IRlxLocalizationManager<TContext> _rlxLocalizationManager;
    protected ILogger<RlxLocalizationControllerBase<TContext>> _logger;
    public RlxLocalizationControllerBase(TContext dbContext, IRlxLocalizationManager<TContext> rlxLocalizationManager, ILogger<RlxLocalizationControllerBase<TContext>> logger)
    {
        _dbContext = dbContext;
        _rlxLocalizationManager = rlxLocalizationManager;
        _logger = logger;
    }
    // [HttpPost("savelocalizations")]
    public virtual async Task<IActionResult> SaveLocalizations(RlxLocalizationSaveDto[] dto)
    {
        if (dto == null || dto.Length == 0)
        {
            return BadRequest("No data to save");
        }
        await _rlxLocalizationManager.SaveLocalizationsAsync(dto);
        return Ok();
    }
    // [HttpGet("getlocalizations/{id}")]
    public virtual async Task<IActionResult> GetLocalizations(GetRlxLocalizationsCo co)
    {
        var data = await _rlxLocalizationManager.GetLocalizationsAsync(co);
        return Ok(data);
    }
}