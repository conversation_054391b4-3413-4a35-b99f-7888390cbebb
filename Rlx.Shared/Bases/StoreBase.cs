// namespace Rlx.Shared.Bases
// {
//     using System.Collections.Generic;
//     using System.Threading.Tasks;
//     using Interfaces;
//     using Models.Dtos;
//     using Models.RlxEnumDbContextModels;

//     public abstract class StoreBase<DbContext>
//     {
//         protected DbContext _context;
//         public StoreBase(DbContext context)
//         {
//             _context = context;
//         }
//         public virtual async Task SaveChagesAsync()
//         {
//             await _context.SaveChangesAsync();
//         }
//     }
// }