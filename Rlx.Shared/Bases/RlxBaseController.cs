using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Rlx.Shared.Resources;
using Rlx.Shared.Models;
using Rlx.Shared.Helpers;
namespace Rlx.Shared.Bases;

[ApiController]
public abstract class BaseApiController : ControllerBase
{
    protected readonly IStringLocalizer<SharedResource> _localizer;
    protected BaseApiController(IStringLocalizer<SharedResource> localizer)
    {
        _localizer = localizer;
    }
    protected IActionResult SuccessResponse<T>(T? data = default, string? message = null, object? meta = null)
    {
        var response = RlxApiResponser<T>.Success(data, message, meta);
        return Ok(response);
    }
    protected IActionResult SuccessResponse(string? message = null)
    {
        var response = RlxApiResponser.Success(message);
        return Ok(response);
    }
    protected IActionResult CreatedResponse<T>(T? data = default, string? message = null, string? location = null)
    {
        var response = RlxApiResponser<T>.Success(data, message ?? _localizer["ResourceCreatedSuccessfully"].Value);
        if (!string.IsNullOrEmpty(location))
        {
            return Created(location, response);
        }
        return StatusCode(201, response);
    }
    protected IActionResult ErrorResponse(RlxApiErrorDetail? error, string message, object? meta = null)
    {
        var response = RlxApiResponser<RlxApiErrorDetail>.Error(error, message, meta);
        return StatusCode(ErrorCodeHelper.GetErrorCode(error?.StatusCode), response);
    }
    protected IActionResult WarningResponseWithSuccess<T>(T? data = default, string? message = null, object? meta = null)
    {
        var response = RlxApiResponser<T>.Warning(data, message, meta);
        return Ok(response);
    }
    protected IActionResult InfoResponseWithSuccess<T>(T? data = default, string? message = null, object? meta = null)
    {
        var response = RlxApiResponser<T>.Info(data, message, meta);
        return Ok(response);
    }
    protected IActionResult WarningResponseWithError(RlxApiErrorDetail? error = default, string? message = null, object? meta = null)
    {
        var response = RlxApiResponser<RlxApiErrorDetail>.Warning(error, message, meta);
        return StatusCode(ErrorCodeHelper.GetErrorCode(error?.StatusCode), response);
    }
    protected IActionResult InfoResponseWithError(RlxApiErrorDetail? error = default, string? message = null, object? meta = null)
    {
        var response = RlxApiResponser<RlxApiErrorDetail>.Info(error, message, meta);
        return StatusCode(ErrorCodeHelper.GetErrorCode(error?.StatusCode), response);
    }
}
