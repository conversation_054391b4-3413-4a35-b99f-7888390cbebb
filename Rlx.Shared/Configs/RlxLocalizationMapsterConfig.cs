using Mapster;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.RlxLocalizationDbContextModels;

public static class RlxLocalizationMapsterConfig
{
    public static void RegisterMappings()
    {
        TypeAdapterConfig<RlxLocalization, RlxLocalizationCreateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<RlxLocalization, RlxLocalizationUpdateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<RlxLocalization, RlxLocalizationDto>.NewConfig().TwoWays();
        TypeAdapterConfig<RlxLocalizationSaveDto, RlxLocalizationCreateDto>.NewConfig().TwoWays();
        TypeAdapterConfig<RlxLocalizationSaveDto, RlxLocalizationUpdateDto>.NewConfig().TwoWays();
    }
}