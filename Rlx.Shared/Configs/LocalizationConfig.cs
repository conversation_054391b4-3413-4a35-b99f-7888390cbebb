using System.Globalization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Localization;

namespace Rlx.Shared.Configs;

public static class LocalizationConfig
{

    public static void UseLocalization(this WebApplication app)
    {
        var supportedCultures = new[]
        {
            new CultureInfo("en"),
            new CultureInfo("tr")
        };

        var localizationOptions = new RequestLocalizationOptions
        {
            DefaultRequestCulture = new RequestCulture("en"),
            SupportedCultures = supportedCultures,
            SupportedUICultures = supportedCultures
        };

        app.UseRequestLocalization(localizationOptions);
    }
}
