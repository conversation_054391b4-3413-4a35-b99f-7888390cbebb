using Mapster;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.RlxEnumDbContextModels;

public static class RlxEnumMapsterConfig
{
    public static void RegisterMappings()
    {
        TypeAdapterConfig<RlxEnum, RlxEnumSaveDto>.NewConfig().TwoWays();
        TypeAdapterConfig<RlxEnumValue, RlxEnumValueSaveDto>.NewConfig().TwoWays();
        TypeAdapterConfig<RlxEnum, RlxEnumDto>.NewConfig().TwoWays();
        TypeAdapterConfig<RlxEnumValue, RlxEnumValueDto>.NewConfig().TwoWays();
    }
}