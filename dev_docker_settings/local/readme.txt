### GitHub giris
echo "****************************************" | docker login ghcr.io -u slckkhrmn-arel --password-stdin
----------------------------------
### macos a hosts tanimlama
code /etc/hosts
127.0.0.1 local-rlxidentity-api.arel.edu.tr
127.0.0.1 local-logincenter.arel.edu.tr
127.0.0.1 local-usermanagement.arel.edu.tr
127.0.0.1 local-organizationmanagement.arel.edu.tr
127.0.0.1 local-academicperformance.arel.edu.tr
127.0.0.1 local-organizationmanagement-api.arel.edu.tr
127.0.0.1 local-academicperformance-api.arel.edu.tr
----------------------------------
### macosta localca trusted yapma
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain certs/localCA.crt
----------------------------------
### docker compose degismiyorsa
docker-compose build --no-cache
