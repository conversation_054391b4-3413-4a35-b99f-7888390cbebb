# #!/bin/bash
# set -e

# DB_NAME="RlxIdentity"
# READ_USER="readonly_user"
# READ_PASS="*j3aT@yq&HByVT"
# TABLES=("AspNetUsers" "AspNetRoles" "AspNetUserRoles" "AspNetRoleClaims")

# # Kullanıcıyı oluştur
# psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" <<-EOSQL
#     CREATE USER $READ_USER WITH PASSWORD '$READ_PASS';
# EOSQL

# # RlxIdentity veritabanının hazır olduğundan emin olmak için birkaç saniye bekle
# until psql -U "$POSTGRES_USER" -d "$DB_NAME" -c '\q' 2>/dev/null; do
#   echo "Waiting for $DB_NAME to be available..."
#   sleep 1
# done

# # Yetkileri ver
# for table in "${TABLES[@]}"; do
#   psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$DB_NAME" <<-EOSQL
#       GRANT CONNECT ON DATABASE $DB_NAME TO $READ_USER;
#       GRANT USAGE ON SCHEMA public TO $READ_USER;
#       GRANT SELECT ON public."$table" TO $READ_USER;
# EOSQL
# done
