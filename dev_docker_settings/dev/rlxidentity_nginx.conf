worker_processes 1;
events {
    worker_connections 1024;
}
http {
    server {
        listen 80;
        server_name _;
        return 301 https://$host$request_uri;
    }
    server {
        listen 443 ssl;
        server_name _ localhost dev-rlxidentity-api.arel.edu.tr;
        ssl_certificate /etc/nginx/certs/localhost.crt;
        ssl_certificate_key /etc/nginx/certs/localhost.key;
        location / {
            proxy_pass http://rlxidentity:8080;
            # Temel header'lar
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # HTTPS için kritik header'lar
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            # Header'ların iletildiğini doğrulamak için
            add_header X-Proxy "nginx";
            add_header X-Forwarded-Proto-Debug $scheme;
            proxy_set_header Cookie $http_cookie; # Cookie'yi forward et
            proxy_pass_request_headers on;
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;
        }
    }
}