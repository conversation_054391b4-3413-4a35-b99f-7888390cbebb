# version: "3.8"
services:
  mongo:
    image: mongo:latest
    container_name: rlxmongo
    restart: unless-stopped
    environment:
      - MONGO_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=2bvUqfVRCNcxg6iuXrZv6u
    ports:
      - "6003:27017"
    volumes:
      - mongo_data:/data/db    
    networks:
      - rlxnetwork  
  rabbitmq:
    image: rabbitmq:3-management
    container_name: rlxrabbitmq
    restart: unless-stopped
    environment:
      - RABBITMQ_DEFAULT_USER=UKjbldeMP7swx72qgIfqtU
      - RABBITMQ_DEFAULT_PASS=j8WkyK91Y6yjKS8vHG52lZ
    ports:
      - "6004:5672"
      - "6005:15672"
    networks:
      - rlxnetwork  
  elasticsearch:
    image: elasticsearch:8.17.3
    container_name: rlxelasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "6000:9200"
      - "6001:9300"
    volumes:
      - elastic_data:/usr/share/elasticsearch/data
    networks:
      - rlxnetwork  
  kibana:
    image: kibana:8.17.3
    container_name: rlxkibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://rlxelasticsearch:9200
    ports:
      - "6002:5601"
    networks:
      - rlxnetwork  
  rlxlog:
    image: ghcr.io/arel-uni/rlx-log:arm-1.0
    container_name: rlxlog
    restart: unless-stopped
    environment:
      - EntityLog__ConnectionString=**********************************************************************
      - RequestLog__ConnectionString=**********************************************************************
      - SystemLog__ConnectionString=http://rlxelasticsearch:9200
      - RabbitMQ__Host=rlxrabbitmq
      - RabbitMQ__Port=5672
      - RabbitMQ__Username=UKjbldeMP7swx72qgIfqtU
      - RabbitMQ__Password=j8WkyK91Y6yjKS8vHG52lZ
    networks:
      - rlxnetwork  
  redis:
    image: redis:latest
    container_name: rlxredis
    restart: unless-stopped
    ports:
      - "6006:6379"
    environment:
      - REDIS_PASSWORD=qkpWm2qLJqzVwN
    command: ["redis-server", "--requirepass", "qkpWm2qLJqzVwN"]  
    volumes:
      - redis_data:/data
    networks:
      - rlxnetwork    
  postgres:
    image: postgres:latest
    container_name: rlxpostgres
    restart: unless-stopped 
    ports:
      - "6007:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=1sKti45ApRbkADrwexU49a
      - POSTGRES_DB=RlxIdentity
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init:/docker-entrypoint-initdb.d
    networks:
      - rlxnetwork    
  rlxidentity:
    build:
      context: .
      dockerfile: Dockerfile.rlxidentity
    extra_hosts:
      - "dev-rlxidentity-api.arel.edu.tr:host-gateway"
      - "dev-logincenter.arel.edu.tr:host-gateway"
      - "dev-usermanagement.arel.edu.tr:host-gateway"
      - "dev-organizationmanagement.arel.edu.tr:host-gateway"
      - "dev-academicperformance.arel.edu.tr:host-gateway"  
    # image: ghcr.io/arel-uni/rlx-identity:1.0
    container_name: rlxidentity
    restart: unless-stopped 
    # ports:
    #   - "6008:8080"
    environment:
      - ASPNETCORE_FORWARDEDHEADERS_ENABLED=true
      - ConnectionStrings__RlxIdentity=Host=rlxpostgres;Database=RlxIdentity;Username=postgres;Password=1sKti45ApRbkADrwexU49a;Port=5432
      - ConnectionStrings__RlxIdentityShared=Host=rlxpostgres;Database=RlxIdentity;Username=readonly_user;Password=*j3aT@yq&HByVT;Port=5432
      - EntityLog__Host=rlxrabbitmq
      - EntityLog__Port=5672
      - EntityLog__Username=UKjbldeMP7swx72qgIfqtU
      - EntityLog__Password=j8WkyK91Y6yjKS8vHG52lZ     
      - RequestLog__Host=rlxrabbitmq
      - RequestLog__Port=5672
      - RequestLog__Username=UKjbldeMP7swx72qgIfqtU
      - RequestLog__Password=j8WkyK91Y6yjKS8vHG52lZ     
      - SystemLog__Host=rlxrabbitmq
      - SystemLog__Port=5672
      - SystemLog__Username=UKjbldeMP7swx72qgIfqtU
      - SystemLog__Password=j8WkyK91Y6yjKS8vHG52lZ     
      - RedisCache__ConnectionString=rlxredis:6379,password=qkpWm2qLJqzVwN
      - CorsOrigins=["https://dev-rlxidentity-api.arel.edu.tr:6010","https://dev-logincenter.arel.edu.tr:6014","https://usermanagement.arel.edu.tr:6040","https://organizationmanagement.arel.edu.tr:6050","https://academicperformance.arel.edu.tr:6060","http://localhost:6010",]
      - CookieDomain=arel.edu.tr
    # volumes:
    #   - rlxidentity_data:/app
    networks:
      - rlxnetwork
  rlxidentitynginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    extra_hosts:
      - "dev-rlxidentity-api.arel.edu.tr:host-gateway"
      - "dev-logincenter.arel.edu.tr:host-gateway"
      - "dev-usermanagement.arel.edu.tr:host-gateway"
      - "dev-organizationmanagement.arel.edu.tr:host-gateway"
      - "dev-academicperformance.arel.edu.tr:host-gateway"  
    container_name: rlxidentitynginx
    restart: unless-stopped 
    ports:
      - "6009:80"
      - "6010:443"
    volumes:
      - ./rlxidentity_nginx.conf:/etc/nginx/nginx.conf
      - ./certs:/etc/nginx/certs
    networks:
      - rlxnetwork
  logincenterredis:
    image: redis:latest
    container_name: logincenterredis
    restart: unless-stopped
    ports:
      - "6011:6379"
    environment:
      - REDIS_PASSWORD=k8TJPK8E0Jhx4rYtvLdG2A
    command: ["redis-server", "--requirepass", "k8TJPK8E0Jhx4rYtvLdG2A"]  
    volumes:
      - redis_data:/data
    networks:
      - rlxnetwork
  logincenter:
    build:
      context: .
      dockerfile: Dockerfile.logincenter
      # args:
      #   NEXT_PUBLIC_BASE_URL: https://dev-logincenter.arel.edu.tr:6014/
      #   NEXT_PUBLIC_LOGIN_URL: https://dev-logincenter.arel.edu.tr:6014/
    # image: ghcr.io/arel-uni/login-center:arm-1.0
    extra_hosts:
      - "dev-rlxidentity-api.arel.edu.tr:host-gateway"
      - "dev-logincenter.arel.edu.tr:host-gateway"
      - "dev-usermanagement.arel.edu.tr:host-gateway"
      - "dev-organizationmanagement.arel.edu.tr:host-gateway"
      - "dev-academicperformance.arel.edu.tr:host-gateway"  
    container_name: logincenter
    restart: unless-stopped 
    ports:
      - "6012:3000"
    # env_file:
    #   - .env.development      
    environment:
      - REDIS_CONNECTION_STRING=redis://:k8TJPK8E0Jhx4rYtvLdG2A@logincenterredis:6379
      - OPENIDDICT_AUTHORITY=https://dev-rlxidentity-api.arel.edu.tr:6010
      - OPENIDDICT_AUTHORITY_CONTAINER=https://dev-rlxidentity-api.arel.edu.tr:6010
      - OPENIDDICT_REDIRECT_URI=https://dev-logincenter.arel.edu.tr:6014/account/token
    # volumes:
    #   - rlxidentity_data:/app
    networks:
      - rlxnetwork
  logincenternginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    extra_hosts:
      - "dev-rlxidentity-api.arel.edu.tr:host-gateway"
      - "dev-logincenter.arel.edu.tr:host-gateway"
      - "dev-usermanagement.arel.edu.tr:host-gateway"
      - "dev-organizationmanagement.arel.edu.tr:host-gateway"
      - "dev-academicperformance.arel.edu.tr:host-gateway"  
    container_name: logincenternginx
    restart: unless-stopped 
    ports:
      - "6013:80"
      - "6014:443"
    volumes:
      - ./logincenter_nginx.conf:/etc/nginx/nginx.conf
      - ./certs:/etc/nginx/certs
    networks:
      - rlxnetwork       
  usermanagement:
    build:
      context: .
      dockerfile: Dockerfile.usermanagement
      # args:
      #   NEXT_PUBLIC_BASE_URL: https://dev-logincenter.arel.edu.tr:6014/
      #   NEXT_PUBLIC_LOGIN_URL: https://dev-logincenter.arel.edu.tr:6014/
    # image: ghcr.io/arel-uni/login-center:arm-1.0
    extra_hosts:
      - "dev-rlxidentity-api.arel.edu.tr:host-gateway"
      - "dev-logincenter.arel.edu.tr:host-gateway"
      - "dev-usermanagement.arel.edu.tr:host-gateway"
      - "dev-organizationmanagement.arel.edu.tr:host-gateway"
      - "dev-academicperformance.arel.edu.tr:host-gateway"  
    container_name: usermanagement
    restart: unless-stopped 
    ports:
      - "6015:3000"
    # env_file:
    #   - .env.development      
    # environment:
    #   - REDIS_CONNECTION_STRING=redis://:k8TJPK8E0Jhx4rYtvLdG2A@logincenterredis:6379
    #   - OPENIDDICT_AUTHORITY=https://dev-rlxidentity-api.arel.edu.tr:6010
    #   - OPENIDDICT_AUTHORITY_CONTAINER=https://dev-rlxidentity-api.arel.edu.tr:6010
    #   - OPENIDDICT_REDIRECT_URI=https://dev-logincenter.arel.edu.tr:6014/account/token
    # volumes:
    #   - rlxidentity_data:/app
    networks:
      - rlxnetwork     
  usermanagementnginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    extra_hosts:
      - "dev-rlxidentity-api.arel.edu.tr:host-gateway"
      - "dev-logincenter.arel.edu.tr:host-gateway"
      - "dev-usermanagement.arel.edu.tr:host-gateway"
      - "dev-organizationmanagement.arel.edu.tr:host-gateway"
      - "dev-academicperformance.arel.edu.tr:host-gateway"
    container_name: usermanagementnginx
    restart: unless-stopped 
    ports:
      - "6016:80"
      - "6040:443"
    volumes:
      - ./usermanagement_nginx.conf:/etc/nginx/nginx.conf
      - ./certs:/etc/nginx/certs
    networks:
      - rlxnetwork                 
volumes:
  elastic_data:
    driver: local
  mongo_data:
    driver: local
  redis_data:
    driver: local
  postgres_data:
    driver: local
  # rlxidentity_data:
  #   driver: local
networks:
  rlxnetwork: 
    driver: bridge
