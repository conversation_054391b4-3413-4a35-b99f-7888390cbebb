### GitHub giris
echo "****************************************" | docker login ghcr.io -u slckkhrmn-arel --password-stdin
----------------------------------
### macos a hosts tanimlama
code /etc/hosts
127.0.0.1 rlxidentity.arel.edu.tr
127.0.0.1 logincenter.arel.edu.tr
127.0.0.1 usermanagement.arel.edu.tr
127.0.0.1 organizationmanagement.arel.edu.tr
127.0.0.1 academicperformance.arel.edu.tr
127.0.0.1 rlxidentity-api.arel.edu.tr
127.0.0.1 dev-rlxidentity-api.arel.edu.tr
127.0.0.1 dev-logincenter.arel.edu.tr
127.0.0.1 dev-usermanagement.arel.edu.tr
127.0.0.1 dev-organizationmanagement.arel.edu.tr
127.0.0.1 dev-academicperformance.arel.edu.tr
127.0.0.1 dev-organizationmanagement-api.arel.edu.tr
127.0.0.1 dev-academicperformance-api.arel.edu.tr
----------------------------------
### macosta localca trusted yapma
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain certs/localCA.crt
----------------------------------
### docker compose degismiyorsa
docker-compose build --no-cache
