openssl genrsa -out localCA.key 2048
openssl req -x509 -new -nodes -key localCA.key -sha256 -days 3650 -out localCA.pem -subj "/CN=Local Dev CA"
openssl genrsa -out localhost.key 2048
openssl req -new -key localhost.key -out localhost.csr -subj "/CN=localhost"
openssl x509 -req -in localhost.csr -CA localCA.pem -CAkey localCA.key -CAcreateserial -out localhost.crt -days 1825 -sha256 -extfile localhost.ext

#macos
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain localCA.crt

